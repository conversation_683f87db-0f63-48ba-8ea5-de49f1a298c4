package com.caidaocloud.hrpaas.service.application.metadata.service;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.ImportTemplateDo;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.ImportTemplateRepository;
import com.caidaocloud.hrpaas.service.interfaces.dto.template.ImportTemplateDto;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.RequestHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class ImportTemplateService {
    private final static String IMPORT_TEMPLATE_OWNER = "hrpaas";

    @Resource
    private ImportTemplateRepository importTemplateRepository;
    @Resource
    private ISessionService sessionService;

    public void saveImportTemplate(ImportTemplateDto importTemplateDto) {
        log.info("saveImportTemplate. ImportTemplateDto = {}", FastjsonUtil.toJson(importTemplateDto));
        UserInfo userInfo = sessionService.getUserInfo();
        PreCheck.preCheckArgument(userInfo == null, "用户登录过期！");
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        ImportTemplateDo importTemplateDo = ObjectConverter.convert(importTemplateDto, ImportTemplateDo.class);
        importTemplateDo.setTenantId(sessionService.getTenantId());
        importTemplateDo.setModel(IMPORT_TEMPLATE_OWNER);
        importTemplateDo.setDeleted(0);
        importTemplateDo.setPath(String.join(",", importTemplateDto.getFiles().getUrls()));
        importTemplateDo.setTemplateName(String.join(",", importTemplateDto.getFiles().getNames()));
        importTemplateDo.setCreateBy(userId);
        importTemplateDo.setCreateTime(System.currentTimeMillis());
        importTemplateDo.setUpdateBy(userId);
        importTemplateDo.setUpdateTime(importTemplateDo.getCreateTime());
        importTemplateDo.save();
    }

    public void updateImportTemplate(ImportTemplateDto importTemplateDto) {
        ImportTemplateDo importTemplateDo = this.selectImportTemplate(importTemplateDto);
        if(null == importTemplateDo || StringUtil.isEmpty(importTemplateDo.getTenantId())){
            log.info("The template information does not exist. importTemplateDto={}", FastjsonUtil.toJson(importTemplateDto));
            PreCheck.preCheckArgument(true, "操作的数据不存在!");
        }
        BeanUtil.copyWithNoValue(importTemplateDto, importTemplateDo);
        // 附件 支持删除，因此这里无需判空
        importTemplateDo.setPath(String.join(",", importTemplateDto.getFiles().getUrls()));
        importTemplateDo.setTemplateName(String.join(",", importTemplateDto.getFiles().getNames()));
        importTemplateDo.update();
    }

    public void delete(String id) {
        importTemplateRepository.delete(id);
    }

    public ImportTemplateDo selectImportTemplate(ImportTemplateDto importTemplateDto) {
        return importTemplateRepository.loadById(RequestHolder.getTenantId(),importTemplateDto.getId());
    }

    public List<ImportTemplateDo> getList() {
        UserInfo userInfo = sessionService.getUserInfo();
        PreCheck.preCheckArgument(userInfo == null, "用户登录过期！");
        List<ImportTemplateDo> list = ImportTemplateDo.getList(0, 5000);
        if(null == list || list.isEmpty()){
            return new ArrayList();
        }
        return list;
    }

    public ImportTemplateDo selectImportTemplateByCode(String templateCode) {
        return importTemplateRepository.loadByCode(RequestHolder.getTenantId(),templateCode);
    }
}
