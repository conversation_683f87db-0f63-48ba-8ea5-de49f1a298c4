package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
public class UiGeneralDataSourceDto extends UiDataSourceRenderDto {
    private final static long serialVersionUID = 1L;

    /**
     * 作用范围是否是一次请求只请求一次接口
     */
    @ApiModelProperty("作用范围")
    private boolean requestScope = true;

    /**
     * 前置 hook
     */
    @ApiModelProperty("前置 hook")
    private String preHook;

    /**
     * 前置 hook 参数
     */
    @ApiModelProperty("前置 hook 参数")
    private Map preOption;

    /**
     * 后置 hook
     */
    @ApiModelProperty("后置 hook")
    private String postHook;

    /**
     * 后置 hook 参数
     */
    @ApiModelProperty("后置 hook 参数")
    private Map postOption;

    // 认证信息 ...
}
