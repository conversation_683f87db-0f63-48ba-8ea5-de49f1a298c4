package com.caidaocloud.metadata.infrastructure.repository.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-06-29
 */
public abstract class HolderUtil {
    private static final String HOLDER_DEFAULT_KEY = "_set_holder_default_key", HOLDER_TENANTID_KEY = "_TenantId";
    private static final ThreadLocal<Map<String, Object>> HOLDER_THREAD_LOCAL = new ThreadLocal<>();

    public static Map<String, Object> get(){
        Map<String, Object> map = HOLDER_THREAD_LOCAL.get();
        return map;
    }

    public static Object get(String key){
        Map<String, Object> map = HOLDER_THREAD_LOCAL.get();
        if(null == map){
            return null;
        }
        return map.get(key);
    }

    public static String getStr(){
        return getStr(HOLDER_DEFAULT_KEY);
    }

    public static String getTenantId(){
        return getStr(HOLDER_TENANTID_KEY);
    }

    public static String getStr(String key){
        Object obj = get(key);
        if(null == obj){
            return null;
        }

        return obj2Str(obj);
    }

    public static Map<String, Object> pop(){
        Map<String, Object> map = get();
        HOLDER_THREAD_LOCAL.remove();
        return map;
    }

    public static Object pop(String key){
        Object obj = get(key);
        HOLDER_THREAD_LOCAL.remove();
        return obj;
    }

    public static String popStr(String key){
        Object obj = pop(key);
        return obj2Str(obj);
    }

    public static void set(Map<String, Object> map){
        HOLDER_THREAD_LOCAL.set(map);
    }

    public static void setTenantId(Object value){
        set(HOLDER_TENANTID_KEY, value);
    }

    public static void set(Object value){
        set(HOLDER_DEFAULT_KEY, value);
    }

    public static void set(String key, Object value){
        Map<String, Object> map = new HashMap();
        map.put(key, value);
        set(map);
    }

    public static void clear(){
        HOLDER_THREAD_LOCAL.remove();
    }

    private static String obj2Str(Object obj){
        return null == obj ? null : obj.toString();
    }
}
