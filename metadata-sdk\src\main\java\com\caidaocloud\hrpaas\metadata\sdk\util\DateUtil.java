package com.caidaocloud.hrpaas.metadata.sdk.util;

import lombok.val;

import java.text.SimpleDateFormat;
import java.time.*;
import java.util.Date;

public class DateUtil {

    public static ZoneOffset currentOffset = OffsetDateTime.now().getOffset();

    public static long MAX_TIMESTAMP = LocalDateTime
            .of(LocalDate.of(9999,12,31), LocalTime.MAX)
            .toEpochSecond(currentOffset) * 1000;

    public static String getNowDate(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(new Date());
    }

    public static Integer getTodayDate(){
        String nowDate = getNowDate();
        return Integer.parseInt(nowDate);
    }

    public static long getMidnightTimestamp(){
        val time = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        return time.toEpochSecond(currentOffset) * 1000;
    }
}
