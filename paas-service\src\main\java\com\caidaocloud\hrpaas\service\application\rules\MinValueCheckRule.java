package com.caidaocloud.hrpaas.service.application.rules;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MinValueCheckRule implements ICheckRule{
    private static String MIN_VALUE_TIPS = "最小值不合法";

    @Override
    public String getRuleType() {
        return "minValue";
    }

    @Override
    public String[] getRuleGroup() {
        return new String[]{"Number"};
    }

    @Override
    public void check(IRule checkRuleDto) {
        if(null == checkRuleDto){
            return;
        }

        String tips = StringUtil.isBlank(checkRuleDto.tips()) ? MIN_VALUE_TIPS : checkRuleDto.tips();
        try {
            if(StringUtil.isNotEmpty(checkRuleDto.value()) && Long.parseLong(checkRuleDto.value()) < Long.parseLong(checkRuleDto.rule())){
                throw new ServerException(tips);
            }
        } catch (Exception e){
            log.error("MinValue Validation exception, {}", e.getMessage(), e);
            throw new ServerException(tips);
        }
    }
}
