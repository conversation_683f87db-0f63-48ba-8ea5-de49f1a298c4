package com.caidaocloud.hrpaas.metadata.sdk.query;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.CountUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.service.DataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class DataJoin {
    private ModelInfo firstModel;
    private ModelInfo secondModel;
    private ModelInfo thirdModel;
    private JoinInfo firstJoin;
    private JoinInfo secondJoin;
    private int pageSize = 20;
    private int pageNo = 1;
    private int modelCount;
    private long queryTime;
    private boolean isMasterDataQuery = false;

    @Data
    public static class ModelInfo {
        private String identifier;
        private List<String> properties = Lists.list();
        private String filter;
        public static ModelInfo model(String identifier, List<String> properties,
                               @Nullable DataFilter filter){
            ModelInfo info = new ModelInfo();
            info.identifier = identifier;
            info.properties.addAll(properties.stream().map(it->it.replaceAll("\\$", ".")).collect(Collectors.toList()));
            if(null != filter){
                info.filter = FastjsonUtil.toJson(filter);
            }
            return info;
        }
    }

    @Data
    public static class JoinPropertyInfo{
        private String leftIdentifier;
        private String rightIdentifier;
        private String leftProperty;
        private String rightProperty;
        public static JoinPropertyInfo joinProperty(String leftIdentifier,String rightIdentifier,
                                                    String leftProperty, String rightProperty){
            JoinPropertyInfo info = new JoinPropertyInfo();
            info.leftIdentifier = leftIdentifier;
            info.rightIdentifier = rightIdentifier;
            info.leftProperty = leftProperty.replaceAll("\\$", ".");
            info.rightProperty = rightProperty.replaceAll("\\$", ".");
            return info;
        }
    }

    @Data
    public static class JoinInfo{
        private List<JoinPropertyInfo> join = Lists.list();
        public static JoinInfo joinInfo(List<JoinPropertyInfo> join){
            val info = new JoinInfo();
            info.join.addAll(join);
            return info;
        }
    }

    public DataJoin limit(int pageSize, int pageNo){
        if(pageSize > CountUtil.MAX_LIMIT){
            throw new ServerException("超出最大查询限制");
        }
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        return this;
    }

    public static DataJoin joinModels(ModelInfo firstModel, ModelInfo secondModel, JoinInfo on){
        DataJoin dataJoin = new DataJoin();
        dataJoin.firstModel = firstModel;
        dataJoin.secondModel = secondModel;
        dataJoin.firstJoin = on;
        dataJoin.modelCount = 2;
        on.join.forEach(joinProperty->{
            if(!StringUtils.equals(firstModel.identifier, joinProperty.leftIdentifier) ||
                    !StringUtils.equals(secondModel.identifier, joinProperty.rightIdentifier)
            ){
                throw new ServerException("illegal join");
            }
        });
        return dataJoin;
    }

    public DataJoin joinModel(ModelInfo thirdModel, JoinInfo on){
        if(modelCount == 3){
            throw new ServerException("too many join");
        }
        this.thirdModel = thirdModel;
        this.secondJoin = on;
        this.modelCount++;
        on.join.forEach(joinProperty->{
            if(!StringUtils.equals(firstModel.identifier, joinProperty.leftIdentifier) &&
                    !StringUtils.equals(secondModel.identifier, joinProperty.leftIdentifier)||
                    !StringUtils.equals(thirdModel.identifier, joinProperty.rightIdentifier)
            ){
                throw new ServerException("illegal join");
            }
        });
        return this;
    }

    public <T extends DataSimple> PageResult<Triple<T, T, T>> join(Class<T> clazz, long queryTime){
        try{
            MdQueryTL.set(isMasterDataQuery);
            QueryInfoCache.init();
            this.queryTime = queryTime;
            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val firstMetadata = metadataService.load(firstModel.identifier);
            val secondMetadata = metadataService.load(secondModel.identifier);
            if(null != this.getFirstModel() && StringUtils.isNotEmpty(this.getFirstModel().getFilter())){
                this.getFirstModel().setFilter(FastjsonUtil.toJson(DataQuery.addScope(
                        this.getFirstModel().getIdentifier(),
                        DataFilter.fromJsonString(this.getFirstModel().getFilter()),
                        System.currentTimeMillis())));
            }
            if(null != this.getSecondModel() && StringUtils.isNotEmpty(this.getSecondModel().getFilter())){
                this.getSecondModel().setFilter(FastjsonUtil.toJson(DataQuery.addScope(
                        this.getSecondModel().getIdentifier(),
                        DataFilter.fromJsonString(this.getSecondModel().getFilter()),
                        System.currentTimeMillis())));
            }
            if(null != this.getThirdModel() && StringUtils.isEmpty(this.getThirdModel().getFilter())){
                this.getThirdModel().setFilter(FastjsonUtil.toJson(DataQuery.addScope(
                        this.getThirdModel().getIdentifier(),
                        DataFilter.fromJsonString(this.getThirdModel().getFilter()),
                        System.currentTimeMillis())));
            }
            PageResult<Triple<EntityDataDto,EntityDataDto,EntityDataDto>> dataPage = SpringUtil.getBean(DataOperatorService.class).join(this);
            val list =  Sequences.sequence(dataPage.getItems()).map(triple->{
                T firstData = DataSimple.fromPersist(DataQuery.identifier(firstModel.identifier),
                        firstMetadata, triple.getLeft(), clazz);
                T secondData = DataSimple.fromPersist(DataQuery.identifier(secondModel.identifier),
                        secondMetadata, triple.getMiddle(), clazz);
                T thirdData = null;
                if(modelCount == 3){
                    val thirdMetadata = metadataService.load(thirdModel.identifier);
                    thirdData = DataSimple.fromPersist(DataQuery.identifier(thirdModel.identifier),
                            thirdMetadata, triple.getRight(), clazz);
                }
                return Triple.of(firstData,secondData,thirdData);
                    }).toList();
            return new PageResult(list, dataPage.getPageNo(), dataPage.getPageSize(), dataPage.getTotal());
        }finally {
            MdQueryTL.remove();
            QueryInfoCache.clear();
        }
    }

    public void demo(){
        val join = DataJoin.joinModels(
                DataJoin.ModelInfo.model("entity.hr.EmpWorkInfo", Lists.list("bid", "company"),DataFilter.eq("tenantId", "33")),
                DataJoin.ModelInfo.model("entity.hr.EmpBasicInfo", Lists.list("bid", "empStatus"),DataFilter.eq("tenantId", "33")),
                DataJoin.JoinInfo.joinInfo(
                        Lists.list(
                                DataJoin.JoinPropertyInfo.joinProperty(
                                        "entity.hr.EmpWorkInfo","entity.hr.EmpBasicInfo",
                                        "empId","bid"
                                )
                        )
                )
        );
        join.joinModel(DataJoin.ModelInfo.model("entity.hr.EmpEduExp", Lists.list("bid", "school"),null),
                DataJoin.JoinInfo.joinInfo(
                        Lists.list(
                                DataJoin.JoinPropertyInfo.joinProperty(
                                        "entity.hr.EmpWorkInfo","entity.hr.EmpEduExp",
                                        "empId","empId"
                                ),
                                DataJoin.JoinPropertyInfo.joinProperty(
                                        "entity.hr.EmpBasicInfo","entity.hr.EmpEduExp",
                                        "bid","empId"
                                )
                        )
                )
        );
        val r = join.join(DataSimple.class, System.currentTimeMillis());
    }

}
