package com.caidaocloud.hrpaas.paas.common.feign;

import java.util.List;

import com.caidaocloud.hrpaas.paas.common.dto.FormDataDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDataMapDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        value = "${feign.rename.caidaocloud-hr-paas-service:caidaocloud-hr-paas-service}",
        fallback = FormFeignFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "paasFormFeignClient"
)
public interface FormFeignClient {
    /**
     * 表单配置列表(无分页)
     *
     * @param formType
     * @param implantable
     * @return
     */
    @GetMapping("/api/hrpaas/v1/form/def/list")
    Result<List<FormDefDto>> getFormDefList(@RequestParam String formType,
                                            @RequestParam(required = false, defaultValue = "false") boolean implantable);

    @GetMapping("/api/hrpaas/v1/form/def/list")
    Result<List<FormDefDto>> getFormDefList(@RequestParam String formType);

    /**
     * 表单配置详情(名称查询)
     *
     * @param name
     * @return
     */
    @GetMapping("/api/hrpaas/v1/form/def/one/:name")
    Result<FormDefDto> getFormDef(@RequestParam("name") String name);

    /**
     * 表单配置详情
     *
     * @param id
     * @return
     */
    @GetMapping("/api/hrpaas/v1/form/def/one")
    Result<FormDefDto> getFormDefById(@RequestParam("id") String id);

    /**
     * 表单配置详情
     *
     * @param code
     * @return
     */
    @GetMapping("/api/hrpaas/v1/form/def/one/:code")
    Result<FormDefDto> getFormDefByCode(@RequestParam("code") String code);

    /**
     * 查询表单数据
     *
     * @param formId
     * @param id
     * @return
     */
    @GetMapping("/api/hrpaas/v1/form/data/one/{formId}")
    Result<FormDataMapDto> getFormDataMap(@PathVariable("formId") String formId,
                                          @RequestParam("id") String id);

    /**
     * 保存表单数据
     *
     * @param formId
     * @param formDataDto
     * @return
     */
    @PostMapping("/api/hrpaas/v1/form/data/create/{formId}")
    Result<String> saveFormData(@PathVariable String formId, @RequestBody FormDataDto formDataDto);

    /**
     * 更新表单
     *
     * @param formId
     * @param formDataDto
     * @return
     */
    @PutMapping("/api/hrpaas/v1/form/data/update/{formId}")
    Result update(@PathVariable String formId, @RequestBody FormDataDto formDataDto);
}