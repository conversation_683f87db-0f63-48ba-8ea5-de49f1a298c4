package com.caidaocloud.hrpaas.service.application.rules;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通用正则校验规则
 * <AUTHOR>
 * @date 2022-01-04
 */
@Service
public class RegexCheckRule implements ICheckRule {
    @Override
    public String getRuleType() {
        return "regex";
    }

    @Override
    public String [] getRuleGroup() {
        return new String [] {"String"};
    }

    @Override
    public void check(IRule checkRuleDto) {
        if(null == checkRuleDto || StringUtil.isEmpty(checkRuleDto.value())){
            return;
        }

        final Pattern REGEX_PATTERN = Pattern.compile(checkRuleDto.rule());
        Matcher matcher = REGEX_PATTERN.matcher(checkRuleDto.value());
        if(!matcher.matches()){
            throw new ServerException(checkRuleDto.tips());
        }
    }
}
