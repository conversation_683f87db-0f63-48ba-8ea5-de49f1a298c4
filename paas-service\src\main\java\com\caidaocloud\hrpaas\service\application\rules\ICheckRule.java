package com.caidaocloud.hrpaas.service.application.rules;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.StringUtil;

import javax.annotation.PostConstruct;

public interface ICheckRule {

    /**
     * 规则类型
     */
    String getRuleType();

    /**
     * 规则分组
     */
    String [] getRuleGroup();

    @PostConstruct
    default void defaultRegisterCheckRule(){
        String ruleType = getRuleType();
        if(StringUtil.isEmpty(ruleType)){
            throw new ServerException("rule type Cannot be empty");
        }

        String [] ruleGroup = getRuleGroup();
        if(null == ruleGroup || ruleGroup.length <= 0){
            throw new ServerException("rule group Cannot be empty");
        }

        for (String group : ruleGroup) {
            CheckRuleFactory.registerCheckRuleGroup(group, ruleType);
            CheckRuleFactory.registerCheckRule(ruleType, this);
        }
    }

    /**
     * 字段校验
     * @param value 字段值
     * @param rule 字段校验规则
     * @return 校验是否通过；true 校验通过，false 未通过
     */
    default boolean check(String value, String rule){
        return false;
    }

    /**
     * 字段校验
     */
    default void check(IRule checkRuleDto){}

    default IRule getCheckRule(){
        return null;
    }

    default void setCheckRule(IRule checkRuleDto){

    }
}
