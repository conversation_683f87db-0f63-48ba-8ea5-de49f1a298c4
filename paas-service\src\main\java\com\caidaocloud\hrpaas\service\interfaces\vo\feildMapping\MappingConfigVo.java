package com.caidaocloud.hrpaas.service.interfaces.vo.feildMapping;

import com.caidaocloud.masterdata.entity.mapping.PropertyMapping;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MappingConfigVo {
    @ApiModelProperty("配置id")
    private String id;
    @ApiModelProperty("映射源identifier")
    private String source;
    @ApiModelProperty("映射源名称")
    private String sourceName;
    @ApiModelProperty("映射目标identifier")
    private String target;
    @ApiModelProperty("映射目标名称")
    private String targetName;
    @ApiModelProperty("属性映射")
    private List<PropertyMapping> mapping;
    @ApiModelProperty("模型字段映射名称")
    private String name;
    @ApiModelProperty("模型字段映射类别")
    private String category;
}
