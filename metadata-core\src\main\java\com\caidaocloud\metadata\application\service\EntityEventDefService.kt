package com.caidaocloud.metadata.application.service

import com.caidaocloud.hrpaas.core.metadata.interfaces.dto.EntityEventDefAddDto
import com.caidaocloud.hrpaas.core.metadata.interfaces.dto.EntityEventDefUpdateDto
import com.caidaocloud.hrpaas.core.metadata.interfaces.vo.EntityEventDefVo
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityEventCreateDto
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil
import com.caidaocloud.metadata.domain.service.EntityEventDefDomainService
import com.caidaocloud.metadata.domain.enums.DefChannel
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class EntityEventDefService {

    @Autowired
    private lateinit var entityEventDefDomainService: EntityEventDefDomainService

    fun open(id : String){
        entityEventDefDomainService.open(id)
    }

    fun close(id : String){
        entityEventDefDomainService.close(id)
    }

    fun updateConsumerList(id : String, consumerList : List<String>){
        entityEventDefDomainService.updateConsumerList(id, consumerList)
    }

    fun addConsumerList(id : String, consumerList : List<String>){
        entityEventDefDomainService.addConsumerList(id, consumerList)
    }

    fun removeConsumerList(id : String, consumerList : List<String>){
        entityEventDefDomainService.removeConsumerList(id, consumerList)
    }

    fun update(id : String, channel: DefChannel, def: EntityEventDefUpdateDto){
        entityEventDefDomainService.update(id, channel, def)
    }

    fun create(channel: DefChannel, def: EntityEventDefAddDto): String{
        return entityEventDefDomainService.create(channel, def)
    }

    fun loadByModelRef(modelRef : String) : List<EntityEventDefVo>{
        return entityEventDefDomainService.loadByModelRef(modelRef).map {
            JsonEnhanceUtil.toObject(it, EntityEventDefVo::class.java)
        }
    }

    fun createStandardEvent(def: EntityEventCreateDto) {
        val addDto =   EntityEventDefAddDto(
            def.identifier,
            def.modelRef,
            def.name,
            def.isTrackAll,
            def.trackedProperties,
            def.notifyProperties,
            def.isTrackRelation
        )
        val id = create(DefChannel.SCRIPT, addDto)
        addConsumerList(id, def.consumerList)
        open(id)
    }


}