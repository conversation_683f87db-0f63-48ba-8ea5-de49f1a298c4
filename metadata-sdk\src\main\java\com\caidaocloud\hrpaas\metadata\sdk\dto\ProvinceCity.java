package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

@Deprecated
@Data
@DataComponent(dataType = PropertyDataType.PROVINCE_CITY)
public class ProvinceCity implements ComponentPropertyValue{

    private String cityId;

    private String cityName;

    private String provinceId;

    private String provinceName;

    @Override
    public Map<String, Boolean> propertySuffixToWhetherLong(){
        return Maps.map(
                Sequences.sequence(
                        Pair.pair(".cityId", false),
                        Pair.pair(".provinceId", false)
                )
        );
    }

    @Override
    public PropertyValue extractDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties){
        return toDisplay(propDef, properties);
    }

    //todo 省市缓存
    public static ProvinceCity toDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties){
        val provinceCity = properties.stream()
                .filter(propertyDataDto -> propertyDataDto.getProperty().startsWith(propDef.getProperty() + "."))
                .findFirst();
        if(provinceCity.isPresent()){
            ProvinceCity pc = new ProvinceCity();
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals(propDef.getProperty() + ".cityId"))
                    .findFirst().ifPresent(cityId->{
                pc.setCityId(cityId.getValue());
                if(null != cityId.getValue()){
                    pc.setCityName(
                            QueryInfoCache.getCity(cityId.getValue()));
                }
            });
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals(propDef.getProperty() + ".provinceId"))
                    .findFirst().ifPresent(provinceId->{
                pc.setProvinceId(provinceId.getValue());
                if(null!=provinceId.getValue()){
                    pc.setProvinceName(
                            QueryInfoCache.getProvince(provinceId.getValue()));
                }
            });
            return pc;
        }else{
            return null;
        }
    }

    @Override
    public List<PropertyDataDto> extractPersist(DataSimple data, MetadataPropertyVo propDef){
        return toPersist(data, propDef);
    }

    public static List<PropertyDataDto> toPersist(DataSimple data, MetadataPropertyVo propDef){
        List<PropertyDataDto> result = Lists.newArrayList();
        val dataMap = JsonEnhanceUtil.toObject(data, Map.class);
        val customPropertyValue = data.getProperties().get(propDef.getProperty());
        ProvinceCity provinceCity = null;
        if(dataMap.containsKey(propDef.getProperty())){
            val standardPropertyValue = dataMap.get(propDef.getProperty());
            if(null != standardPropertyValue){
                provinceCity = JsonEnhanceUtil.toObject(standardPropertyValue, ProvinceCity.class);
            }
        }else if(null != customPropertyValue){
            if(customPropertyValue instanceof ProvinceCity){
                provinceCity = (ProvinceCity) customPropertyValue;
            }else{
                provinceCity = FastjsonUtil.toObject(
                        ((SimplePropertyValue)customPropertyValue).getValue(), ProvinceCity.class);
            }
        }
        if(null != provinceCity){
            if(null!=provinceCity.getProvinceId()){
                PropertyDataDto provinceId = new PropertyDataDto();
                provinceId.setProperty(propDef.getProperty() + ".provinceId");
                provinceId.setDataType(PropertyDataType.PROVINCE_CITY);
                provinceId.setValue(provinceCity.getProvinceId());
                result.add(provinceId);
            }
            if(null!=provinceCity.getCityId()){
                PropertyDataDto cityId = new PropertyDataDto();
                cityId.setProperty(propDef.getProperty() + ".cityId");
                cityId.setDataType(PropertyDataType.PROVINCE_CITY);
                cityId.setValue(provinceCity.getCityId());
                result.add(cityId);
            }
        }
        return result;
    }

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {
        ProvinceCity dataProvinceCity = (ProvinceCity) dataVal;
        if(null == dataProvinceCity){
            return;
        }
        dataValue.fitDataValue(dataProvinceCity);
        // 列表显示内容
        DataValueFunction provinceCityTxt = dataValue.dataValueBulid();

        provinceCityTxt.fitDataProp(dataValue.loadDataProp() + "_txt");
        provinceCityTxt.fitDataValue(
                StringUtils.trimToEmpty(dataProvinceCity.getProvinceName())
                + StringUtils.trimToEmpty(dataProvinceCity.getCityName())
        );
        dataVals.add(provinceCityTxt);
    }

    @Override
    public String toText() {
        return null;
    }
}
