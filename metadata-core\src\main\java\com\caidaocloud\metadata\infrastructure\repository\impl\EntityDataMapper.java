package com.caidaocloud.metadata.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.metadata.infrastructure.repository.po.EntityDataPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

public interface EntityDataMapper extends BaseMapper<EntityDataPo> {

    @Update("${execSql}")
    void exec(@Param("execSql") String execSql);

    @Select("${countSql}")
    long count(@Param("countSql") String countSql);

    @Select("${selectSql}")
    List<Map<String, Object>> select(@Param("selectSql") String selectSql);
}
