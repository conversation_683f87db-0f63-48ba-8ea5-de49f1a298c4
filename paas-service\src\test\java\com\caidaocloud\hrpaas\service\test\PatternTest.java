package com.caidaocloud.hrpaas.service.test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.junit.Assert;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @date 2023/4/12
 */
public class PatternTest {
	String api = "/api/bcc/dict/common/v1/dict/getEnableDictList?belongModule=Employee&typeCode=YesOrNoType";

	@Test
	public void  test(){
		Pattern pattern = Pattern.compile("typeCode=([^&]+)");
		Matcher matcher = pattern.matcher(api);
		if (matcher.find()) {
			String code = matcher.group(1);
			Assert.assertEquals("正则获取code失败", "YesOrNoType", code);
			System.out.println(code);
		}
	}
}
