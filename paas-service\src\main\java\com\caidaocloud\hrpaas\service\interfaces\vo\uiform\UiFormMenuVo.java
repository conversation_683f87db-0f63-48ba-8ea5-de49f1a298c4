package com.caidaocloud.hrpaas.service.interfaces.vo.uiform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-10-26
 */
@Data
public class UiFormMenuVo {
    /**
     * 菜单id
     */
    @ApiModelProperty("菜单id")
    private String id;

    /**
     * 菜单code
     */
    @ApiModelProperty("菜单code")
    private String code;

    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称")
    private String name;

    /**
     * 菜单icon
     */
    @ApiModelProperty("菜单icon")
    private String icon;

    /**
     * 菜单url
     */
    @ApiModelProperty("菜单url")
    private String url;

    /**
     * 子菜单
     */
    @ApiModelProperty("子菜单")
    private List<UiFormMenuVo> children;

    /**
     * 菜单类型
     */
    @ApiModelProperty("菜单类型")
    private String type;

    /**
     * 菜单所属模块
     * hrpaas 模块，attendance 考勤模块，payroll 薪资模块
     */
    @ApiModelProperty("菜单所属模块")
    private String model;

    /**
     * 菜单 share，是否分享
     */
    @ApiModelProperty("是否分享")
    private boolean share;

    /**
     * 菜单 share 相关的配置
     */
    @ApiModelProperty("菜单 share 相关的配置")
    private String shareConfig;

}
