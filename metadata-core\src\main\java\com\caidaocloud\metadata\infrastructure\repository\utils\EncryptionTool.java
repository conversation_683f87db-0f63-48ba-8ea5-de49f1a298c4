package com.caidaocloud.metadata.infrastructure.repository.utils;

import cn.hutool.crypto.SecureUtil;
import com.googlecode.totallylazy.Lists;
import lombok.SneakyThrows;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.analysis.tokenattributes.TermAttribute;
import org.wltea.analyzer.lucene.IKAnalyzer;

import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

public class EncryptionTool {

    public final static String encryptionSign = "*detpyrcne";

    public static boolean isSearchableEncrypted(String s) {
        return s != null && s.endsWith(encryptionSign);
    }

    public static void main(String[] args) {
        val key = "jlt4fdgak56jsdladsjdgladsjgldsjl";
        System.out.println(key.getBytes(StandardCharsets.UTF_8).length);
        val a = searchableEncrypt("thoma<PERSON>", key);
        val b = searchableDecrypt(a, key);
        System.out.println(a);
        System.out.println(b);
    }

    public static String encrypt(String s, String key){
        if(StringUtils.isEmpty(s)){
            return s;
        }
        val aes = SecureUtil.aes(key.getBytes(StandardCharsets.UTF_8));
        return aes.encryptBase64(s);
    }

    public static String searchableEncrypt(String s, String key){
        if(StringUtils.isEmpty(s)){
            return s;
        }
        val analysed = analyse(s);
        analysed.add(0, s);
        return analysed.stream().map(it
                -> encrypt(it, key) + "*").collect(Collectors.joining()) + encryptionSign;
    }

    public static String searchableDecrypt(String s, String key){
        if(StringUtils.isEmpty(s)){
            return s;
        }
        return decrypt(s.substring(0, s.indexOf("*")), key);
    }

    public static String decrypt(String s, String key){
        if(StringUtils.isEmpty(s)){
            return s;
        }
        val aes = SecureUtil.aes(key.getBytes(StandardCharsets.UTF_8));
        return aes.decryptStr(s);
    }

    @SneakyThrows
    private static List<String> analyse(String str){
        if(StringUtils.isEmpty(str)){
            return Lists.list();
        }
        List<String> result = Lists.list();
        Analyzer anal=new IKAnalyzer(true);
        StringReader reader=new StringReader(str);
        try {
            TokenStream ts=anal.tokenStream("", reader);
            TermAttribute term=ts.getAttribute(TermAttribute.class);
            while(ts.incrementToken()){
                result.add(term.term());
            }
        } finally {
            reader.close();
        }
        return result;
    }
}
