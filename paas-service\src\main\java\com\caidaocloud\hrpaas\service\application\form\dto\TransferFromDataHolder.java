package com.caidaocloud.hrpaas.service.application.form.dto;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import com.caidaocloud.hrpaas.service.application.common.dto.TextValueDto;
import com.caidaocloud.hrpaas.service.application.form.feign.ITransferFeign;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.Data;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TransferFromDataHolder {

    @Autowired
    private ITransferFeign transferFeign;

    private static ThreadLocal<TransferFromData> threadLocal = new ThreadLocal();

    @Data
    public static class TransferFromData {
        private String company;
        private String companyTxt;
        private TextValueDto empType;
        private JobGradeRange jobGrade;
        private String job;
        private String jobTxt;
        private String leadEmpId;
        private String organize;
        private String organizeTxt;
        private String salary;
        private TextValueDto salaryType;
        private String post;
        private String postTxt;
        private TextValueDto workHour;
        private String workplace;
        private String workplaceTxt;
    }

    public void init(String empId){
        val result = transferFeign.getCurrentData(empId);
        if(!result.isSuccess()){
            String errMsg = result.getMsg();
            throw new ServerException(StringUtil.isEmpty(errMsg) ? "获取异动初始信息异常" : errMsg);
        }
        val data = FastjsonUtil.convertObject(result.getData(),
                TransferFromData.class);
        threadLocal.set(data);
    }

    public static void clear(){
        threadLocal.remove();
    }

    public static TransferFromData get(){
        return threadLocal.get();
    }
}
