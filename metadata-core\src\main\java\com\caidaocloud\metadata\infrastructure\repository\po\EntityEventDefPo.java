package com.caidaocloud.metadata.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.metadata.sdk.enums.EventDefStatus;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.metadata.domain.entity.EntityEventDef;
import com.caidaocloud.metadata.infrastructure.configuration.handler.BooleanTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("paas_event_def")
public class EntityEventDefPo extends BasePo<EntityEventDefPo, EntityEventDef> {

    private String identifier;

    private String name;

    private String modelRef;
    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean builtIn;
    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean trackAll = true;

    @DisplayAsArray
    private String standardTrackedProperties;

    @DisplayAsArray
    private String customTrackedProperties;

    @DisplayAsArray
    private String standardNotifyProperties;

    @DisplayAsArray
    private String customNotifyProperties;

    @DisplayAsArray
    private String consumerList;

    private EventDefStatus status;

    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean trackRelation;

    private Long createTime;

    private String createBy;

    private Long updateTime;

    private String updateBy;

}
