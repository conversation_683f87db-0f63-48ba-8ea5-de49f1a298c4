package com.caidaocloud.hrpaas.metadata.sdk.expression.function;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import org.jetbrains.annotations.NotNull;

/**
 *
 * <AUTHOR>
 * @date 2024/3/11
 */
public abstract class AbsRoundFunction extends AbstractFunction {

	@Override
	public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
		Number value = FunctionUtils.getNumberValue(arg1, env);
		BigDecimal result = round(value, 0);
		return FunctionUtils.wrapReturn(result);
	}

	@Override
	public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
		Number value = FunctionUtils.getNumberValue(arg1, env);
		Number scale = FunctionUtils.getNumberValue(arg2, env);
		BigDecimal result = round(value, scale.intValue());
		return FunctionUtils.wrapReturn(result);
	}


	@NotNull
	private BigDecimal round(Number value, int scale) {
		BigDecimal bdValue = new BigDecimal(value.toString());
		return bdValue.setScale(scale, getRoundingMode());
	}

	protected abstract RoundingMode getRoundingMode();

}
