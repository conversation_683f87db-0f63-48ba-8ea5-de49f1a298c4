package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.MetadataTenantSchemaPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface MetadataTenantSchemaMapper extends BaseMapper<MetadataTenantSchemaPo> {
    @Update("${execSql}")
    void exec(@Param("execSql") String execSql);
}
