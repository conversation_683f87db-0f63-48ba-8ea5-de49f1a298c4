package com.caidaocloud.hrpaas.service.interfaces.facade.form;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.service.application.form.dto.FormNoticeVarQueryDto;
import com.caidaocloud.hrpaas.service.application.form.service.FormDataService;
import com.caidaocloud.hrpaas.service.interfaces.vo.form.FormDataMapVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.annotation.WfCallback;
import com.caidaocloud.workflow.dto.WfApproverDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/hrpaas/v1/form/workflow")
@Api(value = "/api/hrpaas/v1/form/workflow", description = "表单回调接口", tags = "表单回调接口")
public class FormWorkflowController {
    @Autowired
    private FormDataService formDataService;

    @ApiOperation("流程获取paas序列流的值")
    @GetMapping("/seq/{formDefId}")
    public Result<String> getSequenceData(@PathVariable("formDefId") String formDefId,
                                          @RequestParam(value = "businessId") String businessId,
                                          @RequestParam(value = "initiatorId") String initiatorId,
                                          @RequestParam(value = "applicantId") String applicantId,
                                          @RequestParam(value = "code") String code) {
        if (log.isDebugEnabled()) {
            log.debug("prepare execute method, formDefId={} businessId={} initiatorId={} applicantId={} code={}",
                    formDefId, businessId, initiatorId, applicantId, code);
        }
        PreCheck.preCheckArgument(StringUtils.isBlank(formDefId) ||
                        StringUtils.isBlank(businessId) ||
                        StringUtils.isBlank(code),
                "parameters were illedge");
        var formData = formDataService.loadById(formDefId, businessId);
        if (formData == null) {
            log.error("not found formData, formDefId={} businessId={} code={}", formDefId, businessId, code);
            throw new ServerException("not found formData");
        }
        val property = code.replaceAll("FORM_FUNC_\\d*_","");
        return Result.ok(
                String.valueOf(FormDataMapVo.fromFormData(formDataService.loadById(formDefId,businessId)).getPropertiesMap().get(property))
        );
    }

    @ApiOperation("流程获取paas审批人")
    @GetMapping("/approver/{formDefId}")
    public Result getApprover(@PathVariable("formDefId") String formDefId,
                              @RequestParam("applicantId") String applicantId,
                              @RequestParam("initiatorId") String initiatorId,
                              @RequestParam("code") String code,
                              @RequestParam("value") String value,
                              @RequestParam("businessKey") String businessKey) {
        if (log.isDebugEnabled()) {
            log.debug("prepare execute method, formDefId={} applicantId={} initiatorId={} code={} value={} businessKey={}",
                    formDefId, applicantId, initiatorId, code, value, businessKey);
        }
        PreCheck.preCheckArgument(StringUtils.isBlank(formDefId) ||
                StringUtils.isBlank(businessKey) || StringUtils.isBlank(code), "parameters were illedge");
        var optional = formDataService.getPropertyValue(formDefId,
                businessKey.split("_")[0], code.split("_")[1]);
        if (!optional.isPresent()) {
            return Result.ok("");
        }
        var formPropertyData = optional.get();
        if (StringUtils.isBlank(formPropertyData.getValue())) {
            return Result.ok("");
        }
        var empSimple = FastjsonUtil.toObject(formPropertyData.getValue(), EmpSimple.class);
        return Result.ok(Lists.newArrayList(new WfApproverDto(empSimple.getEmpId(), empSimple.getName(), "")));
    }

    @ApiOperation("离职流程变量值获取")
    @PostMapping("/notice/var")
    public Result<Map<String, String>> getNoticeVar(@RequestBody FormNoticeVarQueryDto request) {
        return Result.ok(formDataService.getNoticeVar(request.getBusinessKey(), request.getVariables()));
    }
}
