package com.caidaocloud.metadata.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.metadata.domain.entity.AddressDo;
import com.caidaocloud.metadata.infrastructure.configuration.handler.BooleanTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("paas_address")
public class AddressPo extends BasePo<AddressPo, AddressDo> {
    private String address;
    private String code;
    private String pidPath;
    private String i18n;
    private String type;
    /**
     * 是否删除。0 默认值，未删除，1 已删除
     */
    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean deleted;
}
