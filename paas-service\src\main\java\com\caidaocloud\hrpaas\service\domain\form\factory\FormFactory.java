package com.caidaocloud.hrpaas.service.domain.form.factory;

import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.service.application.form.dto.FormDefDto;
import com.caidaocloud.hrpaas.service.application.form.dto.FormPropDefDto;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormOrgSelectPropDef;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormSelectPropDef;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 * @date 2024/8/9
 */
public class FormFactory {
	public static FormPropDef convertPropertyDef(FormPropDefDto propDefDto, FormDef exist) {
		Option<FormPropDef> existProp = Sequences.sequence(exist.getProperties())
				.find(prop -> propDefDto.getProperty().equals(prop.getProperty()));
		FormPropDef propDef;
		if (existProp.isDefined()) {
			propDef = existProp.get();
			BeanUtil.copyProperties(propDefDto, propDef);
		}else {
			if (propDefDto.getWidgetType() == null) {
				propDef = FastjsonUtil.convertObject(propDefDto, FormPropDef.class);
			}else {
				switch (propDefDto.getWidgetType()) {
				case ChooseJob:
				case ChoosePost:
					propDef= FastjsonUtil.convertObject(propDefDto, FormSelectPropDef.class);
					break;
				case OrgSelect:
					propDef= FastjsonUtil.convertObject(propDefDto, FormOrgSelectPropDef.class);
					break;
				default:
					propDef = FastjsonUtil.convertObject(propDefDto, FormPropDef.class);
				}
			}
		}

		if (StringUtils.isEmpty(propDef.getName()) && MapUtils.isNotEmpty(propDef.getI18nName())) {
			propDef.setName(propDef.getI18nName().get("default"));
		}
		return propDef;
	}

	public static FormPropDef convertPropertyDef(FormPropDef propDef) {
		if (propDef.getWidgetType() == null) {
			return propDef;
		}
		switch (propDef.getWidgetType()) {
		case ChooseJob:
		case ChoosePost:
			return FastjsonUtil.convertObject(propDef, FormSelectPropDef.class);
		case OrgSelect:
			return FastjsonUtil.convertObject(propDef, FormOrgSelectPropDef.class);
		default:
			return propDef;
		}
	}

	public static FormDef createFormDef(FormDefDto formDefDto) {
		return createFormDef(formDefDto, null);
	}

	public static FormDef createFormDef(FormDefDto formDef,FormDef exist) {
		FormDef entity = formDef.toEntity(exist);
		entity.setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		entity.setUpdateTime(System.currentTimeMillis());
		if (exist == null) {
			entity.setId(SnowUtil.nextId());
			entity.setCodeAsId(formDef.getCode());
			entity.setCreateBy(entity.getUpdateBy());
			entity.setCreateTime(entity.getUpdateTime());
		}else{
			entity.setId(exist.getId());
			entity.setCodeAsId(exist.getCodeAsId());
			entity.setCreateBy(exist.getCreateBy());
			entity.setCreateTime(exist.getCreateTime());
		}
		return entity;
	}
}
