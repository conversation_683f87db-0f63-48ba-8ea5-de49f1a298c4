package com.caidaocloud.metadata.infrastructure.repository.transaction;

import java.util.Enumeration;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.CastUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.RequestHelper;
import com.caidaocloud.web.ResponseWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

@Slf4j
@Component
public class TransactionInterceptorAdapter extends HandlerInterceptorAdapter {

    public final static ThreadLocal<String> transactionIdHolder = new ThreadLocal<>();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Enumeration<String> headers = WebUtil.getRequest().getHeaders("Paas-Tx-Id");
        if (headers.hasMoreElements()) {
            transactionIdHolder.set(headers.nextElement());
        }
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        transactionIdHolder.remove();
        super.afterCompletion(request, response, handler, ex);
    }
}