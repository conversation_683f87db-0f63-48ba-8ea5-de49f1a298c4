package com.caidaocloud.hrpaas.service.application.auth.service;

import com.caidaocloud.metadata.application.event.EntityEventMsg;
import com.caidaocloud.msg.handler.MessageHandler;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EmpSubordinateChangeSubscriber implements MessageHandler<EntityEventMsg> {

    @Autowired
    private AllSubordinateService allSubordinateService;

    @Override
    public String topic() {
        return "EMP_LEADER_CHANGE";
    }

    @Override
    public void handle(EntityEventMsg message) throws Exception {
        try{
            allSubordinateService.empWorkInfoChange(message.getData());
        }catch (Exception e){
            log.error("更新全部下属异常，" + FastjsonUtil.toJson(message), e);
        }
    }
}
