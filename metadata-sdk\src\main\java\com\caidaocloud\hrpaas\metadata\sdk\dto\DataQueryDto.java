package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.DataQueryType;
import com.googlecode.totallylazy.Lists;
import lombok.Data;

import java.util.List;

@Data
public class DataQueryDto {

    private String identifier;

    private List<String> relatedProperties = Lists.list();

    private boolean showDept = false;

    private boolean decrypt = false;

    private boolean queryInvisible = false;

    private List<String> languages = Lists.list();

    private int pageSize = 20;

    private int pageNo = 1;

    private String filter;

    private String bid;

    private DataQueryType type;

    private boolean group = false;

    private List<String> specifyProperties = Lists.list();

    private String orderBy;

    private boolean expression = false;

}
