package com.caidaocloud.hrpaas.service.interfaces.vo.condition;

import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionData;
import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionDef;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Sequences;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 * @date 2022/11/2
 */
@ApiModel("匹配条件列表vo")
@Data
public class ConditionDefPageVo {

	@ApiModelProperty("id")
	private String id;

	@ApiModelProperty("所属模块")
	private String module;

	@ApiModelProperty("匹配条件字段")
	private String properties;

	@ApiModelProperty("备注")
	private String remark;

	public static ConditionDefPageVo fromEntity(ConditionDef entity) {
		ConditionDefPageVo vo = ObjectConverter.convert(entity, ConditionDefPageVo.class);
		vo.setProperties(StringUtils.join(Sequences.sequence(entity.getProperties())
				.filter(ConditionData::isEnabled)
				.map(ConditionData::getName)
				.toList(), ","));
		return vo;
	}

}
