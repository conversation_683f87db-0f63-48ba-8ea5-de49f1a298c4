package com.caidaocloud.hrpaas.service.application.schedule.dto;

import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.service.domain.schedule.entity.ScheduleTask;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import lombok.val;

@Data
public class ScheduleTaskDto {

    private String tenantId;

    private String taskTopic;

    private String taskId;

    private String taskDetail;

    private long execTime;

    public ScheduleTask toEntity() {
        val result = FastjsonUtil.convertObject(this, ScheduleTask.class);
        result.setId(SnowUtil.nextId());
        return result;
    }
}
