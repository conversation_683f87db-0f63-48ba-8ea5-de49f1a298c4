package com.caidaocloud.hrpaas.paas.common.event;

import com.caidaocloud.hrpaas.metadata.sdk.event.AbstractInteriorEvent;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

@Data
public class PageDetailChangeEvent extends AbstractInteriorEvent {
	private String pageId;
	private String tenantId;


	public final static String topic = "PAGE_DETAIL_CHANGE";

	public PageDetailChangeEvent() {
		super(topic);
	}

	public PageDetailChangeEvent(String pageId, String tenantId) {
		super(topic);
		this.pageId = pageId;
		this.tenantId = tenantId;
	}


	@Override
	public void doPublish() {
		MqMessageProducer producer = SpringUtil.getBean(MqMessageProducer.class);
		RabbitBaseMessage message = new RabbitBaseMessage();
		message.setBody(FastjsonUtil.toJson(this));
		message.setExchange("caidao.hrpaas");
		message.setRoutingKey("caidao.hrpaas.page.detail.change");
		producer.publish(message);
	}

}
