package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.service.domain.page.entity.TenantPage;
import com.caidaocloud.hrpaas.service.domain.page.repository.ITenantPageRepository;
import org.springframework.stereotype.Repository;

import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.TenantPagePo;
import com.googlecode.totallylazy.Option;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

@Repository
public class TenantPageRepositoryImpl implements ITenantPageRepository {

    @Autowired
    private TenantPageMapper tenantPageMapper;

    @Override
    public void deleteById(String id) {
        tenantPageMapper.deleteById(Long.valueOf(id));
    }

    @Override
    public List<TenantPage> loadAll() {
        return tenantPageMapper.selectList(new LambdaQueryWrapper<TenantPagePo>().orderByAsc(TenantPagePo::getCreateTime)).stream().map(it->
                it.toEntity(TenantPage.class)).collect(Collectors.toList());
    }

    @Override
    public String insert(TenantPage entity) {
        checkForm(entity);
        tenantPageMapper.insert(TenantPagePo.fromEntity(TenantPagePo.class, entity));
        return entity.getId();
    }

    private void checkForm(TenantPage entity) {
        if (entity.getType() == PageType.ModelPage) {
            LambdaQueryWrapper<TenantPagePo> queryWrapper = new QueryWrapper().lambda();
            queryWrapper.eq(TenantPagePo::getJoinModel, entity.getJoinModel());
            List<TenantPagePo> exist = tenantPageMapper.selectList(queryWrapper);
            if (exist.size() > 0) {
                throw new ServerException("关联表单与页面 " + exist.get(0).getName() + " 重复");
            }
        }
    }

    @Override
    public void update(TenantPage entity) {
        tenantPageMapper.updateById(TenantPagePo.fromEntity(TenantPagePo.class, entity));
    }

    @Override
    public Option<TenantPage> load(String id){
        return Option.option(tenantPageMapper.selectById(Long.valueOf(id))).map(it->it.toEntity(TenantPage.class));
    }
}
