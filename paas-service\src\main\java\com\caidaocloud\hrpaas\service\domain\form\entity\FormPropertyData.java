package com.caidaocloud.hrpaas.service.domain.form.entity;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.base.Joiner;
import lombok.Data;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class FormPropertyData {
    private String property;
    private String value;
    private String suffix;
    //private List<String> arrayValues = Lists.list();
    private PropertyDataType dataType;

    public String convertData(Map<String, FormPropDef> formPropDefMap) {
        if (dataType == null || StringUtils.isBlank(value)) {
            return "";
        }
        var formPropDef = formPropDefMap.getOrDefault(property, new FormPropDef());
        Map<String, String> styleExtraMap = null;
        switch (dataType) {
            case Timestamp:
                if (StringUtils.isBlank(formPropDef.getStyleExtras())) {
                    return DateUtil.formatTime(Long.valueOf(value));
                }
                styleExtraMap = FastjsonUtil.toObject(formPropDef.getStyleExtras(), Map.class);
                if (styleExtraMap.containsKey("format")) {
                    if (value.contains("[")) {
                        var list = FastjsonUtil.toObject(value, List.class);
                        var stringBuilder = new StringBuilder();
                        for (int i = 0; i < list.size(); i++) {
                            stringBuilder.append(DateUtil.format(Long.valueOf(list.get(i).toString()), styleExtraMap.get("format")));
                            if (i != list.size() - 1 && list.size() > 1) {
                                stringBuilder.append(" - ");
                            }
                        }
                        return stringBuilder.toString();
                    }
                    return DateUtil.format(Long.valueOf(value), styleExtraMap.get("format"));
                }
                return DateUtil.formatTime(Long.valueOf(value));
            case Enum:
                Map<String, String> map = FastjsonUtil.toObject(value, Map.class);
                var enumValue = map.containsKey("value") ? map.get("value") : "";
                return formPropDef.getEnumDef().stream().filter(e -> enumValue.equals(e.getValue()))
                        .map(e -> e.getDisplay()).findFirst().orElse("");
            case Boolean:
                return Boolean.valueOf(value) ? "是" : "否";
            case Attachment:
                var attachment = FastjsonUtil.toObject(value, Attachment.class);
                return attachment.getNames().toString();
            case Address:
                var address = FastjsonUtil.toObject(value, Address.class);
                return address.doText();
            case Dict:
                var dictSimple = FastjsonUtil.toObject(value, DictSimple.class);
                return dictSimple.getText();
            case Enum_Array:
                var list = FastjsonUtil.toObject(value, new TypeReference<List<Map>>() {
                });
                if (CollectionUtils.isEmpty(list)) {
                    return "";
                }
                val collect = list.stream().map(e -> e.entrySet().stream().map(en -> ((Map.Entry) en).getValue()).findFirst().orElse(null))
                        .filter(e -> Objects.nonNull(e))
                        .collect(Collectors.toList());
                return Joiner.on(",").join(collect);
            case Emp:
                EmpSimple empSimple = FastjsonUtil.toObject(value, EmpSimple.class);
                return empSimple.getName();
            case Timestamp_Array:
                var timeStampList = FastjsonUtil.toList(value, String.class);
                if (CollectionUtils.isEmpty(timeStampList)) {
                    return "";
                }
                styleExtraMap = FastjsonUtil.toObject(formPropDef.getStyleExtras(), Map.class);
                val format = styleExtraMap.getOrDefault("format", "yyyy-MM-dd");
                timeStampList = timeStampList.stream().filter(e -> StringUtils.isNotBlank(e)).map(e -> DateUtil.format(Long.valueOf(e), StringUtils.isBlank(format) ? "yyyy-MM-dd" : format)).collect(Collectors.toList());
                return Joiner.on(" - ").join(timeStampList);
            default:
                return value;
        }
    }
}