package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.domain.page.entity.TenantPageDetail;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("paas_tenant_page_detail")
public class TenantPageDetailPo extends BasePo<TenantPageDetailPo, TenantPageDetail> {

    private String pageId;

    private PageType type;

    private String standardPageConfig;

    private long createTime;

    private String createBy;

    private long updateTime;

    private String updateBy;

}
