package com.caidaocloud.metadata.interfaces.convertor;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataRelationVo;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.domain.entity.PropertyDefaultValue;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.val;

import java.util.List;
import java.util.stream.Collectors;

public class MetadataConvertor {

    public static EntityDef fromDto(MetadataDto metadata){
        val def = JsonEnhanceUtil.toObject(metadata.getBasicInfo(), EntityDef.class);
        val props = JsonEnhanceUtil
                .toObjects(Sequences.sequence(metadata.getProperties()), PropertyDef.class);
        def.setCustomProperties(props.toList());
        return def;
    }

    public static EntityDef standardFromDto(MetadataDto metadata){
        val def = JsonEnhanceUtil.toObject(metadata.getBasicInfo(), EntityDef.class);
        val props = JsonEnhanceUtil
                .toObjects(Sequences.sequence(metadata.getProperties()), PropertyDef.class);
        def.setStandardProperties(props.toList());
        return def;
    }

    public static EntityDef fromDto(MetadataBasicInfoDto metadata){
        val def = JsonEnhanceUtil.toObject(metadata, EntityDef.class);
        return def;
    }

    public static MetadataVo toVo(EntityDef def, Pair<Sequence<PropertyDef>, Sequence<PropertyDef>> inherited){
        // val result = JsonEnhanceUtil.toObject(def, MetadataVo.class);
        // result.setInheritedStandardProperties(JsonEnhanceUtil.toObjects(inherited.first(),MetadataPropertyVo.class).toList());
        // result.setInheritedCustomProperties(JsonEnhanceUtil.toObjects(inherited.second(),MetadataPropertyVo.class).toList());
        // return result;
        return toVo(def, inherited, JsonEnhanceUtil.toObjects(def.getRelationProperties(), MetadataRelationVo.class));
    }

    public static MetadataVo toVo(EntityDef def, Pair<Sequence<PropertyDef>, Sequence<PropertyDef>> inherited, List<MetadataRelationVo> relations){
        val result = JsonEnhanceUtil.toObject(def, MetadataVo.class);
        result.setInheritedStandardProperties(JsonEnhanceUtil.toObjects(inherited.first(),MetadataPropertyVo.class).toList());
        result.setInheritedCustomProperties(JsonEnhanceUtil.toObjects(inherited.second(),MetadataPropertyVo.class).toList());
        result.setRelations(relations);
        return result;
    }

    public static PageResult<MetadataVo> toVoPage(PageResult<EntityDef> defPage){
        val items = JsonEnhanceUtil
                .toObjects(Sequences.sequence(defPage.getItems()), MetadataVo.class).toList();
        return new PageResult(items, defPage.getPageNo(), defPage.getPageSize(), defPage.getTotal());
    }

    public static List<MetadataVo> toVoList(Sequence<EntityDef> defSeq){
        return JsonEnhanceUtil.toObjects(defSeq, MetadataVo.class).toList();
    }

    public static Sequence<PropertyDef> toPropDefSeq(MetadataPropertyInsertDto insert){
        return JsonEnhanceUtil.toObjects(Sequences.sequence(insert.getProps()), PropertyDef.class);
    }

    public static Sequence<Pair<String, PropertyDef>> toPropDefSeq(MetadataPropertyUpdateDto update){
        return Sequences.sequence(
                update.getProps().entrySet().stream().map(prop-> Pair.pair(prop.getKey(),
                        JsonEnhanceUtil.toObject(prop.getValue(), PropertyDef.class))
                ).collect(Collectors.toList())
        );
    }

    public static Sequence<Pair<String, PropertyDefaultValue>> toDefaultValueSeq(MetadataPropertyRecoverDto recover){
        return Sequences.sequence(
                recover.getProps().stream().map(prop -> Pair.pair(prop.getProperty(),
                        PropertyDefaultValue.newInstance(prop.getDataType(), prop.getValue(), prop.getArrayValues()))
                ).collect(Collectors.toList())
        );
    }

}
