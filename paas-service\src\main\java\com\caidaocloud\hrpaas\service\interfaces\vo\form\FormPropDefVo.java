package com.caidaocloud.hrpaas.service.interfaces.vo.form;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormWidgetType;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.domain.entity.PropertyEnumDef;
import com.googlecode.totallylazy.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("表单属性定义vo")
public class FormPropDefVo {

    private String property;

    private String name;

    private Map<String, String> i18nName;

    private PropertyDataType dataType;

    private FormWidgetType widgetType;

    private List<PropertyEnumDef> enumDef;

    private Map componentDetail = Maps.map();

    private boolean required = false;

    private boolean encrypted = false;

    private List<FormPropRuleVo> rules;

    private String styleExtras;

    private String onEvent;

    // 字段说明
    private String desc;

    private Map<String, String> i18nDesc;
    @ApiModelProperty("是否作为流程审批人")
    private Boolean approver;

    private List<FormPropDefVo> slaveProperties;
}
