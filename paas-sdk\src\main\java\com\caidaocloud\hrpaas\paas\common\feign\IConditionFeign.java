package com.caidaocloud.hrpaas.paas.common.feign;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.feign.EntityDataFeignFallback;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "${feign.rename.caidaocloud-hr-paas-service:caidaocloud-hr-paas-service}",
        fallback = EntityDataFeignFallback.class,
        configuration = {FeignConfiguration.class},
        contextId = "conditionFeign")
public interface IConditionFeign {

    @GetMapping("/api/hrpaas/condition/v1/property")
    Result<List<ConditionDataVo>> getConditionDataByCode(@RequestParam("code") String code, @RequestParam(value = "showDisable", defaultValue = "false") boolean showDisable);
}
