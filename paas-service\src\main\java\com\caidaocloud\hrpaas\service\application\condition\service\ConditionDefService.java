package com.caidaocloud.hrpaas.service.application.condition.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMdMetadataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.paas.match.ConditionComponentEnum;
import com.caidaocloud.hrpaas.service.application.condition.dto.ConditionDataDto;
import com.caidaocloud.hrpaas.service.application.condition.dto.ConditionDefDto;
import com.caidaocloud.hrpaas.service.application.condition.enums.ConditionPlatform;
import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionData;
import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionDef;
import com.caidaocloud.hrpaas.service.interfaces.dto.condition.ConditionQueryDto;
import com.caidaocloud.hrpaas.service.interfaces.vo.condition.ConditionDataVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.condition.ConditionDefVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.condition.ConditionPropertyVo;
import com.caidaocloud.masterdata.enums.IdentifyRelationCodeEnum;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> Zhou
 * @date 2022/11/1
 */
@Service
public class ConditionDefService {
	@Autowired
	private List<FixedConditionFactory> fixedConditionFactories;
	@Autowired
	private IMdMetadataFeign mdMetadataFeign;

	public void update(ConditionDefDto dto) {
		ConditionDef def = ConditionDef.loadById(dto.getId());
		def.refreshProperties(buildProperties(dto.getProperties()));
		def.update();
	}

	public PageResult<ConditionDef> page(ConditionQueryDto dto) {
		return ConditionDef.page(dto);
	}


	public ConditionDef getDetail(String id) {
		return ConditionDef.loadById(id);
	}

	/**
	 * 条件定义详情（返回enable的字段）
	 * @param id
	 * @return
	 */
	public ConditionDefVo getEnableDetail(String id) {
		ConditionDef detail = getDetail(id);
		return ConditionDefVo.fromEntity(detail,false);
	}

	private List<ConditionData> buildProperties(List<ConditionDataDto> properties) {
		List<ConditionData> list = new ArrayList<>();
		for (ConditionDataDto property : properties) {
			EntityDef entityDef = loadEntity(property.getIdentifier());
			Option<PropertyDef> propertyOption = Sequences.sequence(entityDef.fetchProperties())
					.find(p -> p.getProperty().equals(property.getProperty()));
			if (propertyOption.isEmpty()) {
				throw new ServerException("Property def is empty");
			}
			PropertyDef propertyDef = propertyOption.get();
			boolean isFix = false;
			for (FixedConditionFactory factory : fixedConditionFactories) {
				if (factory.apply(entityDef, propertyDef)) {
					list.add(factory.fixedConditionData(entityDef.getIdentifier(), entityDef.getName(), propertyDef.getProperty(), propertyDef.getName()));
					isFix = true;
					break;
				}
			}
			if (!isFix) {
				list.add(ConditionData.builder()
						.entity(entityDef)
						.property(propertyDef)
						.build());
			}
		}
		return list;
	}

	public List<ConditionPropertyVo> getProperties(String id) {
		ConditionDef def = ConditionDef.loadById(id);
		List<EntityDef> entityDefs = new ArrayList<>();
		for (String identifier : def.getIdentifiers()) {
			if (def.getPlatform().equals(ConditionPlatform.MASTERDATA) && IdentifyRelationCodeEnum.getEnum(identifier).isPresent()) {
				entityDefs.add(FastjsonUtil.convertObject(mdMetadataFeign.one(identifier).getData(), EntityDef.class));
			}else {
				entityDefs.add(loadEntity(identifier));
			}

		}
		return filterProperties(entityDefs);
	}

	public List<ConditionPropertyVo> getPassProperties(List<String> identifiers){
		List<EntityDef> entityDefs = new ArrayList<>();
		for (String identifier : identifiers) {
			entityDefs.add(loadEntity(identifier));
		}
		return filterProperties(entityDefs);
	}

	public List<ConditionPropertyVo> filterProperties(List<EntityDef> entityDefs){
		// TODO: 2022/11/2 过滤empId
		return Sequences.sequence(entityDefs).map( entityDef-> {
			ConditionPropertyVo vo = ObjectConverter.convert(entityDef, ConditionPropertyVo.class);
			List<ConditionDataVo> voList = entityDef.fetchProperties().stream().map(fp -> {
				ConditionDataVo cdVo = ObjectConverter.convert(fp, ConditionDataVo.class);
				switch (fp.getDataType()){
					case Address:
						cdVo.setComponent(ConditionComponentEnum.ADDRESS);
						cdVo.setDataSourceParams(Maps.map("datasource", fp.getDatasource()));
						break;
				}
				return cdVo;
			}).collect(Collectors.toList());
			vo.setProperties(voList);
			return vo;
		}).toList();
	}

	private EntityDef loadEntity(String identifier) {
		Option<EntityDef> option = EntityDef.load(identifier);
		if (option.isEmpty()) {
			throw new ServerException("Entity def is empty");
		}
		return option.get();
	}

	public List<ConditionData> getConditionData(String code, boolean showDisable) {
		ConditionDef conditionDef = ConditionDef.loadByCode(code);
		if (showDisable) {
			return conditionDef.getProperties();
		}
		return Sequences.sequence(conditionDef.getProperties()).filter(ConditionData::isEnabled).toList();
	}

	@PaasTransactional
    public void testLock() {
		val one = DataQuery.identifier("entity.hr.EmpWorkInfo")
				.limit(1,1).filter(DataFilter.eq("tenantId", "11"),
						DataSimple.class).getItems().get(0);
		DataUpdate.identifier("entity.hr.EmpWorkInfo").update(one);
		DataUpdate.identifier("entity.hr.EmpWorkInfo").update(one);
    }
}
