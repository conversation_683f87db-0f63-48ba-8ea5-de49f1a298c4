package com.caidaocloud.hrpaas.service.infrastructure.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectStreamClass;

@Slf4j
public class DefaultRedisObjectInputStream extends ObjectInputStream {

    protected DefaultRedisObjectInputStream() throws IOException, SecurityException {
        super();
    }

    public DefaultRedisObjectInputStream(InputStream inputStream) throws IOException {
        super(inputStream);
    }

    @Override
    protected Class<?> resolveClass(ObjectStreamClass desc) throws IOException, ClassNotFoundException{
        String name = desc.getName();
        try {

            if(name.startsWith("com.caidao1.system.mybatis.model.SysParmDict")){
                name = name.replace("com.caidao1.system.mybatis.model.SysParmDict", "com.caidaocloud.hrpaas.service.application.uiform.dto.SysParmDict");
            }

            return Class.forName(name);

        } catch (ClassNotFoundException ex) {
            log.error("DefaultRedisObjectInputStream err,{}", ex.getMessage(), ex);
        }

        return super.resolveClass(desc);
    }
}
