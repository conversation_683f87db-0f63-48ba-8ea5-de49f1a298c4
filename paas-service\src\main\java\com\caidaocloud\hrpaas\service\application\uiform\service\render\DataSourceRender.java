package com.caidaocloud.hrpaas.service.application.uiform.service.render;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiDataSourceRenderDto;

import java.util.Map;

public interface DataSourceRender {
    PageResult<Map> doRender(String dataSourceConfig, Integer pageNo, Integer pageSize, Map<String, String[]> parameterMap);

    PageResult<Map> doRender(UiDataSourceRenderDto dataSourceConfig, Integer pageNo, Integer pageSize, Map<String, String[]> parameterMap);
}
