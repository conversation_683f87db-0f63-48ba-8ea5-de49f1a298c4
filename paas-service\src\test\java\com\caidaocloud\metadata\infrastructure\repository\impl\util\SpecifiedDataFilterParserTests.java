package com.caidaocloud.metadata.infrastructure.repository.impl.util;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SpecifiedMultiDataFilter;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Pair;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 *
 * <AUTHOR>
 * @date 2023/7/26
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class SpecifiedDataFilterParserTests {


	@Before
	public void bf(){
		SecurityUserInfo info = new SecurityUserInfo();
		info.setTenantId("11");
		SecurityUserUtil.setSecurityUserInfo(info);

	}

	@Test
	public void sqlGenerateTest(){
		SqlGeneratorUtil.initQuerySqlGenInfo(SecurityUserUtil.getSecurityUserInfo().getTenantId(), "\"", "pgsql", null);
		SpecifiedMultiDataFilter dataFilter = DataFilter.specifiedAnd("entity.hr.EmpWorkInfo")
				.andEq("empId", "123")
				.andEq("workno", "456")
				.or(DataFilter.specifiedOr("entity.hr.EmpPrivateInfo").andEq("name", "asb"))
				.and(DataFilter.specifiedAnd("entity.hr.EmpWorkInfo").andEq("name", "p1").andEq("name", "p2")
						.or(DataFilter.specifiedAnd("entity.hr.EmpWorkInfo").andEq("organize", "abc")));
		DataFilter object = DataFilter.fromJsonString(FastjsonUtil.toJson(dataFilter));
		Assert.assertEquals(FastjsonUtil.toJson(dataFilter),FastjsonUtil.toJson(object));
		SpecifiedDataFilterParser parser = new SpecifiedDataFilterParser(dataFilter);
		Pair<String, List<Object>> pair = parser.generateSqlAndParam();
		log.info("sql,{}", pair.first());
		log.info("param,{}", pair.second());
	}
}