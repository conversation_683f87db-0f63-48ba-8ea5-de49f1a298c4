package com.caidaocloud.hrpaas.service.domain.form.enums;

import java.util.List;

import com.googlecode.totallylazy.Lists;

public enum FormWidgetType {
     Input,Textarea,InputNumber,
     InputNumberString,
     Radio,Checkbox,Switch,Select,CRUD,Operation,
     Cascader,Panel,
     DatePicker,DateRangePicker,Attachment,EmployeePicker,Text, SeniorEmpPicker,Link,
     RichText,
     // 组织选择
     OrgSelect,
     // 职务选择
     ChooseJob,
     // 职级选择
     ChooseRank,
     // 岗位选择
     ChoosePost,

     ;

     public static boolean withNoPersist(FormWidgetType type){
          return type != null && (type == Link || type == Text || type == RichText);
     }

     public static List<FormWidgetType> getSelectType(){
          return Lists.list(OrgSelect, ChooseJob, <PERSON>osePost);
     }
}
