package com.caidaocloud.metadata.infrastructure.repository.impl.util;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.stream.Collectors;

@Data
public class PathPrefixAndProperties {

    public PathPrefixAndProperties(String path) {
        this.path = path;
    }

    private String path;

    private String prePathSnake;

    private String selectSql;

    private String joinTable;

    private List<RelatedProperty> properties = Lists.list();

    private Map<String, PathPrefixAndProperties> subProperties = Maps.map();

    @Data
    public static class RelatedProperty {

        private String property;

        private PropertyDataType dataType;

    }

    public void generateSql(String identifier, String prePath, long time) {
        val relationDef = SqlGeneratorUtil.fetchDef(identifier)
                .getRelationProperties().stream()
                .filter(it ->
                        StringUtils.equals(it.getProperty(), path)
                ).findFirst().orElseThrow(()-> new ServerException("关联不存在"));
        val relatedIdentifier = relationDef.getRelatedIdentifier();
        val tableName = SnakeCaseConvertor.toSnake(relatedIdentifier) +
                "_" + SqlGeneratorUtil.getTenantId();
        val relatedDef = SqlGeneratorUtil.fetchDef(relatedIdentifier);
        boolean propertiesContainsBid = false;
        List<String> colList = Lists.list();
        val fullPathSnake = SnakeCaseConvertor.toSnake(prePath + path);
        for (RelatedProperty property : properties) {
            if ("bid".equals(property.getProperty())) {
                propertiesContainsBid = true;
            }
            if (Lists.list("bid", "identifier", "tenantId").contains(property.getProperty())) {
                property.dataType = PropertyDataType.String;
            } else if (Lists.list("createTime", "updateTime",
                    "dataStartTime", "dataEndTime").contains(property.getProperty())) {
                property.dataType = PropertyDataType.Timestamp;
            } else if ("deleted".equals(property.getProperty())) {
                property.dataType = PropertyDataType.Boolean;
            } else {
                property.dataType = relatedDef.fetchProperties().stream().filter(propertyDef ->
                        StringUtils.equals(property.getProperty(), propertyDef.getProperty())
                                || property.getProperty().startsWith(propertyDef.getProperty() + "."))
                        .findFirst().orElseThrow(() -> new ServerException("关联属性不存在")).getDataType();
            }
            val col = SqlGeneratorUtil.getColEscape()
                    + SnakeCaseConvertor.toSnake(SqlGeneratorUtil.fetchColumnName(SqlGeneratorUtil.fetchDefPo(relatedIdentifier), property.getProperty()))
                    + SqlGeneratorUtil.getColEscape() + " " +
                    SnakeCaseConvertor.toSnake(fullPathSnake + "_" +property.getProperty());
            colList.add(col);
            SqlGeneratorUtil.putRelationDataType(fullPathSnake + "_"+ SnakeCaseConvertor.toSnake(property.getProperty()), property.dataType);
        }
        if (!propertiesContainsBid) {
            RelatedProperty property = new RelatedProperty();
            property.setProperty("bid");
            property.setDataType(PropertyDataType.String);
            properties.add(property);
            val col = SqlGeneratorUtil.getColEscape()
                    + SnakeCaseConvertor.toSnake(SqlGeneratorUtil.fetchColumnName(SqlGeneratorUtil.fetchDefPo(relatedIdentifier), "bid"))
                    + SqlGeneratorUtil.getColEscape() + " " +
                    SnakeCaseConvertor.toSnake(fullPathSnake + "_bid");
            colList.add(col);
            SqlGeneratorUtil.putRelationDataType(fullPathSnake + "_" + SnakeCaseConvertor.toSnake(property.getProperty()), property.dataType);
        }
        val cols = StringUtils.join(
                colList.stream().distinct().collect(Collectors.toList()),
                ",");
        selectSql = new StringBuilder("select ").append(cols).append(" from ")
                .append(tableName).append(" where data_start_time <= ")
                .append(time).append(" and data_end_time >= ").append(time).toString();
        joinTable = SnakeCaseConvertor.toSnake(identifier + ".relation." + relationDef.getProperty() + "_" + SqlGeneratorUtil.getTenantId());
        prePathSnake = SnakeCaseConvertor.toSnake(prePath);
        subProperties.values().forEach(it->{
            it.generateSql(relatedIdentifier, fullPathSnake + ".", time);
        });
    }

    public void appendSql(List<String> selectTableShortNames, StringBuilder joinTableBuilder,
                          Queue<String> readyShortNames, String preJoinTable, long time) {
        val selectTableShortName = readyShortNames.poll();
        if(selectTableShortName == null){
            throw new ServerException("too many entity join");
        }
        selectTableShortNames.add(selectTableShortName);
        val joinTableShortName = preJoinTable + "_" + selectTableShortName;
        joinTableBuilder.append(" left join ")
                .append(joinTable).append(" ").append(joinTableShortName)
                .append(" on ").append(preJoinTable).append(".").append(prePathSnake + "bid")
                .append("=").append(joinTableShortName).append(".source_id")
                .append(" and ").append(joinTableShortName).append(".data_start_time<= ").append(time)
                .append(" and ").append(joinTableShortName).append(".data_end_time >= ").append(time)
                .append(" left join (").append(selectSql).append(") ").append(selectTableShortName)
                .append(" on ").append(joinTableShortName).append(".target_id = ")
                .append(selectTableShortName).append(".").append(prePathSnake + SnakeCaseConvertor.toSnake(path) + "_bid") ;
        subProperties.values().forEach(it->
                it.appendSql(selectTableShortNames, joinTableBuilder,
                        readyShortNames, selectTableShortName, time)
                );
    }
}




