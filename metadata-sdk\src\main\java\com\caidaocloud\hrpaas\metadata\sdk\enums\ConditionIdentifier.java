package com.caidaocloud.hrpaas.metadata.sdk.enums;

import com.caidaocloud.excption.ServerException;

/**
 * paas 条件join可选identifier
 * <AUTHOR>
 * @date 2023/7/26
 */
public enum ConditionIdentifier {
	WORK_INFO("entity.hr.EmpWorkInfo", "emp_id"),
	PRIVATE_INFO("entity.hr.EmpPrivateInfo", "emp_id"),
	;

	public final String identifier;
	public final String keyColumn;

	ConditionIdentifier(String identifier, String keyColumn) {
		this.identifier = identifier;
		this.keyColumn = keyColumn;
	}

	public static ConditionIdentifier getByIdentifier(String identifier) {
		for (ConditionIdentifier value : ConditionIdentifier.values()) {
			if (value.identifier.equals(identifier)) {
				return value;
			}
		}
		throw new ServerException("Identifier not found");
	}
}
