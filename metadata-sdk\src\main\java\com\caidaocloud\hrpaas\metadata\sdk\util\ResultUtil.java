package com.caidaocloud.hrpaas.metadata.sdk.util;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class ResultUtil {
    public static void checkMetadataResult(Result result){
        if(null == result || result.isSuccess()){
            return;
        }

        log.error("访问metadata异常, {}", result);
        String errMsg = result.getMsg();
        throw new ServerException(StringUtil.isEmpty(errMsg) ? "访问metadata异常" : errMsg);
    }
}
