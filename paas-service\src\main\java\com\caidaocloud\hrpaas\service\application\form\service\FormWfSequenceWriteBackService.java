package com.caidaocloud.hrpaas.service.application.form.service;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.workflow.dto.WfAfterHandleDto;
import com.caidaocloud.workflow.service.IProcessHandleService;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class FormWfSequenceWriteBackService implements IProcessHandleService {

    @Override
    public void postHandleOfBeginProc(WfAfterHandleDto wfAfterHandleDto) {
        val businessKey = wfAfterHandleDto.getBusinessKey();
        val dataId = StringUtils.substringBefore(businessKey, "_FORM-FUNC-");
        val defId = StringUtils.substringAfter(businessKey, "_FORM-FUNC-");
        //val def = FormDef.loadById(defId).get();
        val identifier = "entity.form." + defId;
        val data = DataQuery.identifier(identifier).one(dataId, DataSimple.class);
        data.getProperties().add("sysProcessCode", wfAfterHandleDto.getProcessCode());
        DataUpdate.identifier(identifier).update(data);
    }
}
