package com.caidaocloud.hrpaas.paas.common.feign;

import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.paas.common.dto.UserDynamicConfig;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicColumnConfigDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DynamicFeignClientFallback implements DynamicFeignClient {
    @Override
    public Result<UserDynamicConfig> userDynamicTableLoad(String code) {
        return Result.fail();
    }

    @Override
    public Result<List<MetadataPropertyDto>> dynamicTableLoad(String code) {
        return Result.fail();
    }

    @Override
    public Result<Boolean> dynamicTableSet(DynamicColumnConfigDto config) {
        return Result.fail();
    }
}
