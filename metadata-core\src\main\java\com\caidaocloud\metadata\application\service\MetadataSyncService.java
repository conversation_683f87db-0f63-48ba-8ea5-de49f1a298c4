package com.caidaocloud.metadata.application.service;

import java.util.Map;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.metadata.application.event.factory.EventFactory;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.infrastructure.configuration.EntitySyncProperties;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2024/3/11
 */
@Service
@Slf4j
public class MetadataSyncService {
	@Autowired
	private EntitySyncProperties entitySyncProperties;

	public void sync2Md(EntityDef def) {
		if (entitySyncProperties.getIdentifier().contains(def.getIdentifier())){
			try {
				EventFactory.createEntityPropertySyncEvent(def).publish();
			}
			catch (ServerException e) {
				log.warn("Failed to send msg, identifier: {}", def.getIdentifier());
			}
		}
	}

	public void sync2Md(String identifier) {
		sync2Md(EntityDef.load(identifier).get());
	}
}
