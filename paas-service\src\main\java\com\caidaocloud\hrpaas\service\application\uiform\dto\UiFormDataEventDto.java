package com.caidaocloud.hrpaas.service.application.uiform.dto;

import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.WorkflowAction;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class UiFormDataEventDto {
    /**
     * 表单ID
     */
    private String formId;

    /**
     * 表单数据ID
     */
    private String formDataId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 员工empId
     */
    private Long empId;

    /**
     * 用户userId
     */
    private Long userId;

    /**
     * 流程功能类型
     */
    private String functionType;

    /**
     * 流程事件类型
     */
    private WorkflowAction action;
}
