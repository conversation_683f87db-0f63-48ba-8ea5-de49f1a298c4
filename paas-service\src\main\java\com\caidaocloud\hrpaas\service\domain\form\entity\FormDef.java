package com.caidaocloud.hrpaas.service.domain.form.entity;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDefStatus;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormHeader;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormTarget;
import com.caidaocloud.hrpaas.service.domain.form.repository.IFormDefRepository;
import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.caidaocloud.hrpaas.service.application.form.service.FormDefService.WORKFLOW_CODE_PREFIX;

@Data
@Slf4j
public class FormDef extends BaseEntity<IFormDefRepository> {

    private String name;

    private String code;

    private String codeAsId;

    private Map<String, String> i18nName;

    private FormTarget target;

    private boolean workflowNodeAvailable;

    private boolean workflowAvailable;

    private String description;

    private FormDefStatus status;

    private boolean standard = false;

    private boolean implantable = false;

    private List<FormHeader> headers = Lists.list();

    private List<FormPropDef> properties = Lists.list();
    //一个字段

//    private List<FormComponentDef> components = Lists.list();

    private long createTime;

    private String createBy;

    private long updateTime;

    private String updateBy;

    private String mappingId;

    public static Option<FormDef> loadById(String id) {
        return repository(FormDef.class).load(id);
    }

    public static PageResult<FormDef> page(int pageNo, int pageSize) {
        return ((IFormDefRepository)repository(FormDef.class)).page(pageNo, pageSize);
    }

    public static PageResult<FormDef> pageByKeyWorks(int pageNo, int pageSize,String keywords) {
        return ((IFormDefRepository) repository(FormDef.class)).pageByKeyWorks(pageNo, pageSize, keywords);
    }

    public static List<FormDef> list(){
        return ((IFormDefRepository)repository(FormDef.class)).list();
    }

    public static Option<FormDef> loadByName(String name) {
        return ((IFormDefRepository)repository(FormDef.class)).loadByName(name);
    }

    public static Option<FormDef> loadByCode(String code) {
        return ((IFormDefRepository)repository(FormDef.class)).loadByCode(code);
    }

	public void publish() {
        ((IFormDefRepository)repository(FormDef.class)).publish(this);
    }

    public void disable() {
        ((IFormDefRepository)repository(FormDef.class)).disable(this);
    }

    public static void deleteById(String id) {
        repository(FormDef.class).remove(id);
    }

    public FormDef appendEventOn(Map<String, String> eventOn){
        eventOn.forEach((property, event)->{
            properties.stream().filter(it->it.getProperty().equals(property)).forEach(it->{
                it.setOnEvent(event);
            });
        });
        return this;
    }

    public void updateApproverProp(Set<String> propertySet) {
        for (FormPropDef property : getProperties()) {
            if (property == null) {
                continue;
            }
            if (StringUtils.isBlank(property.getProperty()) || !propertySet.contains(property.getProperty())) {
                if (this.status == FormDefStatus.PUBLISHED && BooleanUtils.isTrue(property.getApprover())) {
                    throw new ServerException("已经添加过的审批人不可再删除");
                }
                property.setApprover(false);
            }
            else {
                property.setApprover(true);
            }
        }
        update();
    }

  public   String getWfCode() {
      return WORKFLOW_CODE_PREFIX + getId();
    }
}
