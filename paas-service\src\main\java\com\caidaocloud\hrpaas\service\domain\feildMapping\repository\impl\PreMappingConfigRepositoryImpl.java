package com.caidaocloud.hrpaas.service.domain.feildMapping.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.hrpaas.service.domain.feildMapping.entity.PreMappingConfig;
import com.caidaocloud.hrpaas.service.domain.feildMapping.repository.PreMappingConfigRepository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl.PreMappingConfigMapper;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.PreMappingConfigPo;
import com.googlecode.totallylazy.Sequences;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

@Component
public class PreMappingConfigRepositoryImpl implements PreMappingConfigRepository {
    @Resource
    private PreMappingConfigMapper preEmpConverterMapper;

    @Override
    public List<PreMappingConfig> list(List<String> sourceList) {
        var queryWrapper = new QueryWrapper<PreMappingConfigPo>();
        if (!CollectionUtils.isEmpty(sourceList)) {
            queryWrapper.in("source", sourceList);
        }
        List<PreMappingConfigPo> poList = preEmpConverterMapper.selectList(queryWrapper);
        return Sequences.sequence(poList).map(PreMappingConfigPo::toEntity).toList();
    }

    @Override
    public void deleteByIds(Collection<String> ids) {
        LambdaQueryWrapper<PreMappingConfigPo> queryWrapper = new QueryWrapper<PreMappingConfigPo>().lambda();
        queryWrapper.in(PreMappingConfigPo::getId, ids);
        preEmpConverterMapper.delete(queryWrapper);
    }

    @Override
    public String insert(PreMappingConfig entity) {
        PreMappingConfigPo po = PreMappingConfigPo.fromEntity(entity);
        preEmpConverterMapper.insert(po);
        return po.getId();
    }


    @Override
    public void delete(PreMappingConfig preMappingConfig) {
        preEmpConverterMapper.deleteById(preMappingConfig.getId());
    }

    @Override
    public void update(PreMappingConfig preMappingConfig) {
        PreMappingConfigPo po = PreMappingConfigPo.fromEntity(preMappingConfig);
        preEmpConverterMapper.updateById(po);
    }

    @Override
    public PreMappingConfig selectById(String id) {
        PreMappingConfigPo po = preEmpConverterMapper.selectById(id);
        return po == null ? null : po.toEntity();
    }

    @Override
    public PreMappingConfig loadByName(String name) {
        PreMappingConfigPo po = preEmpConverterMapper.selectOne(new LambdaQueryWrapper<PreMappingConfigPo>().eq(PreMappingConfigPo::getName, name));
        return po == null ? null : po.toEntity();
    }

    @Override
    public List<PreMappingConfig> listByCategory(String category) {
        var queryWrapper = new QueryWrapper<PreMappingConfigPo>();
        if (StringUtils.isNotEmpty(category)) {
            queryWrapper.eq("category", category);
        }
        List<PreMappingConfigPo> poList = preEmpConverterMapper.selectList(queryWrapper);
        return Sequences.sequence(poList).map(PreMappingConfigPo::toEntity).toList();
    }

    @Override
    public List<PreMappingConfig> listByIds(List<String> idList) {

        var queryWrapper = new QueryWrapper<PreMappingConfigPo>();
        if (!CollectionUtils.isEmpty(idList)) {
            queryWrapper.in("id", idList);
        }
        List<PreMappingConfigPo> poList = preEmpConverterMapper.selectList(queryWrapper);
        return Sequences.sequence(poList).map(PreMappingConfigPo::toEntity).toList();
    }

    @Override
    public List<PreMappingConfig> listFormByCategory(String category) {
        var queryWrapper = new QueryWrapper<PreMappingConfigPo>();
        if (StringUtils.isNotEmpty(category)) {
            queryWrapper.eq("category", category);
        }
        queryWrapper.likeRight("source", "entity.form.");
        List<PreMappingConfigPo> poList = preEmpConverterMapper.selectList(queryWrapper);
        return Sequences.sequence(poList).map(PreMappingConfigPo::toEntity).toList();
    }
}
