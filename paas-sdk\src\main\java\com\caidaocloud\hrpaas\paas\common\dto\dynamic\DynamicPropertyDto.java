package com.caidaocloud.hrpaas.paas.common.dto.dynamic;

import java.util.Objects;

import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class DynamicPropertyDto extends MetadataPropertyDto {

    private String identifierName;

    private int width = 100;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        DynamicPropertyDto that = (DynamicPropertyDto) o;
        return Objects.equals(identifierName, that.identifierName) && Objects.equals(getProperty(), that.getProperty());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), identifierName, getProperty());
    }

    public DynamicPropertyDto(String identifier, String property, String name, String identifierName, PropertyDataType dataType) {
        super(property + '@' + identifier, name,dataType);
        this.identifierName = identifierName;
    }
}
