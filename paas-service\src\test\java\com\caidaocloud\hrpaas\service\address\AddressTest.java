package com.caidaocloud.hrpaas.service.address;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.metadata.application.service.MetadataAddressService;
import com.caidaocloud.metadata.domain.entity.AddressDo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class AddressTest {
    @Resource
    private MetadataAddressService metadataAddressService;

    private List<String> provinceList = new ArrayList<>();
    private List<AddressDo> provinceAddressList = new ArrayList<>();
    Map<String, String> codeMap = new HashMap<>();
    Map<String, List<Map<String, List<AddressDo>>>> sortMap = new HashMap<>();

    @Test
    public void testLoadCache(){
        metadataAddressService.loadCache();

        String cacheKey = String.format("address_%s", "1543458670639110");
        val cacheValue = SpringUtil.getBean(CacheService.class).getValue(cacheKey);
        if(null != cacheValue){
            val data = FastjsonUtil.toObject(cacheValue, Map.class);
            System.out.println((String) data.get("address"));
        }
    }

    @Test
    public void testLoadAll(){
//        List<AddressDo> list = AddressDo.getAllList(null);
//        System.out.println(FastjsonUtil.toJson(list));

        List<AddressDo> dataList = AddressDo.getAllList("0");
        System.out.println(FastjsonUtil.toJson(dataList));
    }

    @Before
    public void beforeTest(){
        provinceList.add("安徽省_340000");
        provinceList.add("北京_110000");
        provinceList.add("重庆_500000");
        provinceList.add("福建省_350000");
        provinceList.add("甘肃省_620000");
        provinceList.add("广东省_440000");
        provinceList.add("广西壮族自治区_450000");
        provinceList.add("贵州省_520000");
        provinceList.add("海南省_460000");
        provinceList.add("河北省_130000");
        provinceList.add("河南省_410000");
        provinceList.add("黑龙江省_230000");
        provinceList.add("湖北省_420000");
        provinceList.add("湖南省_430000");
        provinceList.add("吉林省_220000");
        provinceList.add("江苏省_320000");
        provinceList.add("江西省_360000");
        provinceList.add("辽宁省_210000");
        provinceList.add("内蒙古自治区_150000");
        provinceList.add("宁夏回族自治区_640000");
        provinceList.add("青海省_630000");
        provinceList.add("山东省_370000");
        provinceList.add("上海_310000");
        provinceList.add("山西省_140000");
        provinceList.add("陕西省_610000");
        provinceList.add("四川省_510000");
        provinceList.add("天津_120000");
        provinceList.add("西藏自治区_540000");
        provinceList.add("新疆维吾尔族自治区_650000");
        provinceList.add("云南省_530000");
        provinceList.add("浙江省_330000");
        provinceList.add("香港_810000");
        provinceList.add("澳门_820000");
        provinceList.add("台湾_710000");

        String json = "[{\"address\":\"安徽省\",\"deleted\":false,\"id\":\"1542646035183617\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"北京\",\"deleted\":false,\"id\":\"1542646035183618\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"重庆\",\"deleted\":false,\"id\":\"1542646035183619\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"福建省\",\"deleted\":false,\"id\":\"1542646035183620\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"甘肃省\",\"deleted\":false,\"id\":\"1542646035183621\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"广东省\",\"deleted\":false,\"id\":\"1542646035183622\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"广西壮族自治区\",\"deleted\":false,\"id\":\"1542646035183623\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"贵州省\",\"deleted\":false,\"id\":\"1542646035183624\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"海南省\",\"deleted\":false,\"id\":\"1542646035183625\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"河北省\",\"deleted\":false,\"id\":\"1542646035183626\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"河南省\",\"deleted\":false,\"id\":\"1542646035183627\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"黑龙江省\",\"deleted\":false,\"id\":\"1542646035183628\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"湖北省\",\"deleted\":false,\"id\":\"1542646035183629\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"湖南省\",\"deleted\":false,\"id\":\"1542646035183630\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"吉林省\",\"deleted\":false,\"id\":\"1542646035183631\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"江苏省\",\"deleted\":false,\"id\":\"1542646035183632\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"江西省\",\"deleted\":false,\"id\":\"1542646035183633\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"辽宁省\",\"deleted\":false,\"id\":\"1542646035183634\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"内蒙古自治区\",\"deleted\":false,\"id\":\"1542646035183635\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"宁夏回族自治区\",\"deleted\":false,\"id\":\"1542646035183636\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"青海省\",\"deleted\":false,\"id\":\"1542646035183637\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"山东省\",\"deleted\":false,\"id\":\"1542646035183638\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"上海\",\"deleted\":false,\"id\":\"1542646035183639\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"山西省\",\"deleted\":false,\"id\":\"1542646035183640\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"陕西省\",\"deleted\":false,\"id\":\"1542646035183641\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"四川省\",\"deleted\":false,\"id\":\"1542646035183642\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"天津\",\"deleted\":false,\"id\":\"1542646035183643\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"西藏自治区\",\"deleted\":false,\"id\":\"1542646035183644\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"新疆维吾尔族自治区\",\"deleted\":false,\"id\":\"1542646035183645\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"云南省\",\"deleted\":false,\"id\":\"1542646035183646\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"浙江省\",\"deleted\":false,\"id\":\"1542646035183647\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"香港\",\"deleted\":false,\"id\":\"1542646035183648\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"澳门\",\"deleted\":false,\"id\":\"1542646035183649\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"},{\"address\":\"台湾\",\"deleted\":false,\"id\":\"1542646035183650\",\"pidPath\":\"0\",\"type\":\"PROVINCE\"}]";
        provinceAddressList = FastjsonUtil.toList(json, AddressDo.class);
        //readAddress();

        beforeTenantTest();
    }

    @Test
    public void testAddress(){
        List<AddressDo> list = new ArrayList<>();
        for (String str : provinceList) {
            String [] splitArr = str.split("_");
            AddressDo address = new AddressDo();
            address.setDeleted(false);
            address.setI18n(null);
            address.setAddress(splitArr[0]);
            address.setCode(splitArr[1]);
            // 省
            address.setType("PROVINCE");
            address.setId(splitArr[1]);
            address.setPidPath("0");
            list.add(address);

            // 处理市
            doCity(str, list, address.getCode());
        }

        // log.info("{}", FastjsonUtil.toJson(list));

        String sql = "INSERT INTO `hrpaas`.`paas_address` (`id`, `address`, `code`, `pid_path`, `i18n`, `type`, `deleted`) SELECT ";
        String formatSql = "%s, '%s', %s, '%s', %s, '%s', 0 WHERE NOT EXISTS(SELECT 1 FROM `hrpaas`.`paas_address` WHERE `id` = %s);";
        for (AddressDo address : list) {
            // address.save();
            String newSql = String.format(formatSql, address.getCode(), address.getAddress(),
                    null == address.getCode() ? "NULL" : "'" + address.getCode() + "'",
                    address.getPidPath(), "NULL", address.getType(), address.getCode());
            System.out.println(sql + newSql);
        }
    }

    private void doCity(String str, List<AddressDo> list, String pidPath){
        Set<String> strings = sortMap.keySet();
        for (String key : strings) {
            if(key.startsWith(str)){
                List<Map<String, List<AddressDo>>> maps = sortMap.get(key);
                for (Map<String, List<AddressDo>> cityMap : maps) {
                    Set<String> cityKey = cityMap.keySet();
                    for (String cityCode : cityKey) {
                        String city = codeMap.get(cityCode);
                        if(null == city){
                            System.out.println(cityCode);
                        }
                        String [] cityArr = city.split("_");

                        AddressDo address = new AddressDo();
                        address.setDeleted(false);
                        address.setI18n(null);
                        address.setAddress(cityArr[0]);
                        address.setCode(cityArr[1]);
                        // 省
                        address.setType("CITY");
                        address.setId(cityArr[1]);
                        address.setPidPath(pidPath);
                        list.add(address);

                        // 处理区和县
                        List<AddressDo> addressDoList = cityMap.get(cityCode);
                        for (AddressDo area : addressDoList) {
                            area.setType("AREA");
                            area.setPidPath(pidPath + "/" + address.getCode());
                            list.add(area);
                        }
                    }
                }
            }
        }
    }

    @SneakyThrows
    private void readAddress() {
        //System.out.println("------------------------------");
        FileInputStream inputStream = new FileInputStream("/Users/<USER>/Downloads/address.txt");
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

        List<AddressDo> list = new ArrayList<>(5000);
        String str = null;
        while((str = bufferedReader.readLine()) != null){
            //System.out.println(str);
            AddressDo address = new AddressDo();
            address.setDeleted(false);
            address.setI18n(null);
            address.setCode(str.substring(0, 6));
            address.setAddress(str.substring(7).trim());
            // 省
            address.setType("CITY");
            address.setId(SnowUtil.nextId());
            address.setPidPath("0");
            list.add(address);

            String preKey = address.getCode().substring(0, 4);
            if(!codeMap.containsKey(preKey)){
                codeMap.put(preKey, address.getAddress() + "_" + address.getCode());
            }
        }

        //close
        inputStream.close();
        bufferedReader.close();
        //System.out.println("------------------------------");
        //System.out.println("readAddress data=" + FastjsonUtil.toJson(list));
        //System.out.println("------------------------------");

        sortCode(list);
    }

    private void sortCode(List<AddressDo> list){
        Map<String, List<AddressDo>> map = new HashMap<>();
        String pre = list.get(0).getCode().substring(0, 2);
        AddressDo sort = list.get(0);
        // 归类
        for (AddressDo address : list) {
            String substring = address.getCode().substring(0, 2);
            if(pre.equals(substring)){
                String key = sort.getAddress() + "_" + sort.getCode();
                List subList = map.get(key);
                subList = null == subList ? new ArrayList() : subList;
                subList.add(address);
                map.put(key, subList);
            } else {
                pre = substring;
                sort = address;
                String key = sort.getAddress() + "_" + sort.getCode();
                List subList = map.get(key);
                subList = null == subList ? new ArrayList() : subList;
                subList.add(address);
                map.put(key, subList);
            }
        }

        list.clear();
        //System.out.println("-------------------------map-------");
        //System.out.println("map=" + FastjsonUtil.toJson(map));
        //System.out.println("-------------------------map-------");
        //System.out.println("--------------------------------");
        //System.out.println("--------------------------------");
        sortMap(map);
    }

    private void sortMap(Map<String, List<AddressDo>> map){
        Set<String> strings = map.keySet();
        for (String key : strings) {
            List<AddressDo> addressDos = map.get(key);
            List<Map<String, List<AddressDo>>> newList = new ArrayList<>();
            String preKey = "";
            AddressDo base = null;
            Map<String, List<AddressDo>> subMap = null;
            String sbuKey = null;
            for (AddressDo address : addressDos) {
                if(null == base){
                    base = address;
                    preKey = address.getCode().substring(0, 4);
                    continue;
                }

                String substring = address.getCode().substring(0, 4);
                if(preKey.equals(substring)){
                    List<AddressDo> subList = subMap.get(substring);
                    subList = null == subList ? new ArrayList<>() : subList;
                    subList.add(address);
                    subMap.put(preKey, subList);
                } else {
                    subMap = new HashMap<>();
//                    List<AddressDo> subList = new ArrayList<>();
//                    subList.add(address);
                    //subMap.put(preKey, subList);
                    newList.add(subMap);
                    sbuKey = base.getAddress() + "_" + base.getCode();
                    base =  address;
                    preKey = substring;
                }
            }

            sortMap.put(key, newList);
        }

        //System.out.println("" + FastjsonUtil.toJson(sortMap));
        //System.out.println("" + FastjsonUtil.toJson(codeMap));
    }

    @Test
    public void testSortMap(){

        //sortMap();
    }

    public void beforeTenantTest(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setUserId(0L);
        userInfo.setTenantId("11");
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    @Test
    public void testAddressTxt(){
        // metadataAddressService.selectAddressByStr(Lists.newArrayList("朝阳区"));

        String addTxt = "上海市//黄浦区";
        String [] split = addTxt.split("/");
        System.out.println(split.length);

        // metadataAddressService.loadCache();

        Address address = new Address();
        address.setText(addTxt);
        Address.txtToAddress(address);
        System.out.println(FastjsonUtil.toJson(address));
        System.out.println();
        System.out.println();
        System.out.println(FastjsonUtil.toJson(Address.txtToAddress(addTxt)));

        System.out.println();
        System.out.println();
        System.out.println(FastjsonUtil.toJson(Address.txtToAddress("上海市//徐汇区")));

        System.out.println();
        System.out.println();
        System.out.println(FastjsonUtil.toJson(Address.txtToAddress("北京市//丰台区")));

        System.out.println();
        System.out.println();
        QueryInfoCache.init();
        System.out.println(FastjsonUtil.toJson(Address.txtToAddress("北京市//朝阳区")));
        QueryInfoCache.clear();
        System.out.println(FastjsonUtil.toJson(Address.txtToAddress("新疆维吾尔族自治区/乌鲁木齐市/高新技术产业开发区")));
        System.out.println(FastjsonUtil.toJson(Address.txtToAddress("四川省/成都市/高新技术产业开发区")));
        System.out.println(FastjsonUtil.toJson(Address.txtToAddress("广东省/东莞市/高新技术产业开发区")));
    }

    @Test
    public void testAddress2Txt(){
        String value = "上海市/上海市";
        val address = new Address();
        address.setText(value);
        Address.txtToAddress(address);
        address.doValue();
        System.out.println("-------");
        System.out.println(FastjsonUtil.toJson(address));
    }
}
