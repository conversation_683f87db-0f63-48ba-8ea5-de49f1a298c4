package com.caidaocloud.hrpaas.service.application.uiform.service;

import com.caidaocloud.hrpaas.service.application.rules.CheckRuleFactory;
import com.caidaocloud.hrpaas.service.application.rules.ICheckRule;
import com.caidaocloud.hrpaas.service.application.rules.IRule;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Maps;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-01-07
 */
@Service
public class CheckRuleService {
    /**
     * 获取模型字段规则集合
     * @param checkRuleConfig
     */
    public Map getRuleMap(String checkRuleConfig, Class clazz){
        if(StringUtil.isEmpty(checkRuleConfig)){
            return Maps.map();
        }

        List<IRule> rules = FastjsonUtil.toList(checkRuleConfig, clazz);
        if(null == rules || rules.isEmpty()){
            return Maps.map();
        }

        Map<String, List<IRule>> map = rules.stream().collect(Collectors.groupingBy(IRule::prop));
        return map;
    }

    /**
     * 执行规则校验
     * @param rules 字段规则集合
     */
    public void checkRuleExecute(List<IRule> rules, String dVal){
        if(null == rules || rules.isEmpty()){
            return;
        }

        rules.forEach(checkRuleDto -> {
            ICheckRule checkRule = CheckRuleFactory.getCheckRule(checkRuleDto);
            if(null != checkRule){
                checkRuleDto.value(dVal);
                checkRule.check(checkRuleDto);
            }
        });
    }
}
