package com.caidaocloud.metadata.infrastructure.configuration;

import java.util.List;

import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;

/**
 *
 * <AUTHOR>
 * @date 2024/3/12
 */
public class DefaultEntityPropertyRule implements IEntityPropertyRule{
	@Override
	public boolean isBuiltInField(List<PropertyDef> dbExpList, PropertyDef propertyDef, Boolean useBuiltInFields) {
		boolean isBuiltInField = useBuiltInFields ;
		Option<PropertyDef> dbProperty = Sequences.sequence(dbExpList)
				.find(p -> p.getProperty().equals(propertyDef.getProperty()));
		if (dbProperty.isDefined() && dbProperty.get().isExpEnable()) {
			isBuiltInField = false;
		}
		return isBuiltInField;
	}
}
