package com.caidaocloud.hrpaas.service.test;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class DataQueryTest {

    @Test
    public void testLeAndGe(){
        HolderUtil.setTenantId("caidaotest");
        DataFilter filter = DataFilter.eq("tenant_id", "caidaotest")
                .andGe("effectiveYear", "1577808000000")
                .andLe("invalidYear", "1709430399999")
                .andEq("deleted", Boolean.FALSE.toString())
                .andEq("status", "1")
                .andIn("bid", Lists.list("1375886341273721", "1375886341273722"));

        PageResult<DataSimple> pageResult = DataQuery.identifier("entity.hrpms.PmsTag").decrypt().specifyLanguage()
                .queryInvisible().filter(filter, DataSimple.class);
        System.out.println(FastjsonUtil.toJson(pageResult));
    }

    @Test
    public void testEffectiveYear(){
        HolderUtil.setTenantId("caidaotest");
        DataFilter filter = DataFilter.eq("tenantId", "caidaotest")
                .andNe("deleted", Boolean.TRUE.toString());

        Long effectiveYear = 1577808000000L;
        filter = filter.andLe("effectiveYear", effectiveYear.toString())
                .andGe("invalidYear", effectiveYear.toString());

        PageResult<DataSimple> pageResult = DataQuery.identifier("entity.hrpms.PmsConfig").decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(filter, DataSimple.class);
        System.out.println(FastjsonUtil.toJson(pageResult));
    }

    @Test
    public void testTag(){
        HolderUtil.setTenantId("caidaotest");
        DataFilter filter = DataFilter.eq("tenantId", "caidaotest")
                .andNe("deleted", Boolean.TRUE.toString());

        /*if(null != data.getStatus()){
            filter = filter.andEq("status", data.getStatus().getValue());
        }*/

        filter = filter.andLe("effectiveYear", "1646236800000")
                .andGe("invalidYear", "1646236800000");

        /*if(null != data.getInvalidYear()){
            filter = filter.andEq("invalidYear", data.getInvalidYear().toString());
        }*/

        PageResult<DataSimple> pageResult = DataQuery.identifier("entity.hrpms.PmsTag").decrypt().specifyLanguage()
                .queryInvisible().filter(filter, DataSimple.class);
        System.out.println(FastjsonUtil.toJson(pageResult));
    }

    @Test
    public void testA(){
        HolderUtil.setTenantId("caidaotest");
        DataFilter filter = DataFilter.eq("tenantId", "caidaotest")
                .andNe("deleted", Boolean.TRUE.toString());
        filter = filter.andEq("organize", "56596");

        filter = filter.andLe("effectiveYear", "1648742400000")
                .andGe("effectiveYear", "1648742400000");

        PageResult<DataSimple> pageResult = DataQuery.identifier("entity.hrpms.PmsOrganizeReport").decrypt().specifyLanguage().queryInvisible()
                .limit(10, 1).filter(filter, DataSimple.class);

        System.out.println(FastjsonUtil.toJson(pageResult));
    }
}
