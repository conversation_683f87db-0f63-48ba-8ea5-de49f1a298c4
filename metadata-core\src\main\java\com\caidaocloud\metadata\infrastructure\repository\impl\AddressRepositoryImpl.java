package com.caidaocloud.metadata.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.metadata.domain.entity.AddressDo;
import com.caidaocloud.metadata.domain.repository.IAddressRepository;
import com.caidaocloud.metadata.infrastructure.repository.po.AddressPo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Option;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class AddressRepositoryImpl implements IAddressRepository {

    @Resource
    AddressMapper mapper;

    @Override
    public AddressDo loadById(String id) {
        return ObjectConverter.convert(mapper.selectById(id), AddressDo.class);
    }

    @Override
    public String insert(AddressDo entity) {
        AddressPo data = ObjectConverter.convert(entity, AddressPo.class);
        mapper.insert(data);
        entity.setId(String.valueOf(data.getId()));
        return entity.getId();
    }

    @Override
    public void update(AddressDo entity) {
        AddressPo data = ObjectConverter.convert(entity, AddressPo.class);
        mapper.updateById(data);
    }

    @Override
    public Option<AddressDo> load(String id) {
        return Option.option(loadById(id));
    }

    @Override
    public List<AddressDo> getList(AddressDo address, int pageNo, int pageSize) {
        return null;
    }

    @Override
    public void delete(String id) {
        mapper.deleteById(id);
    }

    @Override
    public List<AddressDo> getAllList(String pidPath) {
        LambdaQueryWrapper<AddressPo> queryWrapper = new LambdaQueryWrapper();
        if(null == pidPath){
            // 查询所有
        } else if(AddressDo.ROOT_PID.equals(pidPath)){
            // 查询省
            queryWrapper.eq(AddressPo::getPidPath, pidPath);
        } else {
            // 查询市区县
            queryWrapper.like(AddressPo::getPidPath, pidPath);
        }

        List<AddressPo> dataList = mapper.selectList(queryWrapper);
        return FastjsonUtil.convertList(dataList, AddressDo.class);
    }

    @Override
    public List<AddressDo> getAddressList(List<String> list) {
        LambdaQueryWrapper<AddressPo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(AddressPo::getAddress, list);
        List<AddressPo> dataList = mapper.selectList(queryWrapper);
        return FastjsonUtil.convertList(dataList, AddressDo.class);
    }
}
