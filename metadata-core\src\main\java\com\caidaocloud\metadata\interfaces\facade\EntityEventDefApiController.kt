package com.caidaocloud.metadata.interfaces.facade

import com.caidaocloud.hrpaas.core.metadata.interfaces.dto.EntityEventDefAddDto
import com.caidaocloud.hrpaas.core.metadata.interfaces.dto.EntityEventDefConsumerDto
import com.caidaocloud.hrpaas.core.metadata.interfaces.dto.EntityEventDefUpdateDto
import com.caidaocloud.hrpaas.core.metadata.interfaces.vo.EntityEventDefVo
import com.caidaocloud.metadata.application.service.EntityEventDefService
import com.caidaocloud.metadata.domain.enums.DefChannel
import com.caidaocloud.web.Result
import io.swagger.annotations.Api
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/\${service.name.short:hrpaas}/v1/event/def")
@Api(value = "事件定义维护", tags = ["事件定义--后端接口"])
class EntityEventDefApiController {

    @Autowired
    private lateinit var entityEventDefService: EntityEventDefService

    @PostMapping
    fun addEventDef(@RequestBody def: EntityEventDefAddDto) : Result<String>{
        return Result.ok(entityEventDefService.create(DefChannel.API, def))
    }

    @PutMapping
    fun updateEventDef(@RequestBody def: EntityEventDefUpdateDto){
        entityEventDefService.update(def.id, DefChannel.API, def)
    }

    @PutMapping("/:open")
    fun open(@RequestParam("id") id : String){
        entityEventDefService.open(id)
    }

    @PutMapping("/:close")
    fun close(@RequestParam("id")  id : String){
        entityEventDefService.close(id)
    }

    @GetMapping("/list")
    fun loadByModelRel(@RequestParam("modelRef")  modelRef : String) : List<EntityEventDefVo> {
        return entityEventDefService.loadByModelRef(modelRef)
    }

    @PutMapping("/consumer")
    fun updateConsumerList(@RequestBody consumers: EntityEventDefConsumerDto){
        entityEventDefService.updateConsumerList(consumers.id, consumers.consumerList)
    }

    @PostMapping("/consumer")
    fun addConsumerList(@RequestBody consumers: EntityEventDefConsumerDto){
        entityEventDefService.addConsumerList(consumers.id, consumers.consumerList)
    }

    @DeleteMapping("/consumer")
    fun removeConsumerList(@RequestBody consumers: EntityEventDefConsumerDto){
        entityEventDefService.removeConsumerList(consumers.id, consumers.consumerList)
    }


}