package com.caidaocloud.hrpaas.service.application.rules;

public interface IRule {
    default String type(){
        return "";
    }

    default String prop(){
        return type();
    }

    default String name(){
        return type();
    }

    default String value(){
        return type();
    }

    default void value(String dataValue){}

    default String rule(){
        return type();
    }

    default String tips(){
        return type();
    }
}
