package com.caidaocloud.hrpaas.service.interfaces.facade.feildMapping;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.service.application.feildMapping.service.MappingConfigService;
import com.caidaocloud.hrpaas.service.application.form.dto.MappingConfigDto;
import com.caidaocloud.hrpaas.service.domain.feildMapping.entity.MappingTypeEnum;
import com.caidaocloud.hrpaas.service.interfaces.vo.feildMapping.MappingConfigVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.feildMapping.MappingMetadataSelectVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/hrpaas/v1/mapping/config")
@Api(value = "字段映射配置", tags = "字段映射配置")
public class MappingConfigController {
    @Resource
    private MappingConfigService mappingConfigService;

    @GetMapping("type/list")
    @ApiOperation(value = "根据类型映射配置列表")
    public Result<List<MappingConfigVo>> listByType(@RequestParam("type")
                                                    @ApiParam("映射类型：PREEMP_TO_HR入职模型映射到人事;HR_TO_PREEMP人事映射到入职")
                                                            MappingTypeEnum type) {
        return Result.ok(FastjsonUtil.convertList(mappingConfigService.listByType(type), MappingConfigVo.class));
    }

    @GetMapping
    @ApiOperation(value = "映射配置详情")
    public Result<MappingConfigVo> loadConfig(@RequestParam("id") String id) {
        return Result.ok(ObjectConverter.convert(mappingConfigService.loadConfig(id), MappingConfigVo.class));
    }

    @GetMapping("mappingMetadata")
    @ApiOperation(value = "模型字段映射下拉项")
    public Result<MappingMetadataSelectVo> mappingMetadata() {
        return Result.ok(mappingConfigService.loadMappingMetadataList());
    }

    @PostMapping
    @ApiOperation(value = "映射配置保存")
    public Result saveConfig(@RequestBody MappingConfigDto config) {
        mappingConfigService.saveConfig(config);
        return Result.ok(true);
    }

    @PutMapping
    @ApiOperation(value = "映射配置编辑")
    public Result editConfig(@RequestBody MappingConfigDto config) {
        mappingConfigService.editConfig(config);
        return Result.ok(true);
    }

    @DeleteMapping
    @ApiOperation(value = "映射配置删除")
    public Result deleteConfig(@RequestParam("id") String id) {
        mappingConfigService.deleteConfig(id);
        return Result.ok(true);
    }

    @GetMapping("/mappingModel/list")
    @ApiOperation(value = "目标为普通表单列表")
    public Result mappingList(@RequestParam("id") String id) {
        return Result.ok(FastjsonUtil.convertList(mappingConfigService.listByModelMapping(id), MappingConfigVo.class));
    }

    @GetMapping("listByIds")
    @ApiOperation(value = "查询类型映射配置列表")
    public Result<List<MappingConfigVo>> listByIds(@RequestParam("ids") String ids) {
        PreCheck.preCheckArgument(StringUtils.isEmpty(ids), "参数ids为空！");

        return Result.ok(FastjsonUtil.convertList(mappingConfigService.listByIds(ids), MappingConfigVo.class));
    }
}
