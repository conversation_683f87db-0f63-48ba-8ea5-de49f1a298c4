package com.caidaocloud.metadata.application.record;

import com.caidaocloud.metadata.application.service.MetadataService;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.record.core.service.IParseFunction;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * created by: FoAng
 * create time: 29/7/2024 5:32 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class EntityDefNameParseFunc implements IParseFunction {

    private MetadataService metadataService;

    @Override
    public String functionName() {
        return "defName";
    }

    @Override
    public boolean executeBefore() {
        return true;
    }

    @Override
    public String apply(String identifier) {
        return Optional.ofNullable(identifier).map(it -> {
            EntityDef entityDef = metadataService.one(identifier);
            return entityDef != null ? entityDef.getName() : null;
        }).orElse("未知模型");
    }
}
