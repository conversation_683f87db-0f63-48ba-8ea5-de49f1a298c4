package com.caidaocloud.hrpaas.service.application.uiform.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-06-23
 * 参考设计文档：https://caidaopdd.feishu.cn/docs/doccnSkCgZMqScCulngCEgpSFBe
 */
@Data
public class WorkflowRegisterDto {
    /**
     * 字符串，回调函数，必填，智能表单一般会使用通用回调
     */
    private String endAction;

    /**
     * 字符串，服务名称，与回调函数配合使用，必填，最大长度32
     */
    private String serviceName;

    /**
     * 字符串，租户ID，非必填，优先从用户中获取
     */
    private String tenantId;

    /**
     * 字符串，表单编码，必填，最大长度16
     */
    private String formCode;

    /**
     * 字符串，表单名称，必填，最大长度100
     */
    private String formName;

    /**
     * 长整形，用户ID，非必填，优先从用户中获取
     */
    private Long userId;

    /**
     * 字段列表
     */
    private List<WorkflowRegisterFieldDto> fieldItems;
}
