package com.caidaocloud.hrpaas.metadata.sdk.query;

import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.enums.RelationOperationType;
import com.caidaocloud.hrpaas.metadata.sdk.service.DataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Data
public class DataInsert {

    private String identifier;

    private List<RelationOperationDto> relationList;

    private boolean isMasterDataQuery = false;

    private DataInsert() {

    }

    public DataInsert setMasterDataQuery(boolean masterDataQuery) {
        isMasterDataQuery = masterDataQuery;
        return this;
    }

    protected DataInsert(String identifier){
        this.identifier = identifier;
        this.relationList = Lists.newArrayList();
    }

    public static DataInsert identifier(String identifier) {
        DataInsert request = new DataInsert();
        request.identifier = identifier;
        request.relationList = Lists.newArrayList();
        return request;
    }

    private void insertSlaves(String masterBid, DataTable dataTable, String slaveIdentifier, long dataStartTime){
        dataTable.getDataList().forEach(dataSlave -> {
            dataSlave.setMasterBid(masterBid);
            dataSlave.setDataStartTime(dataStartTime);
            dataSlave.setCreateTime(System.currentTimeMillis());
            dataSlave.setUpdateTime(dataSlave.getCreateTime());
            DataInsert.identifier(slaveIdentifier).setMasterDataQuery(isMasterDataQuery).insert(dataSlave);
        });
    }



    public <T extends DataSimple> String insert(T data) {
        try {
            MdQueryTL.set(isMasterDataQuery);
            val start = System.currentTimeMillis();

            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val metadata = metadataService.load(identifier);
            val entityData = data.toPersistData(metadata);
            entityData.setIdentifier(identifier);
            val entityDataService = SpringUtil.getBean(DataOperatorService.class);
            String masterBid;
            if(relationList.isEmpty()){
                // todo sdk 支持非feign调用
                masterBid = entityDataService.insert(entityData);
                val end = System.currentTimeMillis();
                if (log.isDebugEnabled()) {
                    log.debug("insert time:" + (end-start));
                }
            }else{
                for(RelationOperationDto relation : relationList){
                    relation.setStartTime(data.getDataStartTime());
                }
                DataWithRelationDTO dataDTO = DataWithRelationDTO.builder()
                        .data(entityData)
                        .operations(relationList)
                        .build();
                masterBid =  entityDataService.insertWithRelation(dataDTO);
                val end = System.currentTimeMillis();
                if (log.isDebugEnabled()) {
                    log.debug("paas insert time:" + (end-start));
                }
            }
            data.getProperties().forEach((property, value) -> {
                if(value instanceof DataTable){
                    metadata.fetchAllProperties().stream()
                            .filter(it->it.getProperty().equals(property)).findFirst().ifPresent(it->
                        insertSlaves(masterBid, (DataTable) value, fetchSlaveIdentifier(identifier, it.getProperty()), data.getDataStartTime()));
                }
            });
            return masterBid;
        } finally {
            MdQueryTL.remove();
        }
    }

    private String fetchSlaveIdentifier(String identifier, String property){
        return identifier + "_" + property;
    }

    public <T extends DataSimple> void batchInsert(List<T> dataList) {
        try {
            MdQueryTL.set(isMasterDataQuery);
            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val metadata = metadataService.load(identifier);
            val list = dataList.stream()
                    .map(it-> {
                        EntityDataDto entityData = it.toPersistData(metadata);
                        entityData.setIdentifier(identifier);
                        return entityData;
                    }
                    ).collect(Collectors.toList());
            val entityDataService = SpringUtil.getBean(DataOperatorService.class);
            entityDataService.batchInsert(identifier, list);
        } finally {
            MdQueryTL.remove();
        }
    }

    /**
     * 替代关联
     *
     * @param property
     * @param targetIds
     * @return com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert
     * <AUTHOR>
     */
    public DataInsert replaceRelations(String property, List<String> targetIds) {
        val dto = buildRelationOperationDTO(RelationOperationType.REPLACE_ALL, null, property, targetIds);
        relationList.add(dto);
        return this;
    }


    private RelationOperationDto buildRelationOperationDTO(RelationOperationType type, String sourceId, String property, List<String> targetIds) {
        return RelationOperationDto.builder()
                .identifier(identifier)
                .sourceId(sourceId)
                .operationType(type)
                .property(property)
                .targetIds(targetIds)
                .build();
    }

}
