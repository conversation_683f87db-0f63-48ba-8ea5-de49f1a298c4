package com.caidaocloud.hrpaas.service.domain.metadata.repository;

import com.caidaocloud.hrpaas.service.domain.metadata.entity.DataSourceDo;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceType;
import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import com.googlecode.totallylazy.Option;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-12
 */
public interface IDataSourceRepository extends IBaseRepository<DataSourceDo> {

    Option<DataSourceDo> loadById(String dataSourceId);

    DataSourceDo checkDuplicateName(String id, String sourceName);

    List<DataSourceDo> getAllList();

    List<DataSourceDo> loadListById(List<String> ids);

    List<DataSourceDo> getListByPage(int pageNo, int pageSize);

    List<DataSourceDo> getListByType(DataSourceType type, int pageNo, int pageSize);
}
