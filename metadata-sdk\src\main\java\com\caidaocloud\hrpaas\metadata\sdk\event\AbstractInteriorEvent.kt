package com.caidaocloud.hrpaas.metadata.sdk.event

import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil
import com.caidaocloud.msg.message.AbstractBasicMessage
import com.caidaocloud.msg.producer.MessageProducer
import com.caidaocloud.util.SpringUtil
import org.springframework.transaction.support.TransactionSynchronizationAdapter
import org.springframework.transaction.support.TransactionSynchronizationManager

abstract class AbstractInteriorEvent : AbstractBasicMessage{

    private constructor(): super()

    var allowAnonymous : Boolean = false

    constructor(topic : String) : super(){
        this.topic = topic
    }

    private val messageProducer by lazy {
        SpringUtil.getBean(MessageProducer::class.java)
    }

    fun publish(){
        //  事务开启时，注册synchronization，事务commit后发布消息
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(object : TransactionSynchronizationAdapter() {
                override fun afterCommit() {
                    doPublish()
                }
            })
            return
        }
        doPublish()
    }

    open fun doPublish() {
        if (ThreadLocalUtil.isAnonymousAllowed()) {
            this.allowAnonymous = true
        }
        messageProducer.publish(this.topic, this)
    }
}