package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataKvDo;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.IMetadataKvRepository;
import org.springframework.stereotype.Repository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.MetadataKvPo;
import com.caidaocloud.util.FastjsonUtil;

import javax.annotation.Resource;

@Repository
public class MetadataKvRepositoryImpl implements IMetadataKvRepository {

    @Resource
    MetadataKvMapper mapper;

    @Override
    public MetadataKvDo loadByKey(String key) {
        LambdaQueryWrapper<MetadataKvPo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(MetadataKvPo::getProperty, key);
        MetadataKvPo kv = mapper.selectOne(queryWrapper);
        return FastjsonUtil.convertObject(kv, MetadataKvDo.class);
    }

    @Override
    public void deleteByKey(String key) {
        LambdaQueryWrapper<MetadataKvPo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(MetadataKvPo::getProperty, key);
        mapper.delete(queryWrapper);
    }

    @Override
    public String insert(MetadataKvDo entity) {
        MetadataKvPo kv = FastjsonUtil.convertObject(entity, MetadataKvPo.class);
        mapper.insert(kv);
        return String.valueOf(kv.getId());
    }

    @Override
    public void update(MetadataKvDo entity) {
        MetadataKvPo kv = FastjsonUtil.convertObject(entity, MetadataKvPo.class);
        LambdaQueryWrapper<MetadataKvPo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(MetadataKvPo::getProperty, entity.getProperty());
        mapper.update(kv, queryWrapper);
    }
}
