package com.caidaocloud.hrpaas.service.domain.form.entity;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormWidgetType;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.domain.entity.PropertyEnumDef;
import com.caidaocloud.metadata.domain.factory.PropertyDefFactory;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.workflow.dto.WfMetaApproverDto;
import com.caidaocloud.workflow.enums.WfApproverFetchType;
import com.caidaocloud.workflow.enums.WfValueComponentEnum;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Data
@Slf4j
public class FormPropDef {
    private String property;

    private String name;

    private Map<String, String> i18nName;

    private PropertyDataType dataType;

    private FormWidgetType widgetType;

    private List<PropertyEnumDef> enumDef = Lists.list();

    private Map componentDetail = Maps.map();

    private boolean required = false;

    private boolean encrypted = false;

    private int sort;

    private String styleExtras;

    private List<FormPropRule> rules = Lists.list();

    private String onEvent;

    // 字段说明
    private String desc;

    private Map<String, String> i18nDesc;

    //是否作为审批人
    private Boolean approver;

    private List<FormPropDef> slaveProperties;

    @Override
    public boolean equals(Object obj){
        if(null == obj){
            return false;
        }
        return this.property.equals(((FormPropDef)obj).property)
                && this.dataType.equals(((FormPropDef)obj).dataType)
                && encrypted == ((FormPropDef) obj).encrypted
                && widgetType == ((FormPropDef) obj).widgetType
                && (approver == null?false:approver.booleanValue()) == (((FormPropDef) obj).approver == null?false:((FormPropDef) obj).approver.booleanValue())
                && ((FormPropDef) obj).enumDef.stream().allMatch(enumD->enumDef.contains(enumD));
    }

    public boolean notCoveredBy(List<FormPropDef> properties) {
        if(FormWidgetType.withNoPersist(widgetType)){
            return false;
        }
        return !properties.stream().anyMatch(property->
                        this.property.equals(property.property)
                                && this.dataType.equals(property.dataType)
                                && encrypted == property.encrypted
                                && widgetType == property.widgetType
                                && (approver == null?false:approver.booleanValue()) == (property.approver == null?false:property.approver.booleanValue())
                                && this.enumDef.stream().allMatch(enumD->enumD.coveredBy(property.getEnumDef()))
                );
    }

    public void handleComponentInfo() {
        if(FormWidgetType.SeniorEmpPicker.equals(widgetType) && "form_owner".equals(property)){
            val empDef = FastjsonUtil.convertObject(componentDetail, FormPropEmpDef.class);
            empDef.setFormOwner(true);
            componentDetail = FastjsonUtil.convertObject(empDef, Map.class);
        }
    }

    public List<PropertyDef> toEntityProperties() {
        PropertyDef property = initProperty();
        if(FormWidgetType.SeniorEmpPicker.equals(widgetType)){
            List<PropertyDef> defList = Sequences.sequence(property)
                    .join(FastjsonUtil.convertObject(componentDetail, FormPropEmpDef.class)
                            .appendEntityProperties(this)).toList();
            if ("form_owner".equals(property.getProperty())) {
                defList.add(PropertyDefFactory.createPropertyDef("form_owner_organize", "员工所属组织", PropertyDataType.String));
                defList.add(PropertyDefFactory.createPropertyDef("form_owner_emp_type", "员工用工类型", PropertyDataType.Dict));
            }
            return defList;
        }else{
            return Lists.list(property);
        }
    }

    protected PropertyDef initProperty() {
        val property = FastjsonUtil.convertObject(this, PropertyDef.class);
        if (StringUtils.isNotEmpty(this.getStyleExtras())) {
            val style = FastjsonUtil.toObject(this.getStyleExtras(), FormPropStyle.class);
            if (null != style.getI18nPlaceholder()) {
                property.setI18nPlaceholder(style.getI18nPlaceholder());
            } else if (StringUtils.isNotEmpty(style.getPlaceholder())) {
                property.setI18nPlaceholder(Maps.map("default", style.getPlaceholder()));
            }
            if (StringUtils.isNotEmpty(style.getFormat())) {
                property.setFormat(style.getFormat());
            }
        }
        return property;
    }

    public String fetchName(){
        return i18nName.get("default");
    }

    public boolean isApproverProp(){
        return approver != null && approver && dataType == PropertyDataType.Emp;
    }

    public List<WfMetaApproverDto> buildApproverDto(String formId, String funCode, String tenantId) {
        String approverCode = String.format("approver_%s_%s", property, formId);
        var wfMetaApproverDto = new WfMetaApproverDto(name, approverCode,
                tenantId, "caidaocloud-hr-paas-service",
                String.format("/api/hrpaas/v1/form/workflow/approver/%s",formId),
                WfApproverFetchType.RELATIVE_PATH,
                WfValueComponentEnum.NONE,
                Lists.list(),
                "");
        wfMetaApproverDto.setFunCode(funCode);
        return Lists.list(wfMetaApproverDto);
    }
}
