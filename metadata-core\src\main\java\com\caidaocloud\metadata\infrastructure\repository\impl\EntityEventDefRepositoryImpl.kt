package com.caidaocloud.metadata.infrastructure.repository.impl

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.caidaocloud.metadata.domain.entity.EntityEventDef
import com.caidaocloud.metadata.domain.repository.IEntityEventDefRepository
import com.caidaocloud.metadata.infrastructure.repository.po.EntityEventDefPo
import com.googlecode.totallylazy.Option
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Repository

@Repository
class EntityEventDefRepositoryImpl : IEntityEventDefRepository {

    @Value("\${anonymous.tenant:}")
    private val anonymousTenant: String? = null

    @Autowired
    private lateinit var entityEventDefMapper : EntityEventDefMapper

    override fun loadByModelRef(modelRef: String): List<EntityEventDef> {
        return entityEventDefMapper.selectList(QueryWrapper<EntityEventDefPo>()
            .eq("model_ref", modelRef)).map {
                it.toEntity(EntityEventDef::class.java)
        }
    }

    override fun load(id: String): Option<EntityEventDef> {
        return Option.option(entityEventDefMapper
            .selectOne(QueryWrapper<EntityEventDefPo>().eq("id", id.toLong())))
            .map { it.toEntity(EntityEventDef::class.java) }
    }

    override fun insert(entity: EntityEventDef): String {
        val po = EntityEventDefPo.fromEntity(
            EntityEventDefPo::class.java, entity);
        entityEventDefMapper.insert(po)
        return po.id.toString()
    }

    override fun update(entity: EntityEventDef) {
        val po = EntityEventDefPo.fromEntity(
            EntityEventDefPo::class.java, entity);
        entityEventDefMapper.update(po, QueryWrapper<EntityEventDefPo>().eq("id", po.id.toLong()))
    }
}