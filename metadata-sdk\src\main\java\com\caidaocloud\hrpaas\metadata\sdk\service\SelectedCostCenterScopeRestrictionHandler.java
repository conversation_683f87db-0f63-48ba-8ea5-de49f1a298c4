package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.jarvis.cache.annotation.Cache;
import lombok.val;

import java.util.List;
import java.util.stream.Collectors;

public class SelectedCostCenterScopeRestrictionHandler implements ScopeRestrictionHandler{
    @Override
    @Cache(key = "'select_cost_center_scope_restriction_' + #args[0] + '_' + #args[1]",expire = 5 *60)
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        Boolean isFilter = AuthScopeFilterUtil.get();
        val tlUser = SecurityUserUtil.getThreadLocalSecurityUserInfo();
        try{
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            val splitValues = simpleValues.split(",");
            AuthScopeFilterUtil.put(false);
            List<String> result = Lists.list();
            for (String splitValue : splitValues) {
                val list = DataQuery.identifier("entity.hr.EmpWorkInfo").limit(-1, 1).filterProperties(DataFilter.regex("costCenters", splitValue),
                Lists.list("empId"), System.currentTimeMillis()).getItems();
                result.addAll(list.stream().map(it -> it.get("empId")).collect(Collectors.toList()));
            }
            return result;
        }finally {
            if(null == tlUser){
                SecurityUserUtil.removeSecurityUserInfo();
            }else{
                SecurityUserUtil.setSecurityUserInfo(tlUser);
            }
            AuthScopeFilterUtil.put(isFilter);
        }

    }
}
