package com.caidaocloud.hrpaas.paas.common.feign;

import com.caidaocloud.hrpaas.paas.common.dto.PageDetailDto;
import com.caidaocloud.hrpaas.paas.common.dto.TenantPageDetailDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
@Component
public class PageFeignFallback  implements IPageFeignClient{
    @Override
    public Result<TenantPageDetailDto> pageDetail(String pageId) {
        return Result.fail();
    }
}