package com.caidaocloud.hrpaas.metadata.sdk.transaction.ribbon;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionHolder;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.exception.PaasTransactionException;
import com.netflix.loadbalancer.ILoadBalancer;
import com.netflix.loadbalancer.Server;
import com.netflix.loadbalancer.ZoneAvoidanceRule;
import lombok.extern.slf4j.Slf4j;

import static com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect.TRANSACTION_HOLDER;

/**
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
@Slf4j
public class TransactionRule extends ZoneAvoidanceRule {

	@Override
	public Server choose(Object key) {
		TransactionHolder txHolder = TransactionAspect.getTransactionHolder();
		if (!txHolder.isTransactionActivating()) {
			Server server = super.choose(key);
			if (log.isDebugEnabled()) {
				log.debug("default choosing server,server={}", server);
			}
			return server;
		}
		ILoadBalancer lb = getLoadBalancer();
		return chooseServer(lb.getAllServers(), txHolder.getIp(),txHolder.getPort());
	}

	private Server chooseServer(List<Server> allServers, String ip, int port) {
		for (Server server : allServers) {
			if (server.getHost().equals(ip) && server.getPort()==port) {
				if (log.isDebugEnabled()) {
					log.debug("Transaction choosing server,server={}", server);
				}
				return server;
			}
		}
		log.error("Could not find server by transaction info");
		throw new PaasTransactionException("Error choosing paas server");
	}
}
