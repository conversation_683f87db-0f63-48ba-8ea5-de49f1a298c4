package com.caidaocloud.hrpaas.service.domain.display.entity;

import com.caidaocloud.hrpaas.paas.match.vo.display.DisplayProperty;
import com.caidaocloud.hrpaas.service.application.display.dto.DisplayPropertyDto;
import com.caidaocloud.hrpaas.service.domain.display.repository.IDisplayPropertyDefRepository;
import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class DisplayPropertyDef extends BaseEntity<IDisplayPropertyDefRepository> {
    private String code;
    private List<DisplayProperty> properties;
    private String remark;
    private boolean deleted = false;
    private String tenantId;
    private long createTime;
    private String createBy;
    private long updateTime;
    private String updateBy;

    public static DisplayPropertyDef loadByCode(String code) {
        return ((IDisplayPropertyDefRepository) repository(DisplayPropertyDef.class)).loadByCode(code);
    }

    public static DisplayPropertyDef getInsertDef(DisplayPropertyDto dto) {
        DisplayPropertyDef def = getInstance();
        def.setProperties(dto.getProperties());
        def.setCode("emp");
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        String userId = String.valueOf(userInfo.getUserId());
        def.setTenantId(userInfo.getTenantId());
        def.setCreateBy(userId);
        def.setCreateTime(System.currentTimeMillis());
        def.setUpdateBy(userId);
        def.setUpdateTime(System.currentTimeMillis());
        return def;
    }

    public static DisplayPropertyDef getUpdateDef(DisplayPropertyDto dto) {
        DisplayPropertyDef def = loadByCode("emp");
        def.setProperties(dto.getProperties());
        def.setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        def.setUpdateTime(System.currentTimeMillis());
        return def;
    }

    public static DisplayPropertyDef getInstance() {
        return new DisplayPropertyDef();
    }
}
