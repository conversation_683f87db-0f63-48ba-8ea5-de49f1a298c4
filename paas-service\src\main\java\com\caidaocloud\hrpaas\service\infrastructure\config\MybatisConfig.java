package com.caidaocloud.hrpaas.service.infrastructure.config;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.caidaocloud.metadata.infrastructure.configuration.handler.StringJsonTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandlerRegistry;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * pgsql的json字段兼容
 * <AUTHOR>
 * @date 2023/5/29
 */
@Configuration
@ConditionalOnProperty(name = "spring.datasource.driver-class-name", havingValue = "org.postgresql.Driver")
public class MybatisConfig implements ConfigurationCustomizer {
	/**
	 * Customize the given a {@link MybatisConfiguration} object.
	 *
	 * @param configuration the configuration object to customize
	 */
	@Override
	public void customize(MybatisConfiguration configuration) {
		// 注册自定义的 TypeHandler
		TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
		typeHandlerRegistry.register(String.class, JdbcType.OTHER, new StringJsonTypeHandler());
	}
}
