package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("表单数据实体")
public class UiFormDataQueryDto extends BasePage {
    @ApiModelProperty("字段ID")
    private String formId;

    @ApiModelProperty("数据ID")
    private String dataId;

    @ApiModelProperty("数据体")
    private List<UiFormDataValueQueryDto> dataVals;

}
