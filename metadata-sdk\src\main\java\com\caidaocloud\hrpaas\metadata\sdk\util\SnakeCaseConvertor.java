package com.caidaocloud.hrpaas.metadata.sdk.util;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SnakeCaseConvertor {

    private static Pattern camelPattern = Pattern.compile("[A-Z]");

    public final static List<String> preBuiltSnakeCase = Lists.newArrayList("a_b", "b_c",
            "c_d", "d_e", "e_f", "f_g", "g_h", "h_i", "i_j", "j_k", "k_l", "l_m", "m_n");
    public final static List<String> preBuiltEncryptSnakeCase = Lists.newArrayList("e_a_b", "e_b_c",
            "e_c_d", "e_d_e", "e_e_f");
    public final static List<String> preBuiltLargeSnakeCase = Lists.newArrayList("aa_bb", "bb_cc", "cc_dd");

    public static String toSnake(String camel){
        camel = camel.replaceAll("\\.","_");
        Matcher match = camelPattern.matcher(camel);
        StringBuffer sb = new StringBuffer();
        while(match.find()){
            match.appendReplacement(sb, "_" + match.group(0).toUpperCase());
        }
        match.appendTail(sb);
        return sb.toString().toLowerCase().replaceAll("__","_").replaceFirst("^_", "");
    }

}
