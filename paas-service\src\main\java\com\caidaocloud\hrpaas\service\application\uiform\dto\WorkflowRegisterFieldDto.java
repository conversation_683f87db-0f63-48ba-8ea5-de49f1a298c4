package com.caidaocloud.hrpaas.service.application.uiform.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-06-23
 * 参考设计文档：https://caidaopdd.feishu.cn/docs/doccnSkCgZMqScCulngCEgpSFBe
 */
@Data
public class WorkflowRegisterFieldDto {
    /**
     * 字符串，字段ID，必填，最大长度16位
     */
    private String fieldId;

    /**
     * 字符串，字段名称，必填，最大长度32位
     */
    private String fieldName;

    /**
     * 枚举，字段类型，STR/BOOL/NUM
     */
    private FieldType fieldType;

    /**
     * JSON对象/JSON字符串，必填
     */
    private String fieldConfig;

    /**
     * 整形，排序号
     */
    private Integer orderNum;

    public enum FieldType {
        STR,
        BOOL,
        NUM
    }
}
