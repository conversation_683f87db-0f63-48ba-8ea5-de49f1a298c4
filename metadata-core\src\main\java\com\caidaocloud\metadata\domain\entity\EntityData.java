package com.caidaocloud.metadata.domain.entity;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.enums.IndicateType;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SpecifiedMultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.metadata.domain.repository.IEntityDataRepository;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class EntityData  extends BaseEntity<IEntityDataRepository> {
    private String identifier;
    private String bid;
    private String tenantId;
    private long createTime;
    private String createBy;
    private long updateTime;
    private String updateBy;
    private long dataStartTime;
    private long dataEndTime;
    private boolean deleted;
    private List<PropertyData> properties = Lists.list();
    private List<EntityRelation> relations = Lists.list();

    public static PageResult<List<EntityData>> join(DataJoin dataJoin) {
        return ((IEntityDataRepository)repository(EntityData.class))
                .join(dataJoin);
    }

    public static String searchableEncrypt(String str) {
        return ((IEntityDataRepository)repository(EntityData.class))
                .searchableEncrypt(str);
    }

    public static List<EmpSimple> loadByCondition(SpecifiedMultiDataFilter filter, long queryTime) {
        return ((IEntityDataRepository) repository(EntityData.class))
                .loadByCondition(filter, queryTime);
    }

    public List<EntityDataChange> create(){
        this.setId(null);
        this.setDataEndTime(DateUtil.MAX_TIMESTAMP);
        this.insert();
        return Lists.list(new EntityDataChange(null, this));
    }

    public EntityData copy(){
        return JsonEnhanceUtil.toObject(this, EntityData.class);
    }

    @SneakyThrows
    public List<EntityDataChange> updateWithTimeline() {
        return updateWithTimeline(Lists.list());
    }

    /**
     * 数据更新（时间轴）
     * @param relationDefs 需要更新的关联定义
     * @return
     */
    @SneakyThrows
    public List<EntityDataChange> updateWithTimeline(List<EntityRelationDef> relationDefs) {
        this.setId(null);
        List<EntityData> list = repository().loadAll(identifier, bid);
        if (list.isEmpty()) {
            throw new ServerException("数据不存在");
        }
        val sorted = Sequences.sequence(list).sortBy(data -> data.getDataStartTime());
        for (int i = 0; i < sorted.size(); i++) {
            EntityData entityData = sorted.get(i);
            if (entityData.getDataStartTime() == this.getDataStartTime()) {
                this.setId(entityData.getId());
                this.dataEndTime = entityData.dataEndTime;
                update(relationDefs);
                return Lists.list(new EntityDataChange(entityData, this));
            }
            if (entityData.getDataStartTime() > this.getDataStartTime()) {
                if (i == 0) {
                    this.dataEndTime = entityData.dataStartTime - 1;
                    this.insert();
                    return Lists.list(new EntityDataChange(null, this));
                }
                else {
                    val before = sorted.get(i - 1);
                    this.dataEndTime = entityData.dataStartTime - 1;
                    this.insert();
                    List<EntityDataChange> result = Lists.list(new EntityDataChange(null, this));
                    if (before.dataEndTime >= this.dataStartTime) {
                        val beforeCopy = before.copy();
                        before.dataEndTime = this.dataStartTime - 1;
                        repository().update(before);
                        result.add(new EntityDataChange(beforeCopy, before));
                    }
                    return result;
                }
            }
        }
        val last = sorted.get(sorted.size() - 1);
        if (last.dataEndTime >= this.dataStartTime) {
            this.dataEndTime = last.dataEndTime;
            this.insert();
            val lastCopy = last.copy();
            last.dataEndTime = this.dataStartTime - 1;
            last.directUpdate();
            return Lists.list(new EntityDataChange(null, this),
                    new EntityDataChange(lastCopy, last));
        }
        else {
            this.dataEndTime = DateUtil.MAX_TIMESTAMP;
            this.insert();
            return Lists.list(new EntityDataChange(null, this));
        }
    }

    private void update(List<EntityRelationDef> relationDefs) {
        repository().update(this, relationDefs);
    }

    @Override
    public void update(){
        throw new ServerException("illegal method calling");
    }

    public void directUpdate(){
        super.update();
    }

    @SneakyThrows
    public static List<EntityDataChange> delete(String identifier, String bid, long startTime){
        List<EntityDataChange> result = Lists.list();
        val repository = (IEntityDataRepository)repository(EntityData.class);
        List<EntityData> list = repository.loadAll(identifier, bid).stream()
                .filter(it -> StringUtils.equals(identifier, it.identifier)).collect(Collectors.toList());
        if(list.isEmpty()){
            throw new ServerException("数据不存在");
        }
        for(EntityData entityData : list){
            if(entityData.dataEndTime < startTime){
                //nothing to do
            }else if(entityData.dataStartTime < startTime && entityData.dataEndTime >= startTime){
                val entityDataCopy = entityData.copy();
                entityData.dataEndTime = startTime - 1;
                repository.update(entityData);
                result.add(new EntityDataChange(entityDataCopy, entityData));
            }else if(!(startTime < entityData.dataStartTime)){
                repository.delete(identifier, entityData.getId());
                result.add(new EntityDataChange(entityData, null));
            }
        }
        return result;
    }

    public static Option<EntityData> load(String identifier, String bid, long time){
        val option = ((IEntityDataRepository)repository(EntityData.class)).load(identifier, bid, time, Lists.list(), null);
        if(option.isDefined()){
            val data = option.get();
            if(!StringUtils.equals(identifier, data.identifier)){
                return Option.none();
            }
        }
        return option;
    }

    public static Option<EntityData> load(String identifier, String bid, long time, List<String> relateList, DataQueryDto query){
        val option = ((IEntityDataRepository) repository(EntityData.class)).load(identifier, bid, time, relateList, query);
        if(option.isDefined()){
            val data = option.get();
            if(!StringUtils.equals(identifier, data.identifier)){
                return Option.none();
            }
        }
        return option;
    }

    public static List<EntityData> loadAll(String identifier, String bid){
        val list = ((IEntityDataRepository)repository(EntityData.class))
                .loadAll(identifier, bid).stream().filter(it -> StringUtils.equals(identifier, it.identifier))
                .collect(Collectors.toList());
        return list;
    }

    public static PageResult<EntityData> page(String identifier, DataFilter filter, int pageNo, int pageSize, List<String> relateList, boolean group, long time){
        return ((IEntityDataRepository) repository(EntityData.class))
                .page(identifier, filter, pageNo, pageSize, relateList, Lists.list(), group, null, time, null);
    }

    public static PageResult<EntityData> page(String identifier, DataFilter filter, int pageNo, int pageSize, List<String> relateList, List<String> specifyProperties, boolean group, String orderBy, long time, DataQueryDto query){
        return ((IEntityDataRepository) repository(EntityData.class))
                .page(identifier, filter, pageNo, pageSize, relateList, specifyProperties, group, orderBy, time, query);
    }

    public static long countByBids(String identifier, List<String> bids, long time){
        return ((IEntityDataRepository)repository(EntityData.class)).countBids(identifier, bids, time);
    }

    public Option<EntityRelation> filterRelation(String property){
        return Sequences.sequence(this.relations).find(relation -> StringUtils.equals(property, relation.getProperty()));
    }

    public Object fetchPropertyValue(String property){
        for(PropertyData propertyData : this.getProperties()){
            if(StringUtils.equals(propertyData.getProperty(), property)){
                if(propertyData.getDataType().name().contains("Array")){
                    return propertyData.getArrayValues();
                }else{
                    return propertyData.getValue();
                }
            }
        }
        return null;
    }

    public void addPropertyValue(String property, String value, PropertyDataType dataType){
        PropertyData propertyData = new PropertyData();
        propertyData.setProperty(property);
        propertyData.setDataType(dataType);
        propertyData.setValue(value);
        this.getProperties().add(propertyData);
    }

    public void removePropertyValue(String property){
        this.getProperties().removeIf(it->
                StringUtils.equals(it.getProperty(), property)
        );
    }

    public String fetchPropertyStringValue(String property){
        Object objVal = fetchPropertyValue(property);
        return null == objVal ? "" : objVal.toString();
    }

    public String fetchTreePidAndReplaceToPersist(){
        for(PropertyData propertyData : this.getProperties()){
            if(StringUtils.equals(propertyData.getProperty(), Constant.PROPERTY_PID)){
                propertyData.setProperty(Constant.PROPERTY_PID_PERSIST);
                return propertyData.getValue();
            }
        }
        return null;
    }

    public static List<EntityData> range(String identifier, String bid, long startTime, long endTime) {
        return ((IEntityDataRepository)repository(EntityData.class))
                .range(identifier, bid, startTime, endTime);
    }

    public void batchUpdate(DataFilter filter){
        ((IEntityDataRepository)repository(EntityData.class)).batchUpdate(identifier, filter, this);
    }

    public static void batchDeleteWithoutRecord(String identifier, DataFilter filter) {
        ((IEntityDataRepository)repository(EntityData.class)).batchDelete(identifier, filter);
    }

    public static void batchInsertWithoutRecord(String identifier, List<EntityData> dataList) {
        ((IEntityDataRepository)repository(EntityData.class)).batchInsert(identifier, dataList);
    }

    public static long count(String identifier, DataFilter filter,List<String> relateList, boolean group, long time){
        return ((IEntityDataRepository)repository(EntityData.class))
                .count(identifier, filter,relateList, group, time);
    }


    public static PageResult<EntityData> max(String identifier, String filter, String maxProperty,
                           List<String> by, int pageSize, int pageNo, long queryTime) {
        return ((IEntityDataRepository)repository(EntityData.class))
                .max(identifier, filter, maxProperty, by,pageSize,pageNo, queryTime);
    }

    public static List<Map<String, Object>> countByGroup(String identifier, DataFilter filter, List<String> by, long queryTime){
        return ((IEntityDataRepository)repository(EntityData.class)).countByGroup(identifier,filter,by,queryTime);
    }

    public static List<Map<String, Object>> indicateByGroup(String identifier, DataFilter filter, List<String> by, long queryTime, IndicateType indicateType, String indicateProperty) {
        return ((IEntityDataRepository)repository(EntityData.class)).indicateByGroup(identifier,filter,by,queryTime, indicateType, indicateProperty);
    }
}
