package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.jarvis.cache.annotation.Cache;
import lombok.val;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class MyConcurrentOrgAndBelongingsScopeRestrictionHandler implements ScopeRestrictionHandler {

    @Override
    @Cache(key = "'5_scope_restriction_' + #args[0] + '_' + #args[1]",expire = 5 *60)
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        Boolean isFilter = AuthScopeFilterUtil.get();
        val tlUser = SecurityUserUtil.getThreadLocalSecurityUserInfo();
        try{
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            val empId = SecurityUserUtil.getSecurityUserInfo().getEmpId();
            if(empId == null){
                return Lists.list();
            }
            AuthScopeFilterUtil.put(false);
            val now = System.currentTimeMillis();
            val orgs = DataQuery.identifier("entity.hr.EmpConcurrentPost")
                    .filterProperties(DataFilter.eq("empId", empId.toString())
                                    .andLe("startDate", String.valueOf(now))
                                    .andGe("endDate", String.valueOf(now)),
                            Lists.list("organize"), now).getItems().stream().map(it->it.get("organize")).collect(Collectors.toList());
            if(orgs.isEmpty()){
                return Lists.list();
            }
            val results = Lists.list(orgs);
            for(String organize: orgs){
                for(int i = 1; i<20; i++){
                    val list = DataQuery.identifier("entity.hr.Org").limit(1000, i).filterProperties(DataFilter.regex("pid$path", organize),
                            Lists.list("bid"), System.currentTimeMillis()).getItems();
                    if(list.isEmpty()){
                        break;
                    }else{
                        results.addAll(list.stream().map(it->it.get("bid")).collect(Collectors.toList()));
                    }
                }
            }
            return results;
        }finally {
            if(null == tlUser){
                SecurityUserUtil.removeSecurityUserInfo();
            }else{
                SecurityUserUtil.setSecurityUserInfo(tlUser);
            }
            AuthScopeFilterUtil.put(isFilter);
        }

    }

}
