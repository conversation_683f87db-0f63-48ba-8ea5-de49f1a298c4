package com.caidaocloud.hrpaas.service.infrastructure.util;

import com.aliyuncs.utils.StringUtils;
import com.caidaocloud.util.FastjsonUtil;
import lombok.val;
import org.apache.commons.io.IOUtils;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class TimelineAnalyzeTool {

    public static void main(String[] args) throws Exception {
        val reader = new BufferedReader(new FileReader("D:\\workspace\\caidao-hr-paas-service\\paas-service\\src\\main\\java\\com\\caidaocloud\\hrpaas\\service\\infrastructure\\util\\timelineData.txt"));
        String line = "";
        int i = 1;
        while(true){
            line = reader.readLine();
            if(StringUtils.isEmpty(line)){
                break;
            }
            val datas = FastjsonUtil.toList(line, Map.class);
            datas.forEach(data->{
                if(null != data.get("before")){
                    System.out.println("before");
                    System.out.println(((Map)data.get("before")).get("id"));
                    System.out.println(((Map)data.get("before")).get("dataStartTime"));
                    System.out.println(((Map)data.get("before")).get("dataEndTime"));
                }
                if(null != data.get("after")){
                    System.out.println("after");
                    System.out.println(((Map)data.get("after")).get("id"));
                    System.out.println(((Map)data.get("after")).get("dataStartTime"));
                    System.out.println(((Map)data.get("after")).get("dataEndTime"));
                }
            });
            System.out.println("======================" + i);
            i++;
        }
    }
}
