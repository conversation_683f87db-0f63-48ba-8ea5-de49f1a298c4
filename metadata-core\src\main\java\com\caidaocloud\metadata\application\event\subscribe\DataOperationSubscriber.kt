package com.caidaocloud.metadata.application.event.subscribe

import com.caidaocloud.hrpaas.metadata.sdk.enums.EventDefStatus
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil
import com.caidaocloud.metadata.application.event.EntityEventMsg
import com.caidaocloud.metadata.application.event.publish.DATA_MODIFICATION
import com.caidaocloud.metadata.application.event.publish.EntityDataOperationEvent
import com.caidaocloud.metadata.application.event.publish.EntityEventSender
import com.caidaocloud.metadata.domain.entity.EntityEventDef
import com.caidaocloud.metadata.domain.service.EntityEventDefDomainService
import com.caidaocloud.metadata.domain.entity.DataModification
import com.caidaocloud.metadata.domain.repository.IDataModificationRepository
import com.caidaocloud.metadata.domain.enums.OperationTarget
import com.caidaocloud.msg.handler.MessageHandler
import com.caidaocloud.msg.producer.MessageProducer
import com.caidaocloud.security.util.SecurityUserUtil
import com.caidaocloud.util.FastjsonUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class DataOperationSubscriber : MessageHandler<EntityDataOperationEvent> {

    @Autowired
    private lateinit var entityEventDefDomainService : EntityEventDefDomainService

    @Autowired
    private lateinit var entityEventSender: EntityEventSender

    @Autowired
    private lateinit var dataModificationRepository: IDataModificationRepository

    @Value("\${allSub.open:false}")
    private lateinit var allSubOpen: String

    override fun topic(): String {
        return DATA_MODIFICATION
    }

    override fun handle(event: EntityDataOperationEvent) {
        if(event.allowAnonymous){
            try{
                ThreadLocalUtil.allowAnonymous()
                handleEvent(event)
            }finally {
                ThreadLocalUtil.anonymousEnd()
            }
        }else{
            handleEvent(event)
        }
    }

    fun handleEvent(event: EntityDataOperationEvent) {
        if(event.allowAnonymous){

        }
        DataModification(
            event.identifier,
            event.targetId,
            OperationTarget.EntityData,
            FastjsonUtil.toJson(event.data),
            event.eventTime,
            SecurityUserUtil.getSecurityUserInfo().userId?.toString()?:"",
            event.operation,
            event.startTime
        ).insert()

        val eventDefList: List<EntityEventDef> = entityEventDefDomainService.loadByModelRef(event.identifier)
            .filter { entityEventDef->
                EventDefStatus.OPEN == entityEventDef.status
            }
        for (entityEventDef in eventDefList) {
            val entityEvent = entityEventDefDomainService.initDataEvent(event.targetId, event.operation, event.data, entityEventDef)
            entityEventSender.publish(entityEvent, entityEventDef.consumerList)
        }
        if("entity.hr.EmpWorkInfo" == event.identifier && "true" == allSubOpen){
            val entityEvent = entityEventDefDomainService.initDataEvent(event.targetId, event.operation, event.data, "EMP_LEADER_CHANGE")
            entityEventSender.sendEmpLeaderChange(JsonEnhanceUtil.toObject(entityEvent, EntityEventMsg::class.java), SecurityUserUtil.getSecurityUserInfo())
        }

    }
}