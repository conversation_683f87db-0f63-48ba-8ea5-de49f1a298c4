package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("user_dynamic_column_config")
public class UserDynamicColumnConfigPo {
    @TableId(type = IdType.INPUT)
    private Long id;
    private String code;
    private String userId;
    private String config;
}
