package com.caidaocloud.hrpaas.metadata.sdk.query;

import com.googlecode.totallylazy.Lists;
import lombok.val;

import java.util.List;

public class MdQueryTL {

    private static ThreadLocal<List<Boolean>> isMasterDataQueryTL = new ThreadLocal<>();

    public static void set(Boolean isMasterDataQueryTl){
        List<Boolean> tl = isMasterDataQueryTL.get();
        if(null == tl){
            tl = Lists.list();
        }
        tl.add(0, isMasterDataQueryTl);
        isMasterDataQueryTL.set(tl);
    }

    public static void remove(){
        isMasterDataQueryTL.get().remove(0);
    }

    public static Boolean get(){
        val isMasterDataQuery = isMasterDataQueryTL.get();
        if(null == isMasterDataQuery || isMasterDataQuery.isEmpty()){
            return false;
        }
        return isMasterDataQuery.get(0);
    }
}
