package com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign;

import com.caidaocloud.hrpaas.metadata.sdk.dto.TransactionInfoDto;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

@Component
public class TransactionFeignFallback implements ITransactionFeign {

	@Override
	public Result<TransactionInfoDto> begin() {
		return Result.fail();
	}

	@Override
	public Result commit(String transactionId) {
		return Result.fail();
	}

	@Override
	public Result rollback(String transactionId) {
		return Result.fail();
	}
}
