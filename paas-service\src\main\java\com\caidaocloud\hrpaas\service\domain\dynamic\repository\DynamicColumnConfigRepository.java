package com.caidaocloud.hrpaas.service.domain.dynamic.repository;

import com.caidaocloud.hrpaas.paas.common.dto.UserDynamicConfig;
import com.caidaocloud.hrpaas.service.domain.dynamic.entity.DynamicColumnConfig;
import com.googlecode.totallylazy.Sequence;

import java.util.List;

public interface DynamicColumnConfigRepository {
    DynamicColumnConfig loadByCode(String code);

    void create(DynamicColumnConfig dynamicColumnConfig);

    void update(DynamicColumnConfig dynamicColumnConfig);

    List<DynamicColumnConfig> list();

    UserDynamicConfig loadUser(String code, String userId);

    void addUser(String code, String userId, UserDynamicConfig userConfig);

    void updateUser(String code, String userId, UserDynamicConfig userConfig);

    Sequence<DynamicColumnConfig> loadByCodes(List<String> codeList);
}
