package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Data
public class SimplePropertyValue implements PropertyValue {

    private String value;
    private List<String> arrayValues = Lists.list();
    private PropertyDataType type;

    public SimplePropertyValue(){

    }

    public SimplePropertyValue(String value){
        this.value = value;
    }

    public SimplePropertyValue(List<String> values){
        this.arrayValues = values;
    }

    public SimplePropertyValue(String value, PropertyDataType type){
        this.type = type;
        this.value = value;
    }

    public SimplePropertyValue(List<String> value, PropertyDataType type){
        this.type = type;
        this.arrayValues = value;
    }

    public static SimplePropertyValue toDisplay(List<PropertyDataDto> properties, MetadataPropertyVo propDef, String addParentPath){
        val valueDto = properties.stream()
                .filter(propertyDataDto -> propertyDataDto.getProperty().equals(StringUtils.trimToEmpty(addParentPath) + propDef.getProperty()))
                .findFirst();
        if(valueDto.isPresent()){
            SimplePropertyValue value = new SimplePropertyValue();
            value.setValue(valueDto.get().getValue());
            value.setArrayValues(valueDto.get().getArrayValues());
            value.setType(propDef.getDataType());
            return value;
        }else if(!StringUtils.equals(propDef.getProperty(),"id")){
            if(null!= propDef.getDefaultValue()){
                SimplePropertyValue value = new SimplePropertyValue();
                value.setValue(propDef.getDefaultValue().getValue());
                value.setArrayValues(propDef.getDefaultValue().getArrayValues());
                value.setType(propDef.getDataType());
                return value;
            }else{
                return null;
            }
        }else{
            return null;
        }
    }

    public static List<PropertyDataDto> toPersist(DataSimple data, MetadataPropertyVo propDef){
        List<PropertyDataDto> result = Lists.list();
        val dataMap = JsonEnhanceUtil.toObject(data, Map.class);
        val standardPropertyValue = dataMap.get(propDef.getProperty());
        val customPropertyValue = data.getProperties().get(propDef.getProperty());
        if(null == standardPropertyValue && null == customPropertyValue){
            PropertyDataDto propertyDataDto = new PropertyDataDto();
            propertyDataDto.setProperty(propDef.getProperty());
            propertyDataDto.setDataType(propDef.getDataType());
            result.add(propertyDataDto);
            return result;
        }
        if(null != customPropertyValue){
            PropertyDataDto propertyDataDto = ((SimplePropertyValue)customPropertyValue).toPropertyData(propDef, "");
            if (data.containProperty(propDef.getProperty())) {
                fillInValue(propertyDataDto, propDef.getDataType(), standardPropertyValue);
            }
            result.add(propertyDataDto);
        }else{
            PropertyDataDto propertyDataDto = new PropertyDataDto();
            propertyDataDto.setProperty(propDef.getProperty());
            propertyDataDto.setDataType(propDef.getDataType());
            fillInValue(propertyDataDto,propDef.getDataType(), standardPropertyValue);
            result.add(propertyDataDto);
        }
        return result;
    }

    public PropertyDataDto toPropertyData(MetadataPropertyVo propDef, String addParentPath){
        PropertyDataDto propertyDataDto = new PropertyDataDto();
        propertyDataDto.setProperty(addParentPath + propDef.getProperty());
        propertyDataDto.setDataType(propDef.getDataType());
        if(propDef.getDataType().isArray()){
            propertyDataDto.setArrayValues(this.getArrayValues());
        }else{
            propertyDataDto.setValue(this.getValue());
        }
        return propertyDataDto;
    }

    private static void fillInValue(PropertyDataDto propertyDataDto, PropertyDataType dataType, Object value){
        if(value == null){
            propertyDataDto.setValue(null);
            propertyDataDto.setArrayValues(null);
        }else if(dataType.isArray()){
            propertyDataDto.setArrayValues(FastjsonUtil.convertList((List)value, String.class));
        }else{
            propertyDataDto.setValue(value.toString());
        }
    }

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {

    }

    @Override
    public String toText() {
        return value;
    }

}
