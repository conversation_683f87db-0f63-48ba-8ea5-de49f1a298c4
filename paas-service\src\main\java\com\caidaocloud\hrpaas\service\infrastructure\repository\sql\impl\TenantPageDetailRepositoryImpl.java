package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.service.domain.page.entity.TenantPageDetail;
import com.caidaocloud.hrpaas.service.domain.page.repository.ITenantPageDetailRepository;
import org.springframework.stereotype.Repository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.TenantPageDetailPo;
import com.googlecode.totallylazy.Option;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;

@Repository
public class TenantPageDetailRepositoryImpl implements ITenantPageDetailRepository {

    @Autowired
    private TenantPageDetailMapper mapper;

    @Override
    public void deleteById(String id) {
        mapper.delete(new LambdaQueryWrapper<TenantPageDetailPo>()
                .eq(TenantPageDetailPo::getPageId, id));
    }

    @Override
    public String insert(TenantPageDetail entity) {
        val pageId = entity.getId();
        entity.setId(null);
        val po = TenantPageDetailPo.fromEntity(TenantPageDetailPo.class, entity);
        po.setId(Long.valueOf(SnowUtil.nextId()));
        po.setPageId(pageId);
        mapper.insert(po);
        return po.getPageId();
    }

    @Override
    public void update(TenantPageDetail entity) {
        val pageId = entity.getId();
        entity.setId(null);
        val po = TenantPageDetailPo.fromEntity(TenantPageDetailPo.class, entity);
        entity.setId(pageId);
        po.setPageId(pageId);
        mapper.update(po, new LambdaQueryWrapper<TenantPageDetailPo>().eq(TenantPageDetailPo::getPageId, pageId));
    }

    @Override
    public Option<TenantPageDetail> load(String id){
        return Option.option(
                mapper.selectOne(new LambdaQueryWrapper<TenantPageDetailPo>().eq(TenantPageDetailPo::getPageId, id))
        ).map(it->{
            it.setId(null);
            TenantPageDetail detail = it.toEntity(TenantPageDetail.class);
            detail.setId(it.getPageId());
            return detail;
        });
    }
}
