package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 表单引擎Dto
 * <AUTHOR>
 * @date 2021-06-15
 */
@Data
public class UiDataSourceDto implements Serializable {
    private final static long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("数据源名字")
    private String sourceName;

    @ApiModelProperty("数据源描述")
    private String description;

    @ApiModelProperty("数据源类型。CUSTOM：自定义数据源,SYSTEM_DICT：系统字典数据源,SYSTEM_FORM_ENGINE：其他表单数据源," +
            "SYSTEM_STANDARD_COMPONENT：系统标准组件数据源,SYSTEM_LIST：系统功能列表数据源,METADATA：元数据数据源,EXTERNAL_SYSTEM：外部系统数据源")
    private DataSourceType type;

    @ApiModelProperty("数据源配置")
    private UiGeneralDataSourceDto config;

    protected boolean isJoinModel(){
        return false;
    }
}
