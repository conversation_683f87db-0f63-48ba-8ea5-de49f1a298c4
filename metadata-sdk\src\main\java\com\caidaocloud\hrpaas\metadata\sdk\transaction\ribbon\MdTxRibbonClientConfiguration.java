package com.caidaocloud.hrpaas.metadata.sdk.transaction.ribbon;

import com.netflix.client.config.IClientConfig;
import com.netflix.loadbalancer.IRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

// @Configuration
@Slf4j
public class MdTxRibbonClientConfiguration {

	@Bean
	@ConditionalOnMissingBean
	public IRule ribbonRule(IClientConfig config) {
		MdTransactionRule rule = new MdTransactionRule();
		rule.initWithNiwsConfig(config);
		return rule;
	}
}