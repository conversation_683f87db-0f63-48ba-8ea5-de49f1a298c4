package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 枚举组件
 * <AUTHOR>
 * @date 2022-02-22
 */
@Data
@DataComponent(dataType = PropertyDataType.Enum)
public class EnumSimple implements ComponentPropertyValue {
    private String text;

    private String value;

    @Override
    public Map<String, Boolean> propertySuffixToWhetherLong(){
        return Maps.map(
                Sequences.sequence(
                        Pair.pair("", false)
                )
        );
    }

    /**
     * 存储属性转换成对象
     */
    @Override
    public PropertyValue extractDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties){
        return EnumSimple.toDisplay(propDef, properties);
    }

    public static EnumSimple toDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> propList){
        String fieldProp = propDef.getProperty();
        if(null == propList || propList.isEmpty() || StringUtil.isEmpty(fieldProp)){
            return null;
        }

        EnumSimple enumSimple = new EnumSimple();
        for (PropertyDataDto prop : propList) {
            if(null == prop || !prop.getProperty().equals(fieldProp)){
                continue;
            }

            enumSimple.setValue(prop.getValue());
            List<PropertyEnumDefDto> enumDef = propDef.getEnumDef();
            if(null == enumDef || enumDef.isEmpty()){
                return enumSimple;
            }

            Optional<PropertyEnumDefDto> first = enumDef.stream().filter(item -> Objects.equals(item.getValue(), enumSimple.getValue())).findFirst();
            PropertyEnumDefDto propEnum = null;
            if(first.isPresent() && null != (propEnum = first.get())){
                enumSimple.setText(propEnum.getDisplay());
                return enumSimple;
            }
        }

        return enumSimple;
    }

    @Override
    public List<PropertyDataDto> extractPersist(DataSimple data, MetadataPropertyVo propDef){
        return EnumSimple.toPersist(data, propDef);
    }

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {
        dataValue.fitDataValue(((EnumSimple) dataVal).getValue());
        DataValueFunction dataValueDto = dataValue.dataValueBulid();
        dataValueDto.fitDataProp(String.format(UIFORM_RELATION_FORMAT_TXT, dataValue.loadDataProp()));
        dataValueDto.fitDataValue(((EnumSimple) dataVal).toText());
        dataVals.add(dataValueDto);
    }

    @Override
    public String toText() {
        return text;
    }

    public static List<PropertyDataDto> toPersist(DataSimple data, MetadataPropertyVo propDef){

        Map dataMap = JsonEnhanceUtil.toObject(data, Map.class);
        // 获取属性中的枚举字段
        String dictPrefix = propDef.getProperty();
        PropertyValue customPropertyValue = data.getProperties().get(dictPrefix);

        EnumSimple enumSimple = null;
        // 枚举存在于原生属性中
        if(dataMap.containsKey(dictPrefix)){
            Object standardPropertyValue = dataMap.get(dictPrefix);
            if(null != standardPropertyValue){
                enumSimple = JsonEnhanceUtil.toObject(standardPropertyValue, EnumSimple.class);
                return EnumSimple.toPropList(enumSimple, dictPrefix);
            }

            return EnumSimple.toPropList(null, dictPrefix);
        }

        if(null != customPropertyValue){
            if(customPropertyValue instanceof EnumSimple){
                enumSimple = ((EnumSimple) customPropertyValue);
            } else{
                enumSimple = new EnumSimple();
                String dictValue = ((SimplePropertyValue) customPropertyValue).getValue();
                enumSimple.setValue(dictValue);

            }

            return EnumSimple.toPropList(enumSimple, dictPrefix);
        }

        return EnumSimple.toPropList(null, dictPrefix);
    }

    private static List<PropertyDataDto> toPropList(EnumSimple enumSimple, String dictPrefix){
        List<PropertyDataDto> result = Lists.newArrayList();
        if(null == enumSimple){
            enumSimple = new EnumSimple();
        }

        PropertyDataDto dataDto = new PropertyDataDto();
        dataDto.setProperty(dictPrefix);
        dataDto.setValue(enumSimple.getValue());
        dataDto.setDataType(PropertyDataType.Enum);
        result.add(dataDto);
        return result;
    }

}
