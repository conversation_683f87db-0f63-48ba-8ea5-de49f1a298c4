package com.caidaocloud.hrpaas.service.test;

import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.workflow.dto.WfMetaFunDto;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.enums.WfFunctionPageJumpType;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class WorkflowTest {
    @Resource
    private IWfRegisterFeign iwfRegisterFeign;

    @Test
    public void testReg(){

        String tenantId = "33";

        List<WfMetaFunFormFieldDto> fields = new ArrayList<>();
        WfMetaFunDto dto = new WfMetaFunDto("涛哥测试", "form_funcCode",
                WfFunctionPageJumpType.RELATIVE_PATH, tenantId, "caidaocloud-hr-paas-service",
                "", "", "", fields);
        iwfRegisterFeign.registerFunction(dto);

        log.info("test workflow reg ....");
    }
}
