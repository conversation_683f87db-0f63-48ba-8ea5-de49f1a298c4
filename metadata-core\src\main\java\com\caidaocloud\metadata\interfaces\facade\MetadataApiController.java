package com.caidaocloud.metadata.interfaces.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.metadata.application.dto.MetadataIndexDto;
import com.caidaocloud.metadata.application.service.MetadataPropertyService;
import com.caidaocloud.metadata.application.service.MetadataRelationService;
import com.caidaocloud.metadata.application.service.MetadataService;
import com.caidaocloud.metadata.domain.entity.EntityRelationDef;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.caidaocloud.metadata.interfaces.convertor.MetadataConvertor;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataRelationVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Sequences;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/api/${service.name.short:hrpaas}/v1/metadata")
@Api(value = "元数据维护", tags = "元数据--后端接口")
public class MetadataApiController {

    @Autowired
    private MetadataService metadataService;

    @Autowired
    private MetadataPropertyService metadataPropertyService;

    @Autowired
    private MetadataRelationService metadataRelationService;

    @PostMapping
    @ApiOperation("新增元数据信息")
    public Result<Boolean> create(@RequestBody MetadataDto metadata){
        metadataService.create(MetadataConvertor.fromDto(metadata), DefChannel.API);
        return Result.ok();
    }

    @PutMapping("/basic")
    @ApiOperation("修改元数据基本信息")
    public Result<Boolean> update(@RequestBody MetadataBasicInfoDto basicInfoDto){
        metadataService.update(MetadataConvertor.fromDto(basicInfoDto), DefChannel.API);
        return Result.ok();
    }

    @PutMapping("/basic/script")
    @ApiOperation("修改元数据基本信息--脚本")
    public Result<Boolean> updateByScript(@RequestBody MetadataBasicInfoDto basicInfoDto){
        metadataService.update(MetadataConvertor.fromDto(basicInfoDto), DefChannel.SCRIPT);
        return Result.ok();
    }

    @GetMapping
    @ApiOperation("查询元数据信息")
    public Result<MetadataVo> one(@RequestParam("identifier") String identifier){
        val def = metadataService.one(identifier);
        val inherited = metadataService.loadHierarchyProperties(def);
        return Result.ok(MetadataConvertor.toVo(def, inherited));
    }

    @GetMapping("/page")
    @ApiOperation("元数据信息列表")
    public Result<PageResult<MetadataVo>> page(@RequestParam("pageNo")int pageNo, @RequestParam("pageSize")int pageSize){
        val page = metadataService.page(null, pageNo, pageSize);
        return Result.ok(MetadataConvertor.toVoPage(page));
    }

    @GetMapping("/:search")
    @ApiOperation("元数据信息搜索")
    public Result<List<MetadataVo>> search(@RequestParam("identifierPattern") String identifierPattern,
            @RequestParam(value = "filterInheritable", defaultValue = "false") boolean filterInheritable,
            @RequestParam(value = "maxCount", defaultValue = "10") int maxCount) {
        val defs = metadataService.search(identifierPattern, filterInheritable, false, maxCount);
        return Result.ok(MetadataConvertor.toVoList(defs));
    }

    @PostMapping("/property")
    @ApiOperation("新增元数据自定义属性")
    public Result<Boolean> insertCustomProps(@RequestBody MetadataPropertyInsertDto insert){
        val props = MetadataConvertor.toPropDefSeq(insert);
        metadataPropertyService.insertCustomProps(insert.getIdentifier(), props, DefChannel.API);
        return Result.ok();
    }

    @DeleteMapping("/standard/v1/property/delete")
    @ApiOperation("删除元数据标准属性")
    public Result<Boolean> physicalDeleteStandardProps(@RequestParam("identifier") String identifier, @RequestBody List<String> properties){
        metadataPropertyService.physicalDeleteStandardProps(identifier, Sequences.sequence(properties), DefChannel.API);
        return Result.ok();
    }

    @PutMapping("/property")
    @ApiOperation("更新元数据自定义属性")
    public Result<Boolean> updateCustomProps(@RequestBody MetadataPropertyUpdateDto update){
        val props = MetadataConvertor.toPropDefSeq(update);
        metadataPropertyService.updateCustomProps(update.getIdentifier(), props, DefChannel.API);
        return Result.ok();
    }

    @PutMapping("/property/:remove")
    @ApiOperation("禁用元数据自定义属性")
    public Result<Boolean> removeCustomProps(@RequestBody MetadataPropertyRemoveOrDeleteDto remove){
        metadataPropertyService.removeCustomProps(remove.getIdentifier(),
                Sequences.sequence(remove.getProps()), DefChannel.API);
        return Result.ok();
    }

    @PutMapping("/property/:recover")
    @ApiOperation("恢复元数据自定义属性")
    public Result<Boolean> recoverCustomProps(@RequestBody MetadataPropertyRecoverDto recover){
        val props = MetadataConvertor.toDefaultValueSeq(recover);
        metadataPropertyService.recoverCustomProps(recover.getIdentifier(), props, DefChannel.API);
        return Result.ok();
    }

    @DeleteMapping("/property")
    @ApiOperation("删除元数据自定义属性")
    public Result<Boolean> physicalDeleteCustomProps(@RequestBody MetadataPropertyRemoveOrDeleteDto delete){
        val props = Sequences.sequence(delete.getProps());
        metadataPropertyService.physicalDeleteCustomProps(delete.getIdentifier(), props, DefChannel.API);
        return Result.ok();
    }

    @PostMapping("/relation")
    @ApiOperation("新增元数据关联")
    public Result<Boolean> insertRelation(@RequestBody MetadataRelationDto relation){
        metadataRelationService
                .addRelation(JsonEnhanceUtil.toObject(relation, EntityRelationDef.class), DefChannel.API);
        return Result.ok();
    }

    @DeleteMapping("/relation")
    @ApiOperation("删除元数据关联")
    public Result<Boolean> deleteRelation(@RequestParam("identifier") String identifier, @RequestParam("property") String property){
        metadataRelationService.deleteRelation(identifier, property, DefChannel.API);
        return Result.ok();
    }

    @GetMapping("/relation")
    @ApiOperation("查询元数据关联")
    public Result<List<MetadataRelationVo>> queryRelation(@RequestParam("identifier") String identifier){
        val relations = metadataRelationService.loadRelationsByIdentifier(identifier);
        return Result.ok(JsonEnhanceUtil.toObjects(relations, MetadataRelationVo.class).toList());
    }

    @PostMapping("/index")
    @ApiOperation("添加索引")
    public Result addIndex(@RequestBody MetadataIndexDto metadataIndexDto){
        metadataService.addIndex(metadataIndexDto);
        return Result.ok();
    }

}
