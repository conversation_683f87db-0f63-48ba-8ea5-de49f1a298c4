package com.caidaocloud.hrpaas.service.application.form.dto;

import com.caidaocloud.workflow.dto.WfComponentValueDto;
import com.caidaocloud.workflow.enums.WfValueComponentEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 组册流程序列流信息
 *
 * <AUTHOR>
 * @date 2022/11/7
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WfRegistrySeqDto {

    /**
     * 序列流code
     */
    private String seqCode;

    /**
     * 序列流名称
     */
    private String seqName;

    /**
     * 数据源
     */
    private String dataSource;

    /**
     * 组件类型
     */
    private WfValueComponentEnum component;

    /**
     * 枚举类型的值
     */
    private List<WfComponentValueDto> componentEnumValue;

}
