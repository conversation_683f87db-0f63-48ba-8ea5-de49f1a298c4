package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.FilterOperator;
import com.caidaocloud.hrpaas.metadata.sdk.filter.MultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.jarvis.cache.annotation.Cache;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class SpecifiedOrgCodePrefixScopeRestrictionHandler implements ScopeRestrictionHandler {

    @Override
    @Cache(key = "'specified_org_code_prefix_scope_restriction_' + #args[0] + '_' + #args[1]",expire = 5 *60)
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        Boolean isFilter = AuthScopeFilterUtil.get();
        val tlUser = SecurityUserUtil.getThreadLocalSecurityUserInfo();
        try{
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            AuthScopeFilterUtil.put(false);
            if(StringUtils.isEmpty(simpleValues)){
                return Lists.list();
            }
            val codePrefix = Lists.list(simpleValues.split(";"));
            DataFilter filter;
            if(codePrefix.size() > 1){
                filter = new MultiDataFilter(FilterOperator.OR, codePrefix.stream().map(it->DataFilter.regex("fullName", it))
                        .collect(Collectors.toList()).toArray(new DataFilter[codePrefix.size()]));
            }else{
                filter = DataFilter.regex("fullName", codePrefix.get(0));
            }
            val orgs = DataQuery.identifier("entity.hr.Org").limit(-1,1)
                    .filterProperties(filter,
                            Lists.list("fullName", "bid"), System.currentTimeMillis()).getItems()
                    .stream().collect(Collectors.toMap(it->it.get("bid"), it->it.get("fullName")));
            List<String> orgIds = Lists.list();
            orgs.forEach((bid, fullName)->{
                val startedWith = codePrefix.stream().anyMatch(it->fullName.toLowerCase().startsWith(it.toLowerCase()));
                if(startedWith){
                    orgIds.add(bid);
                }
            });
            return orgIds;
        }finally {
            if(null == tlUser){
                SecurityUserUtil.removeSecurityUserInfo();
            }else{
                SecurityUserUtil.setSecurityUserInfo(tlUser);
            }
            AuthScopeFilterUtil.put(isFilter);
        }

    }

}
