package com.caidaocloud.hrpaas.service.domain.metadata.entity;

import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.ImportTemplateRepository;
import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import com.caidaocloud.hrpaas.service.interfaces.dto.template.ImportTemplateDto;
import lombok.Data;

import java.util.List;

@Data
public class ImportTemplateDo extends BaseEntity<IBaseRepository> {

    private String tenantId;

    /**
     * 模版code
     */
    private String templateCode;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * 模版名路径
     */
    private String path;

    /**
     * 菜单所属模块
     * hrpaas 模块，attendance 考勤模块，payroll 薪资模块
     */
    private String model;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * UT 更新时间
     */
    private Long updateTime;

    /**
     * UT 更新人
     */
    private String updateBy;

    /**
     * 是否删除。0 默认值，未删除，1 已删除
     */
    private Integer deleted;

    public void save(){
        if(null == createTime){
            createTime = System.currentTimeMillis();
        }
        this.insert();
    }

    @Override
    public void update() {
        repository(ImportTemplateDo.class).update(this);
    }

    public static List<ImportTemplateDo> getList(int pageNo, int pageSize){
        return ((ImportTemplateRepository) repository(ImportTemplateDo.class)).getList(new ImportTemplateDto(),pageNo, pageSize);
    }
}
