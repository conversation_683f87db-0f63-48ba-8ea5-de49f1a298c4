package com.caidaocloud.hrpaas.service.application.uiform.service.render;

import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceType;
import com.caidaocloud.util.SpringUtil;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DataSourceRenderBuilder {
    /**
     * DataSourceRender 缓存池
     */
    private static Map<DataSourceType, DataSourceRender> renderPool = new ConcurrentHashMap<>();

    static {
        renderPool.put(DataSourceType.SYSTEM_DICT, SpringUtil.getBean(SysDictDataSourceRender.class));
        renderPool.put(DataSourceType.EXTERNAL_SYSTEM, SpringUtil.getBean(GeneralExtDataSourceRender.class));
    }

    public static DataSourceRender getDataSourceRender(DataSourceType dataSourceType){
        DataSourceRender fieldValueAssemble = renderPool.getOrDefault(dataSourceType, renderPool.get(DataSourceType.SYSTEM_DICT));
        return fieldValueAssemble;
    }
}
