package com.caidaocloud.hrpaas.service.test;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.hrpaas.service.application.uiform.service.hook.DefaultCallbackHookService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class DefaultCallbackHookServiceTest {
    @Resource
    private DefaultCallbackHookService defaultCallbackHookService;
    @Test
    public void testDefaultSelectConvert(){
        Map sourceMap = new HashMap();
        sourceMap.put("empid", 1008);
        sourceMap.put("name", "一号人员");
        sourceMap.put("workno", "A8888");
        sourceMap.put("sex", 30);
        sourceMap.put("dept", "技术部");

        Map convertMap = new HashMap();
        convertMap.put("a", "name$workno$dept$sex");
        convertMap.put("b", "empid");

        Map targetMap = defaultCallbackHookService.defaultSelectConvert(sourceMap, convertMap);
        log.info("targetMap={}", JSON.toJSONString(targetMap));

        Map newMap = defaultCallbackHookService.putTxtValSelectConvert(sourceMap, convertMap);
        log.info("newMap={}", JSON.toJSONString(newMap));

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/getSystemParams");
        mongoMap.put("requestMethod", "POST");

        params.put("typeCode", "EmployStatus");
        params.put("sysConstant", true);

        mongoMap.put("requestParams", params);

        String mongoJson = JSON.toJSONString(mongoMap);
        log.info("mongoJson={}", mongoJson);
        String configJson = "{\"requestMethod\":\"POST\",\"requestParams\":{\"sysConstant\":true,\"typeCode\":\"EmployStatus\"},\"url\":\"api/bcc/dict/getSystemParams\"}";
        Map jsonMap = JSON.parseObject(configJson, Map.class);
        log.info("jsonMap={}", JSON.toJSONString(jsonMap));
    }
}
