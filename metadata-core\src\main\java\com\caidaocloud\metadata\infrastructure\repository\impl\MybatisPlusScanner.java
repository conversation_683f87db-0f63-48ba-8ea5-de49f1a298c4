package com.caidaocloud.metadata.infrastructure.repository.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.RestUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.List;
import java.util.Optional;

@Configuration
@EnableTransactionManagement
public class MybatisPlusScanner {

    @NacosValue("${anonymous.tenant:}")
    private String anonymousTenant;

    @NacosValue("${dbType:mysql}")
    private String dbType;

    @Value("${tenant.splitTable:}")
    private String splitTable = "";

    private List<String> tenantConcatTables = Sequences.sequence("paas_entity_def", "paas_entity_relation_def"
            , "paas_metadata_tenant_schema", "paas_data_modification", "paas_event_def", "paas_tenant_page",
            "paas_tenant_page_detail", "paas_form_def", "paas_condition_def", "paas_property_event",
            "paas_display_property", "dynamic_column_config", "user_dynamic_column_config", "pre_mapping_config")
            .join(Sequences.sequence(splitTable.split(","))
                    .filter(it -> StringUtils.isNotEmpty(it))).toList();

    private String getDbName(String prefix) {
        if(!tenantConcatTables.contains(prefix)){
            return prefix;
        }
        String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        if(StringUtil.isEmpty(tenantId)){
            if(ThreadLocalUtil.isAnonymousAllowed()){
                tenantId = anonymousTenant;
            }
        }
        return prefix + "_" + tenantId;
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        dynamicTableNameInnerInterceptor.setTableNameHandler(new TenantTableNameHandler());
        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.getDbType(dbType)));
        return interceptor;
    }

    public class TenantTableNameHandler implements TableNameHandler {
        @Override
        public String dynamicTableName(String sql, String tableName) {
            return getDbName(tableName);
        }
    }
}
