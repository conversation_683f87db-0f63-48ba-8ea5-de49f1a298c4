package com.caidaocloud.hrpaas.service.infrastructure.transaction;

import java.util.concurrent.ArrayBlockingQueue;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.metadata.interfaces.facade.EntityDataApiController;
import com.caidaocloud.metadata.interfaces.facade.TransactionController;
import com.caidaocloud.metadata.infrastructure.repository.transaction.PaasTransactionManager;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import static com.caidaocloud.metadata.infrastructure.repository.transaction.TransactionInterceptorAdapter.transactionIdHolder;

/**
 *
 * <AUTHOR> Zhou
 * @date 2022/10/12
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class TransactionManagerTests {
	ArrayBlockingQueue queue = new ArrayBlockingQueue(10, true);
	volatile Integer status = 1;
	@Autowired
	private EntityDataApiController entityDataApiController;
	@Autowired
	private TransactionController transactionController;
	@Autowired
	private PaasTransactionManager paasTransactionManager;

	@Test
	public void threadTest() {
		for (int i = 0; i < 5; i++) {
			queue.add(new Object());
		}
		Thread thread = new Thread(() -> {
			Object obj;

			while ((obj = getTask()) != null) {
				log.info("get task");
			}
			log.info("thread end");
		});
		thread.run();

		try {
			Thread.sleep(5000);
			log.info("stop queue");
			status = 0;
			thread.interrupt();
		}
		catch (Exception e) {

		}
	}

	Object getTask() {
		while (status == 1) {
			try {
				Object object = queue.take();
				return object;
			}
			catch (InterruptedException e) {
				log.info("Thread interrupted");
			}
		}
		return null;

	}

	String txId;


	@Before
	public void bf() {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setUserId(0L);
		userInfo.setTenantId("33");
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);

		String transactionId = transactionController.begin().getData().getTransactionId();
		transactionIdHolder.set(transactionId);
		txId = transactionId;
	}

	public EntityDataDto insertData(){
		EntityDataDto dataDto = FastjsonUtil.toObject("{\"bid\":\"****************\",\"createTime\":0,\"dataEndTime\":0,\"dataStartTime\":0,\"deleted\":false,\"identifier\":\"entity.hr.EmpBank\",\"properties\":[{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"String\",\"multilingual\":{},\"property\":\"empId\",\"value\":\"123456\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"String\",\"multilingual\":{},\"property\":\"bank\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"String\",\"multilingual\":{},\"property\":\"subbranch\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"String\",\"multilingual\":{},\"property\":\"name\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"String\",\"multilingual\":{},\"property\":\"cardNumber\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"String\",\"multilingual\":{},\"property\":\"id\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"String\",\"multilingual\":{},\"property\":\"identifier\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"String\",\"multilingual\":{},\"property\":\"bid\",\"value\":\"****************\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"String\",\"multilingual\":{},\"property\":\"tenantId\",\"value\":\"33\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"Timestamp\",\"multilingual\":{},\"property\":\"createTime\",\"value\":\"0\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"String\",\"multilingual\":{},\"property\":\"createBy\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"Timestamp\",\"multilingual\":{},\"property\":\"updateTime\",\"value\":\"0\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"String\",\"multilingual\":{},\"property\":\"updateBy\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"Timestamp\",\"multilingual\":{},\"property\":\"dataStartTime\",\"value\":\"0\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"Timestamp\",\"multilingual\":{},\"property\":\"dataEndTime\",\"value\":\"0\"},{\"arrayMultilingual\":{},\"arrayValues\":[],\"dataType\":\"Boolean\",\"multilingual\":{},\"property\":\"deleted\",\"value\":\"false\"}],\"relations\":[],\"tenantId\":\"33\",\"updateTime\":0}", EntityDataDto.class);
		dataDto.setBid(SnowUtil.nextId());
		return dataDto;
	}

	public DataQueryDto queryTemplate(){
		DataQueryDto queryDto = FastjsonUtil.toObject("{\"bid\":\"****************\",\"decrypt\":false,\"group\":false,\"identifier\":\"entity.hr.EmpBank\",\"languages\":[],\"pageNo\":1,\"pageSize\":20,\"queryInvisible\":false,\"relatedProperties\":[],\"showDept\":false,\"specifyProperties\":[],\"type\":\"ONE\"}", DataQueryDto.class);
		return queryDto;
	}

	@Test
	public void insertCommitTest(){
		String bid = entityDataApiController.insert(insertData()).getData();
		transactionIdHolder.remove();
		DataQueryDto dto = queryTemplate();
		dto.setBid(bid);
		var beforeCommit = entityDataApiController.oneOrNull(dto, System.currentTimeMillis());
		transactionController.commit(txId);
		var afterCommit = entityDataApiController.oneOrNull(dto, System.currentTimeMillis());
		Assert.isTrue(beforeCommit == null && afterCommit != null, "do insert commit failed");
		log.info("insert commit succeed");
	}


	@Test
	public void insertRollbackTest(){
		String bid = entityDataApiController.insert(insertData()).getData();
		DataQueryDto dto = queryTemplate();
		dto.setBid(bid);
		var beforeRollback = entityDataApiController.oneOrNull(dto, System.currentTimeMillis());
		transactionController.rollback(txId);
		transactionIdHolder.remove();
		var afterRollback = entityDataApiController.oneOrNull(dto, System.currentTimeMillis());
		Assert.isTrue(beforeRollback != null && afterRollback == null, "do insert rollback failed");
		log.info("insert rollback succeed");
	}
}