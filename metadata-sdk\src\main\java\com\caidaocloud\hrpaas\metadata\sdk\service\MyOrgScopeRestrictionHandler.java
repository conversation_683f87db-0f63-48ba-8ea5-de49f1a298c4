package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.hrpaas.metadata.sdk.feign.IMdDataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.jarvis.cache.annotation.Cache;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MyOrgScopeRestrictionHandler implements ScopeRestrictionHandler {

    @Autowired
    private IMdDataFeign mdDataFeign;

    @Override
    @Cache(key = "'8_scope_restriction_' + #args[0] + '_' + #args[1]",expire = 5 *60)
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        Boolean isFilter = AuthScopeFilterUtil.get();
        val tlUser = SecurityUserUtil.getThreadLocalSecurityUserInfo();
        try{
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            val empId = SecurityUserUtil.getSecurityUserInfo().getEmpId();
            if(empId == null){
                return Lists.list();
            }
            AuthScopeFilterUtil.put(false);
            val orgs = DataQuery.identifier("entity.hr.EmpWorkInfo")
                    .filterProperties(DataFilter.eq("empId", empId.toString()),
                            Lists.list("organize"), System.currentTimeMillis()).getItems();
            if(orgs.isEmpty()){
                return Lists.list();
            }
            String organize = orgs.get(0).get("organize");
            if(StringUtils.isNotEmpty(simpleValues) && !"0".equals(simpleValues)){
                organize = mdDataFeign.upAdministrationOrgFromTop(organize, Integer.valueOf(simpleValues)).getData();
                if(StringUtils.isEmpty(organize)){
                    return Lists.list();
                }
            }
            return Lists.list(organize);
        }finally {
            if(null == tlUser){
                SecurityUserUtil.removeSecurityUserInfo();
            }else{
                SecurityUserUtil.setSecurityUserInfo(tlUser);
            }
            AuthScopeFilterUtil.put(isFilter);
        }

    }

}
