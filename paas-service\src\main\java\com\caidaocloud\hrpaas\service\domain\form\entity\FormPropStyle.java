package com.caidaocloud.hrpaas.service.domain.form.entity;

import com.googlecode.totallylazy.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FormPropStyle {

    private String placeholder;

    private Map<String, String> i18nPlaceholder = Maps.map();

    private String format;

    private boolean multiple;

    private boolean showOverflowTooltip;

    private boolean hidden;

    private String value;
    private String fontSize;
    private int fontWeight;
    private String fontColor;
    private String width;
    private String height;
    private String align;
    private String justify;
    private String margin;
    private String padding;
    private String content;
    private Map componentDetail;
    private Map editorSetting;
    private List<Map> headerToolbar;
    private List<Map> buttons;

    private String body;
    private String display;
    private String href;

    private boolean showWorkNo;

    private boolean filterable;

    private int decimalLength;

    private String status;

}
