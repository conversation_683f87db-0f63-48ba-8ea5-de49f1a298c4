package com.caidaocloud.hrpaas.service.domain.metadata.entity;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR>
 * @date 2023/5/29
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class MetadataTenantDoTests {

	@Test
	public void save() {
		MetadataTenantDo t0001 = bulid();
		t0001.save();
		List<MetadataTenantDo> doList = MetadataTenantDo.getListByPage(t0001.getTenantId(), 1, 10);
		Assert.assertEquals(t0001.getAppTenantLogo(), doList.get(0).getAppTenantLogo());
		System.out.println(doList.get(0).getAppTenantLogo());
	}

	public MetadataTenantDo bulid() {
		MetadataTenantDo t0001 = MetadataTenantDo.bulid("T0001");
		t0001.setPcTenantLogo(FastjsonUtil.toJson(extracted()));
		t0001.setAppTenantLogo(FastjsonUtil.toJson(extracted()));
		t0001.setThirdPart("test");
		t0001.setManageSystem(1);
		return t0001;
	}

	private Attachment extracted() {
		Attachment attachment = new Attachment();
		attachment.setNames(Lists.newArrayList());
		attachment.setUrls(Lists.newArrayList());
		return attachment;
	}
}