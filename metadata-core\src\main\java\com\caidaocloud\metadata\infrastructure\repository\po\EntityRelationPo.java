package com.caidaocloud.metadata.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EntityRelationPo {

    @TableId
    private Long id;

    private String identifier;

    private String property;

    private String sourceId;

    private String targetId;

    private long createTime;

    private String createBy;

    private long dataStartTime;

    private long dataEndTime;
}
