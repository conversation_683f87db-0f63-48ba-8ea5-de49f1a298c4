package com.caidaocloud.hrpaas.service.interfaces.facade.metadata;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.service.application.metadata.dto.MetadataSchemaInfoDto;
import com.caidaocloud.hrpaas.service.application.metadata.service.MetadataSchemaService;
import com.caidaocloud.hrpaas.service.application.metadata.service.MetadataSchemaUpgradeService;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataSchemaDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SchemaDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataUpgradeDto;
import com.caidaocloud.hrpaas.service.interfaces.vo.uiform.MetadataSchemaVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/hrpaas/metadata/migration/v1")
@Api(value = "元数据migration", tags = "v1.4", description = "元数据migration接口")
public class MetadataMigrationController {

    @Autowired
    private MetadataSchemaService metadataSchemaService;
    @Autowired
    private MetadataSchemaUpgradeService metadataSchemaUpgradeService;

    @PostMapping("/version")
    @ApiOperation("同步scrip版本")
    public void version(@RequestBody List<MetadataSchemaDto> schemaList){
        if(null == schemaList || schemaList.isEmpty()){
            return;
        }

        metadataSchemaService.syncSchemaVersion(schemaList);
    }

    @PostMapping("/upgrade")
    @ApiOperation("同步Script")
    public Result upgrade(@RequestBody List<SchemaDto> schemaList){
        metadataSchemaUpgradeService.upgrade(schemaList);
        return Result.success();
    }

    @PostMapping("/simpleUpgrade")
    @ApiOperation("同步Script")
    public boolean simpleUpgrade(@RequestBody List<MetadataSchemaInfoDto> scriptList){
        if(null == scriptList || scriptList.isEmpty()){
            return true;
        }

        metadataSchemaService.upgrade(scriptList);
        return true;
    }

    @PostMapping("/upload")
    @ApiOperation(value = "上传脚本", tags = "v1.4")
    public MetadataSchemaVo upload(@RequestPart MultipartFile file, String appName) {
        String fileName = file.getOriginalFilename();
        boolean fileVail = StringUtil.isBlank(fileName);
        PreCheck.preCheckArgument(fileVail, "Script file name cannot be empty.");

        fileVail = StringUtil.isBlank(appName);
        PreCheck.preCheckArgument(fileVail, "Script appName cannot be empty.");

        fileVail = fileName.endsWith(".json") && fileName.indexOf("_") > -1;
        PreCheck.preCheckArgument(!fileVail, "Script file Incorrect format.");

        String fileSchmea = getSchmeaJsonContext(file);
        fileVail = StringUtil.isBlank(fileSchmea);

        PreCheck.preCheckArgument(fileVail, "Script file cannot be empty.");
        MetadataSchemaDto MetadataSchemaDto = metadataSchemaService.upload(fileName, fileSchmea, appName);
        MetadataSchemaDto.setContent(null);
        return ObjectConverter.convert(MetadataSchemaDto, MetadataSchemaVo.class);
    }

    /**
     * 从 multipartFile 中获取内容
     * @param multipartFile
     * @return
     */
    public String getSchmeaJsonContext (MultipartFile multipartFile){
        StringBuilder stringBuilder = new StringBuilder();
        try (InputStreamReader isr = new InputStreamReader(multipartFile.getInputStream());
             BufferedReader bufferedReader = new BufferedReader(isr)){
            String lineTxt;
            while ((lineTxt = bufferedReader.readLine()) != null){
                stringBuilder.append(lineTxt);
            }
        } catch (Exception e){
            log.error("getSchmeaJsonContext err,{}", e.getMessage(), e);
        }

        return stringBuilder.toString();
    }

    @PostMapping("/modelUpgrade")
    @ApiOperation(value = "单租户指定模型升级", tags = "v1.4")
    public void modelUpgrade(@RequestBody MetadataUpgradeDto upgradeDto) {
        String [] scriptIds = upgradeDto.getScriptIds();
        boolean idVaild = null == scriptIds || scriptIds.length < 1;
        PreCheck.preCheckArgument(idVaild, "The upgraded model does not exist or has been deleted.");

        metadataSchemaService.modelUpgrade(upgradeDto);
    }

    @PostMapping("/lastUpgrade")
    @ApiOperation(value = "单租户最后版本升级", tags = "v1.4")
    public void lastUpgrade(@RequestBody MetadataUpgradeDto upgradeDto) {
        metadataSchemaService.lastUpgrade(upgradeDto);
    }

    @PostMapping("/modelSimpleUpgrade")
    @ApiOperation(value = "所有租户指定版本升级", tags = "v1.4")
    public void modelSimpleUpgrade(@RequestBody String [] scriptIds) {
        metadataSchemaService.modelSimpleUpgrade(scriptIds);
    }

    @PostMapping("/modelLastUpgrade")
    @ApiOperation(value = "所有租户从最后一次版本升级", tags = "v1.4")
    public void modelLastUpgrade() {
        metadataSchemaService.modelLastUpgrade();
    }
}
