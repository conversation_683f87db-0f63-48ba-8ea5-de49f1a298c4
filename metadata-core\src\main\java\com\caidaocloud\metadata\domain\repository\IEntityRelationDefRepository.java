package com.caidaocloud.metadata.domain.repository;

import com.caidaocloud.metadata.domain.entity.EntityRelationDef;
import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import com.googlecode.totallylazy.Sequence;

public interface IEntityRelationDefRepository extends IBaseRepository<EntityRelationDef> {

    Sequence<EntityRelationDef> loadByIdentifier(String identifier);

    void delete(String identifier, String property);

    long checkEntityDefRelatedCount(String identifier);
}
