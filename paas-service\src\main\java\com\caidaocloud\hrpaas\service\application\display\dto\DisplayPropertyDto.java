package com.caidaocloud.hrpaas.service.application.display.dto;

import com.caidaocloud.hrpaas.paas.match.vo.display.DisplayProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DisplayPropertyDto {
//    @ApiModelProperty("主键id")
//    private Long id;
//    private String code;
    @ApiModelProperty("显示字段")
    private List<DisplayProperty> properties;

    @ApiModelProperty("备注")
    private String remark;
}
