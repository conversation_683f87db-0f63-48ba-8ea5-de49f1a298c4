package com.caidaocloud.metadata.domain.entity

import com.caidaocloud.excption.ServerException
import com.caidaocloud.hrpaas.metadata.sdk.enums.EventDefStatus
import com.caidaocloud.metadata.domain.enums.DefChannel
import com.caidaocloud.metadata.domain.repository.IEntityEventDefRepository
import com.caidaocloud.security.service.ISessionService
import com.caidaocloud.util.SpringUtil

data class EntityEventDef(
    val identifier : String,
    val name : String,
    val modelRef : String,
    val builtIn : Boolean,
    val trackAll: Boolean,
    val standardTrackedProperties : List<TrackedPropertiesGroup>,
    val customTrackedProperties : List<TrackedPropertiesGroup>,
    val standardNotifyProperties : List<String>,
    val customNotifyProperties : List<String>,
    var consumerList : List<String> = listOf(),
    var status : EventDefStatus,
    var trackRelation : Boolean = false,
    val createTime: Long,
    val createBy: String?,
    val updateTime: Long,
    val updateBy: String?
) : BaseEntity<IEntityEventDefRepository>(){

    companion object{

        val repository by lazy{
            repository(EntityEventDef::class.java) as IEntityEventDefRepository
        }

        fun loadByModelRef(modelRef : String) : List<EntityEventDef>{
            return repository.loadByModelRef(modelRef)
        }

        fun loadById(id: String) : EntityEventDef?{
            return repository.load(id).orNull
        }

        fun open(id: String){
            loadById(id)?.apply {
                if(this.status == EventDefStatus.INACTIVE){
                    throw ServerException("事件订阅者列表为空，不允许手动修改状态")
                }
                this.status = EventDefStatus.OPEN
                this.update()
            }?:throw ServerException("事件不存在")
        }

        fun close(id: String){
            loadById(id)?.apply {
                if(this.status == EventDefStatus.INACTIVE){
                    throw ServerException("事件订阅者列表为空，不允许手动修改状态")
                }
                this.status = EventDefStatus.CLOSED
                this.update()
            }?:throw ServerException("事件不存在")
        }



        fun initNew(identifier : String, modelRef :String, defChannel: DefChannel, name:String,
                    trackAll: Boolean,
                    trackedProperties : List<TrackedPropertiesGroup>,
                    notifyProperties : List<String>) : EntityEventDef {
            return init(defChannel,name,trackAll, trackedProperties, notifyProperties,
                identifier = identifier, modelRef = modelRef)
        }

        fun initExist(id: String, defChannel: DefChannel, name:String,
                      trackAll: Boolean,
                      trackedProperties : List<TrackedPropertiesGroup>,
                      notifyProperties : List<String>
                      ) : EntityEventDef {
            return init(defChannel,name,trackAll, trackedProperties, notifyProperties, id)
        }

        private fun init(defChannel: DefChannel, name:String, trackAll: Boolean,
                         trackedProperties : List<TrackedPropertiesGroup>, notifyProperties : List<String>,
                         id: String? = null, identifier : String?=null, modelRef :String?=null) : EntityEventDef {
            val userId = if(defChannel != DefChannel.SCRIPT){
                SpringUtil.getBean(ISessionService::class.java).userInfo?.userId?.toString()
            }else{
                null
            }
            val now = System.currentTimeMillis()
            val script = defChannel == DefChannel.SCRIPT
            return if(id is String){
                loadById(id)?.let {
                    if(modelRef is String && it.modelRef != modelRef){
                        throw ServerException("事件相关模型不允许修改")
                    }
                    if(identifier is String && it.identifier != identifier){
                        throw ServerException("事件identifier不允许修改")
                    }
                    EntityEventDef(name = name, modelRef = it.modelRef, identifier = it.identifier, builtIn = it.builtIn,
                        trackAll = trackAll,
                        standardTrackedProperties = if(script) trackedProperties else it.standardTrackedProperties,
                        customTrackedProperties = if(script) it.customTrackedProperties else trackedProperties,
                        standardNotifyProperties = if(script) notifyProperties else it.standardNotifyProperties,
                        customNotifyProperties = if(script) it.customNotifyProperties else notifyProperties,
                        status = it.status, consumerList = it.consumerList,
                        createBy = it.createBy, createTime = it.createTime, updateBy = userId, updateTime = now
                    ).apply {
                        this.trackRelation = listOf(this.standardTrackedProperties, this.customTrackedProperties)
                            .flatten().map {
                                it.properties
                            }.flatten().find {
                                it.contains("\\.")
                            } is String
                        this.id = id
                    }
                }?:throw ServerException("事件不存在")
            }else{
                modelRef?:throw ServerException("新建事件必须指定相关模型")
                identifier?:throw ServerException("新建事件必须指定identifier")
                EntityEventDef(name = name, modelRef = modelRef, builtIn = script, identifier = identifier,
                    trackAll = trackAll,
                    standardTrackedProperties = if(script) trackedProperties else listOf(),
                    customTrackedProperties = if(script) listOf() else trackedProperties,
                    standardNotifyProperties = if(script) notifyProperties else listOf(),
                    customNotifyProperties = if(script) listOf() else notifyProperties,
                    status = EventDefStatus.INACTIVE,
                    createBy = userId, createTime = now, updateBy = userId, updateTime = now
                ).apply {
                    this.trackRelation = listOf(this.standardTrackedProperties, this.customTrackedProperties)
                        .flatten().map {
                            it.properties
                        }.flatten().find {
                            it.contains("\\.")
                        } is String
                }
            }
        }

    }

    fun create() : String{
        return this.insert()
    }

    override fun update(){
        return super.update()
    }

    fun updateConsumerList(consumerList : List<String>){
        this.consumerList = consumerList
        if(this.consumerList.isEmpty()){
            this.status = EventDefStatus.INACTIVE
        }else if(this.status == EventDefStatus.INACTIVE){
            this.status = EventDefStatus.OPEN
        }
        this.update()
    }

}

data class TrackedPropertiesGroup(
    val properties : List<String> = listOf()
)




