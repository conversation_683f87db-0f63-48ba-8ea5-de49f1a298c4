package com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect;

import com.caidaocloud.hrpaas.metadata.sdk.dto.TransactionInfoDto;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.IMdTransactionFeign;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.exception.PaasTransactionException;
import com.caidaocloud.web.Result;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.util.CollectionUtils;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/10/9
 */
@Aspect
@Slf4j
@Order(0)
public class MdTransactionAspect {
    @Autowired
    private IMdTransactionFeign transactionFeign;


    public static final ThreadLocal<MdTransactionHolder> TRANSACTION_HOLDER = new ThreadLocal<>();

    public static final ThreadLocal<Set<MdTransactionAfterCommit>> SYNCRONIZATIONS = new ThreadLocal<>();

    @Pointcut("@annotation(com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.MdTransactional)")
    public void mdTransactionPointCut() {
    }

    /**
     * paas事务拦截
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("mdTransactionPointCut()")
    public Object doTransaction(ProceedingJoinPoint joinPoint) throws Throwable {
        // 当前线程已经拥有事务，直接执行业务逻辑
        MdTransactionHolder txHolder = getTransactionHolder();
        if (txHolder.isTransactionActivating()) {
            if (log.isDebugEnabled()) {
                log.debug("Found existed masterdata transaction,tx id={}", txHolder.getTransactionId());
            }
            return joinPoint.proceed();
        }

        // 开启事务
        doBegin(txHolder);
        try {
            // 执行业务逻辑
            Object retVal = joinPoint.proceed();
            // 事务提交
            doCommit(txHolder);
            return retVal;
        } catch (Exception e) {
            // 事务回滚
            log.error("Rolling back masterdata transaction by exception,tx id={}", txHolder.getTransactionId(), e);
            doRollback(txHolder);
            throw e;
        } finally {
            // 清理事务缓存
            cleanUpTransactionHolder();
        }
    }

    private void cleanUpTransactionHolder() {
        TRANSACTION_HOLDER.remove();
        if (SYNCRONIZATIONS.get() != null) {
            SYNCRONIZATIONS.remove();
        }
    }


    public static MdTransactionHolder getTransactionHolder() {
        return TRANSACTION_HOLDER.get() == null ? new MdTransactionHolder() : TRANSACTION_HOLDER.get();
    }

    public void doBegin(MdTransactionHolder txHolder) {
        if (log.isDebugEnabled()) {
            log.debug("Acquiring masterdata transaction");
        }
        Result<TransactionInfoDto> result;
        try {
            result = transactionFeign.begin();
            if (!result.isSuccess()) {
                throw new PaasTransactionException("Failed to acquire masterdata transaction");
            }
        } catch (Exception e) {
            throw new PaasTransactionException("Could not acquire masterdata transaction", e);
        }
        TransactionInfoDto transactionInfo = result.getData();
        BeanUtils.copyProperties(transactionInfo, txHolder);
        TRANSACTION_HOLDER.set(txHolder);
    }

    private void doCommit(MdTransactionHolder txHolder) {
        if (log.isDebugEnabled()) {
            log.debug("Committing masterdata transaction,tx id={}", txHolder.getTransactionId());
        }
        try {
            Result result = transactionFeign.commit(txHolder.getTransactionId());
            if (!result.isSuccess()) {
                throw new PaasTransactionException("Failed to commit masterdata transaction");
            }
            Set<MdTransactionAfterCommit> transactionSynchronizations = SYNCRONIZATIONS.get();
            if (!CollectionUtils.isEmpty(transactionSynchronizations)) {
                transactionSynchronizations.forEach(e -> {
                    e.handle();
                });
            }
        } catch (Exception e) {
            throw new PaasTransactionException("Could not commit masterdata transaction", e);
        }
    }


    private void doRollback(MdTransactionHolder txHolder) {
        if (log.isDebugEnabled()) {
            log.debug("Rolling back paas transaction,tx id={}", txHolder.getTransactionId());
        }
        try {
            Result result = transactionFeign.rollback(txHolder.getTransactionId());
            if (!result.isSuccess()) {
                throw new PaasTransactionException("Failed to roll back masterdata transaction");
            }
        } catch (Exception e) {
            throw new PaasTransactionException("Could not roll back masterdata transaction", e);
        }
    }

    /**
     * 注册事务提交后置处理
     *
     * @param synchronization
     */
    public static synchronized void registerSynchronization(MdTransactionAfterCommit synchronization) {
        if (synchronization == null) {
            return;
        }
        Set<MdTransactionAfterCommit> transactionSynchronizations = SYNCRONIZATIONS.get();
        if (transactionSynchronizations == null) {
            transactionSynchronizations = Sets.newHashSet();
        }
        transactionSynchronizations.add(synchronization);
        SYNCRONIZATIONS.set(transactionSynchronizations);
    }
}
