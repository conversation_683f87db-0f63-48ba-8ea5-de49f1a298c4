package com.caidaocloud.hrpaas.metadata.sdk.expression.function;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import org.jetbrains.annotations.NotNull;

import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/3/7
 */

@Component
public class Ceil extends AbsRoundFunction {


    @Override
    protected RoundingMode getRoundingMode() {
        return RoundingMode.CEILING;
    }

    public String getName() {
        return "ceil";
    }
}
