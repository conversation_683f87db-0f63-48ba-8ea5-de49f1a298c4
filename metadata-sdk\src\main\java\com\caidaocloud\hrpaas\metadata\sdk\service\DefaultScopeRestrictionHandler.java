package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.security.dto.SecurityUserInfo;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * created by: FoAng
 * create time: 25/6/2024 1:38 下午
 */
@Component
public class DefaultScopeRestrictionHandler implements ScopeRestrictionHandler{

    @Override
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        return Lists.newArrayList(simpleValues);
    }
}
