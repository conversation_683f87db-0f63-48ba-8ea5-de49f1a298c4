package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataTenantDo;
import com.caidaocloud.metadata.infrastructure.configuration.handler.BooleanTypeHandler;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

@Data
@TableName("paas_metadata_tenant")
public class MetadataTenantPo extends BasePo<MetadataTenantPo, MetadataTenantDo> {

    private String tenantId;

    private String code;

    private String name;

    private String thirdPart;

    private Long createTime;

    @TableField(jdbcType = JdbcType.OTHER)
    private String pcTenantLogo;

    @TableField(jdbcType = JdbcType.OTHER)
    private String appTenantLogo;

    private Integer manageSystem;

    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean taxInfo;

}
