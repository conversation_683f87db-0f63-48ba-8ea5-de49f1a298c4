package com.caidaocloud.hrpaas.metadata.sdk.filter;

import com.alibaba.fastjson.JSONArray;
import com.caidaocloud.dto.FilterFunction;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.PersistNameUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.val;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public abstract class DataFilter implements FilterFunction {

    public static DataFilter fromJsonString(String json) {
        val filterMap = FastjsonUtil.toObject(json, Map.class);
        if ("simple".equals(filterMap.get("type"))) {
            if (filterMap.containsKey("identifier")) {
                return FastjsonUtil.toObject(json, SpecifiedSimpleDataFilter.class);
            }
            return FastjsonUtil.toObject(json, SimpleDataFilter.class);
        } else {
            MultiDataFilter result;
            if (filterMap.containsKey("identifier")) {
                result = FastjsonUtil.toObject(json, SpecifiedMultiDataFilter.class);
            }else {
                result = FastjsonUtil.toObject(json, MultiDataFilter.class);
            }
            val filters = ((JSONArray) filterMap.get("filters")).stream().map(filter -> fromJsonString(FastjsonUtil.toJson(filter))).collect(Collectors.toList());
            result.setFilters(filters);
            return result;
        }
    }

    public static List<DataFilter> fromJsonStringArray(String json) {
        val filterMapList = FastjsonUtil.toList(json, Map.class);
        return filterMapList.stream().map(filterMap->{
            if ("simple".equals(filterMap.get("type"))) {
                return FastjsonUtil.convertObject(filterMap, SimpleDataFilter.class);
            } else {
                val result = FastjsonUtil.convertObject(filterMap, MultiDataFilter.class);
                val filters = ((JSONArray) filterMap.get("filters")).stream().map(filter -> fromJsonString(FastjsonUtil.toJson(filter))).collect(Collectors.toList());
                result.setFilters(filters);
                return result;
            }
        }).collect(Collectors.toList());
    }



    public static SimpleDataFilter eq(String property, String value) {
        return new SimpleDataFilter(FilterOperator.EQ, property, value);
    }

    @Override
    public MultiDataFilter andEq(String property, String value) {
        return this.and(DataFilter.eq(property, value));
    }

    public DataFilter andEqIf(String property, String value, FilterConditionPredict predict) {
        if (predict.test()) {
            return this.and(DataFilter.eq(property, value));
        } else {
            return this;
        }
    }

    public MultiDataFilter orEq(String property, String value) {
        return this.or(DataFilter.eq(property, value));
    }

    public static SimpleDataFilter ne(String property, String value) {
        return new SimpleDataFilter(FilterOperator.NE, property, value);
    }

    public DataFilter andNeIf(String property, String value, FilterConditionPredict predict) {
        if (predict.test()) {
            return this.and(DataFilter.ne(property, value));
        } else {
            return this;
        }
    }

    @Override
    public MultiDataFilter andNe(String property, String value) {
        return this.and(DataFilter.ne(property, value));
    }

    public MultiDataFilter orNe(String property, String value) {
        return this.or(DataFilter.ne(property, value));
    }

    public static SimpleDataFilter le(String property, String value) {
        return new SimpleDataFilter(FilterOperator.LE, property, value);
    }

    @Override
    public MultiDataFilter andLe(String property, String value) {
        return this.and(DataFilter.le(property, value));
    }

    public MultiDataFilter orLe(String property, String value) {
        return this.or(DataFilter.le(property, value));
    }

    public static SimpleDataFilter ge(String property, String value) {
        return new SimpleDataFilter(FilterOperator.GE, property, value);
    }

    @Override
    public MultiDataFilter andGe(String property, String value) {
        return this.and(DataFilter.ge(property, value));
    }

    public MultiDataFilter orGe(String property, String value) {
        return this.or(DataFilter.ge(property, value));
    }

    public static SimpleDataFilter lt(String property, String value) {
        return new SimpleDataFilter(FilterOperator.LT, property, value);
    }

    @Override
    public MultiDataFilter andLt(String property, String value) {
        return this.and(DataFilter.lt(property, value));
    }

    public MultiDataFilter orLt(String property, String value) {
        return this.or(DataFilter.lt(property, value));
    }

    public static SimpleDataFilter gt(String property, String value) {
        return new SimpleDataFilter(FilterOperator.GT, property, value);
    }

    @Override
    public MultiDataFilter andGt(String property, String value) {
        return this.and(DataFilter.gt(property, value));
    }

    public MultiDataFilter orGt(String property, String value) {
        return this.or(DataFilter.gt(property, value));
    }


    public static SimpleDataFilter regex(String property, String value) {
        return new SimpleDataFilter(FilterOperator.REGEX, property, value);
    }

    public static SimpleDataFilter notLike(String property, String value){
        return new SimpleDataFilter(FilterOperator.NOT_LIKE, property, value);
    }

    public DataFilter andRegexIf(String property, String value, FilterConditionPredict predict) {
        if (predict.test()) {
            return this.and(DataFilter.regex(property, value));
        } else {
            return this;
        }
    }

    @Override
    public MultiDataFilter andRegex(String property, String value) {
        return this.and(DataFilter.regex(property, value));
    }

    public MultiDataFilter orRegex(String property, String value) {
        return this.or(DataFilter.regex(property, value));
    }

    public static SimpleDataFilter in(String property, List<String> values) {
        if(values.isEmpty()){
            throw new ServerException(property + " in 语句不能为空");
        }
        return new SimpleDataFilter(FilterOperator.IN, property, values);
    }

//    public static SimpleDataFilter inSelect(String property, SubSelect subSelect) {
//        return new SimpleDataFilter(FilterOperator.IN_SELECT, property, FastjsonUtil.toJson(subSelect));
//    }

    @Override
    public MultiDataFilter andIn(String property, List<String> values) {
        return this.and(DataFilter.in(property, values));
    }

    public DataFilter andInIf(String property, List<String> values, FilterConditionPredict predict) {
        if (predict.test()) {
            return this.and(DataFilter.in(property, values));
        } else {
            return this;
        }
    }

    public MultiDataFilter orIn(String property, List<String> values) {
        return this.or(DataFilter.in(property, values));
    }

    public DataFilter orInIf(String property, List<String> values, FilterConditionPredict predict) {
        if (predict.test()) {
            return this.or(DataFilter.in(property, values));
        } else {
            return this;
        }
    }

    public static SimpleDataFilter notIn(String property, List<String> values) {
        return new SimpleDataFilter(FilterOperator.NOT_IN, property, values);
    }

    public MultiDataFilter andNotIn(String property, List<String> values) {
        return this.and(DataFilter.notIn(property, values));
    }

    public DataFilter andNotInIf(String property, List<String> values, FilterConditionPredict predict) {
        if (predict.test()) {
            return this.and(DataFilter.notIn(property, values));
        } else {
            return this;
        }
    }

    public MultiDataFilter orNotIn(String property, List<String> values) {
        return this.or(DataFilter.notIn(property, values));
    }

    public DataFilter orNotInIf(String property, List<String> values, FilterConditionPredict predict) {
        if (predict.test()) {
            return this.or(DataFilter.notIn(property, values));
        } else {
            return this;
        }
    }

    public MultiDataFilter and(DataFilter... filters) {
        val newFilters = Sequences.sequence(filters).append(this).toArray(DataFilter.class);
        return new MultiDataFilter(FilterOperator.AND, newFilters);
    }

    public MultiDataFilter or(DataFilter... filters) {
        val newFilters = Sequences.sequence(filters).append(this).toArray(DataFilter.class);
        return new MultiDataFilter(FilterOperator.OR, newFilters);
    }

    public static SpecifiedMultiDataFilter specifiedAnd(String identifier) {
        return new SpecifiedMultiDataFilter(identifier,FilterOperator.AND);
    }
    public static SpecifiedMultiDataFilter specifiedOr(String identifier) {
        return new SpecifiedMultiDataFilter(identifier,FilterOperator.OR);
    }

    public void replaceToPersistKey(MetadataVo metadata) {
        val persistInfo = PersistNameUtil.generatePropertyPersistInfo(metadata);
        if (this instanceof SimpleDataFilter) {
            ((SimpleDataFilter) this).setProperty(
                    PersistNameUtil.getPersist(((SimpleDataFilter) this).getProperty(), persistInfo)
                            .getOrElse(new PersistNameUtil.PropertyPersistInfo(((SimpleDataFilter) this).getProperty(), null))
                            .getPersistName());
        } else {
            ((MultiDataFilter) this).getFilters().forEach(filter -> filter.replaceToPersistKey(metadata));
        }
    }

    @Data
    public static class SubSelect{
        private String identifier;
        private String property;
        private DataFilter filter;
    }

    public static void main(String[] args) {
        // DataFilter.eq("1", String.valueOf(1)).specified("workInfo").and();
        DataFilter.specifiedAnd("1").andEq("a", "a").and(DataFilter.specifiedOr("2"));
    }
}
