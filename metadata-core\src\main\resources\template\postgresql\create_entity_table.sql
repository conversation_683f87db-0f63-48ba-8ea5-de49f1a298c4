create table if not exists $tableName (
    "id" bigserial primary key,
    "identifier" varchar(50) NOT NULL,
    "bid" varchar(50) NOT NULL,
    "tenant_id" varchar(20) NOT NULL,
    "create_time" bigint NOT NULL,
    "update_time" bigint default NULL,
    "create_by" varchar(50) default NULL,
    "update_by" varchar(50) default NULL,
    "data_start_time" bigint default NULL,
    "data_end_time" bigint default NULL,
    ##varcharLoopBy:,:"$column_name" varchar(200) default NULL##,
    ##encryptLoopBy:,:"$column_name" varchar(600) default NULL##,
    ##textLoopBy:,:"$column_name" text default NULL##,
    "deleted" boolean default false
);
create index "$index_name" on $tableName ("bid");
create index "$index_tenant" on $tableName ("tenant_id");
create index "$index_deleted" on $tableName ("deleted");
create index "$index_start_time" on $tableName ("data_start_time");
create index "$index_end_time" on $tableName ("data_end_time");
COMMENT on table $tableName is '$table_desc';