package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMdDataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.MdQueryTL;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IEntityDataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.hrpaas.metadata.sdk.util.ResultUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
public class DataOperatorServiceFeignImpl implements DataOperatorService {

    @Resource
    private IEntityDataFeign entityDataFeign;

    @Resource
    private IMdDataFeign mdDataFeign;

    @Value("${paas.entity.truncate.allow:}")
    private String entityTruncateAllow;

    @Override
    public String insert(EntityDataDto data) {
        val result = MdQueryTL.get()?mdDataFeign.insert(data):entityDataFeign.insert(data);
        if (result.isSuccess()) {
            Object rData = result.getData();
            return null != rData ? rData.toString() : null;
        }

        ResultUtil.checkMetadataResult(result);
        return null;
    }

    @Override
    public String insertWithRelation(DataWithRelationDTO data) {
        Result result = MdQueryTL.get()?mdDataFeign.insertWithRelation(data):entityDataFeign.insertWithRelation(data);
        if (result.isSuccess()) {
            Object rData = result.getData();
            return null != rData ? rData.toString() : null;
        }
        ResultUtil.checkMetadataResult(result);
        return null;
    }

    @Override
    public void update(EntityDataDto data) {
        val result = MdQueryTL.get()?mdDataFeign.update(data):entityDataFeign.update(data);

        ResultUtil.checkMetadataResult(result);
    }

    @Override
    public void updateWithRelation(DataWithRelationDTO data) {
        val result = MdQueryTL.get()?mdDataFeign.updateWithRelation(data):entityDataFeign.updateWithRelation(data);
        ResultUtil.checkMetadataResult(result);
    }

    @Override
    public EntityDataDto one(DataQueryDto query, long queryTime) {
        val result = MdQueryTL.get()?mdDataFeign.one(query, queryTime):entityDataFeign.one(query, queryTime);
        if (result.isSuccess()) {
            return JsonEnhanceUtil.toObject(result.getData(), EntityDataDto.class);
        }

        ResultUtil.checkMetadataResult(result);
        return null;
    }

    @Override
    public PageResult<EntityDataDto> filter(DataQueryDto query, long queryTime) {
        val result = MdQueryTL.get()?mdDataFeign.filter(query, queryTime):entityDataFeign.filter(query, queryTime);
        if (result.isSuccess()) {
            return JsonEnhanceUtil.toPage(result.getData(), new TypeReference<PageResult<EntityDataDto>>() {
            });
        }

        ResultUtil.checkMetadataResult(result);
        return null;
    }

    public long count(DataQueryDto query, long queryTime){
        val result = MdQueryTL.get()?mdDataFeign.count(query, queryTime):entityDataFeign.count(query, queryTime);
        if (result.isSuccess()) {
            Long count = Long.valueOf(result.getData().toString());
            return count;
        }
        ResultUtil.checkMetadataResult(result);
        return 0;
    }

    @Override
    public void delete(String identifier, String bid, long startTime) {
        val result = MdQueryTL.get()?mdDataFeign.delete(identifier, bid,startTime):entityDataFeign.delete(identifier, bid, startTime);

        ResultUtil.checkMetadataResult(result);
    }

    @Override
    public EntityDataDto oneOrNull(DataQueryDto query, long queryTime) {
        val result = MdQueryTL.get()?mdDataFeign.oneOrNull(query, queryTime):entityDataFeign.oneOrNull(query, queryTime);
        if (result.isSuccess()) {
            return JsonEnhanceUtil.toObject(result.getData(), EntityDataDto.class);
        }

        ResultUtil.checkMetadataResult(result);
        return null;
    }

    @Override
    public void softDelete(String identifier, String bid, long startTime) {
        val result = MdQueryTL.get()?mdDataFeign.softDelete(identifier, bid, startTime):entityDataFeign.softDelete(identifier, bid, startTime);

        ResultUtil.checkMetadataResult(result);
    }

    @Override
    public List<EntityDataDto> range(String identifier, String bid, long startTime, long endTime) {
        val result = MdQueryTL.get()?mdDataFeign.range(identifier, bid, startTime, endTime):entityDataFeign.range(identifier, bid, startTime, endTime);
        if (result.isSuccess()) {
            return JsonEnhanceUtil.toObjects(result.getData(), EntityDataDto.class);
        }
        ResultUtil.checkMetadataResult(result);
        return null;
    }

    @Override
    public void batchUpdate(DataBatchUpdateDto updateData, long queryTime) {
        val result = MdQueryTL.get()?mdDataFeign.batchUpdate(updateData, queryTime):entityDataFeign.batchUpdate(updateData, queryTime);
        ResultUtil.checkMetadataResult(result);
    }

    @Override
    public void batchDelete(String identifier, DataFilter filter){
        val dto = new BatchDeleteDto();
        dto.setIdentifier(identifier);
        dto.setFilter(FastjsonUtil.toJson(filter));
        val result = MdQueryTL.get()?mdDataFeign.batchDeleteWithoutRecord(dto):entityDataFeign.batchDeleteWithoutRecord(dto);
        ResultUtil.checkMetadataResult(result);
    }

    @Override
    public void batchUpdate(String identifier, DataFilter filter, Map<String, String> updateValue){
        val dto = new BatchUpdateDto();
        dto.setIdentifier(identifier);
        dto.setFilter(FastjsonUtil.toJson(filter));
        dto.setUpdateValue(updateValue);
        val result = MdQueryTL.get()?mdDataFeign.batchUpdateWithoutRecord(dto):entityDataFeign.batchUpdateWithoutRecord(dto);
        ResultUtil.checkMetadataResult(result);
    }

    @Override
    public void batchInsert(String identifier, List<EntityDataDto> list) {
        if(MdQueryTL.get()){
            mdDataFeign.batchInsertWithoutRecord(identifier,list);
        }else{
            entityDataFeign.batchInsertWithoutRecord(identifier, list);
        }
    }

    @Override
    public List<EntityDataDto> allJobGrade() {
        val result = MdQueryTL.get()?mdDataFeign.allJobGrade():entityDataFeign.allJobGrade();
        if (result.isSuccess()) {
            return JsonEnhanceUtil.toObjects(result.getData(), EntityDataDto.class);
        }
        ResultUtil.checkMetadataResult(result);
        return null;
    }

    @Override
    public List<EntityDataDto> allJobGradeChannel() {
        val result = MdQueryTL.get()?mdDataFeign.allJobGradeChannel():entityDataFeign.allJobGradeChannel();
        if (result.isSuccess()) {
            return JsonEnhanceUtil.toObjects(result.getData(), EntityDataDto.class);
        }
        ResultUtil.checkMetadataResult(result);
        return null;
    }

    public PageResult<EntityDataDto> max(MaxQueryDto query){
        val result = MdQueryTL.get()?mdDataFeign.max(query):entityDataFeign.max(query);
        if (result.isSuccess()) {
            return JsonEnhanceUtil.toPage(result.getData(), new TypeReference<PageResult<EntityDataDto>>() {
            });
        }
        ResultUtil.checkMetadataResult(result);
        return null;
    }

    @Override
    public List<Map<String, Object>> countByGroup(GroupedCountDto dto) {
        val result = MdQueryTL.get()?mdDataFeign.countByGroup(dto):entityDataFeign.countByGroup(dto);
        if (result.isSuccess()) {
            String json = FastjsonUtil.toJson(result.getData());
            return FastjsonUtil.toObject(json, new TypeReference<List<Map<String, Object>>>(){});
        }
        ResultUtil.checkMetadataResult(result);
        return null;
    }

    @Override
    public List<Map<String, Object>> indicateByGroup(GroupedIndicateDto dto) {
        val result = MdQueryTL.get()?mdDataFeign.indicateByGroup(dto):entityDataFeign.indicateByGroup(dto);
        if (result.isSuccess()) {
            String json = FastjsonUtil.toJson(result.getData());
            return FastjsonUtil.toObject(json, new TypeReference<List<Map<String, Object>>>(){});
        }
        ResultUtil.checkMetadataResult(result);
        return null;
    }

    @Override
    public PageResult<Triple<EntityDataDto, EntityDataDto, EntityDataDto>> join(DataJoin dataJoin) {
        val result = MdQueryTL.get()?mdDataFeign.join(dataJoin):entityDataFeign.join(dataJoin);
        if (result.isSuccess()) {
            String json = FastjsonUtil.toJson(result.getData());
            val page = FastjsonUtil.toObject(json, new TypeReference<PageResult<List<EntityDataDto>>>(){});
            List<Triple<EntityDataDto, EntityDataDto, EntityDataDto>> list;
            if(dataJoin.getModelCount() == 3){
                list = Sequences.sequence(page.getItems()).map(it->Triple.of(it.get(0), it.get(1), it.get(2))).toList();
            }else{
                list = Sequences.sequence(page.getItems()).map(it->Triple.of(it.get(0), it.get(1), (EntityDataDto)null)).toList();
            }
            return new PageResult<>(list, page.getPageNo(), page.getPageSize(), page.getTotal());
        }
        ResultUtil.checkMetadataResult(result);
        return null;
    }

    @Override
    public List<EmpSimple> loadByCondition(DataQueryDto query, Long queryTime) {
        // TODO: 2023/7/26 根据匹配条件查询员工id
        val result = MdQueryTL.get() ? mdDataFeign.condition(query, queryTime) : entityDataFeign.condition(query, queryTime);
        return result.getData();
    }

    @Override
    public void truncate(String identifier) {
        if(!Lists.list(entityTruncateAllow.split(",")).contains(identifier)){
            throw new ServerException("entity truncate not allowed");
        }
        val result = MdQueryTL.get()?mdDataFeign.truncate(identifier):entityDataFeign.truncate(identifier);
        if (result.isSuccess()) {
            return;
        }
        ResultUtil.checkMetadataResult(result);
        return;
    }

}
