package com.caidaocloud.hrpaas.service.interfaces.vo.condition;

import java.util.List;

import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionData;
import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionDef;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 * @date 2022/11/2
 */
@ApiModel("匹配条件详情vo")
@Data
public class ConditionDefVo {

	@ApiModelProperty("id")
	private String id;

	@ApiModelProperty("所属模块")
	private String module;

	@ApiModelProperty("匹配条件字段")
	private List<ConditionDataVo> properties;

	/**
	 *
	 * @param detail
	 * @param showDisable 是否显示disable的字段
	 * @return
	 */
	public static ConditionDefVo fromEntity(ConditionDef detail, boolean showDisable) {
		ConditionDefVo vo = ObjectConverter.convert(detail, ConditionDefVo.class);
		Sequence<ConditionData> sequence = Sequences.sequence(detail.getProperties());
		if (!showDisable) {
			sequence = sequence.filter(ConditionData::isEnabled);
		}
		vo.setProperties(sequence.map(data -> ObjectConverter.convert(data, ConditionDataVo.class)).toList());
		return vo;
	}

}
