package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.util.WebUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.var;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 字典组件
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@Data
@DataComponent(dataType = PropertyDataType.Dict)
public class DictSimple implements ComponentPropertyValue {
    private final static String DICT_KEY = "DICT_%s", CODE_2_DICT = "CODE_2_DICT_%s", CODE_TYPE_2_DICT = "CODE_TYPE_2_DICT_%s_%s";

    private String text;

    private String value;

    private String code;

    public DictSimple() {
    }

    public DictSimple(String dictValue) {
        this.value = dictValue;
    }

    @Override
    public Map<String, Boolean> propertySuffixToWhetherLong() {
        return Maps.map(
                Sequences.sequence(
                        Pair.pair(".dict.value", false),
                        // Pair.pair(".dict.text", false),
                        Pair.pair(".dict.code", false)
                )
        );
    }

    /**
     * 存储属性转换成对象
     *
     * @param propDef
     * @param properties
     * @return
     */
    @Override
    public PropertyValue extractDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties) {
        return DictSimple.toDisplay(propDef.getProperty(), properties);
    }

    public static DictSimple toDisplay(String fieldProp, List<PropertyDataDto> propList) {
        if (null == propList || propList.isEmpty() || StringUtil.isEmpty(fieldProp)) {
            return null;
        }

        DictSimple dictSimple = new DictSimple();
        for (PropertyDataDto prop : propList) {
            if (null == prop || !prop.getProperty().startsWith(fieldProp + ".")) {
                continue;
            }

            if (prop.getProperty().endsWith("dict.value")) {
                dictSimple.setValue(prop.getValue());
            } else if (prop.getProperty().endsWith("dict.code")) {
                dictSimple.setCode(prop.getValue());
            }
        }

        doDictSimple(dictSimple);
        return dictSimple;
    }

    @Override
    public List<PropertyDataDto> extractPersist(DataSimple data, MetadataPropertyVo propDef) {
        return DictSimple.toPersist(data, propDef);
    }

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {
        dataValue.fitDataValue(((DictSimple) dataVal).getValue());
        DataValueFunction dataValueDto = dataValue.dataValueBulid();
        dataValueDto.fitDataProp(String.format(UIFORM_RELATION_FORMAT_TXT, dataValue.loadDataProp()));
        dataValueDto.fitDataValue(((DictSimple) dataVal).toText());
        dataVals.add(dataValueDto);
    }

    @Override
    public String toText() {
        return text;
    }

    public static List<PropertyDataDto> toPersist(DataSimple data, MetadataPropertyVo propDef) {

        Map dataMap = JsonEnhanceUtil.toObject(data, Map.class);
        // 获取属性中的字典字段
        String dictPrefix = propDef.getProperty();
        PropertyValue customPropertyValue = data.getProperties().get(dictPrefix);


        DictSimple dictSimple = null;
        // 字典存在于原生属性中
        if (dataMap.containsKey(dictPrefix)) {
            Object standardPropertyValue = dataMap.get(dictPrefix);
            if (null != standardPropertyValue) {
                dictSimple = JsonEnhanceUtil.toObject(standardPropertyValue, DictSimple.class);
                return DictSimple.toPropList(dictSimple, dictPrefix);
            }

            return DictSimple.toPropList(null, dictPrefix);
        } else if ((dictSimple = handleFlatProperty(data, dictPrefix, StringUtils.defaultString(propDef.getDatasource(), ""))) != null) {
            return DictSimple.toPropList(dictSimple, dictPrefix);
        }

        if (null != customPropertyValue) {
            if (customPropertyValue instanceof DictSimple) {
                dictSimple = ((DictSimple) customPropertyValue);
            } else {
                dictSimple = new DictSimple();
                String dictValue = ((SimplePropertyValue) customPropertyValue).getValue();
                dictSimple.setValue(dictValue);

                doDictSimple(dictSimple);
            }
            return DictSimple.toPropList(dictSimple, dictPrefix);
        }
        return DictSimple.toPropList(null, dictPrefix);
    }

    private static DictSimple handleFlatProperty(DataSimple data, String dictPrefix, String dataSource) {
        var b = data.getProperties().containsKey(String.format("%s.dict.text", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.dict.code", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.dict.value", dictPrefix));
        if (!b) {
            return null;
        }
        var dictSimple = new DictSimple();
        if (data.getProperties().containsKey(String.format("%s.dict.text", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.dict.text", dictPrefix));
            dictSimple.setText(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.dict.code", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.dict.code", dictPrefix));
            dictSimple.setCode(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.dict.value", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.dict.value", dictPrefix));
            dictSimple.setValue(propertyValue.getValue());
        }
        if (StringUtils.isBlank(dictSimple.getValue()) && StringUtils.isNotBlank(dictSimple.getCode())) {
            StringUtils.contains(dataSource, "typeCode=");
            return code2Dict(StringUtils.contains(dataSource, "typeCode=") ? StringUtils.substringAfter(dataSource, "typeCode=") : StringUtils.defaultString(dataSource, ""), dictSimple.getCode());
        }
        return dictSimple;
    }

    private static List<PropertyDataDto> toPropList(DictSimple dictSimple, String dictPrefix) {
        List<PropertyDataDto> result = Lists.newArrayList();
        if (null == dictSimple) {
            dictSimple = new DictSimple();
        } else {
            doDictSimple(dictSimple);
        }

        PropertyDataDto dataDto = new PropertyDataDto();
        dataDto.setProperty(String.format("%s.dict.value", dictPrefix));
        dataDto.setDataType(PropertyDataType.Dict);
        dataDto.setValue(dictSimple.getValue());
        result.add(dataDto);

        doDictSimple(dictSimple);

        dataDto = new PropertyDataDto();
        dataDto.setProperty(String.format("%s.dict.text", dictPrefix));
        dataDto.setDataType(PropertyDataType.Dict);
        dataDto.setValue(dictSimple.getText());
        result.add(dataDto);

        dataDto = new PropertyDataDto();
        dataDto.setProperty(String.format("%s.dict.code", dictPrefix));
        dataDto.setDataType(PropertyDataType.Dict);
        dataDto.setValue(dictSimple.getCode());
        result.add(dataDto);

        return result;
    }

    public static void doDictSimple(DictSimple dictSimple) {
        String dictValue = dictSimple.getValue();
        if (StringUtil.isBlank(dictValue)) {
            return;
        }
        SysParamDictDto sysParamDictDto = QueryInfoCache.getDict(DICT_KEY, dictValue);
        if (null == sysParamDictDto) {
            return;
        }

        dictSimple.setText(getLangTxt(sysParamDictDto));
        dictSimple.setCode(sysParamDictDto.getDictCode());
    }

    public static DictSimple code2Dict(String dictCode) {
        DictSimple dictSimple = new DictSimple();
        dictSimple.setCode(dictCode);
        if (StringUtil.isBlank(dictCode)) {
            return dictSimple;
        }

        SysParamDictDto sysParamDictDto = QueryInfoCache.code2Dict(CODE_2_DICT, dictCode);
        if (null == sysParamDictDto) {
            return dictSimple;
        }

        dictSimple.setText(getLangTxt(sysParamDictDto));
        dictSimple.setValue(null == sysParamDictDto.getDictId() ? null : sysParamDictDto.getDictId().toString());
        return dictSimple;
    }

    public static DictSimple code2Dict(String dictType, String dictCode) {
        DictSimple dictSimple = new DictSimple();
        dictSimple.setCode(dictCode);
        if (StringUtil.isBlank(dictCode)) {
            return dictSimple;
        }

        SysParamDictDto sysParamDictDto = QueryInfoCache.code2Dict(CODE_TYPE_2_DICT, dictType, dictCode);
        if (null == sysParamDictDto) {
            return dictSimple;
        }

        dictSimple.setText(sysParamDictDto.getDictChnName());
        dictSimple.setValue(null == sysParamDictDto.getDictId() ? null : sysParamDictDto.getDictId().toString());
        return dictSimple;
    }

    public static DictSimple doDictSimple(String dictValue) {
        DictSimple dictSimple = new DictSimple(dictValue);
        doDictSimple(dictSimple);
        return dictSimple;
    }

    public static String getLangTxt(SysParamDictDto spd) {
        // 转大写
        String lang = getLang().toUpperCase();
        if ("ZH_CN".equals(lang) || "ZH_TW".equals(lang)) {
            return spd.getDictChnName();
        }

        Map langMap = FastjsonUtil.convertObject(spd.getDictNameLang(), Map.class);
        String dictTxt = null;
        if (null != langMap && !langMap.isEmpty()) {
            if (lang.indexOf("EN") > -1) {
                dictTxt = (String) langMap.get("en");
            } else if (lang.indexOf("JA") > -1) {
                dictTxt = (String) langMap.get("ja");
            } else if (lang.indexOf("KO") > -1) {
                dictTxt = (String) langMap.get("ko");
            } else {
                dictTxt = spd.getDictChnName();
            }
        }

        return StringUtil.isEmpty(dictTxt) ? spd.getDictChnName() : dictTxt;
    }

    public static String getLang() {
        return getLang(null);
    }

    public static String getLang(String lang) {
        if (StringUtil.isNotEmpty(lang)) {
            return lang;
        }

        lang = SecurityUserUtil.getSecurityUserInfo().getLang();
        if (StringUtil.isNotEmpty(lang)) {
            return lang;
        }

        HttpServletRequest request = WebUtil.getRequest();
        if (null == request) {
            return "zh_CN";
        }

        lang = request.getHeader("language");
        if (StringUtil.isNotEmpty(lang)) {
            return lang;
        }

        lang = request.getHeader("accept-language");
        if (StringUtil.isNotEmpty(lang)) {
            return lang;
        }

        return "zh_CN";
    }
}
