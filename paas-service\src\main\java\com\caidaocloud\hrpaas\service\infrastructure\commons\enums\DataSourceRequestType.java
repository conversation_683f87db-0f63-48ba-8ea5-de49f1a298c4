package com.caidaocloud.hrpaas.service.infrastructure.commons.enums;

public enum DataSourceRequestType {
    /**
     * application/json;charset=UTF-8
     */
    JSON("JSON格式参数", 1),
    /**
     * application/json
     */
    APP_JSON("非utf-8JSON格式参数", 1),
    /**
     * application/x-www-form-urlencoded
     * 表单提交时使用url编码
     */
    URLENCODED("表单提交url编码", 2);

    private String name;
    private int value;

    DataSourceRequestType(String name, int value){
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public int getValue() {
        return value;
    }

}
