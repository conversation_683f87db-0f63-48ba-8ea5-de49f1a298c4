package com.caidaocloud.metadata.infrastructure.repository.impl.util;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

@Slf4j
public class TenantMapperUtil {

    public static <T, P extends IPage<T>> P selectTenantPage(BaseMapper<T> mapper, P page, Wrapper<T> queryWrapper){
        if(page.searchCount()){
            long total = 0;
            try {
                total = mapper.selectCount(queryWrapper);
            } catch (Exception exception) {
                log.error("queryError", exception);
                System.out.println(exception);
            }
            ((Page)page).setSearchCount(false);
            val result = mapper.selectPage(page, queryWrapper);
            result.setTotal(total);
            return result;
        }else{
            return mapper.selectPage(page, queryWrapper);
        }
    }
}
