package com.caidaocloud.hrpaas.metadata.sdk.transaction.utils;

import com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionHolder;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign;
import com.caidaocloud.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class PaasTransactionalUtil {
    /**
     * 手动开启事务
     *
     * @param function
     */
    public static boolean manualTransactional(Runnable function) {
        TransactionHolder txHolder = null;
        boolean isException = false;
        try {
            txHolder = TransactionAspect.getTransactionHolder();
            TransactionAspect transactionAspect = SpringUtil.getBean(TransactionAspect.class);
            transactionAspect.doBegin(txHolder);
            function.run();
        } catch (Exception e) {
            isException = true;
            if (txHolder != null && StringUtils.isNotBlank(txHolder.getTransactionId())) {
                try {
                    SpringUtil.getBean(ITransactionFeign.class).rollback((txHolder.getTransactionId()));
                } catch (Exception ex) {
                    log.error("rollback fail", ex);
                }
            }
            log.error("occur error", e);
        } finally {
            if (!isException) {
                try {
                    SpringUtil.getBean(ITransactionFeign.class).commit((txHolder.getTransactionId()));
                } catch (Exception e) {
                    log.error("commit fail", e);
                }
            }
            cleanUpTransactionHolder();
        }
        return isException;
    }

    private static void cleanUpTransactionHolder() {
        TransactionAspect.TRANSACTION_HOLDER.remove();
        if (TransactionAspect.SYNCRONIZATIONS.get() != null) {
            TransactionAspect.SYNCRONIZATIONS.remove();
        }
    }
}