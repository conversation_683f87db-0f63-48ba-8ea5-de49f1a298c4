package com.caidaocloud.metadata.application.event.publish

import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil
import com.caidaocloud.metadata.application.event.EntityEventMsg
import com.caidaocloud.metadata.domain.entity.EntityDataChange
import com.caidaocloud.metadata.domain.entity.EntityEvent
import com.caidaocloud.mq.rabbitmq.MqMessageProducer
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage
import com.caidaocloud.msg.message.AbstractBasicMessage
import com.caidaocloud.msg.producer.MessageProducer
import com.caidaocloud.security.dto.SecurityUserInfo
import com.caidaocloud.security.util.SecurityUserUtil
import com.caidaocloud.util.FastjsonUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import javax.annotation.Resource

private val log = LoggerFactory.getLogger(EntityEventSender::class.java)

@Service
class EntityEventSender {

    @Resource
    private lateinit var producer: MqMessageProducer<RabbitBaseMessage>

    @Value("\${service.name.short:hrpaas}")
    private lateinit var serviceShortName : String

    private val pool = ThreadPoolExecutor(10, 30,
        100, TimeUnit.SECONDS, LinkedBlockingQueue(100),
        ThreadPoolExecutor.CallerRunsPolicy()
    )

    fun publish(event: EntityEvent, consumers : List<String>){
        val user = SecurityUserUtil.getSecurityUserInfo();
        pool.execute {
            consumers.forEach { consumer ->
                try {
                    publish(JsonEnhanceUtil.toObject(event, EntityEventMsg::class.java), consumer, event.eventIdentifier, user)
                } catch (e: Exception) {
                    log.error("发送事件失败，内容：${FastjsonUtil.toJson(event)}, 消费者：${consumer}, identifier:${event.eventIdentifier}", e)
                }
            }

        }
    }

    fun publish(msg: EntityEventMsg, consumer : String, identifier: String, user: SecurityUserInfo) {
        val tenantId = user.tenantId
        val message = RabbitBaseMessage()
        message.body = FastjsonUtil.toJson(
            msg.apply {
                val userInfo = FastjsonUtil.convertObject(user,
                    AbstractBasicMessage.MessageUserInfo::class.java
                )
                this.topic = identifier
                this.userInfo = userInfo
            }
        )
        message.exchange = "$serviceShortName.es.exchange"
        message.routingKey = "routingKey.$serviceShortName.es.$consumer.$tenantId"
        producer.publish(message)
    }

    fun sendEmpLeaderChange(msg: EntityEventMsg, user: SecurityUserInfo) {
        val message = RabbitBaseMessage()
        message.body = FastjsonUtil.toJson(
            msg.apply {
                val userInfo = FastjsonUtil.convertObject(user,
                    AbstractBasicMessage.MessageUserInfo::class.java
                )
                this.topic = "EMP_LEADER_CHANGE"
                this.userInfo = userInfo
            }
        )
        message.exchange = "caidao.hrpaas"
        message.routingKey = "caidao.hrpaas.emp.leader.change"
        producer.publish(message)
    }
}