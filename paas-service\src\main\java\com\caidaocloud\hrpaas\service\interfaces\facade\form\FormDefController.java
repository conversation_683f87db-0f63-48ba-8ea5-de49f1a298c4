package com.caidaocloud.hrpaas.service.interfaces.facade.form;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.service.application.form.dto.FormDefDto;
import com.caidaocloud.hrpaas.service.application.form.service.FormAuthDefService;
import com.caidaocloud.hrpaas.service.application.form.service.FormDefService;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDefStatus;
import com.caidaocloud.hrpaas.service.interfaces.dto.form.FormDefIdDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.form.FormPubDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.form.FormWfApproverDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataTenantDto;
import com.caidaocloud.hrpaas.service.interfaces.vo.form.FormDefVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.form.FormPropDefVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/hrpaas/v1/form/def")
@Api(value = "/api/hrpaas/v1/form/def", description = "表单定义接口", tags = "表单定义接口")
public class FormDefController {
    @Autowired
    private FormDefService formDefService;
    @Resource
    private FormAuthDefService formAuthDefService;

    @ApiOperation("表单配置详情")
    @GetMapping("/one")
    public Result<FormDefVo> one(@RequestParam("id") String id) {
        FormDef formDef = formDefService.one(id);
        return Result.ok(FastjsonUtil.convertObject(formDef, FormDefVo.class));
    }

    @ApiOperation("表单配置详情(名称查询)")
    @GetMapping("/one/:name")
    public Result<FormDefVo> oneByName(@RequestParam("name") String name) {
        FormDef formDef = formDefService.oneByName(name);
        return Result.ok(FastjsonUtil.convertObject(formDef, FormDefVo.class));
    }

    @ApiOperation("表单配置详情(code查询)")
    @GetMapping("/one/:code")
    public Result<FormDefVo> oneByCode(@RequestParam("code") String code, @RequestParam(value = "status", required = false, defaultValue = "PUBLISHED") FormDefStatus status) {
        FormDef formDef = formDefService.oneByCode(code, status);
        return Result.ok(FastjsonUtil.convertObject(formDef, FormDefVo.class));
    }

    @ApiOperation("表单配置列表")
    @GetMapping("/page")
    public Result<PageResult<FormDefVo>> defPage(@RequestParam("pageNo") int pageNo,
                                                 @RequestParam("pageSize") int pageSize,
                                                 @RequestParam(value = "keywords", required = false) String keywords) {

        val page = formDefService.defPage(pageNo, pageSize, keywords);
        val defList = page.getItems();
        val voList = FastjsonUtil.convertList(defList, FormDefVo.class);
        return Result.ok(new PageResult<>(voList, pageNo, pageSize, page.getTotal()));
    }

    @ApiOperation("表单配置列表(无分页)")
    @GetMapping("/list")
    public Result<List<FormDefVo>> defList(@RequestParam(required = false, defaultValue = "false") boolean implantable) {

        val list = formDefService.defList().stream()
                .filter(it -> it.isImplantable() == implantable).collect(Collectors.toList());
        val voList = FastjsonUtil.convertList(list, FormDefVo.class);
        return Result.ok(voList);
    }

    @ApiOperation("新增表单配置")
    @PostMapping
    public Result<Boolean> addDef(@RequestBody FormDefDto formDef) {
        formDefService.addDef(formDef);
        return Result.ok();
    }

    @ApiOperation("修改表单配置")
    @PutMapping
    public Result<Boolean> alterDef(@RequestBody FormDefDto formDef) {
        formDefService.alterDef(formDef);
        return Result.ok();
    }

    @ApiOperation("删除表单配置")
    @DeleteMapping
    public Result<Boolean> deleteDef(@RequestParam String id) {
        formDefService.deleteDef(id);
        return Result.ok();
    }

    @ApiOperation("发布表单配置")
    @PutMapping("/:pub")
    public Result<Boolean> pubDef(@RequestBody FormPubDto pub) {
        formDefService.pubDef(pub.getId(), pub.getWorkflowSeqProperties());
        return Result.ok();
    }

    @ApiOperation("禁用表单配置")
    @PutMapping("/:disable")
    public Result<Boolean> disableDef(@RequestBody FormDefIdDto id) {
        formDefService.disableDef(id.getId());
        return Result.ok();
    }

    @ApiOperation("初始化标准表单")
    @PostMapping("/:standardForm")
    public Result<Boolean> initStandardForm(@RequestBody MetadataTenantDto tenant) {
        formDefService.initStandardForm(tenant.getTenantId());
        return Result.ok();
    }

    @ApiOperation("获取表单中的员工属性")
    @GetMapping("/prop/emp")
    public Result<List<FormPropDefVo>> getEmpProp(@RequestParam("id") String id) {
        return Result.ok(formDefService.loadApproverPropDef(id));
    }

    @ApiOperation("将表单属性更新作为流程审批人")
    @PutMapping("/prop/emp/approver")
    public Result updateEmpPropAsApprover(@RequestBody FormWfApproverDto formWfApproverDto) {
        try {
            formDefService.updateEmpPropAsApprover(formWfApproverDto);
        }
        catch (Exception e) {
            log.error(String.format("updateEmpPropAsApprover occur error, parameter=%s", FastjsonUtil.toJson(formWfApproverDto)), e);
            return Result.fail();
        }
        return Result.ok();
    }

    @PostMapping("/filter")
    public Result addUserFilter(@RequestBody Map config) {
        formDefService.addUserFilter(config);
        return Result.ok();
    }

    @GetMapping("/filter")
    public Result<Map> fetchUserFilter(@RequestParam String key) {
        return Result.ok(formDefService.fetchUserFilter(key));
    }

    @PostMapping("auth")
    public Result initAuth(){
        formAuthDefService.initAuth();
        return Result.ok();
    }

    @PostMapping("/save/mappingId")
    public Result saveMappingId(@RequestBody FormDefDto dto) {
        formDefService.saveMappingId(dto);
        return Result.ok();
    }

    @PostMapping("/addSysProcessCodeColumn")
    public Result addSysProcessCodeColumn() {
        formDefService.addSysProcessCodeColumn();
        return Result.ok();
    }


}
