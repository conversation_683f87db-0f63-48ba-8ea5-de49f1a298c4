package com.caidaocloud.hrpaas.service.application.uiform.service;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.service.application.uiform.service.render.DataSourceRenderBuilder;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.DataSourceDo;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.*;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Option;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-08-20
 */
@Slf4j
@Service
@RefreshScope
public class UiFormDataSourceService {
    @Resource
    private ISessionService sessionService;

    @Transactional
    public UiDataSourceDto putUiDataSource(UiDataSourceDto dataSourceDto){
        DataSourceDo dataSourceDo = ObjectConverter.convert(dataSourceDto, DataSourceDo.class);

        PreCheck.preCheckArgument(dataSourceDo.checkDuplicateName(), "数据源名字不能重复!");

        dataSourceDo.setConfig(FastjsonUtil.toJson(dataSourceDto.getConfig()));
        dataSourceDo.save();
        dataSourceDto.setId(dataSourceDo.getId());
        return dataSourceDto;
    }

    public UiDataSourceDto getDetail(String dataSourceId){
        Option<DataSourceDo> dbData = DataSourceDo.loadById(dataSourceId);
        if(dbData.isEmpty()){
            return null;
        }

        DataSourceDo dataSourceDo = dbData.get();
        UiDataSourceDto dataSourceDto = ObjectConverter.convert(dataSourceDo, UiDataSourceDto.class);
        dataSourceDto.setConfig(JSON.parseObject(dataSourceDo.getConfig(), UiGeneralDataSourceDto.class));
        return dataSourceDto;
    }

    @Transactional
    public void removeDataSource(String dataSourceId){
        DataSourceDo.removeById(dataSourceId);
    }

    public List<UiDataSourceDto> getUiDataSourceList(UiDataSourceDto dataSourceDto){
        List<DataSourceDo> list = DataSourceDo.getListByPage(0, 500);
        List<UiDataSourceDto> result = ObjectConverter.convertList(list, UiDataSourceDto.class);
        return result;
    }

    public List<UiDataSourceDto> getApiList(UiDataSourceDto dataSourceDto){
        List<DataSourceDo> list = DataSourceDo.getListByType(dataSourceDto.getType(), 0, 500);
        List<UiDataSourceDto> result = ObjectConverter.convertList(list, UiDataSourceDto.class);
        return result;
    }

    public List<UiDataSourceDto> getUiDataSourceList(){
        List<DataSourceDo> list = DataSourceDo.getAllList();
        List<UiDataSourceDto> result = ObjectConverter.convertList(list, UiDataSourceDto.class);
        return result;
    }

    public PageResult<Map> dataSourceRender(String id, Integer pageNo, Integer pageSize, Map<String, String[]> parameterMap){
        if(StringUtil.isEmpty(id)){
            return PageResult.pageResultEmpty();
        }

        UiDataSourceDto dataSourceDto = this.getDetail(id);
        if(null == dataSourceDto || null == dataSourceDto.getType() || StringUtil.isEmpty(dataSourceDto.getConfig())){
            return PageResult.pageResultEmpty();
        }

        return DataSourceRenderBuilder.getDataSourceRender(dataSourceDto.getType())
                .doRender(dataSourceDto.getConfig(), pageNo, pageSize, parameterMap);
    }

    public List<UiDataSourceDto> getDataSourceList(List<String> dataSourceIds){
        val dbData = DataSourceDo.loadListById(dataSourceIds);
        if(null == dbData || dbData.isEmpty()){
            return Lists.newArrayList();
        }

        List<UiDataSourceDto> dataList = new ArrayList<>(dbData.size());
        if(null != dbData && !dbData.isEmpty()){
            dbData.forEach(sourceData -> {
                UiDataSourceDto udsd = ObjectConverter.convert(sourceData, UiDataSourceDto.class);
                udsd.setConfig(JSON.parseObject(sourceData.getConfig(), UiGeneralDataSourceDto.class));
                dataList.add(udsd);
            });
        }
        return dataList;
    }
}
