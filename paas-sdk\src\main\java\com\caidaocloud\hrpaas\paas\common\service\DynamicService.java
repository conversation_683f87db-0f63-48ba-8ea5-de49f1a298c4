package com.caidaocloud.hrpaas.paas.common.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.paas.common.feign.DynamicFeignClient;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.val;

/**
 *
 * <AUTHOR>
 * @date 2024/6/18
 */
public class DynamicService {
	private final DynamicFeignClient dynamicFeignClient;

	public DynamicService(DynamicFeignClient dynamicFeignClient) {
		this.dynamicFeignClient = dynamicFeignClient;
	}

	public Map<String, List<MetadataPropertyDto>> dynamicTableConfigLoad(String code) {
		val dynamicSet = dynamicFeignClient.dynamicTableLoad(code).getData();
		List<MetadataPropertyDto> properties = dynamicSet;
		Map<String, List<MetadataPropertyDto>> mappedProperties = Maps.map();
		for (MetadataPropertyDto property : properties) {
			val split = property.getProperty().split("@");
			val identifier = split[1];
			property.setProperty(split[0]);
			if (mappedProperties.containsKey(identifier)) {
				mappedProperties.get(identifier).add(property);
			} else {
				mappedProperties.put(identifier, Lists.list(property));
			}
		}
		return mappedProperties;
	}

	public Map<String, Object> loadDynamicData(String code, String empId, long time,Map<String,String> empIdMap) {
		return loadDynamicData(dynamicTableConfigLoad(code), empId, time, empIdMap);
	}

	public Map<String, Object> loadDynamicData(Map<String, List<MetadataPropertyDto>> mappedProperties, String empId, long time, Map<String, String> empIdMap) {
		Map<String, Object> result = new HashMap<>();
		for (Map.Entry<String, List<MetadataPropertyDto>> entry : mappedProperties.entrySet()) {
			String identifier = entry.getKey();
			if (identifier.startsWith("entity.form.")) {
				// TODO: 2024/6/18 表单数据单独处理
				continue;
			}
			result.putAll(loadDynamicColumnValue(identifier, mappedProperties, time, empId, empIdMap.getOrDefault(identifier, "empId")));
		}
		return result;
	}


	private Map<String, Object> loadDynamicColumnValue(String identifier, Map<String, List<MetadataPropertyDto>> mappedProperties, long time, String empId, String empIdProp) {
		Map<String, Object> result = new HashMap<>();
		if (mappedProperties.containsKey(identifier)) {
			DataQuery.identifier(identifier).limit(1, 1).filter(DataFilter.eq(empIdProp, empId)
							.andNe("deleted", Boolean.TRUE.toString()),
					DataSimple.class, time).getItems().stream().findFirst().ifPresent(emp -> {
				mappedProperties.get(identifier).forEach(property -> {
					val value = emp.getProperties().get(property.getProperty());
					result.put(property.getProperty() + "@" + identifier, value);
				});
			});
		}
		return result;
	}
}
