package com.caidaocloud.hrpaas.service.domain.metadata.repository;

import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataSchemaDo;
import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import com.googlecode.totallylazy.Option;

import java.util.List;

public interface IMetadataSchemaRepository extends IBaseRepository<MetadataSchemaDo> {
    void batchSave(List<MetadataSchemaDo> schemaDoList);

    Option<List<MetadataSchemaDo>> findAllSchema();

    List<MetadataSchemaDo> findSchemaByIds(String [] scriptIds);

    List<MetadataSchemaDo> findSchemaByNameOrVersion(String name, Long version);

    List<MetadataSchemaDo> findSchemaGtVersion(Long version);
}
