package com.caidaocloud.hrpaas.service.application.form.service.datatype.impl;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.service.application.form.dto.WfRegistrySeqDto;
import com.caidaocloud.hrpaas.service.application.form.service.datatype.IDataTypeHandler;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import com.caidaocloud.workflow.dto.WfComponentValueDto;
import com.caidaocloud.workflow.enums.WfValueComponentEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 枚举datatype处理器
 *
 * <AUTHOR>
 * @date 2022/11/8
 **/
@Slf4j
@Component
public class EnumDataTypeHandlerImpl implements IDataTypeHandler {

    @Override
    public PropertyDataType getType() {
        return PropertyDataType.Enum;
    }

    @Override
    public WfRegistrySeqDto generateSeq(FormPropDef formDef, String formDefId) {
        var seqCode = String.format("seq_%s_%s", formDef.getProperty(), formDefId);
        List<WfComponentValueDto> componentEnumValue = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(formDef.getEnumDef())) {
            formDef.getEnumDef().forEach(e -> {
                componentEnumValue.add(new WfComponentValueDto(e.getDisplay(), e.getValue()));
            });
        }
        return new WfRegistrySeqDto(seqCode, formDef.getName(), "", WfValueComponentEnum.ENUM, componentEnumValue);
    }

}
