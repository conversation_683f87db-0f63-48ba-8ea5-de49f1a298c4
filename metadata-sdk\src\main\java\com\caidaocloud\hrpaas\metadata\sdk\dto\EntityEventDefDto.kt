package com.caidaocloud.hrpaas.core.metadata.interfaces.dto

import io.swagger.annotations.ApiModelProperty

data class EntityEventDefAddDto (
    @ApiModelProperty("identifier")
    val identifier : String,
    @ApiModelProperty("关联模型identifier")
    val modelRef : String,
    @ApiModelProperty("名称")
    val name : String,
    @ApiModelProperty("是否追踪任意变化")
    val trackAll : Boolean = true,
    @ApiModelProperty("追踪属性")
    val trackedProperties: List<TrackedPropertiesGroupDto>,
    @ApiModelProperty("通知属性")
    val notifyProperties: List<String>,
    @ApiModelProperty("追踪属性是否关联属性")
    var trackRelation : Boolean
)

data class EntityEventDefUpdateDto (
    @ApiModelProperty("事件定义id")
    val id : String,
    @ApiModelProperty("名称")
    val name : String,
    @ApiModelProperty("是否追踪任意变化")
    val trackAll : Boolean = true,
    @ApiModelProperty("追踪属性")
    val trackedProperties: List<TrackedPropertiesGroupDto>,
    @ApiModelProperty("通知属性")
    val notifyProperties: List<String>,
    @ApiModelProperty("追踪属性是否关联属性")
    var trackRelation : Boolean
)

data class EntityEventDefConsumerDto (
    @ApiModelProperty("事件定义id")
    val id : String,
    @ApiModelProperty("订阅者列表")
    val consumerList : List<String>
)

data class TrackedPropertiesGroupDto(
    @ApiModelProperty("属性组")
    val properties : List<String>
)