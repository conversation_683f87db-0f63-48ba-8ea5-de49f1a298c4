package com.caidaocloud.hrpaas.metadata.sdk.query;

import com.caidaocloud.excption.ServerException;
import org.apache.commons.lang3.StringUtils;

public class MdDataJoin extends DataJoin{

    public MdDataJoin(){
        super();
        this.setMasterDataQuery(true);
    }

    public static MdDataJoin joinModels(ModelInfo firstModel, ModelInfo secondModel, JoinInfo on){
        MdDataJoin dataJoin = new MdDataJoin();
        dataJoin.setFirstModel(firstModel);
        dataJoin.setSecondModel(secondModel);
        dataJoin.setFirstJoin(on);
        dataJoin.setModelCount(2);
        on.getJoin().forEach(joinProperty->{
            if(!StringUtils.equals(firstModel.getIdentifier(), joinProperty.getLeftIdentifier()) ||
                    !StringUtils.equals(secondModel.getIdentifier(), joinProperty.getRightIdentifier())
            ){
                throw new ServerException("illegal join");
            }
        });
        return dataJoin;
    }
}
