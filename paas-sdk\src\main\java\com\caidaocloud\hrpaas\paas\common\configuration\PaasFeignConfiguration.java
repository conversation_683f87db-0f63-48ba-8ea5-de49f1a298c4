package com.caidaocloud.hrpaas.paas.common.configuration;

import com.caidaocloud.hrpaas.paas.common.feign.DynamicFeignClient;
import com.caidaocloud.hrpaas.paas.common.service.DynamicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2021-07-01
 */
@Slf4j
@Configuration
@EnableFeignClients(basePackages = "com.caidaocloud.hrpaas.paas.common.feign")
public class PaasFeignConfiguration {

	@Bean
	public DynamicService dynamicService(DynamicFeignClient dynamicFeignClient) {
		return new DynamicService(dynamicFeignClient);
	}
}
