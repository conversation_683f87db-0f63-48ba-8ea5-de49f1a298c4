package com.caidaocloud.hrpaas.service.interfaces.facade.uiform;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.service.application.uiform.service.UiFormDataSourceService;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiDataSourceDto;
import com.caidaocloud.hrpaas.service.interfaces.vo.uiform.UiFormBaseVo;
import com.caidaocloud.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-08-18
 */
@RestController
@RequestMapping("/api/hrpaas/uiform/datasource/v1")
@Api(value = "/api/hrpaas/uiform/datasource/v1", description = "表单引擎数据源接口", tags = "v1.2")
public class UiFormDataSourceController {
    @Resource
    private UiFormDataSourceService uiFormDataSourceService;

    @PostMapping("/put")
    @ApiOperation("新增/编辑数据源数据")
    public UiDataSourceDto putUiDataSource(@RequestBody UiDataSourceDto dataSourceDto){
        if(StringUtil.isEmpty(dataSourceDto.getSourceName()) || null == dataSourceDto.getType()){
            return dataSourceDto;
        }

        return uiFormDataSourceService.putUiDataSource(dataSourceDto);
    }

    @GetMapping("/detail")
    @ApiOperation("获取表单引擎数据源详情")
    public UiDataSourceDto getDetail(@RequestParam("id") String id){
        PreCheck.preCheckArgument(StringUtil.isEmpty(id), "id 不能为空");

        return uiFormDataSourceService.getDetail(id);
    }

    @DeleteMapping("/remove")
    @ApiOperation("删除/禁用/移除表单引擎数据源")
    public boolean removeDataSource(@RequestParam("id") String id){
        PreCheck.preCheckArgument(StringUtil.isEmpty(id), "id 不能为空");

        uiFormDataSourceService.removeDataSource(id);
        return true;
    }

    @PostMapping("/list")
    @ApiOperation("获取自定义数据源数据列表")
    public UiFormBaseVo<List<UiDataSourceDto>> getUiDataSourceList(@RequestBody UiDataSourceDto dataSourceDto){
        return new UiFormBaseVo(uiFormDataSourceService.getUiDataSourceList(dataSourceDto));
    }

    @PostMapping("/apiList")
    @ApiOperation(value = "获取数据源数据列表")
    public UiFormBaseVo<List<UiDataSourceDto>> getApiList(@RequestBody UiDataSourceDto dataSourceDto){
        return new UiFormBaseVo(uiFormDataSourceService.getApiList(dataSourceDto));
    }

    @GetMapping("/allList")
    @ApiOperation("获取所有数据源数据列表")
    public List<UiDataSourceDto> getUiDataSourceAllList(){
        return uiFormDataSourceService.getUiDataSourceList();
    }

    @GetMapping("/render")
    @ApiOperation("获取数据源提供的数据")
    public PageResult<Map> dataSourceRender(@RequestParam("id") String id,
            @RequestParam(value = "pageNo", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            HttpServletRequest request){
        Map<String, String[]> parameterMap = request.getParameterMap();
        return uiFormDataSourceService.dataSourceRender(id, pageNo, pageSize, parameterMap);
    }

}
