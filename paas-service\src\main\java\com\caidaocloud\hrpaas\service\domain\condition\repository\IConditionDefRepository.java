package com.caidaocloud.hrpaas.service.domain.condition.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionDef;
import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import com.caidaocloud.hrpaas.service.interfaces.dto.condition.ConditionQueryDto;
import com.googlecode.totallylazy.Option;

/**
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
public interface IConditionDefRepository extends IBaseRepository<ConditionDef> {

	PageResult<ConditionDef> page(ConditionQueryDto query);

	Option<ConditionDef> loadByCode(String code);
}
