package com.caidaocloud.hrpaas.service.application.schedule;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.hrpaas.service.application.schedule.dto.ScheduleTaskDto;
import com.caidaocloud.hrpaas.service.application.schedule.dto.ScheduleTaskMsg;
import com.caidaocloud.hrpaas.service.domain.schedule.entity.ScheduleTask;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.msg.message.AbstractBasicMessage;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ScheduleTaskService {

    private static final String scheduleLockKey = "paas.schedule.task.lock";

    private String EXCHANGE_PREFIX = "schedule.task.exchange.";

    private String ROUTING_KEY_PREFIX = "routingKey.schedule.task.";

    @Autowired
    private CacheService cacheService;

    @Autowired
    private MqMessageProducer msgProducer;

    public void addScheduleTask(ScheduleTaskDto scheduleTask){
        ScheduleTask task = scheduleTask.toEntity();
        task.insert();
    }

    public void deleteScheduleTask(String taskTopic, String taskId){
        ScheduleTask.delete(taskTopic, taskId);
    }

    @SneakyThrows
    public void dispatchMsg(ScheduleTask task) {
        val user = new SecurityUserInfo();
        user.setTenantId(task.getTenantId());
        val message = new RabbitBaseMessage();
        val taskMsg = new ScheduleTaskMsg();
        taskMsg.setTaskDetail(task.getTaskDetail());
        taskMsg.setTaskId(task.getTaskId());
        taskMsg.setTopic(task.getTaskTopic());
        taskMsg.setUserInfo(FastjsonUtil.convertObject(user, AbstractBasicMessage.MessageUserInfo.class));
        message.setBody(FastjsonUtil.toJson(taskMsg));
        message.setExchange(EXCHANGE_PREFIX + task.getTaskTopic());
        message.setRoutingKey(ROUTING_KEY_PREFIX + task.getTaskTopic());
        msgProducer.publish(message);
        Thread.sleep(10);
        log.info("dispatch schedule task success:"+task.getTaskTopic()+","+task.getTaskId());
    }

    public boolean dispatchTask(){
        List<ScheduleTask> tasks = ScheduleTask.listSomeTimedUp();
        if(tasks.isEmpty()){
            cacheService.remove(scheduleLockKey);
            return false;
        }else{
            tasks.forEach(it->dispatchMsg(it));
            SpringUtil.getBean(ScheduleTaskService.class).deleteRecentByIds(tasks.stream().map(task->task.getId())
                    .collect(Collectors.toList()));
            cacheService.updateExpire(scheduleLockKey, 3 * 60);
            return true;
        }
    }

    @Transactional
    public void deleteRecentByIds(List<String> ids){
        ScheduleTask.deleteRecentByIds(ids);
    }

    public void startDispatch(){
        val cached = cacheService.cacheValueIfAbsent(scheduleLockKey, "1", 3 * 60);
        try {
            if(cached){
                while(true){
                    if(!dispatchTask()){
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("startDispatch error",e);
            cacheService.remove(scheduleLockKey);
        }
    }

    @PostConstruct
    public void dispatchManage(){
        val schedulePool = Executors
                .newScheduledThreadPool(1);
        schedulePool.scheduleAtFixedRate(()-> startDispatch(), 5, 5, TimeUnit.MINUTES);
    }

    @XxlJob("prepareRecentScheduleTask")
    @Transactional
    public void prepareRecentTask(){
        XxlJobHelper.log("XxlJob prepareRecentScheduleTask start");
        ScheduleTask.prepareRecentTask();
        XxlJobHelper.log("XxlJob prepareRecentScheduleTask end");
    }

}
