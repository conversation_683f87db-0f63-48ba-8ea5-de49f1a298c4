package com.caidaocloud.hrpaas.metadata.sdk.query;

import com.caidaocloud.hrpaas.metadata.sdk.enums.RelationOperationType;
import com.caidaocloud.hrpaas.metadata.sdk.dto.RelationOperationDto;
import com.caidaocloud.hrpaas.metadata.sdk.service.RelationOperatorService;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.val;

import java.util.List;

@Data
public class RelationOperator {

    private String identifier;

    private String property;

    private boolean isMasterDataQuery = false;

    private RelationOperator(){

    }

    protected RelationOperator(String identifier, String property){
        this.identifier = identifier;
        this.property = property;
    }

    public static RelationOperator property(String identifier, String property){
        val result = new RelationOperator();
        result.identifier = identifier;
        result.property = property;
        return result;
    }

    public void addRelations(String sourceId, List<String> targetIds){
        operate(RelationOperationType.ADD, sourceId, targetIds);
    }

    public void deleteRelations(String sourceId, List<String> targetIds){
        operate(RelationOperationType.DELETE, sourceId, targetIds);
    }

    public void replaceAllRelations(String sourceId, List<String> targetIds){
        operate(RelationOperationType.REPLACE_ALL, sourceId, targetIds);
    }

    public void deleteRelationAll(String sourceId){
        operate(RelationOperationType.DELETE_ALL, sourceId, Lists.newArrayList());
    }

    private void operate(RelationOperationType operationType, String sourceId, List<String> targetIds){
        try {
            MdQueryTL.set(isMasterDataQuery);
            val operation = new RelationOperationDto();
            operation.setOperationType(operationType);
            operation.setIdentifier(this.identifier);
            operation.setProperty(this.property);
            operation.setSourceId(sourceId);
            operation.setTargetIds(targetIds);
            SpringUtil.getBean(RelationOperatorService.class).operate(operation);
        } finally {
            MdQueryTL.remove();
        }
    }

}
