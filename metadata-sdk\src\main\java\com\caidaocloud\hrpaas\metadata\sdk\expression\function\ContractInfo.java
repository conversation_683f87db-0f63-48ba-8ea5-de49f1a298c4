package com.caidaocloud.hrpaas.metadata.sdk.expression.function;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorNil;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/3/6
 */
@Slf4j
@Component
public class ContractInfo extends AbstractFunction {

	private static String CONTRACT_IDENTIFIER = "entity.hr.Contract";
	private static final String PASSED = "1";
	private static final String EFFECTIVE = "1";

	@Override
	public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
		String empId = FunctionUtils.getStringValue(arg1, env);
		MetadataVo metadataVo = SpringUtil.getBean(MetadataOperatorService.class).load(CONTRACT_IDENTIFIER);
		PageResult<DataSimple> result = DataQuery.identifier(CONTRACT_IDENTIFIER)
				.limit(500, 1)
				.filter(DataFilter.eq("owner$empId", empId).andEq("approvalStatus", PASSED), DataSimple.class);
		DataSimple data = getCurrentContract(result);
		if (data == null) {
			log.warn("未找到员工合同信息，员工id:{}", empId);
			return AviatorNil.NIL;
		}
		String key = FunctionUtils.getStringValue(arg2, env);
		PropertyValue value = data.getProperties().get(key);
		if (value == null) {
			log.warn("未找到员工合同信息中的属性，员工id:{},属性名:{}", empId, key);
			return AviatorNil.NIL;
		}
		MetadataPropertyVo propertyDef = Sequences.sequence(metadataVo.fetchAllProperties())
				.find(p -> p.getProperty().equals(key)).get();
		if (propertyDef.getDataType().isComponent()) {
			// TODO: 2024/3/7  
			log.warn("未实现组件类型属性值的获取，属性名:{}", key);
			return AviatorNil.NIL;
		}
		String sv = ((SimplePropertyValue) value).getValue();
		return FunctionUtils.wrapReturn(propertyDef.getDataType() == PropertyDataType.String ? sv : propertyDef.getDataType() == PropertyDataType.Timestamp ? Long.parseLong(sv) : Double.parseDouble(sv));
	}

	private DataSimple getCurrentContract(PageResult<DataSimple> result) {
		long datetime = System.currentTimeMillis();
		List<DataSimple> list = result.getItems();
		Map<Boolean, List<DataSimple>> map = Sequences.sequence(list)
				.toMap(data -> EFFECTIVE.equals(((EnumSimple) data.getProperties().get("contractStatus")).getValue()));
		DataSimple ret = getEffectiveContract(datetime, map.getOrDefault(true, new ArrayList<>()));
		if (ret == null) {
			ret = getLastContract(map.getOrDefault(false, new ArrayList<>()));
		}
		return ret;
	}

	private DataSimple getLastContract(List<DataSimple> list) {
		List<DataSimple> sorted = Sequences.sequence(list).sort((d1, d2) -> {
			String s1 = ((SimplePropertyValue) d1.getProperties().get("startDate")).getValue();
			String s2 = ((SimplePropertyValue) d2.getProperties().get("startDate")).getValue();
			String e1 = ((SimplePropertyValue) d1.getProperties().get("endDate")).getValue();
			String e2 = ((SimplePropertyValue) d2.getProperties().get("endDate")).getValue();
			if (e1.equals(e2)) {
				return s2.compareTo(s1);
			}
			else {
				return e2.compareTo(e1);
			}
		}).toList();
		return sorted.isEmpty() ? null : sorted.get(0);
	}

	private DataSimple getEffectiveContract(long datetime, List<DataSimple> list) {
		if (list.isEmpty()) {
			return null;
		}
		DataSimple ret = list.stream()
				.filter(data -> {
					String s = ((SimplePropertyValue) data.getProperties().get("startDate")).getValue();
					String e = ((SimplePropertyValue) data.getProperties().get("endDate")).getValue();
					String t = ((SimplePropertyValue) data.getProperties().get("terminationDate")).getValue();
					if (t != null && Long.parseLong(t) <= datetime) {
						return false;
					}
					return Long.parseLong(s) <= datetime && datetime <= Long.parseLong(e);
				})
				.findFirst().orElse(list.get(0));
		return ret;
	}

	@Override
	public String getName() {
		return "contractInfo";
	}
}
