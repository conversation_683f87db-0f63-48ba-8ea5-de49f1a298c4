package com.caidaocloud.metadata.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.metadata.sdk.enums.EntityRelationType;
import com.caidaocloud.metadata.domain.entity.EntityRelationDef;
import com.caidaocloud.metadata.infrastructure.configuration.handler.BooleanTypeHandler;
import lombok.Data;

@Data
@TableName("paas_entity_relation_def")
public class EntityRelationDefPo extends BasePo<EntityRelationDefPo, EntityRelationDef>{

    private String identifier;
    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean builtIn = false;

    private String relatedIdentifier;

    private String property;

    private String propertyName;

    private EntityRelationType relationType;
    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean multiple;
}
