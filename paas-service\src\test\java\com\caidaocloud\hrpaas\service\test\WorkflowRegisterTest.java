package com.caidaocloud.hrpaas.service.test;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import com.google.common.collect.Lists;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDefStatus;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormTarget;

import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.hrpaas.service.application.form.service.FormDefService;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class WorkflowRegisterTest {

    @Autowired
    private FormDefService formDefService;

    @Test
    public void testRegistry() {
        FormDef formDef = new FormDef();
        formDef.setId("123456789");
        formDef.setName("testzoro");
        formDef.setTarget(FormTarget.NONE);
        formDef.setWorkflowNodeAvailable(false);
        formDef.setWorkflowAvailable(false);
        formDef.setDescription("");
        formDef.setStatus(FormDefStatus.DRAFT);
        formDef.setStandard(false);
        FormPropDef formPropDef = new FormPropDef();
        formPropDef.setProperty("aaa");
        formPropDef.setName("测试");
        List<FormPropDef> properties = Lists.newArrayList(formPropDef);
        formDef.setProperties(properties);
        formDef.setId("");
//        formDefService.formRegWorkflow(formDef, "33");
    }

}
