package com.caidaocloud.hrpaas.service.application.metadata.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.paas.common.dto.KvDto;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataKvDo;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.util.ObjectConverter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class MetadataKvService {
    private final static String kv_cache = "kv_%s", kv_cache_lock  = "hrpaas_kv_cache_lock_";
    @Resource
    private CacheService cacheService;
    @Resource
    private Locker locker;

    public String getDetail(String key) {
        checkKey(key);

        String cacheKey = String.format(kv_cache, key);
        String value = cacheService.getValue(cacheKey);
        if(null != value){
            return value;
        }

        MetadataKvDo kv = MetadataKvDo.getDetailByKey(key);
        if(null == kv){
            return "";
        }

        cacheService.cacheValue(cacheKey, kv.getContent());
        return kv.getContent();
    }

    @SneakyThrows
    public void save(KvDto kv) {
        checkKey(kv.getProperty());

        MetadataKvDo data = MetadataKvDo.getDetailByKey(kv.getProperty());
        if(null != data){
            update(kv);
            return;
        }

        data = ObjectConverter.convert(kv, MetadataKvDo.class);
        val lock = locker.getLock(kv_cache_lock + kv.getProperty());
        val locked = lock.tryLock(10, TimeUnit.SECONDS);
        if(locked){
            try{
                data.save();
                String cacheKey = String.format(kv_cache, kv.getProperty());
                cacheService.cacheValue(cacheKey, kv.getContent());
            }finally {
                lock.unlock();
            }
        } else{
            throw new ServerException("The data is being updated by other requests, please try again later");
        }
    }

    public void update(KvDto kv) {
        MetadataKvDo kvData = ObjectConverter.convert(kv, MetadataKvDo.class);
        String cacheKey = String.format(kv_cache, kv.getProperty());
        cacheService.remove(cacheKey);
        kvData.update();
        cacheService.remove(cacheKey);
        cacheService.cacheValue(cacheKey, kv.getContent());
    }

    private void checkKey(String key){
        if(key.indexOf("_") > 0){
            return;
        }

        throw new ServerException("The key must contain '_' And tenant ID, for example: test_8");
    }
}
