package com.caidaocloud.hrpaas.metadata.sdk.expression.function;

import java.util.Map;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorNil;
import com.googlecode.aviator.runtime.type.AviatorObject;

import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/6/7
 */
@Component
public class Nvl extends AbstractFunction {
	@Override
	public String getName() {
		return "nvl";
	}

	@Override
	public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
		AviatorObject result1 = FunctionUtils.wrapReturn(arg1.getValue(env));
		if (result1 == AviatorNil.NIL) {
			return FunctionUtils.wrapReturn(arg2.getValue(env));
		}
		return result1;
	}
}
