package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class EntityDataDto {

    private String id;
    private String identifier;
    private String bid;
    private String tenantId;
    private long createTime;
    private String createBy;
    private long updateTime;
    private String updateBy;
    private long dataStartTime;
    private long dataEndTime;
    private boolean deleted;
    private List<PropertyDataDto> properties = Lists.newArrayList();
    private List<EntityRelationDto> relations = Lists.newArrayList();
}
