package com.caidaocloud.hrpaas.service.interfaces.vo.orgSet;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MetadataTenantVo {
    @ApiModelProperty("岗位/职位管理体系")
    private Integer manageSystem;

    @ApiModelProperty("是否展示税务信息")
    private boolean taxInfo;

    @ApiModelProperty("电脑端")
    private Attachment pcTenantLogo;

    @ApiModelProperty("移动端")
    private Attachment appTenantLogo;

    @ApiModelProperty("租户名称")
    private String name;

    @ApiModelProperty("租户编码")
    private String code;
}
