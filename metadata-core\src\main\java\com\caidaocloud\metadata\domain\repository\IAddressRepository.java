package com.caidaocloud.metadata.domain.repository;

import com.caidaocloud.metadata.domain.entity.AddressDo;
import com.googlecode.totallylazy.Option;

import java.util.List;

public interface IAddressRepository extends IBaseRepository<AddressDo> {
    AddressDo loadById(String id);

    Option<AddressDo> load(String id);

    List<AddressDo> getList(AddressDo address, int pageNo, int pageSize);

    void delete(String id);

    List<AddressDo> getAllList(String pidPath);

    List<AddressDo> getAddressList(List<String> list);
}
