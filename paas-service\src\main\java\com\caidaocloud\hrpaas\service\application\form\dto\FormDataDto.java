package com.caidaocloud.hrpaas.service.application.form.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormData;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropertyData;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormWidgetType;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.val;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class FormDataDto {

    private String id;

    private String targetId;

    private String workflowTaskId;

    private Long dataStartTime;

    private String empId;

    private Map<String, Object> propertiesMap = Maps.map();

    public FormData toEntity(FormDef formDef, FormData existed, boolean self){
        val entity = FastjsonUtil.convertObject(this, FormData.class);
        formDef.getProperties().forEach(formPropDef->{
            if(propertiesMap.containsKey(formPropDef.getProperty())){
                val propertyData = new FormPropertyData();
                propertyData.setProperty(formPropDef.getProperty());
                propertyData.setValue(FastjsonUtil.toJson(propertiesMap.get(formPropDef.getProperty())));
                propertyData.setDataType(formPropDef.getDataType());
                entity.getProperties().add(propertyData);
            }
        });
        if(self){
            if(null != existed){
                entity.getProperties().removeIf(it->it.getProperty().equals("form_owner"));
                existed.getProperties().stream()
                        .filter(it->it.getProperty().equals("form_owner")).findFirst().ifPresent(it->{
                    entity.getProperties().add(it);
                });
            }
        }
        if(propertiesMap.get("form_owner") != null){
            String empId = (String) ((Map)propertiesMap.get("form_owner")).get("empId");
            entity.setTargetId(empId);
            initAuthData(entity,empId);
        }
        buildSelectTxtData(entity,formDef,FormWidgetType.OrgSelect,keyList->{
            List<String> valueList = Sequences.sequence(keyList).map(Pair::second).filter(Objects::nonNull).toList();
            if (valueList.isEmpty()) {
                return Maps.map();
            }
            List<Map<String, String>> list = DataQuery.identifier("entity.hr.Org")
                    .filterProperties(DataFilter.in("bid", valueList)
                            .andNe("deleted", Boolean.TRUE.toString()), Lists.list("bid", "name"), System.currentTimeMillis())
                    .getItems();
            return list.stream().collect(Collectors.toMap(it->it.get("bid"), it->it.get("name")));
        });
        buildSelectTxtData(entity,formDef,FormWidgetType.ChoosePost,keyList->{
            List<String> valueList = Sequences.sequence(keyList).map(Pair::second).filter(Objects::nonNull).toList();
            if (valueList.isEmpty()) {
                return Maps.map();
            }
            List<Map<String, String>> list = DataQuery.identifier("entity.hr.Post")
                    .filterProperties(DataFilter.in("bid", valueList)
                            .andNe("deleted", Boolean.TRUE.toString()), Lists.list("bid", "name"), System.currentTimeMillis())
                    .getItems();
            return list.stream().collect(Collectors.toMap(it->it.get("bid"), it->it.get("name")));
        });
        buildSelectTxtData(entity,formDef,FormWidgetType.ChooseJob,keyList->{
            List<String> valueList = Sequences.sequence(keyList).map(Pair::second).filter(Objects::nonNull).toList();
            if (valueList.isEmpty()) {
                return Maps.map();
            }
            List<Map<String, String>> list = DataQuery.identifier("entity.hr.Job")
                    .filterProperties(DataFilter.in("bid", valueList)
                            .andNe("deleted", Boolean.TRUE.toString()), Lists.list("bid", "name"), System.currentTimeMillis())
                    .getItems();
            return list.stream().collect(Collectors.toMap(it->it.get("bid"), it->it.get("name")));
        });
        return entity;
    }

    private void initAuthData(FormData entity, String empId) {
        DataSimple empData = DataQuery.identifier("entity.hr.EmpWorkInfo")
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), DataSimple.class).getItems().get(0);
        String organize = ((SimplePropertyValue) empData.getProperties()
                .get("organize")).getValue();
        String empType = ((DictSimple) empData.getProperties().get("empType")).getValue();

        val organizeData = new FormPropertyData();
        organizeData.setProperty("form_owner_organize");
        organizeData.setValue(organize);
        organizeData.setDataType(PropertyDataType.String);
        entity.getProperties().add(organizeData);

        val empTypeData = new FormPropertyData();
        empTypeData.setProperty("form_owner_emp_type");
        empTypeData.setValue(empType);
        empTypeData.setDataType(PropertyDataType.Dict);
        entity.getProperties().add(empTypeData);
    }

    @FunctionalInterface
    private interface TxtDataLoader{
        Map<String, String> loadTxt(List<Pair<FormPropDef, String>> keyList);
    }

    private void buildSelectTxtData(FormData entity, FormDef formDef,FormWidgetType type,TxtDataLoader loader) {
        List<FormPropDef> propList = filterFormWidgetType(formDef, type);
        if (propList.isEmpty()){
            return;
        }
        List<Pair<FormPropDef, String>> keyList = Sequences.sequence(propList).map(prop -> Pair.pair(prop,(String) propertiesMap.get(prop.getProperty())))
                .toList();
        Map<String, String> keyValueMap = loader.loadTxt(keyList);
        for (Pair<FormPropDef,String> pair : keyList) {
            FormPropDef selectProp = pair.first();
            val propertyData = new FormPropertyData();
            propertyData.setProperty(selectProp.getProperty()+"_txt");
            propertyData.setValue(keyValueMap.get(pair.second()));
            propertyData.setDataType(PropertyDataType.String);
            entity.getProperties().add(propertyData);
        }
    }


    private List<FormPropDef> filterFormWidgetType(FormDef formDef, FormWidgetType widgetType) {
        return Sequences.sequence(formDef.getProperties()).filter(prop -> prop.getWidgetType().equals(widgetType))
                .toList();
    }

}
