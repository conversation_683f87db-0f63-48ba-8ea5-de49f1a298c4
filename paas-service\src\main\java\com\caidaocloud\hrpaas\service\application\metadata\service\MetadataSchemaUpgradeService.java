package com.caidaocloud.hrpaas.service.application.metadata.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.hrpaas.metadata.sdk.service.SchemaUpgradeService;
import com.caidaocloud.hrpaas.service.application.metadata.dto.MetadataSchemaInfoDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataSchemaDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SchemaDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SignUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MetadataSchemaUpgradeService implements SchemaUpgradeService {
    private final static String upgrade_metadata_schema_key = "upgrade_metadata_schema_key:";

    @Autowired
    private MetadataSchemaService metadataSchemaService;
    @Resource
    private CacheService cacheService;

    @Override
    public void upgrade(List<SchemaDto> schemaDtoList) {
        if(null == schemaDtoList || schemaDtoList.isEmpty()){
            return;
        }

        String schemaListStr = JSON.toJSONString(schemaDtoList);
        String schemaMd5 = SignUtil.md5(schemaListStr);
        if(checkUpgrade(schemaMd5)){
            return;
        }

        List<MetadataSchemaDto> schemaList = schemaDtoList.stream().map(schemaDto -> {
            MetadataSchemaDto metadataSchemaDto = new MetadataSchemaDto();
            metadataSchemaDto.setFolder(schemaDto.getFolder());
            metadataSchemaDto.setSchemaModel(schemaDto.getSchemaModel());
            metadataSchemaDto.setSchemaName(schemaDto.getSchemaName());
            metadataSchemaDto.setVersion(getVersion(schemaDto.getSchemaName()));
            metadataSchemaDto.setContent(getSchemaInfoList(schemaDto.getSchemaInfo()));
            return metadataSchemaDto;
        }).collect(Collectors.toList());

        try {
            metadataSchemaService.syncSchemaVersion(schemaList);
        } finally {
            cacheUpgrade(schemaMd5);
        }
    }

    public List<MetadataSchemaInfoDto> getSchemaInfoList(String json){
        List<MetadataSchemaInfoDto> list = FastjsonUtil.toObject(json, new TypeReference<List<MetadataSchemaInfoDto>>(){});
        return list;
    }

    private void cacheUpgrade(String schemaMd5){
        cacheService.cacheValueIfAbsent(upgrade_metadata_schema_key + schemaMd5, schemaMd5, -1L);
    }

    private boolean checkUpgrade(String schemaMd5){
        String schemaMd5Val = cacheService.getValue(upgrade_metadata_schema_key + schemaMd5);
        if(StringUtil.isEmpty(schemaMd5Val) || !schemaMd5Val.equals(schemaMd5)){
            return false;
        }

        log.warn("The script has been updated and will be skipped this time, schemaMd5={}", schemaMd5);
        return true;
    }

    private Long getVersion(String name){
        return Long.parseLong(name.substring(1, 15));
    }
}
