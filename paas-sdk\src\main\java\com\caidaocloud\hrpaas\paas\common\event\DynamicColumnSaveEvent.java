package com.caidaocloud.hrpaas.paas.common.event;

import com.caidaocloud.hrpaas.metadata.sdk.event.AbstractInteriorEvent;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

/**
 *
 * <AUTHOR>
 * @date 2024/6/18
 */
@Data
public class DynamicColumnSaveEvent extends AbstractInteriorEvent {
	public final static String topic = "DYNAMIC_COLUMN_SAVED_";
	private String code;
	private String tenantId;

	public DynamicColumnSaveEvent(String code,String tenantId) {
		super(topic+code.toUpperCase());
		this.code = code;
		this.tenantId = tenantId;
	}

	@Override
	public void doPublish() {
		MqMessageProducer producer = SpringUtil.getBean(MqMessageProducer.class);
		RabbitBaseMessage message = new RabbitBaseMessage();
		message.setBody(FastjsonUtil.toJson(this));
		message.setExchange("caidao.hrpaas");
		message.setRoutingKey("caidao.hrpaas.dynamic.column." + code.toLowerCase());
		producer.publish(message);
	}
}
