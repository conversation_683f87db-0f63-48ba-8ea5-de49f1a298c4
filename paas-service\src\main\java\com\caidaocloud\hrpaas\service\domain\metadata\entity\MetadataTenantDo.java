package com.caidaocloud.hrpaas.service.domain.metadata.entity;

import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.metadata.domain.dto.BaseSearchDto;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.IMetadataTenantRepository;
import lombok.Data;

import java.util.List;

@Data
public class MetadataTenantDo extends BaseEntity<IMetadataTenantRepository> {
    private String tenantId;

    private String code;

    private String name;

    private String thirdPart;

    private Long createTime;

    private String pcTenantLogo;

    private String appTenantLogo;

    private Integer manageSystem;

    private boolean taxInfo;

    public static List<MetadataTenantDo> getListByPage(int pageNo, int pageSize){
        return  repository(MetadataTenantDo.class).getListByPage(new BaseSearchDto(), pageNo, pageSize);
    }

    public void save(){
        if(null == createTime){
            createTime = System.currentTimeMillis();
        }

        this.insert();
    }

    public static MetadataTenantDo bulid(String tId){
        MetadataTenantDo metadataTenantDo = new MetadataTenantDo();
        metadataTenantDo.setTenantId(tId);
        metadataTenantDo.setCode(tId);
        metadataTenantDo.setName(tId);
        return metadataTenantDo;
    }

    @Override
    public void update() {
        repository(MetadataTenantDo.class).update(this);
    }

    public static List<MetadataTenantDo> getListByPage(String tenantId, int pageNo, int pageSize){
        BaseSearchDto baseSearchDto = new BaseSearchDto();
        baseSearchDto.getEqCondition().put("tenantId", tenantId);
        return repository(MetadataTenantDo.class).getListByPage(baseSearchDto, pageNo, pageSize);
    }

}
