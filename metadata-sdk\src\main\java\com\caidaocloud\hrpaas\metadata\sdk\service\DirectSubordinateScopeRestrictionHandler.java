package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.jarvis.cache.annotation.Cache;
import lombok.val;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class DirectSubordinateScopeRestrictionHandler implements ScopeRestrictionHandler {

    @Override
    @Cache(key = "'1_scope_restriction_' + #args[0] + '_' + #args[1]",expire = 5 *60)
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        Boolean isFilter = AuthScopeFilterUtil.get();
        val tlUser = SecurityUserUtil.getThreadLocalSecurityUserInfo();
        try{
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            val currentEmp = SecurityUserUtil.getSecurityUserInfo().getEmpId();
            if(null == currentEmp){
                return Lists.list();
            }
            List<String> results = Lists.list();
            AuthScopeFilterUtil.put(false);
            for(int i = 1;i < 20;i++){
                val list = DataQuery.identifier("entity.hr.EmpWorkInfo")
                        .limit(1000, i).filterProperties(DataFilter.eq("leadEmpId$empId", currentEmp.toString())
                                , Lists.list("empId"), System.currentTimeMillis()).getItems()
                        .stream().map(it->it.get("empId")).collect(Collectors.toList());
                if(list.isEmpty()){
                    break;
                }else{
                    results.addAll(list);
                }
            }
            return results;
        }finally {
            if(null == tlUser){
                SecurityUserUtil.removeSecurityUserInfo();
            }else{
                SecurityUserUtil.setSecurityUserInfo(tlUser);
            }
            AuthScopeFilterUtil.put(isFilter);
        }
    }

//    @Override
//    @Cache(key = "'2_scope_restriction_' + #args[0] + '_' + #args[1]",expire = 5 *60)
//    public List<DataFilter.SubSelect> toSubSelect(String userId, String simpleValues, SecurityUserInfo userInfo){
//        SecurityUserUtil.setSecurityUserInfo(userInfo);
//        val currentEmp = SecurityUserUtil.getSecurityUserInfo().getEmpId();
//        if(null == currentEmp){
//            return Lists.list();
//        }
//        val subSelect = new DataFilter.SubSelect();
//        subSelect.setIdentifier("entity.hr.EmpWorkInfo");
//        subSelect.setProperty("empId");
//        subSelect.setFilter(DataFilter.eq("leaderEmp$empId", currentEmp.toString()));
//        return Lists.list(subSelect);
//    }

}
