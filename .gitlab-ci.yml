variables:
  GIT_BASEURL: https://gitee.52emp.com/caidao2.0/caidao-hr-paas-service.git
stages:
  - build_metadata-sdk
  - build_paas-core


# 项目编译相关任务模板
.def_build: &def_build
  script:
    - pwd
    - cd ${FILE_PWD} && ${RUN_IT}
  tags:
    - master
  retry: 2


.def_branch_constraint: &def_branch_constraint
  only:
    - release
    - devops
  except:
    - /^issue-.*$/
    - master

build_metadata-sdk:
  stage: build_metadata-sdk
  <<: *def_build
  variables:
    FILE_PWD: ./metadata-sdk/
    GIT_URL: ${GIT_BASEURL}
    RUN_IT: mvn -T 4C clean --batch-mode -Dmaven.test.skip=true  deploy  -U
  <<: *def_branch_constraint

build_paas-core:
  stage: build_paas-core
  <<: *def_build
  variables:
    FILE_PWD: ./paas-core/
    GIT_URL: ${GIT_BASEURL}
    RUN_IT: mvn -T 4C clean --batch-mode -Dmaven.test.skip=true  deploy  -U
  <<: *def_branch_constraint
