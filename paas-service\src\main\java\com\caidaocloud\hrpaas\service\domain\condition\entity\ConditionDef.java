package com.caidaocloud.hrpaas.service.domain.condition.entity;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.service.application.condition.enums.ConditionPlatform;
import com.caidaocloud.hrpaas.service.domain.condition.repository.IConditionDefRepository;
import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.hrpaas.service.interfaces.dto.condition.ConditionQueryDto;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@Data
public class ConditionDef extends BaseEntity<IConditionDefRepository> {
	private String name;
	private String module;
	private String code;
	private List<String> identifiers;
	private List<ConditionData> properties = Lists.list();
	private String remark;
	private boolean deleted = false;
	private String tenantId;
	private long createTime;
	private String createBy;
	private long updateTime;
	private String updateBy;
	private ConditionPlatform platform;

	public static ConditionDef loadById(String id) {
		return repository(ConditionDef.class).load(id).get();
	}

	public static PageResult<ConditionDef> page(ConditionQueryDto dto){
		return ((IConditionDefRepository) repository(ConditionDef.class)).page(dto);
	}

	public static ConditionDef loadByCode(String code) {
		return ((IConditionDefRepository) repository(ConditionDef.class)).loadByCode(code).get();
	}

	// 刷新勾选字段的状态
	public void refreshProperties(List<ConditionData> buildProperties) {
		Map<ConditionData, ConditionData> map = properties.stream()
				.peek(data -> data.setEnabled(false))
				.collect(Collectors.toMap(obj -> obj, obj -> obj));
		for (ConditionData property : buildProperties) {
			property.setEnabled(true);
			map.put(property, property);
		}

		properties = Sequences.sequence(map.values()).toList();
	}
}
