package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.DataSourceDo;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.IDataSourceRepository;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceType;
import org.springframework.stereotype.Repository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.DataSourcePo;
import com.caidaocloud.security.service.ISessionService;
import com.googlecode.totallylazy.Option;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-08-12
 */
@Repository
public class DataSourceRepositoryImpl implements IDataSourceRepository {

    @Resource
    private ISessionService sessionService;

    @Autowired
    private DataSourceMapper mapper;

    @Override
    public String insert(DataSourceDo dataSourceDo) {
        UserInfo userInfo = sessionService.getUserInfo();
        val po = DataSourcePo.fromEntity(DataSourcePo.class, dataSourceDo);
        if(null == dataSourceDo.getId()){
            po.setCreateBy(userInfo.getUserId());
            po.setCreateTime(System.currentTimeMillis());
            po.setUpdateBy(po.getCreateBy());
            po.setUpdateTime(po.getCreateTime());
            po.setTenantId(Long.valueOf(userInfo.getTenantId()));
            mapper.insert(po);
            return String.valueOf(po.getId());
        }
        po.setUpdateBy(userInfo.getUserId());
        po.setUpdateTime(System.currentTimeMillis());
        po.setTenantId(Long.valueOf(userInfo.getTenantId()));
        mapper.updateById(po);
        return String.valueOf(po.getId());
    }

    @Override
    public Option<DataSourceDo> loadById(String dataSourceId) {
        return Option.option(
                mapper.selectOne(
                        new LambdaQueryWrapper<DataSourcePo>()
                                .in(DataSourcePo::getTenantId,
                                0, sessionService.getUserInfo().getBelongOrgId())
                                .eq(DataSourcePo::getId, Long.valueOf(dataSourceId)))
        ).map(it->it.toEntity(DataSourceDo.class));
    }

    @Override
    public DataSourceDo checkDuplicateName(String id, String sourceName) {
        val wrapper = new LambdaQueryWrapper<DataSourcePo>()
                .eq(DataSourcePo::getSourceName, sourceName);
        if(StringUtils.isNotEmpty(id)){
            wrapper.ne(DataSourcePo::getId, Long.valueOf(id));
        }
        val po = mapper.selectOne(wrapper);
        if(null == po){
            return null;
        }
        return po.toEntity(DataSourceDo.class);
    }

    @Override
    public List<DataSourceDo> getAllList() {
        return mapper.selectList(new LambdaQueryWrapper<DataSourcePo>()
                .in(DataSourcePo::getTenantId,
                        0, sessionService.getUserInfo().getBelongOrgId()))
        .stream().map(it->it.toEntity(DataSourceDo.class)).collect(Collectors.toList());
    }

    @Override
    public List<DataSourceDo> loadListById(List<String> ids) {
        return mapper.selectList(new LambdaQueryWrapper<DataSourcePo>()
                .in(DataSourcePo::getTenantId, ids)
                .in(DataSourcePo::getTenantId,
                        0, sessionService.getUserInfo().getBelongOrgId()))
                .stream().map(it->it.toEntity(DataSourceDo.class)).collect(Collectors.toList());
    }
    @Override
    public void update(DataSourceDo dataSourceDo) {
        val po = DataSourcePo.fromEntity(DataSourcePo.class, dataSourceDo);
        UserInfo userInfo = sessionService.getUserInfo();
        if(null == userInfo){
            throw new RuntimeException("User voucher invalid");
        }
        po.setUpdateBy(userInfo.getUserId());
        po.setUpdateTime(System.currentTimeMillis());
        po.setTenantId(userInfo.getBelongOrgId().longValue());
        mapper.update(po,
                new LambdaQueryWrapper<DataSourcePo>().eq(DataSourcePo::getId, po.getId()).eq(DataSourcePo::getTenantId, po.getTenantId()));
    }


    @Override
    public List<DataSourceDo> getListByPage(int pageNo, int pageSize) {
        val page = new Page<DataSourcePo>(pageNo, pageSize);
        page.setSearchCount(false);
        return mapper.selectPage(page, new LambdaQueryWrapper<DataSourcePo>()
                .in(DataSourcePo::getTenantId, 0, sessionService.getUserInfo().getBelongOrgId()))
                .getRecords().stream().map(it->it.toEntity(DataSourceDo.class)).collect(Collectors.toList());
    }

    @Override
    public List<DataSourceDo> getListByType(DataSourceType type, int pageNo, int pageSize) {
        val wrapper = new LambdaQueryWrapper<DataSourcePo>()
                .in(DataSourcePo::getTenantId, 0, sessionService.getUserInfo().getBelongOrgId());
        if(null != type){
            wrapper.eq(DataSourcePo::getType, type);
        }
        val page = new Page<DataSourcePo>(pageNo, pageSize);
        page.setSearchCount(false);
        return mapper.selectPage(page, wrapper).getRecords().stream()
                .map(it -> it.toEntity(DataSourceDo.class)).collect(Collectors.toList());
    }


    @Override
    public void remove(String id) {
        mapper.delete(new LambdaQueryWrapper<DataSourcePo>()
                .in(DataSourcePo::getTenantId,
                        0, sessionService.getUserInfo().getBelongOrgId())
                .eq(DataSourcePo::getId, Long.valueOf(id)));
    }
}
