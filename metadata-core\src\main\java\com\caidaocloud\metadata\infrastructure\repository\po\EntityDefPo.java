package com.caidaocloud.metadata.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.metadata.sdk.enums.DefStatus;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.metadata.domain.entity.EntityDef;

import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.metadata.infrastructure.configuration.handler.BooleanTypeHandler;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.WebUtil;
import com.googlecode.totallylazy.Maps;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;
import java.util.Map;
import java.util.function.BiFunction;

@Data
@TableName("paas_entity_def")
public class EntityDefPo extends BasePo<EntityDefPo, EntityDef>{

    private String identifier;

    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean builtIn = false;

    private String name;

    //private Map<String, String> i18nName;

    private String owner;

    private String parent = "entity.common.BaseModel";

    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean tree = false;

    private String label;

    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean timelineEnabled = true;

    private long createTime;

    private String createBy;

    private long updateTime;

    private String updateBy;

    private DefStatus status;

    @DisplayAsArray
    private String mainKey;

    @DisplayAsArray
    private String standardProperties;

    @DisplayAsArray
    private String customProperties;

    @DisplayAsArray
    private String relationProperties;

    private String propertyToFieldMapping;

    public EntityDef toEntity(Map<String, String> eventOn) {
        val result = super.toEntity(EntityDef.class);
        result.appendEventOn(eventOn);
        fillLang(result.getName(), Maps.map(), (str, i18n)->{
            result.setName(str);
            result.setI18nName(i18n);
            return result;
        });
        result.fetchProperties().forEach(propertyDef -> {
            fillLang(propertyDef.getName(), propertyDef.getI18nName(), (str, i18n)->{
                propertyDef.setName(str);
                propertyDef.setI18nName(i18n);
                return propertyDef;
            });
            fillLang(propertyDef.getPlaceholder(),propertyDef.getI18nPlaceholder(), (str, i18n)->{
                propertyDef.setPlaceholder(str);
                propertyDef.setI18nPlaceholder(i18n);
                return propertyDef;
            });
            if(null != propertyDef.getEnumDef()){
                propertyDef.getEnumDef().forEach(enumDef->{
                    fillLang(enumDef.getDisplay(),enumDef.getI18nDisplay(), (str, i18n)->{
                        enumDef.setDisplay(str);
                        enumDef.setI18nDisplay(i18n);
                        return enumDef;
                    });
                });
            }
        });
        return result;
    }

    private void fillLang(String str, Map<String, String> i18nStr, BiFunction<String, Map<String,String>, Object> func){
        if(null == i18nStr || i18nStr.isEmpty()){
            i18nStr = Maps.map("default", str);
            try{
                val json = FastjsonUtil.toObject(str, Map.class);
                if(null != json && !json.isEmpty()){
                    i18nStr = json;
                }
            }catch(Exception e){
            }
        }
        func.apply(lang(i18nStr), i18nStr);
    }

    public String lang(Map<String, String> i18n){
        Locale locale = MessageHandler.getLocaleByRequest(WebUtil.getRequest());
        String currentLang = locale.getLanguage();
        String langDetail = currentLang;
        val country = locale.getCountry();
        if(StringUtils.isNotEmpty(country)){
            langDetail = currentLang + "-" + country;
        }
        if(i18n.containsKey(currentLang)){
            return i18n.get(currentLang);
        }else{
            if(i18n.containsKey(langDetail)){
                return i18n.get(langDetail);
            }else{
                return i18n.get("default");
            }
        }
    }

    public static EntityDefPo fromEntity(Class<EntityDefPo> clazz, EntityDef entity){
        val result = JsonEnhanceUtil.toObject(entity, clazz);
        if(null != entity.getI18nName() && !entity.getI18nName().isEmpty()){
            result.setName(FastjsonUtil.toJson(entity.getI18nName()));
        }
        return result;
    }

}
