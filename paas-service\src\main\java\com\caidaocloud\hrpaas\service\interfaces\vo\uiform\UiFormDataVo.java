package com.caidaocloud.hrpaas.service.interfaces.vo.uiform;

import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormDataValueDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("表单数据实体")
public class UiFormDataVo {
    @ApiModelProperty("字段ID")
    private String formId;

    @ApiModelProperty("数据ID")
    private String id;

    @ApiModelProperty("数据体")
    private List<UiFormDataValueDto> dataVals;

    public static UiFormDataVo bulidDataValEmpty(String formId, String dataId){
        UiFormDataVo uiFormDataVo = new UiFormDataVo();
        uiFormDataVo.setFormId(formId);
        uiFormDataVo.setId(dataId);
        return uiFormDataVo;
    }

}
