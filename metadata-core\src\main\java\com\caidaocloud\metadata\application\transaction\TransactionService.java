package com.caidaocloud.metadata.application.transaction;

import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;

import com.caidaocloud.hrpaas.metadata.sdk.dto.TransactionInfoDto;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.metadata.infrastructure.repository.transaction.PaasTransactionManager;
import com.caidaocloud.metadata.infrastructure.repository.transaction.ServiceProperties;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;

import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2022/9/29
 */
@Service
@Slf4j
public class TransactionService  {

	@Autowired
	private PaasTransactionManager transactionManager;

	@Autowired
	private ServiceProperties serviceProperties;

	@Autowired
	private StringRedisTemplate redisTemplate;

	public TransactionInfoDto begin() {
		String transactionId = SnowUtil.nextId();
		val now = System.currentTimeMillis();
		transactionManager.doBegin(transactionId);
		redisTemplate.opsForValue().set("transactionTimeRange" + transactionId,
				now + "," + DateUtil.MAX_TIMESTAMP, 30, TimeUnit.SECONDS);
		if (log.isDebugEnabled()) {
			log.debug("Open transaction '{}'", transactionId);
		}
		return new TransactionInfoDto(transactionId, serviceProperties.getIp(), serviceProperties.getPort(), serviceProperties.getService());
	}

	public void commit(String transactionId) {
		transactionManager.doCommit(transactionId);
	}

	public void rollback(String transactionId) {
		transactionManager.doRollback(transactionId);
	}


	public void doTask(String transactionId, FutureTask<Object> task) {
		PaasTransactionManager.TransactionRunner runner = transactionManager.getRunner(transactionId);
		runner.offerTask(task);
	}
}
