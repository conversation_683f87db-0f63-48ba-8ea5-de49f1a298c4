package com.caidaocloud.hrpaas.service.domain.schedule.repository;

import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import com.caidaocloud.hrpaas.service.domain.schedule.entity.ScheduleTask;

import java.util.List;

public interface IScheduleTaskRepository extends IBaseRepository<ScheduleTask> {
    List<ScheduleTask> listSomeTimedUp();

    void delete(String taskTopic, String taskId);

    void prepareRecentTask(long time);

    void deleteRecentByIds(List<String> ids);

    String insertRecent(ScheduleTask scheduleTask);

    String insertLongTerm(ScheduleTask scheduleTask);
}
