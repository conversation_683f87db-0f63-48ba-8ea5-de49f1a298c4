package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.RelationOperationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RelationOperationDto {

    private String identifier;

    private String property;

    private String sourceId;

    private List<String> targetIds;

    private RelationOperationType operationType;

    private long startTime;

}
