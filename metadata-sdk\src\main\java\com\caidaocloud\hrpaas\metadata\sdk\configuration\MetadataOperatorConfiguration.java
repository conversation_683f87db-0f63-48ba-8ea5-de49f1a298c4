package com.caidaocloud.hrpaas.metadata.sdk.configuration;

import com.caidaocloud.hrpaas.metadata.sdk.service.*;
import com.caidaocloud.hrpaas.metadata.sdk.util.ConvertValueUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2021-07-01
 */
@Slf4j
@Configuration
@EnableFeignClients(basePackages="com.caidaocloud.hrpaas.metadata.sdk.feign")
public class MetadataOperatorConfiguration {

    @ConditionalOnMissingBean(SpringUtil.class)
    @Bean
    public SpringUtil springContext(){
        return new SpringUtil();
    }

    @ConditionalOnMissingBean(MetadataOperatorService.class)
    @Bean
    public MetadataOperatorService metadataOperatorService(){
        return new MetadataOperatorServiceFeignImpl();
    }

    @ConditionalOnMissingBean(DataOperatorService.class)
    @Bean
    public DataOperatorService dataOperatorService(){
        return new DataOperatorServiceFeignImpl();
    }

    @ConditionalOnMissingBean(RelationOperatorService.class)
    @Bean
    public RelationOperatorService relationOperatorService(){
        return new RelationOperatorServiceFeignImpl();
    }


    @ConditionalOnMissingBean()
    @Bean
    public ConvertValueUtil convertValueUtil(){
        return new ConvertValueUtil();
    }
}
