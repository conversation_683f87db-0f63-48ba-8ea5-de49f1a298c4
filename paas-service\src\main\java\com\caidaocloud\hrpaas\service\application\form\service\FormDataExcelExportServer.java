package com.caidaocloud.hrpaas.service.application.form.service;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.googlecode.totallylazy.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FormDataExcelExportServer implements IExcelExportServer {
    @Override
    public List<Object> selectListForExcelExport(Object param, int page) {
        if(page == 1){
            return (List<Object>) param;
        }else{
            return Lists.list();
        }
    }
}
