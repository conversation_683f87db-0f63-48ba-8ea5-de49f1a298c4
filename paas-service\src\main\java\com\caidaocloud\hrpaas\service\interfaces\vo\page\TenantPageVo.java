package com.caidaocloud.hrpaas.service.interfaces.vo.page;

import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageTemplate;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("页面配置vo")
public class TenantPageVo {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("名称")
    private Map<String, String> i18nName;

    @ApiModelProperty("名称(当前语言)")
    private String name;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("页面类型")
    private PageType type;

    @ApiModelProperty("描述")
    private String desc;

    @ApiModelProperty("导航显示：true显示，false不显示")
    private Boolean showNav;

    @ApiModelProperty("父ID")
    private String parentId;

    @ApiModelProperty("是否标准")
    private boolean isStandard;

    @ApiModelProperty("关联模型")
    private String joinModel;

    @ApiModelProperty("页面模板")
    private PageTemplate pageTemplate;

    @ApiModelProperty("路径")
    private String path;

    private String schema;

    @ApiModelProperty("子页面")
    private List<TenantPageVo> children = Lists.list();

}
