package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.ImportTemplateDo;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("paas_import_template")
public class ImportTemplatePo extends BasePo<ImportTemplatePo, ImportTemplateDo> {

    private String tenantId;

    /**
     * 模版code
     */
    private String templateCode;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * 模版路径
     */
    private String path;

    /**
     * 菜单所属模块
     * hrpaas 模块，attendance 考勤模块，payroll 薪资模块
     */
    private String model;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * UT 更新时间
     */
    private Long updateTime;

    /**
     * UT 更新人
     */
    private String updateBy;

    /**
     * 是否删除。0 默认值，未删除，1 已删除
     */
    private Integer deleted;
}
