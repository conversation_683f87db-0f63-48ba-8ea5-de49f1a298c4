package com.caidaocloud.hrpaas.service.application.rules;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MaxValueCheckRule implements ICheckRule{
    private static String MAX_VALUE_TIPS = "最大值不合法";

    @Override
    public String getRuleType() {
        return "maxValue";
    }

    @Override
    public String[] getRuleGroup() {
        return new String[]{"Number"};
    }

    @Override
    public void check(IRule checkRuleDto) {
        if(null == checkRuleDto){
            return;
        }

        String tips = StringUtil.isBlank(checkRuleDto.tips()) ? MAX_VALUE_TIPS : checkRuleDto.tips();
        try {
            if(StringUtil.isNotEmpty(checkRuleDto.value()) && Long.parseLong(checkRuleDto.value()) > Long.parseLong(checkRuleDto.rule())){
                throw new ServerException(tips);
            }
        } catch (Exception e){
            log.error("MaxValue Validation exception, {}", e.getMessage(), e);
            throw new ServerException(tips);
        }
    }
}
