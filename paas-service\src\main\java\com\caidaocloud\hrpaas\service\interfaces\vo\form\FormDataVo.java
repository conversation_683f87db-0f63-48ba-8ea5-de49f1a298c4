package com.caidaocloud.hrpaas.service.interfaces.vo.form;

import com.caidaocloud.hrpaas.service.domain.form.enums.FormDataStatus;
import com.googlecode.totallylazy.Lists;
import lombok.Data;

import java.util.List;

@Data
public class FormDataVo {

    private String id;
    private String formId;
    private long createTime;
    private String createBy;
    private long updateTime;
    private String updateBy;
    private List<FormPropertyDataVo> properties = Lists.list();

    private String targetId;
    private FormDataStatus status;
    private String sysProcessCode;
}
