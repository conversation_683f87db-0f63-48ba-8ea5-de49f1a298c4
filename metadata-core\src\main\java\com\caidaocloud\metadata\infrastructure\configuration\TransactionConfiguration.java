package com.caidaocloud.metadata.infrastructure.configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.caidaocloud.metadata.infrastructure.repository.transaction.PaasTransactionManager;
import com.caidaocloud.metadata.infrastructure.repository.transaction.ServiceProperties;
import com.caidaocloud.metadata.infrastructure.repository.transaction.TransactionRejectHandler;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.PlatformTransactionManager;

/**
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
@Configuration
@EnableAspectJAutoProxy
public class TransactionConfiguration {

	@Value("${caidaocloud.paas.transaction.thread.corePoolSize:30}")
	private int corePoolSize;

	@Value("${caidaocloud.paas.transaction.thread.maxPoolSize:50}")
	private int maxPoolSize;

	@Value("${caidaocloud.paas.transaction.thread.keepAliveTime:60}")
	private int keepAliveTime;

	@Value("${caidaocloud.paas.transaction.thread.workQueueSize:50}")
	private int workQueueSize;

	@Value("${caidaocloud.paas.transaction.expire.interval:5}")
	private int expireInterval;

	@Bean
	public ServiceProperties serviceProperties(){
		return new ServiceProperties();
	}

	@Bean
	public PaasTransactionManager paasTransactionManager(PlatformTransactionManager platformTransactionManager) {
		ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("Transaction-%d").build();
		ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(corePoolSize, maxPoolSize,
				keepAliveTime, TimeUnit.SECONDS, new LinkedBlockingQueue<>(workQueueSize), threadFactory, new TransactionRejectHandler());
		return new PaasTransactionManager(threadPoolExecutor, platformTransactionManager, expireInterval);
	}

}
