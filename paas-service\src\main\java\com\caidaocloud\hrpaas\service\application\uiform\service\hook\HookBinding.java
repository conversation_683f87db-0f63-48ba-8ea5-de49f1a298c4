package com.caidaocloud.hrpaas.service.application.uiform.service.hook;

import groovy.lang.Binding;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2021-09-08
 */
public class HookBinding extends Binding {
    private static ThreadLocal<Map<String, Object>> localVars = new ThreadLocal<Map<String, Object>>();
    private static Map<String, Object> propertyMap = new ConcurrentHashMap();

    public HookBinding() {
    }

    public HookBinding(Map<String, Object> variables) {
        localVars.set(variables);
    }

    public HookBinding(String[] args) {
        this();
        setVariable("args", args);
    }

    @Override
    public Object getVariable(String name) {
        Map<String, Object> map = localVars.get();
        Object result = null;
        if ((map != null) && (map.containsKey(name))) {
            result = map.get(name);
        } else {
            result = propertyMap.get(name);
        }
        return result;
    }

    @Override
    public void setVariable(String name, Object value) {
        if (localVars.get() == null) {
            Map<String, Object> vars = new LinkedHashMap<String, Object>();
            vars.put(name, value);
            localVars.set(vars);
        } else {
            localVars.get().put(name, value);
        }
    }

    @Override
    public Map<String, Object> getVariables() {
        if (localVars.get() == null) {
            return new LinkedHashMap<>();
        }

        return localVars.get();
    }

    public void clearVariables() {
        localVars.remove();
    }

    @Override
    public Object getProperty(String property) {
        return propertyMap.get(property);
    }

    @Override
    public void setProperty(String property, Object newValue) {
        propertyMap.put(property, newValue);
    }

    public boolean hasProperty(String property) {
        return propertyMap != null && propertyMap.containsKey(property);
    }
}
