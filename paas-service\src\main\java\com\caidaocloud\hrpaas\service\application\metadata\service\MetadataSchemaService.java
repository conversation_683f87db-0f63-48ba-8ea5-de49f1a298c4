package com.caidaocloud.hrpaas.service.application.metadata.service;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.service.application.metadata.dto.MetadataSchemaInfoDto;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.*;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.SchemaStatus;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataSchemaDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataUpgradeDto;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.EntityRelationDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MetadataSchemaService {
    @Value("${caidaocloud.script.pageSize:100}")
    private int initScriptPageSize;
    @Resource
    private ISessionService sessionService;

    public void syncSchemaVersion(List<MetadataSchemaDto> schemaList){
        List<MetadataSchemaDo> metadataSchemaList = saveSchema(schemaList);

        Option<List<MetadataSchemaDo>> allSchema = MetadataSchemaDo.findAllSchema();
        if(allSchema.isEmpty()){
            log.error("db is empty, No upgrade required ......");
            return;
        }

        metadataSchemaList = allSchema.get();
        // 同步所有租户脚本
        upgradeSchema(metadataSchemaList);
    }

    public List<MetadataSchemaDo> saveSchema(List<MetadataSchemaDto> schemaList){
        List<MetadataSchemaDo> metadataSchemaList = convertMetadataSchemaDoList(schemaList);
        MetadataSchemaDo.batchSave(metadataSchemaList);
        return metadataSchemaList;
    }

    private List<MetadataSchemaDo> convertMetadataSchemaDoList(List<MetadataSchemaDto> schemaList){
        final Long createTime = System.currentTimeMillis();
        UserInfo userInfo = sessionService.getUserInfo();
        Long createBy = userInfo.getUserId();
        List<MetadataSchemaDo> metadataSchemaList = schemaList.stream().map(schema -> {
            MetadataSchemaDo msd = new MetadataSchemaDo();
            BeanUtils.copyProperties(schema, msd);
            msd.setCreateTime(createTime);
            msd.setCreateBy(createBy);
            msd.setUpdateBy(createBy);
            msd.setUpdateTime(createTime);

            if(null != schema.getContent() && !schema.getContent().isEmpty()){
                List<MetadataSchemaInfoDto> schemaInfoDtos = schema.getContent().stream().map(info -> {
                    info.setFolder(schema.getFolder());
                    info.setScriptName(schema.getSchemaName());
                    info.setModel(schema.getSchemaModel());
                    return info;
                }).collect(Collectors.toList());
                schema.setContent(schemaInfoDtos);
            }

            msd.setSchemaInfo(FastjsonUtil.toJson(schema.getContent()));
            return msd;
        }).collect(Collectors.toList());

        return metadataSchemaList;
    }

    public void upgrade(List<MetadataSchemaInfoDto> list){
        doTenantScript(0, MetadataTenantDo.getListByPage(0, initScriptPageSize), list);
    }

    public void upgradeSchema(List<MetadataSchemaDo> metadataSchemaList){
        doTenantSchema(0, MetadataTenantDo.getListByPage(0, initScriptPageSize), metadataSchemaList);
    }

    public String fileToJson(String srcFilePath){
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(srcFilePath);
             InputStreamReader isr = new InputStreamReader(is);
             BufferedReader br = new BufferedReader(isr)) {
            String line = br.readLine();
            StringBuilder sb = new StringBuilder();
            while (line != null) {
                sb.append(line);
                sb.append("\r\n");
                line = br.readLine();
            }

            return sb.toString();
        } catch (IOException e) {
            log.error("fileToJson err,{}", e.getMessage(), e);
        }

        return "";
    }

    private List<MetadataSchemaInfoDto> jsonToEntity(String json){
        List<MetadataSchemaInfoDto> list = FastjsonUtil.toObject(json, new TypeReference<List<MetadataSchemaInfoDto>>(){});
        return list;
    }

    @PostConstruct
    public void sysInitScript() throws Exception {
        log.info("init system script start ..............");
        //throw new ServerException("启动失败");

        //doTenantScript(0, MetadataTenantDo.getListByPage(0, initScriptPageSize), null);

        log.info("init system script end ..............");
    }

    public void doTenantScript(int pageNo, List<MetadataTenantDo> tenantList, List<MetadataSchemaInfoDto> list){
        if(null == tenantList || tenantList.isEmpty()){
            return;
        }

        for (MetadataTenantDo tenantDo : tenantList) {
            initTenantScript(tenantDo, list);
        }

        pageNo ++;
        tenantList = MetadataTenantDo.getListByPage(pageNo, initScriptPageSize);
        doTenantScript(pageNo, tenantList, list);
    }

    public void doTenantSchema(int pageNo, List<MetadataTenantDo> tenantList, List<MetadataSchemaDo> metadataSchemaList){
        if(null == tenantList || tenantList.isEmpty()){
            return;
        }

        for (MetadataTenantDo tenantDo : tenantList) {
            initTenantSchema(tenantDo, metadataSchemaList);
        }

        pageNo ++;
        tenantList = MetadataTenantDo.getListByPage(pageNo, initScriptPageSize);
        doTenantSchema(pageNo, tenantList, metadataSchemaList);
    }

    public void initTenantScript(MetadataTenantDo tenantDo, List<MetadataSchemaInfoDto> list){
        MetadataTenantSchemaDo metadataSchemaDo = MetadataTenantSchemaDo.load(tenantDo.getId()).getOrNull();
        boolean versionFlag = (null != metadataSchemaDo);
        Long version;
        for (MetadataSchemaInfoDto schemaDto : list) {
            version = getVersionByName(schemaDto.getScriptName());
            if(versionFlag && metadataSchemaDo.getVersion() >= version){
                // init 比当前租户已处理的脚本版本大的脚本
                // 如果不满足当前 if 条件，则 init 所有脚本给当前租户
                continue;
            }

            saveSchemaDto(schemaDto);
        }
    }

    public void initTenantSchema(MetadataTenantDo tenantDo, List<MetadataSchemaDo> metadataSchemaList){
        MetadataTenantSchemaDo metadataTenantSchemaDo = MetadataTenantSchemaDo.load(tenantDo.getTenantId()).getOrNull();
        List<MetadataSchemaInfoDto> schemaInfoDtos;
        boolean versionFlag = (null != metadataTenantSchemaDo);
        Long installedTime, version;
        SchemaStatus success;
        int length = 0;
        for (MetadataSchemaDo metadataSchemaDo : metadataSchemaList) {
            version = getVersionByName(metadataSchemaDo.getSchemaName());
/*            if(versionFlag && metadataTenantSchemaDo.getVersion() >= version){
                // init 比当前租户已处理的脚本版本大的脚本
                // 如果不满足当前 if 条件，则 init 所有脚本给当前租户
                continue;
            }*/

            String schemaInfo = metadataSchemaDo.getSchemaInfo();
            schemaInfoDtos = jsonToEntity(schemaInfo);
            if(null == schemaInfoDtos || schemaInfoDtos.isEmpty()){
                log.error("initTenantSchema err, schemaInfoDtos is null, metadataSchemaDo = {}", FastjsonUtil.toJson(metadataSchemaDo));
                continue;
            }

            installedTime = System.currentTimeMillis();
            success = SchemaStatus.success;
            try {
                HolderUtil.setTenantId(tenantDo.getTenantId());
                for (MetadataSchemaInfoDto schemaDto : schemaInfoDtos){
                    saveSchemaDto(schemaDto);
                }
            } catch (Exception e){
                log.error("initTenantSchema err, schemaInfoDtos is null, metadataSchemaDo = {}, err msg = {}", FastjsonUtil.toJson(metadataSchemaDo), e.getMessage(), e);
                success = SchemaStatus.fail;
            } finally {
                HolderUtil.clear();
            }

            metadataTenantSchemaDo = new MetadataTenantSchemaDo();
            metadataTenantSchemaDo.setChecksum(schemaInfoDtos.size());
            metadataTenantSchemaDo.setFolder(metadataSchemaDo.getFolder());
            metadataTenantSchemaDo.setInstalledTime(installedTime);
            metadataTenantSchemaDo.setExecutionTime(System.currentTimeMillis());
            metadataTenantSchemaDo.setTenantId(tenantDo.getTenantId());
            metadataTenantSchemaDo.setScriptName(metadataSchemaDo.getSchemaName());
            metadataTenantSchemaDo.setStatus(success);
            metadataTenantSchemaDo.setVersion(version);

            length = metadataTenantSchemaDo.getScriptName().length();
            if(length > 22){
                metadataTenantSchemaDo.setDescription(metadataTenantSchemaDo.getScriptName().substring(16, length - 5));
            }

            metadataTenantSchemaDo.save();

            if(SchemaStatus.fail == success){
                // 当前租户等脚本执行出错，跳过当前租户
                log.error("当前租户等脚本执行出错，跳过当前租户, tenantId = {}", tenantDo.getTenantId());
                break;
            }
        }
    }

    private void saveSchemaDto(MetadataSchemaInfoDto schemaDto){
        switch (schemaDto.getAction()){
            case create:
                schemaDto.getEntityDef().create(DefChannel.SCRIPT);
                break;
            case update:
                schemaDto.getEntityDef().update(DefChannel.SCRIPT);
                break;
            case add_prop:
                EntityDef.insertStandardProps(schemaDto.getIdentifier(), Sequences.sequence(schemaDto.getPropertyDef()), DefChannel.SCRIPT);
                break;
            case change_prop:
                List<Pair<String, PropertyDef>> propList = schemaDto.getPropertyDef().stream().map(prop -> Pair.pair(prop.getProperty(), prop.getNewDef())).collect(Collectors.toList());
                EntityDef.updateStandardProps(schemaDto.getIdentifier(), Sequences.sequence(propList), DefChannel.SCRIPT);
                break;
            case add_relation:
                List<EntityRelationDef> relationList = schemaDto.getRelation().stream().map(relationDto -> JsonEnhanceUtil.toObject(relationDto, EntityRelationDef.class)).collect(Collectors.toList());
                relationList.forEach(relation -> EntityDef.addRelation(relation, DefChannel.SCRIPT));
                break;
            case delete_relation:
                EntityDef.deleteRelation(schemaDto.getIdentifier(), schemaDto.getRelationProp(), DefChannel.SCRIPT);
                break;
            default :
                log.error("Unsupported script type, schemaDto = {}", FastjsonUtil.toJson(schemaDto));
        }
    }

    private Long getVersionByName(String scriptName){
        if(StringUtil.isEmpty(scriptName)){
            throw new ServerException("There is an unknown ScriptName");
        }

        Long scriptVersion = Long.parseLong(scriptName.substring(1, 15));
        return scriptVersion;
    }

    // todo 15 魔法数字；关联关系脚本升级
    public MetadataSchemaDto upload(String schemaName, String schemaJson, String appName){
        PreCheck.preCheckArgument(schemaName.length() < 15, "Illegal script file name format.");

        MetadataSchemaDto metadataSchemaDto = new MetadataSchemaDto();
        metadataSchemaDto.setFolder(DateUtil.getTodayDate());
        metadataSchemaDto.setSchemaModel(appName);
        metadataSchemaDto.setSchemaName(schemaName);
        metadataSchemaDto.setVersion(Long.parseLong(schemaName.substring(1, 15)));

        List<MetadataSchemaInfoDto> list = FastjsonUtil.toObject(schemaJson, new TypeReference<List<MetadataSchemaInfoDto>>(){});

        metadataSchemaDto.setContent(list);

        List<MetadataSchemaDo> dbList = MetadataSchemaDo.findSchemaByNameOrVersion(schemaName, metadataSchemaDto.getVersion());
        PreCheck.preCheckArgument(null != dbList && !dbList.isEmpty(), "Duplicate script name or version conflict.");

        // 保存脚本
        saveSchema(Arrays.asList(metadataSchemaDto));

        return metadataSchemaDto;
    }

    public void modelUpgrade(MetadataUpgradeDto upgradeDto){
        List<MetadataSchemaDo> dbList = MetadataSchemaDo.findSchemaByIds(upgradeDto.getScriptIds());
        if(null == dbList || dbList.isEmpty()){
            return;
        }

        String tenantId = upgradeDto.getTenantId();
        if(StringUtil.isEmpty(upgradeDto.getTenantId())){
            UserInfo userInfo = sessionService.getUserInfo();
            tenantId = userInfo.getTenantId();
        }

        initTenantSchema(MetadataTenantDo.bulid(tenantId), dbList);
    }

    public void lastUpgrade(MetadataUpgradeDto upgradeDto){
        String tenantId = upgradeDto.getTenantId();
        if(StringUtil.isEmpty(upgradeDto.getTenantId())){
            UserInfo userInfo = sessionService.getUserInfo();
            tenantId = userInfo.getTenantId();
        }

        MetadataTenantSchemaDo loadTenantSchema = MetadataTenantSchemaDo.load(tenantId).getOrNull();
        List<MetadataSchemaDo> dbList = null == loadTenantSchema ?
                MetadataSchemaDo.findAllSchema().getOrNull() : MetadataSchemaDo.findSchemaGtVersion(loadTenantSchema.getVersion());
        if(null == dbList || dbList.isEmpty()){
            return;
        }

        initTenantSchema(MetadataTenantDo.bulid(tenantId), dbList);
    }

    public void modelSimpleUpgrade(String [] scriptIds){
        List<MetadataSchemaDo> dbList = MetadataSchemaDo.findSchemaByIds(scriptIds);
        if(null == dbList || dbList.isEmpty()){
            return;
        }

        int pageNo = 0;
        List<MetadataTenantDo> tenantList = MetadataTenantDo.getListByPage(pageNo, initScriptPageSize);
        pageNo ++;
        doModelSimpleUpgrade(tenantList, pageNo, dbList);
    }

    private void doModelSimpleUpgrade(List<MetadataTenantDo> tenantList, int pageNo, List<MetadataSchemaDo> dbList){
        if(null == tenantList || tenantList.isEmpty()){
            return;
        }

        tenantList.forEach(tenantDo -> {
            initTenantSchema(tenantDo, dbList);
        });

        tenantList = MetadataTenantDo.getListByPage(pageNo, initScriptPageSize);
        pageNo ++;
        doModelSimpleUpgrade(tenantList, pageNo, dbList);
    }

    public void modelLastUpgrade(){
        int pageNo = 0;
        pageNo ++;
        List<MetadataTenantDo> tenantList = MetadataTenantDo.getListByPage(pageNo, initScriptPageSize);

        doModelLastUpgrade(tenantList, pageNo);
    }

    private void doModelLastUpgrade(List<MetadataTenantDo> tenantList, int pageNo){
        if(null == tenantList || tenantList.isEmpty()){
            return;
        }

        tenantList.forEach(tenantDo -> {
            MetadataTenantSchemaDo loadTenantSchema = MetadataTenantSchemaDo.load(tenantDo.getTenantId()).getOrNull();
            List<MetadataSchemaDo> dbList = null == loadTenantSchema ?
                    MetadataSchemaDo.findAllSchema().getOrNull() : MetadataSchemaDo.findSchemaGtVersion(loadTenantSchema.getVersion());
            if(null != dbList && !dbList.isEmpty()){
                initTenantSchema(tenantDo, dbList);
            }
        });

        tenantList = MetadataTenantDo.getListByPage(pageNo, initScriptPageSize);
        pageNo ++;
        doModelLastUpgrade(tenantList, pageNo);
    }
}
