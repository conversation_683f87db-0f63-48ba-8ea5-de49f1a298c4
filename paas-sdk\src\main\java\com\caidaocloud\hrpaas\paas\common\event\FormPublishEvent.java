package com.caidaocloud.hrpaas.paas.common.event;

import com.caidaocloud.hrpaas.metadata.sdk.event.AbstractInteriorEvent;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

@Data
public class FormPublishEvent extends AbstractInteriorEvent {
	private String formId;
	private String formCode;
	private String tenantId;


	public final static String topic = "FORM_PUBLISH";

	public FormPublishEvent() {
		super(topic);
	}

	public FormPublishEvent(String formId, String formCode, String tenantId) {
		super(topic);
		this.formId = formId;
		this.formCode = formCode;
		this.tenantId = tenantId;
	}

	@Override
	public void doPublish() {
		MqMessageProducer producer = SpringUtil.getBean(MqMessageProducer.class);
		RabbitBaseMessage message = new RabbitBaseMessage();
		message.setBody(FastjsonUtil.toJson(this));
		message.setExchange("caidao.hrpaas");
		message.setRoutingKey("caidao.hrpaas.form.publish");
		producer.publish(message);
	}

}
