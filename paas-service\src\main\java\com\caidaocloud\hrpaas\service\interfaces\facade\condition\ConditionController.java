package com.caidaocloud.hrpaas.service.interfaces.facade.condition;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.service.application.condition.dto.ConditionDefDto;
import com.caidaocloud.hrpaas.service.application.condition.service.ConditionDefService;
import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionData;
import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionDef;
import com.caidaocloud.hrpaas.service.interfaces.dto.condition.ConditionQueryDto;
import com.caidaocloud.hrpaas.service.interfaces.vo.condition.ConditionDataVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.condition.ConditionDefPageVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.condition.ConditionDefVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.condition.ConditionPropertyVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Sequences;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 * <AUTHOR> Zhou
 * @date 2022/11/2
 */
@Slf4j
@RestController
@RequestMapping("/api/hrpaas/condition/v1")
@Api(value = "/api/hrpaas/condition/v1",  tags = "匹配条件接口")
public class ConditionController {

	@Autowired
	private ConditionDefService conditionDefService;

	@PostMapping("/testLock")
	public Result testLock(){
		conditionDefService.testLock();
		return Result.ok();
	}

	@PostMapping("def/page")
	@ApiOperation("匹配条件列表")
	public Result<PageResult<ConditionDefPageVo>> page(@RequestBody ConditionQueryDto query) {
		PageResult<ConditionDef> page = conditionDefService.page(query);
		List<ConditionDefPageVo> list = Sequences.sequence(page.getItems())
				.map(ConditionDefPageVo::fromEntity).toList();
		return Result.ok(new PageResult<>(list, page.getPageNo(), page.getPageSize(), page.getTotal()));
	}

	@PutMapping("def")
	@ApiOperation("匹配条件更新")
	public Result update(@RequestBody ConditionDefDto dto) {
		conditionDefService.update(dto);
		return Result.ok(true);
	}

	@GetMapping("def")
	@ApiOperation("匹配条件详情")
	public Result<ConditionDefVo> detail(String id) {
		ConditionDefVo detail = conditionDefService.getEnableDetail(id);
		return Result.ok(detail);
	}

	@GetMapping("def/property")
	@ApiOperation("匹配条件可选字段列表")
	public Result<List<ConditionPropertyVo>> getProperties(String id) {
		List<ConditionPropertyVo> list = conditionDefService.getProperties(id);
		return Result.ok(list);
	}

	@GetMapping("property")
	@ApiOperation("匹配条件字段详情")
	public Result<List<ConditionDataVo>> getConditionDataByCode(String code,boolean showDisable) {
		List<ConditionData> conditionData = conditionDefService.getConditionData(code, showDisable);
		return Result.ok(ObjectConverter.convertList(conditionData, ConditionDataVo.class));
	}

	@PostMapping("/def/empProperty")
	@ApiOperation("匹配条件可选字段列表")
	public Result<List<ConditionPropertyVo>> getEmpProperties(@RequestBody List<String> identifiers) {
		List<ConditionPropertyVo> list = conditionDefService.getPassProperties(identifiers);
		return Result.ok(list);
	}
}
