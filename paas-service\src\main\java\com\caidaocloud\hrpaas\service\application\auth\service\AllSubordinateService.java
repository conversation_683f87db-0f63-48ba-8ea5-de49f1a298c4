package com.caidaocloud.hrpaas.service.application.auth.service;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.service.application.auth.feign.MaintenanceFeignClient;
import com.caidaocloud.metadata.application.dto.EntityDataChangeDto;
import com.caidaocloud.metadata.domain.entity.EntityDataChange;
import com.caidaocloud.metadata.domain.entity.EntityEvent;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AllSubordinateService {

    @Autowired
    private MaintenanceFeignClient maintenanceFeignClient;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @SneakyThrows
    public void empWorkInfoChange(List<EntityDataChangeDto> empWorkInfoChanged){
        log.info("empWorkInfoChanged" + FastjsonUtil.toJson(empWorkInfoChanged));
        while(redisTemplate.hasKey("empLeaderSyncAll")){
            log.info("empWorkInfoChanged:empLeaderSyncAll");
            Thread.sleep(30000);
        }
        empWorkInfoChanged.forEach(changed->{
            val after = changed.getAfter();
            if(null != after){
                val start = after.getDataStartTime();
                val end = after.getDataEndTime();
                val now = System.currentTimeMillis();
                if(now > start && now < end){
                    changeSubordinate(
                            after.getProperties().stream().filter(it->it.getProperty()
                                    .equals("empId")).map(it->it.getValue()).findFirst().get(),
                            after.getProperties().stream().filter(it->it.getProperty()
                                    .equals("leadEmpId.empId")).map(it->it.getValue()).filter(it->it != null).findFirst().orElse(null));
                }
            }
        });
    }


    public void changeSubordinate(String empId, String leaderEmpId){
        boolean fresh = false;
        String oldPath = "";
        EmpLeader empLeader = DataQuery.identifier("entity.hr.EmpLeaderRedundancy").oneOrNull(empId, EmpLeader.class);
        if(null != empLeader){
            oldPath = empLeader.path;
        }else{
            fresh = true;
        }
        boolean clearAllPath = false;
        String path = "";
        if(null != leaderEmpId){
            path = DataQuery.identifier("entity.hr.EmpLeaderRedundancy")
                    .one(leaderEmpId, EmpLeader.class).getPath() + "/" + leaderEmpId;
            if(path.contains(empId)){
                path = "";
                clearAllPath = true;
            }
        }
        if(!clearAllPath && !fresh && StringUtils.equals(oldPath, path)){
            return;
        }
        if(null == empLeader){
            empLeader = new EmpLeader();
            empLeader.setEmpId(empId);
            empLeader.setPath(path);
            empLeader.setBid(empId);
            DataInsert.identifier("entity.hr.EmpLeaderRedundancy").insert(empLeader);
        }else{
            empLeader.setPath(path);
            DataUpdate.identifier("entity.hr.EmpLeaderRedundancy").update(empLeader);
            val subs = DataQuery.identifier("entity.hr.EmpLeaderRedundancy").limit(-1, 1)
                    .filter(DataFilter.regex("path", empId), EmpLeader.class).getItems();
            for (EmpLeader sub : subs) {
                if(clearAllPath){
                    sub.setPath("");
                }else{
                    sub.setPath(sub.getPath().replace(oldPath, path));
                }
                DataUpdate.identifier("entity.hr.EmpLeaderRedundancy").update(sub);
            }
        }
    }

    @SneakyThrows
    @XxlJob("initAllTenantSubordinate")
    public void initAllTenantSubordinate(){
        redisTemplate.opsForValue().set("empLeaderSyncAll", "1", 5, TimeUnit.MINUTES);
        Thread.sleep(1 * 60 * 1000);
        List<String> tenantIds = maintenanceFeignClient.tenantList().getData().stream()
                .map(it->it.getTenantId()).collect(Collectors.toList());
        tenantIds.forEach(tenantId->{
            val tenant = new SecurityUserInfo();
            tenant.setTenantId(tenantId);
            try{
                SecurityUserUtil.setSecurityUserInfo(tenant);
                initSubordinate();
            }catch (Exception e){
                log.error("同步全部下级异常："+tenantId, e);
            }finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        });
        redisTemplate.delete("empLeaderSyncAll");
    }

    public void initSubordinate(){
        val empLeaders = DataQuery.identifier("entity.hr.EmpWorkInfo").limit(-1, 1)
                .filterProperties(DataFilter.ne("deleted", Boolean.TRUE.toString()),
                        Lists.list("empId","leadEmpId.empId"), System.currentTimeMillis())
                .getItems().stream().map(it->{
                    EmpLeader empLeader = new EmpLeader();
                    empLeader.setEmpId(it.get("empId"));
                    empLeader.setEmpLeaderId(it.get("leadEmpId.empId"));
                    return empLeader;
                }).collect(Collectors.toList());
        for(EmpLeader empLeader : empLeaders){
            redisTemplate.opsForValue().set("empLeaderSyncAll", "1", 5, TimeUnit.MINUTES);
            if(empLeader.isPathCalculated()){
                //continue;
            }else{
                calcPath(empLeader, empLeaders);
            }
            val existed = DataQuery.identifier("entity.hr.EmpLeaderRedundancy").oneOrNull(empLeader.empId, EmpLeader.class);
            if(existed == null){
                empLeader.setBid(empLeader.getEmpId());
                DataInsert.identifier("entity.hr.EmpLeaderRedundancy").insert(empLeader);
            }else{
                existed.setPath(empLeader.getPath());
                DataUpdate.identifier("entity.hr.EmpLeaderRedundancy").update(existed);
            }
            empLeaders.forEach(it->it.setTravelled(false));
        }
    }

    private boolean calcPath(EmpLeader empLeader, List<EmpLeader> empLeaders){
        if(empLeader.travelled){
            empLeader.setPath("");
            empLeader.setPathCalculated(true);
            return false;
        }
        empLeader.travelled = true;
        if(StringUtils.isEmpty(empLeader.getEmpLeaderId())){
            empLeader.setPath("");
            empLeader.setPathCalculated(true);
            return true;
        }
        val leader = empLeaders.stream()
                .filter(it->it.getEmpId().equals(empLeader.getEmpLeaderId())).findFirst().orElse(null);
        if(null == leader){
            empLeader.setPath("");
            empLeader.setPathCalculated(true);
            return true;
        }else if(!leader.isPathCalculated()){
            boolean calcSuccess = calcPath(leader, empLeaders);
            if(!calcSuccess){
                empLeader.setPath("");
                empLeader.setPathCalculated(true);
                return false;
            }
        }
        empLeader.setPath(leader.getPath() + "/" + leader.getEmpId());
        empLeader.setPathCalculated(true);
        return true;
    }

    @Data
    public static class EmpLeader extends DataSimple{
        private String empId;
        private String empLeaderId;//不存
        private String path;
        private boolean pathCalculated;//不存
        private boolean travelled;//不存
    }

}
