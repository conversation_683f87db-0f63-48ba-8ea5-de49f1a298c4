package com.caidaocloud.hrpaas.service.domain.metadata.entity;

import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.SchemaStatus;
import com.googlecode.totallylazy.Option;
import lombok.Data;

@Data
public class MetadataTenantSchemaDo extends BaseEntity<IBaseRepository> {
    private String tenantId;

    /**
     * 文件夹目录
     */
    private Integer folder;

    /**
     * 版本
     */
    private Long version;

    /**
     * 脚本文件名称
     */
    private String scriptName;

    /**
     * 脚本描述
     */
    private String description;

    /**
     * 文件内容总条数
     */
    private Integer checksum;

    /**
     * 执行开始时间
     */
    private Long installedTime;

    /**
     * 执行完毕时间
     */
    private Long executionTime;

    /**
     * 执行状态
     */
    private SchemaStatus status;

    /**
     * 加载租户的最大版本的升级脚本
     * @param tenantId 租户id
     * @return
     */
    public static Option<MetadataTenantSchemaDo> load(String tenantId){
        Option<MetadataTenantSchemaDo> metadataSchema = repository(MetadataTenantSchemaDo.class).load(tenantId);
        return metadataSchema;
    }

    public void save(){
        this.insert();
    }

}
