package com.caidaocloud.hrpaas.service.application.uiform.dto;

import java.io.Serializable;

public class SysParmDict implements Serializable {
    private static final long serialVersionUID = 7567084695890126678L;

    private Integer dictId;

    private Integer typeId;

    private String dictCode;

    private String dictChnName;

    private String dictEngName;

    private Short levelNum;

    private Boolean isaction;

    private Short sortNum;

    private Integer domainId;

    private Integer crtuser;

    private Long crttime;

    private Integer upduser;

    private Long updtime;

    private Integer parentDictId;

    private Object dictNameLang;

    private String zh;
    private String en;
    private String ja;
    private String ko;

    public void setZh(String zh) {
        this.zh = zh;
    }

    public void setEn(String en) {
        this.en = en;
    }

    public void setJa(String ja) {
        this.ja = ja;
    }

    public void setKo(String ko) {
        this.ko = ko;
    }

    public String getLangTxt(String lang){
        if(null == lang){
            return this.dictChnName;
        }

        String langTxt = "";
        switch (lang){
            case "zh":
                langTxt = zh;
                break;
            case "en":
                langTxt = en;
                break;
            case "ja":
                langTxt = ja;
                break;
            case "ko":
                langTxt = ko;
                break;
            default:
                langTxt = dictChnName;
                break;
        }

        if(null == langTxt || "".equals(langTxt)){
            return dictChnName;
        }

        return langTxt;
    }

    public Integer getDictId() {
        return dictId;
    }

    public void setDictId(Integer dictId) {
        this.dictId = dictId;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public String getDictCode() {
        return dictCode;
    }

    public void setDictCode(String dictCode) {
        this.dictCode = dictCode;
    }

    public String getDictChnName() {
        return dictChnName;
    }

    public void setDictChnName(String dictChnName) {
        this.dictChnName = dictChnName;
    }

    public String getDictEngName() {
        return dictEngName;
    }

    public void setDictEngName(String dictEngName) {
        this.dictEngName = dictEngName;
    }

    public Short getLevelNum() {
        return levelNum;
    }

    public void setLevelNum(Short levelNum) {
        this.levelNum = levelNum;
    }

    public Boolean getIsaction() {
        return isaction;
    }

    public void setIsaction(Boolean isaction) {
        this.isaction = isaction;
    }

    public Short getSortNum() {
        return sortNum;
    }

    public void setSortNum(Short sortNum) {
        this.sortNum = sortNum;
    }

    public Integer getDomainId() {
        return domainId;
    }

    public void setDomainId(Integer domainId) {
        this.domainId = domainId;
    }

    public Integer getCrtuser() {
        return crtuser;
    }

    public void setCrtuser(Integer crtuser) {
        this.crtuser = crtuser;
    }

    public Long getCrttime() {
        return crttime;
    }

    public void setCrttime(Long crttime) {
        this.crttime = crttime;
    }

    public Integer getUpduser() {
        return upduser;
    }

    public void setUpduser(Integer upduser) {
        this.upduser = upduser;
    }

    public Long getUpdtime() {
        return updtime;
    }

    public void setUpdtime(Long updtime) {
        this.updtime = updtime;
    }

    public Integer getParentDictId() {
        return parentDictId;
    }

    public void setParentDictId(Integer parentDictId) {
        this.parentDictId = parentDictId;
    }

    public Object getDictNameLang() {
        return dictNameLang;
    }

    public void setDictNameLang(Object dictNameLang) {
        this.dictNameLang = dictNameLang;
    }
}

