package com.caidaocloud.hrpaas.service.application.condition.service;

import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionData;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;

/**
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
public interface FixedConditionFactory {
	boolean apply(EntityDef identifier, PropertyDef property);

	ConditionData fixedConditionData(String identifier, String identifierName, String property, String name);

}
