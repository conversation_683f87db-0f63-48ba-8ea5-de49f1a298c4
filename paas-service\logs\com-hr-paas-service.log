2025-04-21 13:44:51.669 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-04-21 13:44:51.680 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-04-21 13:44:53.398 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-04-21 13:44:53.761 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2025-04-21 13:45:01.047 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2025-04-21 13:45:06.516 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-21 13:45:06.521 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-04-21 13:45:06.668 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 123ms. Found 0 repository interfaces.
2025-04-21 13:45:09.771 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-04-21 13:45:09.779 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-04-21 13:45:10.708 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2025-04-21 13:45:11.134 [main] INFO  org.reflections.Reflections - Reflections took 390 ms to scan 2 urls, producing 16 keys and 379 values 
2025-04-21 13:45:11.231 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2025-04-21 13:45:16.739 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 13:45:16.740 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 13:45:16.793 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@14cc1f5c
2025-04-21 13:45:19.070 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:19.075 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:19.076 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:19.180 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:19.372 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:19.590 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:19.763 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:19.799 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:19.868 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:20.039 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:20.970 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:21.771 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-04-21 13:45:21.920 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-04-21 13:45:22.032 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-04-21 13:45:22.121 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2025-04-21 13:45:22.121 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2025-04-21 13:45:22.477 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:26.628 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:28.156 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 13:45:28.156 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 13:45:29.780 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:30.067 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:30.091 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:30.111 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:31.913 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3de473d9[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2025-04-21 13:45:31.914 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2fb3c642[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$b538f8de#prepareRecentTask]
2025-04-21 13:45:31.964 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:31.982 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:32.025 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:32.049 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:32.059 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:45:32.097 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-04-21 13:45:32.122 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-04-21 13:45:32.136 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-04-21 13:45:32.152 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-04-21 13:45:32.525 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-04-21 13:45:32.904 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2025-04-21 13:45:33.181 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2025-04-21 13:45:33.343 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2025-04-21 13:45:33.345 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2025-04-21 13:45:33.346 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2025-04-21 13:45:33.348 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2025-04-21 13:45:33.350 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2025-04-21 13:45:33.351 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2025-04-21 13:45:33.352 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2025-04-21 13:45:33.353 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2025-04-21 13:45:33.424 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-04-21 13:45:33.462 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-04-21 13:45:33.479 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2025-04-21 13:45:33.494 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2025-04-21 13:45:33.657 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2025-04-21 13:45:33.695 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2025-04-21 13:45:33.705 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2025-04-21 13:45:33.716 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-04-21 13:45:33.796 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-04-21 13:45:33.815 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2025-04-21 13:45:33.824 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2025-04-21 13:45:33.870 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2025-04-21 13:45:33.888 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-04-21 13:45:33.889 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-04-21 13:45:34.024 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-04-21 13:45:34.030 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2025-04-21 13:45:34.035 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2025-04-21 13:45:34.050 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2025-04-21 13:45:34.052 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2025-04-21 13:45:34.062 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2025-04-21 13:45:34.068 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2025-04-21 13:45:34.085 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2025-04-21 13:45:34.089 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2025-04-21 13:45:34.096 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2025-04-21 13:45:34.101 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2025-04-21 13:45:34.110 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2025-04-21 13:45:34.113 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2025-04-21 13:45:34.144 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2025-04-21 13:45:34.161 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2025-04-21 13:45:34.163 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2025-04-21 13:45:34.165 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2025-04-21 13:45:34.173 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2025-04-21 13:45:34.177 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2025-04-21 13:45:34.195 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-04-21 13:45:34.287 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2025-04-21 13:45:34.382 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-04-21 13:45:34.742 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-04-21 13:45:34.743 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-21 13:45:34.906 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-21 13:45:35.061 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-21 13:45:35.062 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-04-21 13:45:35.064 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-04-21 13:45:35.065 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-04-21 13:45:35.081 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-04-21 13:45:35.088 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-04-21 13:45:35.088 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-04-21 13:45:35.088 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-04-21 13:45:35.091 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-04-21 13:45:35.093 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-04-21 13:45:35.095 [Thread-40] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-04-21 13:45:35.262 [Thread-43] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-04-21 13:45:35.403 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-21 13:45:35.420 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-21 13:46:51.265 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-04-21 13:46:51.271 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-04-21 13:46:53.077 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-04-21 13:46:53.441 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2025-04-21 13:46:55.619 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2025-04-21 13:47:01.170 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-21 13:47:01.175 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-04-21 13:47:01.320 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 118ms. Found 0 repository interfaces.
2025-04-21 13:47:04.900 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-04-21 13:47:04.906 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-04-21 13:47:05.622 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2025-04-21 13:47:05.962 [main] INFO  org.reflections.Reflections - Reflections took 296 ms to scan 2 urls, producing 16 keys and 379 values 
2025-04-21 13:47:06.086 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2025-04-21 13:47:15.546 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 13:47:15.548 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 13:47:15.900 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@516370c1
2025-04-21 13:47:18.503 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:18.509 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:18.509 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:18.567 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:18.770 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:18.833 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:18.866 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:18.916 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:18.981 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:19.145 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:19.340 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:20.623 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-04-21 13:47:21.100 [redisson-netty-5-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-04-21 13:47:21.515 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-04-21 13:47:21.776 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2025-04-21 13:47:21.777 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2025-04-21 13:47:23.680 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:31.284 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:33.224 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 13:47:33.224 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 13:47:34.992 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:36.147 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:36.243 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:36.296 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:39.372 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6a275836[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2025-04-21 13:47:39.374 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1a6864f0[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$4a488927#prepareRecentTask]
2025-04-21 13:47:39.416 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:39.435 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:39.453 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:39.465 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:39.474 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:47:39.510 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-04-21 13:47:39.533 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-04-21 13:47:39.551 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-04-21 13:47:39.562 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-04-21 13:47:40.053 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-04-21 13:47:40.528 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2025-04-21 13:47:40.834 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2025-04-21 13:47:41.122 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2025-04-21 13:47:41.143 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2025-04-21 13:47:41.148 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2025-04-21 13:47:41.155 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2025-04-21 13:47:41.158 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2025-04-21 13:47:41.161 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2025-04-21 13:47:41.163 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2025-04-21 13:47:41.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2025-04-21 13:47:41.312 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-04-21 13:47:41.370 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-04-21 13:47:41.395 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2025-04-21 13:47:41.417 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2025-04-21 13:47:41.597 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2025-04-21 13:47:41.653 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2025-04-21 13:47:41.669 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2025-04-21 13:47:41.681 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-04-21 13:47:41.897 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-04-21 13:47:41.932 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2025-04-21 13:47:41.954 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2025-04-21 13:47:42.000 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2025-04-21 13:47:42.040 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-04-21 13:47:42.043 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-04-21 13:47:42.254 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-04-21 13:47:42.257 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2025-04-21 13:47:42.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2025-04-21 13:47:42.273 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2025-04-21 13:47:42.277 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2025-04-21 13:47:42.287 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2025-04-21 13:47:42.293 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2025-04-21 13:47:42.311 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2025-04-21 13:47:42.316 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2025-04-21 13:47:42.321 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2025-04-21 13:47:42.327 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2025-04-21 13:47:42.338 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2025-04-21 13:47:42.343 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2025-04-21 13:47:42.369 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2025-04-21 13:47:42.397 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2025-04-21 13:47:42.400 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2025-04-21 13:47:42.403 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2025-04-21 13:47:42.410 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2025-04-21 13:47:42.412 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2025-04-21 13:47:42.431 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-04-21 13:47:42.512 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2025-04-21 13:47:42.625 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-04-21 13:47:43.028 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-04-21 13:47:43.028 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-21 13:47:43.215 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-21 13:47:43.398 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-04-21 13:47:43.398 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-04-21 13:47:43.398 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-21 13:47:43.404 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-04-21 13:47:43.416 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-04-21 13:47:43.422 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-04-21 13:47:43.422 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-04-21 13:47:43.424 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-04-21 13:47:43.430 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-04-21 13:47:43.433 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-04-21 13:47:43.434 [Thread-40] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-04-21 13:47:43.602 [Thread-43] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-04-21 13:47:43.764 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-21 13:47:43.788 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-21 13:48:31.634 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-04-21 13:48:31.646 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-04-21 13:48:38.095 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-04-21 13:48:41.853 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2025-04-21 13:48:52.628 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2025-04-21 13:49:00.443 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-21 13:49:00.466 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-04-21 13:49:00.793 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 210ms. Found 0 repository interfaces.
2025-04-21 13:49:06.483 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-04-21 13:49:06.501 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-04-21 13:49:07.551 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2025-04-21 13:49:07.990 [main] INFO  org.reflections.Reflections - Reflections took 397 ms to scan 2 urls, producing 16 keys and 379 values 
2025-04-21 13:49:08.086 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2025-04-21 13:49:19.456 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 13:49:19.461 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 13:49:19.497 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@507e3e77
2025-04-21 13:49:22.871 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:22.880 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:22.880 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:22.954 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:23.131 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:23.195 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:23.227 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:23.273 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:23.319 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:23.452 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:23.606 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:24.865 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-04-21 13:49:25.307 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-04-21 13:49:25.498 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-04-21 13:49:25.605 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2025-04-21 13:49:25.605 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2025-04-21 13:49:26.388 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:33.895 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:35.586 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 13:49:35.586 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 13:49:37.089 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:37.588 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:37.645 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:37.679 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:43.225 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6258ea84[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2025-04-21 13:49:43.232 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6c720765[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$4a488927#prepareRecentTask]
2025-04-21 13:49:43.367 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:43.401 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:43.437 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:43.458 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:43.471 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 13:49:43.526 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-04-21 13:49:43.563 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-04-21 13:49:43.582 [Thread-42] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-04-21 13:49:43.688 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-04-21 13:49:45.107 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-04-21 13:49:45.987 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2025-04-21 13:49:46.392 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2025-04-21 13:49:46.618 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2025-04-21 13:49:46.621 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2025-04-21 13:49:46.626 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2025-04-21 13:49:46.631 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2025-04-21 13:49:46.635 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2025-04-21 13:49:46.637 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2025-04-21 13:49:46.643 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2025-04-21 13:49:46.648 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2025-04-21 13:49:46.798 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-04-21 13:49:47.140 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-04-21 13:49:47.270 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2025-04-21 13:49:47.300 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2025-04-21 13:49:47.553 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2025-04-21 13:49:47.660 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2025-04-21 13:49:47.682 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2025-04-21 13:49:47.709 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-04-21 13:49:47.853 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-04-21 13:49:47.899 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2025-04-21 13:49:47.925 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2025-04-21 13:49:47.993 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2025-04-21 13:49:48.020 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-04-21 13:49:48.023 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-04-21 13:49:48.582 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-04-21 13:49:48.591 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2025-04-21 13:49:48.599 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2025-04-21 13:49:48.682 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2025-04-21 13:49:48.717 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2025-04-21 13:49:48.769 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2025-04-21 13:49:48.814 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2025-04-21 13:49:48.937 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2025-04-21 13:49:48.975 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2025-04-21 13:49:49.000 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2025-04-21 13:49:49.029 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2025-04-21 13:49:49.080 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2025-04-21 13:49:49.097 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2025-04-21 13:49:49.299 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2025-04-21 13:49:49.470 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2025-04-21 13:49:49.478 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2025-04-21 13:49:49.485 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2025-04-21 13:49:49.499 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2025-04-21 13:49:49.508 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2025-04-21 13:49:49.566 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-04-21 13:49:49.702 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2025-04-21 13:49:49.999 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-04-21 13:49:50.897 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-04-21 13:49:50.898 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-21 13:49:51.414 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-21 13:50:50.694 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m28s637ms440µs600ns).
2025-04-21 14:03:46.356 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=12m56s186ms731µs100ns).
2025-04-21 14:03:46.558 [AMQP Connection ***************:5672] WARN  c.r.c.impl.ForgivingExceptionHandler - An unexpected connection driver error occured (Exception message: Connection reset)
2025-04-21 14:03:46.588 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-21 14:03:46.588 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-04-21 14:03:46.613 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-04-21 14:03:46.661 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-04-21 14:03:47.245 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-04-21 14:03:47.246 [Thread-42] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-04-21 14:03:47.246 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-04-21 14:03:47.263 [Thread-44] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-04-21 14:03:47.271 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-04-21 14:03:47.285 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-04-21 14:03:47.290 [Thread-41] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-04-21 14:03:48.444 [Thread-44] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-04-21 14:03:48.858 [Thread-44] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-21 14:03:48.897 [Thread-44] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-21 14:04:04.348 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-04-21 14:04:04.356 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-04-21 14:04:06.853 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-04-21 14:04:07.322 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2025-04-21 14:04:09.454 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2025-04-21 14:04:14.221 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-21 14:04:14.228 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-04-21 14:04:14.421 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 165ms. Found 0 repository interfaces.
2025-04-21 14:04:18.454 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-04-21 14:04:18.461 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-04-21 14:04:19.324 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2025-04-21 14:04:19.689 [main] INFO  org.reflections.Reflections - Reflections took 317 ms to scan 2 urls, producing 16 keys and 379 values 
2025-04-21 14:04:19.793 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2025-04-21 14:04:27.142 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 14:04:27.143 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 14:04:27.171 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@516370c1
2025-04-21 14:04:29.845 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:29.852 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:29.854 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:29.953 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:30.186 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:30.279 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:30.319 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:30.386 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:30.471 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:30.725 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:30.947 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:31.837 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-04-21 14:04:32.076 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-04-21 14:04:32.258 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-04-21 14:04:32.356 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2025-04-21 14:04:32.356 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2025-04-21 14:04:32.867 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:39.707 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:42.661 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 14:04:42.662 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 14:04:44.700 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:45.422 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:45.478 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:45.513 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:50.284 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5c26ab0a[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2025-04-21 14:04:50.287 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@774f2d7f[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$4a488927#prepareRecentTask]
2025-04-21 14:04:50.350 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:50.375 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:50.403 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:50.417 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:50.428 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:04:50.475 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-04-21 14:04:50.505 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-04-21 14:04:50.530 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-04-21 14:04:50.541 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-04-21 14:04:51.467 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-04-21 14:04:52.842 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2025-04-21 14:04:53.493 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2025-04-21 14:04:53.982 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2025-04-21 14:04:53.995 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2025-04-21 14:04:53.997 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2025-04-21 14:04:53.998 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2025-04-21 14:04:54.000 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2025-04-21 14:04:54.001 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2025-04-21 14:04:54.002 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2025-04-21 14:04:54.003 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2025-04-21 14:04:54.284 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-04-21 14:04:54.415 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-04-21 14:04:54.814 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2025-04-21 14:04:54.842 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2025-04-21 14:04:55.088 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2025-04-21 14:04:55.137 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2025-04-21 14:04:55.145 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2025-04-21 14:04:55.155 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-04-21 14:04:55.242 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-04-21 14:04:55.267 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2025-04-21 14:04:55.278 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2025-04-21 14:04:55.323 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2025-04-21 14:04:55.342 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-04-21 14:04:55.343 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-04-21 14:04:55.451 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-04-21 14:04:55.454 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2025-04-21 14:04:55.457 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2025-04-21 14:04:55.468 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2025-04-21 14:04:55.472 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2025-04-21 14:04:55.481 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2025-04-21 14:04:55.487 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2025-04-21 14:04:55.503 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2025-04-21 14:04:55.507 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2025-04-21 14:04:55.511 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2025-04-21 14:04:55.516 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2025-04-21 14:04:55.525 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2025-04-21 14:04:55.530 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2025-04-21 14:04:55.584 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2025-04-21 14:04:55.612 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2025-04-21 14:04:55.615 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2025-04-21 14:04:55.617 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2025-04-21 14:04:55.624 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2025-04-21 14:04:55.628 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2025-04-21 14:04:55.652 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-04-21 14:04:55.734 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2025-04-21 14:04:55.846 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-04-21 14:04:56.233 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-04-21 14:04:56.233 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-21 14:04:56.426 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-21 14:05:14.181 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=47s754ms119µs500ns).
2025-04-21 14:05:50.005 [Thread-35] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-04-21 14:05:50.007 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-21 14:05:50.009 [Thread-35] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-04-21 14:05:50.011 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-04-21 14:05:50.035 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-04-21 14:05:50.042 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-04-21 14:05:50.044 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-04-21 14:05:50.044 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-04-21 14:05:50.047 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-04-21 14:05:50.052 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-04-21 14:05:50.054 [Thread-40] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-04-21 14:05:50.246 [Thread-43] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-04-21 14:05:50.400 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-21 14:05:50.411 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-21 14:06:41.251 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-04-21 14:06:41.257 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-04-21 14:06:45.751 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-04-21 14:06:46.491 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2025-04-21 14:06:47.746 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2025-04-21 14:06:51.071 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-21 14:06:51.077 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-04-21 14:06:51.263 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 159ms. Found 0 repository interfaces.
2025-04-21 14:06:54.434 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-04-21 14:06:54.440 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-04-21 14:06:55.286 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2025-04-21 14:06:55.572 [main] INFO  org.reflections.Reflections - Reflections took 253 ms to scan 2 urls, producing 16 keys and 379 values 
2025-04-21 14:06:55.661 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2025-04-21 14:07:01.521 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 14:07:01.524 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 14:07:01.573 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@66694050
2025-04-21 14:07:05.116 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:05.124 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:05.126 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:05.234 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:05.475 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:05.565 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:05.611 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:05.701 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:05.778 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:06.050 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:06.603 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:09.337 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-04-21 14:07:10.317 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-04-21 14:07:10.493 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-04-21 14:07:10.598 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2025-04-21 14:07:10.599 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2025-04-21 14:07:11.660 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:22.654 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:23.620 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 14:07:23.621 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 14:07:26.671 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:27.397 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:27.457 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:27.504 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:30.468 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6eef7af3[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2025-04-21 14:07:30.470 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6de6467a[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$4a488927#prepareRecentTask]
2025-04-21 14:07:30.532 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:30.554 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:30.578 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:30.593 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:30.607 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:07:30.652 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-04-21 14:07:30.678 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-04-21 14:07:30.700 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-04-21 14:07:30.720 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-04-21 14:07:32.383 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-04-21 14:07:32.828 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2025-04-21 14:07:33.176 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2025-04-21 14:07:33.371 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2025-04-21 14:07:33.373 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2025-04-21 14:07:33.375 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2025-04-21 14:07:33.378 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2025-04-21 14:07:33.380 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2025-04-21 14:07:33.382 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2025-04-21 14:07:33.384 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2025-04-21 14:07:33.386 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2025-04-21 14:07:33.459 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-04-21 14:07:33.499 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-04-21 14:07:33.518 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2025-04-21 14:07:33.532 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2025-04-21 14:07:33.633 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2025-04-21 14:07:33.659 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2025-04-21 14:07:33.668 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2025-04-21 14:07:33.674 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-04-21 14:07:33.735 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-04-21 14:07:33.749 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2025-04-21 14:07:33.758 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2025-04-21 14:07:33.784 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2025-04-21 14:07:33.798 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-04-21 14:07:33.799 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-04-21 14:07:33.902 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-04-21 14:07:33.905 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2025-04-21 14:07:33.908 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2025-04-21 14:07:33.919 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2025-04-21 14:07:33.923 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2025-04-21 14:07:33.933 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2025-04-21 14:07:33.940 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2025-04-21 14:07:33.955 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2025-04-21 14:07:33.958 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2025-04-21 14:07:33.962 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2025-04-21 14:07:33.967 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2025-04-21 14:07:33.975 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2025-04-21 14:07:33.979 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2025-04-21 14:07:34.003 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2025-04-21 14:07:34.019 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2025-04-21 14:07:34.021 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2025-04-21 14:07:34.022 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2025-04-21 14:07:34.027 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2025-04-21 14:07:34.029 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2025-04-21 14:07:34.045 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-04-21 14:07:34.112 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2025-04-21 14:07:34.203 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-04-21 14:07:34.600 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-04-21 14:07:34.600 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-21 14:07:34.791 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-21 14:08:29.247 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=47s375ms918µs).
2025-04-21 14:22:16.822 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=13m47s521ms560µs400ns).
2025-04-21 14:22:17.107 [AMQP Connection ***************:5672] WARN  c.r.c.impl.ForgivingExceptionHandler - An unexpected connection driver error occured (Exception message: Connection reset)
2025-04-21 14:22:17.255 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-21 14:22:17.281 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-04-21 14:22:17.352 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-04-21 14:22:17.360 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-04-21 14:22:18.227 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-04-21 14:22:18.232 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-04-21 14:22:18.233 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-04-21 14:22:18.236 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-04-21 14:22:18.242 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-04-21 14:22:18.244 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-04-21 14:22:18.246 [Thread-40] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-04-21 14:22:18.681 [Thread-43] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-04-21 14:22:18.859 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-21 14:22:18.911 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-21 14:22:32.085 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-04-21 14:22:32.095 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-04-21 14:22:34.737 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-04-21 14:22:35.620 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2025-04-21 14:22:39.512 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2025-04-21 14:22:42.018 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-21 14:22:42.023 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-04-21 14:22:42.207 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 157ms. Found 0 repository interfaces.
2025-04-21 14:22:46.607 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-04-21 14:22:46.615 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-04-21 14:22:47.535 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2025-04-21 14:22:47.969 [main] INFO  org.reflections.Reflections - Reflections took 372 ms to scan 2 urls, producing 16 keys and 379 values 
2025-04-21 14:22:48.191 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2025-04-21 14:22:54.774 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 14:22:54.774 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 14:22:54.803 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@516370c1
2025-04-21 14:22:58.250 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:22:58.255 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:22:58.255 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:22:58.313 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:22:58.422 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:22:58.471 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:22:58.491 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:22:58.532 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:22:58.574 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:22:58.665 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:22:58.806 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:22:59.330 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-04-21 14:22:59.852 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-04-21 14:23:00.088 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-04-21 14:23:00.178 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2025-04-21 14:23:00.179 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2025-04-21 14:23:00.685 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:23:10.986 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:23:12.126 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 14:23:12.127 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 14:23:14.133 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:23:14.792 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:23:14.846 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:23:14.937 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:23:17.780 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6c720765[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2025-04-21 14:23:17.782 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6a275836[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$4a488927#prepareRecentTask]
2025-04-21 14:23:17.830 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:23:17.999 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:23:18.205 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:23:18.261 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:23:18.272 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:23:19.103 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-04-21 14:23:19.172 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-04-21 14:23:19.217 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-04-21 14:23:19.226 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-04-21 14:23:19.675 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-04-21 14:23:20.079 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2025-04-21 14:23:20.332 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2025-04-21 14:23:20.475 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2025-04-21 14:23:20.477 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2025-04-21 14:23:20.478 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2025-04-21 14:23:20.480 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2025-04-21 14:23:20.482 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2025-04-21 14:23:20.484 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2025-04-21 14:23:20.486 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2025-04-21 14:23:20.487 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2025-04-21 14:23:20.555 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-04-21 14:23:20.595 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-04-21 14:23:20.613 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2025-04-21 14:23:20.632 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2025-04-21 14:23:20.769 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2025-04-21 14:23:20.811 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2025-04-21 14:23:20.824 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2025-04-21 14:23:20.834 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-04-21 14:23:20.965 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-04-21 14:23:20.987 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2025-04-21 14:23:21.001 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2025-04-21 14:23:21.044 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2025-04-21 14:23:21.066 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-04-21 14:23:21.068 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-04-21 14:23:21.203 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-04-21 14:23:21.208 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2025-04-21 14:23:21.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2025-04-21 14:23:21.227 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2025-04-21 14:23:21.230 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2025-04-21 14:23:21.242 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2025-04-21 14:23:21.249 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2025-04-21 14:23:21.268 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2025-04-21 14:23:21.274 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2025-04-21 14:23:21.278 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2025-04-21 14:23:21.282 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2025-04-21 14:23:21.291 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2025-04-21 14:23:21.298 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2025-04-21 14:23:21.336 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2025-04-21 14:23:21.362 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2025-04-21 14:23:21.364 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2025-04-21 14:23:21.366 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2025-04-21 14:23:21.372 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2025-04-21 14:23:21.375 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2025-04-21 14:23:21.395 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-04-21 14:23:21.479 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2025-04-21 14:23:21.578 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-04-21 14:23:22.785 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-04-21 14:23:22.786 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-21 14:23:23.018 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-21 14:23:23.091 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-04-21 14:23:23.092 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-21 14:23:23.093 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-04-21 14:23:23.095 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-04-21 14:23:23.105 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-04-21 14:23:23.137 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-04-21 14:23:23.138 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-04-21 14:23:23.138 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-04-21 14:23:23.139 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-04-21 14:23:23.140 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-04-21 14:23:23.143 [Thread-40] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-04-21 14:23:23.283 [Thread-43] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-04-21 14:23:23.422 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-21 14:23:23.430 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-21 14:24:16.144 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-04-21 14:24:16.151 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-04-21 14:24:18.604 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-04-21 14:24:19.120 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2025-04-21 14:24:21.794 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2025-04-21 14:24:26.714 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-21 14:24:26.720 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-04-21 14:24:26.878 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 132ms. Found 0 repository interfaces.
2025-04-21 14:24:30.352 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-04-21 14:24:30.359 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-04-21 14:24:31.249 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2025-04-21 14:24:31.587 [main] INFO  org.reflections.Reflections - Reflections took 295 ms to scan 2 urls, producing 16 keys and 379 values 
2025-04-21 14:24:31.732 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2025-04-21 14:24:37.430 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 14:24:37.431 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 14:24:37.566 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@516370c1
2025-04-21 14:24:41.684 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:41.692 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:41.693 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:41.768 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:41.919 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:41.977 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:42.001 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:42.037 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:42.080 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:42.192 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:42.370 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:43.109 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-04-21 14:24:43.389 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-04-21 14:24:43.581 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-04-21 14:24:43.673 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2025-04-21 14:24:43.673 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2025-04-21 14:24:44.211 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:53.415 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:54.757 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-21 14:24:54.757 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-21 14:24:57.501 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:59.259 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:59.325 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:24:59.369 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:25:05.396 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6c720765[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2025-04-21 14:25:05.400 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6a275836[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$16d11579#prepareRecentTask]
2025-04-21 14:25:05.493 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:25:05.524 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:25:05.555 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:25:05.579 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:25:05.594 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-04-21 14:25:05.669 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-04-21 14:25:05.720 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-04-21 14:25:05.764 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-04-21 14:25:05.771 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-04-21 14:25:06.554 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-04-21 14:25:08.217 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2025-04-21 14:25:09.065 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2025-04-21 14:25:09.293 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2025-04-21 14:25:09.296 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2025-04-21 14:25:09.299 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2025-04-21 14:25:09.302 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2025-04-21 14:25:09.305 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2025-04-21 14:25:09.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2025-04-21 14:25:09.309 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2025-04-21 14:25:09.312 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2025-04-21 14:25:09.401 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-04-21 14:25:09.438 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-04-21 14:25:09.468 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2025-04-21 14:25:09.492 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2025-04-21 14:25:09.668 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2025-04-21 14:25:09.726 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2025-04-21 14:25:09.743 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2025-04-21 14:25:09.752 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-04-21 14:25:09.859 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-04-21 14:25:09.896 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2025-04-21 14:25:09.910 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2025-04-21 14:25:09.946 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2025-04-21 14:25:09.960 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-04-21 14:25:09.961 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-04-21 14:25:10.112 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-04-21 14:25:10.117 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2025-04-21 14:25:10.122 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2025-04-21 14:25:10.137 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2025-04-21 14:25:10.145 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2025-04-21 14:25:10.164 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2025-04-21 14:25:10.175 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2025-04-21 14:25:10.193 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2025-04-21 14:25:10.198 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2025-04-21 14:25:10.202 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2025-04-21 14:25:10.206 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2025-04-21 14:25:10.214 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2025-04-21 14:25:10.217 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2025-04-21 14:25:10.246 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2025-04-21 14:25:10.268 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2025-04-21 14:25:10.269 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2025-04-21 14:25:10.271 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2025-04-21 14:25:10.280 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2025-04-21 14:25:10.284 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2025-04-21 14:25:10.312 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-04-21 14:25:10.423 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2025-04-21 14:25:10.550 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-04-21 14:25:11.685 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-04-21 14:25:11.685 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-21 14:25:12.510 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-21 14:25:12.563 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-04-21 14:25:12.564 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-21 14:25:12.564 [Thread-36] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-04-21 14:25:12.569 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-04-21 14:25:12.584 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-04-21 14:25:12.593 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-04-21 14:25:12.595 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-04-21 14:25:12.595 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-04-21 14:25:12.596 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-04-21 14:25:12.596 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-04-21 14:25:12.597 [Thread-40] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-04-21 14:25:12.799 [Thread-43] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-04-21 14:25:12.966 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-21 14:25:13.000 [Thread-43] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
