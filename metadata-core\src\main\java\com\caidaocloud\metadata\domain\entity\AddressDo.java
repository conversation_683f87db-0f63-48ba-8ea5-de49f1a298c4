package com.caidaocloud.metadata.domain.entity;

import com.caidaocloud.metadata.domain.repository.IAddressRepository;
import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import lombok.Data;

import java.util.List;

@Data
public class AddressDo extends BaseEntity<IBaseRepository> {
    public final static String ROOT_PID = "0", ADDRESS_KEY = "address_%s", ADDRESS_ALL_KEY = "address_all";

    private String address;
    /**
     * 行政区划代码
     */
    private String code;
    private String pidPath;
    private String i18n;
    private String type;
    /**
     * 是否删除。0 默认值，未删除，1 已删除
     */
    private boolean deleted;

    public static List<AddressDo> getAddressList(List<String> list) {
        return ((IAddressRepository) repository(AddressDo.class)).getAddressList(list);
    }

    public void save(){
        this.insert();
    }

    @Override
    public void update() {
        repository(AddressDo.class).update(this);
    }

    public static List<AddressDo> getList(int pageNo, int pageSize){
        return ((IAddressRepository) repository(AddressDo.class)).getList(new AddressDo(), pageNo, pageSize);
    }

    public static List<AddressDo> getAllList(String pidPath){
        return ((IAddressRepository) repository(AddressDo.class)).getAllList(pidPath);
    }
}
