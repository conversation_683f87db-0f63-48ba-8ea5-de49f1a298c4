{"childList": [{"childList": [{"key": "Input_jvbFZxM2cD76WNJuadRpfZ", "type": "Input", "label": "单行文本", "props": {"label": "员工工号", "prop": "workno", "placeholder": "请输入员工工号", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true"}], "show": true}}, {"key": "Input_9e9L1MnxP6WaLcPGXPQDAu", "type": "Input", "label": "单行文本", "props": {"label": "员工英文名", "prop": "enName", "placeholder": "请输入员工英文名", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "false"}], "show": true}}, {"key": "DatePicker_7CubNP3eEQ5RQ9S99oYkH4", "type": "DatePicker", "label": "日期", "props": {"label": "入职日期", "prop": "hireDate", "placeholder": "请选择入职日期", "dataType": "Timestamp", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "TreeSelect_wnH4iQ9aHeUCZCcRpKsNmx", "type": "TreeSelect", "label": "组织", "props": {"label": "所属组织", "prop": "organize", "placeholder": "请输入所属组织", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "TreeSelect_rzDGThZdoYoVU7WuxnWudU", "type": "TreeSelect", "label": "岗位", "props": {"label": "岗位", "prop": "post", "placeholder": "请输入岗位", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_eiWVTGXk9xsxjqpMpm4MvV", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "用工类型", "prop": "empType", "placeholder": "请输入用工类型", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "EmployType"}, "rules": [], "show": true}}, {"key": "undefined_jp9EDv9fsMzcKgBZZZqV3F", "props": {"label": "合同公司", "prop": "company", "placeholder": "请输入合同公司", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_t9c7MvFx3f1PqsGuZnAzfd", "type": "DatePicker", "label": "日期", "props": {"label": "预计毕业日期", "prop": "expectGraduateDate", "placeholder": "请选择预计毕业日期", "dataType": "Timestamp", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_qnmfpLGC2cT7JMYBgMVDSD", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "合同类型", "prop": "contractType", "placeholder": "请输入合同类型", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "ContractType"}, "rules": [], "show": true}}, {"key": "Input_sVNg3j6znzBR3QmZKmL7mf", "type": "Input", "label": "单行文本", "props": {"label": "岗位名称", "prop": "postTxt", "placeholder": "请输入岗位名称", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_mdhc9a2217ARz1wWKgaAzc", "type": "DatePicker", "label": "日期", "props": {"label": "转正日期", "prop": "confirmationDate", "placeholder": "请选择转正日期", "dataType": "Timestamp", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_wE5kYQptTzL7GgES4eokXj", "type": "Input", "label": "单行文本", "props": {"label": "员工公司邮箱", "prop": "companyEmail", "placeholder": "请输入员工公司邮箱", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_txMEgUSZamE18TLcFnqz88", "type": "Input", "label": "单行文本", "props": {"label": "工作地名称", "prop": "workplaceTxt", "placeholder": "请输入工作地名称", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_pnJQZ3vu3kmPuXMgTdczhD", "type": "Select", "label": "下拉单选", "props": {"label": "员工状态", "prop": "empStatus", "placeholder": "请输入员工状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "在职", "value": "0"}, {"label": "离职", "value": "1"}, {"label": "试用期", "value": "2"}], "value": "0", "rules": [], "show": true}}, {"key": "EmployeePicker_jd7ShyTRpvCVGW9wwFxsDA", "type": "EmployeePicker", "label": "选人组件", "props": {"label": "直接上级", "prop": "leadEmpId", "placeholder": "请输入直接上级", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_vizHWK8xJWWA8U3iK7RbT2", "type": "Input", "label": "单行文本", "props": {"label": "成本中心", "prop": "costCenters", "placeholder": "请输入成本中心", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}], "label": "候选人任职信息", "type": "EmpTab", "key": "entity.onboarding.EmpWorkInfo", "props": {"show": true, "disabled": false}}, {"childList": [{"key": "Input_asTVJVXpPFoodGpcg5nVhy", "type": "Input", "label": "单行文本", "props": {"label": "员工姓名", "prop": "name", "placeholder": "请输入员工姓名", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true"}], "show": true}}, {"key": "Input_hNj7knGuucWpScAtM7Tjq8", "type": "Input", "label": "单行文本", "props": {"label": "证件号", "prop": "cardNo", "placeholder": "请输入证件号", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true"}], "show": true}}, {"key": "Input_9ZGvgVGPXXW6XaGyGTMu5N", "type": "Input", "label": "单行文本", "props": {"label": "手机号", "prop": "phone", "placeholder": "请输入手机号", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true"}], "show": true}}, {"key": "EnumSelect_qyCAJDQrPtwxEhSbtXGv4s", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "员工性别", "prop": "sex", "placeholder": "请输入员工性别", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Gender"}, "rules": [], "show": true}}, {"key": "EnumSelect_wLvtL7TwTGrYGeTmBhNL1F", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "国籍", "prop": "nationality", "placeholder": "请输入国籍", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Nationality"}, "rules": [], "show": true}}, {"key": "DatePicker_4pNhfksxFxfogtMhPHuVeq", "type": "DatePicker", "label": "日期", "props": {"label": "出生日期", "prop": "birthDate", "placeholder": "请选择出生日期", "dataType": "Timestamp", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_gvSU2PKkuupjKUzfUCfkFA", "type": "Input", "label": "单行文本", "props": {"label": "员工个人邮箱", "prop": "email", "placeholder": "请输入员工个人邮箱", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Switch_opceWiFKjeRy3JXXWvUuSA", "type": "Switch", "label": "开关", "props": {"label": "是否残疾", "prop": "disability", "placeholder": "请输入是否残疾", "dataType": "Boolean", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_m9zjh2CZo13Bqj3ZG9rUeG", "type": "Input", "label": "单行文本", "props": {"label": "监护人姓名", "prop": "guardianName", "placeholder": "请输入监护人姓名", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_3wNbwf3rojtJQHbq96nrVR", "type": "Input", "label": "单行文本", "props": {"label": "监护人手机号", "prop": "guardianPhone", "placeholder": "请输入监护人手机号", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_qWao45jmqFzmYEDfREZZMU", "type": "InputNumber", "label": "数值", "props": {"label": "年龄", "prop": "divisionAge", "placeholder": "请输入年龄", "dataType": "Number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_eCoP1c5YF8Cs6gFbNL6Dap", "type": "Select", "label": "下拉单选", "props": {"label": "婚姻状态", "prop": "maritalStatus", "placeholder": "请输入婚姻状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "未婚", "value": "0"}, {"label": "已婚", "value": "1"}, {"label": "丧偶", "value": "2"}], "rules": [], "show": true}}, {"key": "Select_iWUh3pLAuHwGDsL6Ye4qdU", "type": "Select", "label": "下拉单选", "props": {"label": "生育状态", "prop": "fertilityStatus", "placeholder": "请输入生育状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "未育", "value": "0"}, {"label": "一胎", "value": "1"}, {"label": "二胎", "value": "2"}, {"label": "多胎", "value": "3"}], "rules": [], "show": true}}, {"key": "Input_ry69vCipz81tpf6DczSTnY", "type": "Input", "label": "单行文本", "props": {"label": "员工英文名", "prop": "enName", "placeholder": "请输入员工英文名", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_oqX6asA2cGxsuwapkg7B9M", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "民族", "prop": "nation", "placeholder": "请输入民族", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Nation"}, "rules": [], "show": true}}, {"key": "Input_r9nmp7udqEyhDPfxQHMytk", "type": "Input", "label": "单行文本", "props": {"label": "籍贯", "prop": "nativePlace", "placeholder": "请输入籍贯", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_qCWmQNL73nAyYHhqjeiK87", "type": "Input", "label": "单行文本", "props": {"label": "户籍地址", "prop": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "请输入户籍地址", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_feVpGaDsQc4uQm845atBtk", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "政治面貌", "prop": "politicalOutlook", "placeholder": "请输入政治面貌", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "PoliticalP<PERSON>y"}, "rules": [], "show": true}}, {"key": "Switch_5giwwCLVozjCms6LVWfFAv", "type": "Switch", "label": "开关", "props": {"label": "是否满18周岁", "prop": "adult", "placeholder": "请输入是否满18周岁", "dataType": "Boolean", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}], "label": "候选人个人信息", "type": "EmpTab", "key": "entity.onboarding.EmpPrivateInfo", "props": {"show": true, "disabled": false}}], "label": "候选人员工信息", "type": "EmpInfo", "key": "page.detail.onboardingEmpInfo", "script": "/**\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系才到平台的技术支持获得服务（可能收费）。\n* 我们可以用 JS 面板来开发一些定制度高功能。\n* 你可以点击面板上方的 「使用帮助」了解。\n*/\n\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\nexport function didMount() {\n\tconsole.log(`「页面 JS」：当前页面地址 ${location.href}`);\n // 更多相关 API 请参考：https://aliwork.com/developer/API\n}"}