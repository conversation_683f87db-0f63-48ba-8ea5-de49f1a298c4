package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Data
@DataComponent(dataType = PropertyDataType.Attachment)
@Slf4j
public class Attachment implements ComponentPropertyValue{

    private List<String> names;

    private List<String> urls;

    @Override
    public Map<String, Boolean> propertySuffixToWhetherLong(){
        return Maps.map(
                Sequences.sequence(
                        Pair.pair(".names", true),
                        Pair.pair(".urls", true)
                )
        );
    }

    @Override
    public PropertyValue extractDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties) {
        val attachment = properties.stream()
                .filter(propertyDataDto -> propertyDataDto.getProperty().startsWith(propDef.getProperty() + "."))
                .findFirst();
        if(attachment.isPresent()) {
            Attachment attach = new Attachment();
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals(propDef.getProperty() + ".names"))
                    .findFirst().ifPresent(names -> attach.setNames(names.getArrayValues()));
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals(propDef.getProperty() + ".urls"))
                    .findFirst().ifPresent(urls -> attach.setUrls(urls.getArrayValues()));
            return attach;
        }else{
            return null;
        }
    }

    @Override
    public List<PropertyDataDto> extractPersist(DataSimple data, MetadataPropertyVo propDef) {
        List<PropertyDataDto> result = Lists.newArrayList();
        val dataMap = JsonEnhanceUtil.toObject(data, Map.class);
        val customPropertyValue = data.getProperties().get(propDef.getProperty());
        Attachment attach = null;
        if(dataMap.containsKey(propDef.getProperty())){
            val standardPropertyValue = dataMap.get(propDef.getProperty());
            if(null != standardPropertyValue){
                attach = JsonEnhanceUtil.toObject(standardPropertyValue, Attachment.class);
            }
        }else if(null != customPropertyValue){
            if(customPropertyValue instanceof Attachment){
                attach = (Attachment) customPropertyValue;
            }else{
                attach = FastjsonUtil.toObject(
                        ((SimplePropertyValue)customPropertyValue).getValue(), Attachment.class);
            }
        }else {
            attach = handleFlatProperty(data, propDef.getProperty());
        }
        if(null == attach){
            attach = new Attachment();
        }
        if(null != attach){
            PropertyDataDto names = new PropertyDataDto();
            names.setProperty(propDef.getProperty() + ".names");
            names.setDataType(PropertyDataType.Attachment);
            names.setArrayValues(attach.getNames());
            result.add(names);
            PropertyDataDto urls = new PropertyDataDto();
            urls.setProperty(propDef.getProperty() + ".urls");
            urls.setDataType(PropertyDataType.Attachment);
            urls.setArrayValues(attach.getUrls());
            result.add(urls);
        }
        return result;
    }

    private static Attachment handleFlatProperty(DataSimple data, String prefix) {
        String nk = String.format("%s.names", prefix), uk = String.format("%s.urls", prefix);
        SimplePropertyValue nv = (SimplePropertyValue) data.getProperties().get(nk), uv = (SimplePropertyValue) data.getProperties().get(uk);
        if ( uv ==null) {
            return null;
        }
        Attachment attachment = new Attachment();
        if (nv != null) {
            attachment.setNames(nv.getArrayValues());
        }
        attachment.setUrls(uv.getArrayValues());
        return attachment;
    }

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {
        if(null == dataVal){
            return;
        }
        dataValue.fitDataValue(dataVal);
    }

    @Override
    public String toText() {
        return names == null ? "" : StringUtils.join(names, ",");
    }
}
