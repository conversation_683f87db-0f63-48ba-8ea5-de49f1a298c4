package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.hrpaas.service.domain.display.entity.DisplayPropertyDef;
import com.caidaocloud.hrpaas.service.domain.display.repository.IDisplayPropertyDefRepository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.DisplayPropertyDefPo;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class DisplayPropertyDefRepositoryImpl implements IDisplayPropertyDefRepository {
    @Resource
    private DisplayPropertyDefMapper displayPropertyDefMapper;

    @Override
    public String insert(DisplayPropertyDef entity) {
        DisplayPropertyDefPo po = DisplayPropertyDefPo.fromEntity(DisplayPropertyDefPo.class, entity);
        displayPropertyDefMapper.insert(po);
        return po.getId().toString();
    }

    @Override
    public void update(DisplayPropertyDef entity) {
        DisplayPropertyDefPo po = DisplayPropertyDefPo.fromEntity(DisplayPropertyDefPo.class, entity);
        displayPropertyDefMapper.updateById(po);
    }

    @Override
    public DisplayPropertyDef loadByCode(String code) {
        QueryWrapper<DisplayPropertyDefPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        DisplayPropertyDefPo po = displayPropertyDefMapper.selectOne(queryWrapper);
        return po != null ? po.toEntity(DisplayPropertyDef.class) : null;
    }
}
