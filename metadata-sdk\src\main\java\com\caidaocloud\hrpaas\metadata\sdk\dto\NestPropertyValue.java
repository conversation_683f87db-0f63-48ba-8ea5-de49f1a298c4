package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.google.common.collect.Lists;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;

public class NestPropertyValue extends HashMap<String, PropertyValue> implements PropertyValue {

    public NestPropertyValue add(String property, PropertyValue value){
        this.put(property, value);
        return this;
    }

    public NestPropertyValue add(String property, String value){
        this.put(property, new SimplePropertyValue(value));
        return this;
    }

    public NestPropertyValue add(String property, List<String> values){
        this.put(property, new SimplePropertyValue(values));
        return this;
    }

    public static NestPropertyValue toDisplay(List<PropertyDataDto> properties, MetadataPropertyVo propDef, String addParentPath){
        val nestDto = properties.stream()
                .filter(propertyDataDto -> propertyDataDto.getProperty().startsWith(StringUtils.trimToEmpty(addParentPath) + propDef.getProperty() + "."))
                .findFirst();
        if(nestDto.isPresent()){
            NestPropertyValue result = new NestPropertyValue();
            propDef.getObjDef().forEach(subPropDef->{
                if(PropertyDataType.Object.equals(subPropDef.getDataType())){
                    val nestValue = NestPropertyValue.toDisplay(properties, subPropDef, StringUtils.trimToEmpty(addParentPath) + propDef.getProperty() + ".");
                    if(null != nestValue){
                        result.put(subPropDef.getProperty(), nestValue);
                    }
                }else{
                    val simpleValue = SimplePropertyValue.toDisplay(properties, subPropDef, StringUtils.trimToEmpty(addParentPath) + propDef.getProperty() + ".");
                    if(null != simpleValue){
                        result.put(subPropDef.getProperty(), simpleValue);
                    }
                }
            });
            return result;
        }else{
            return null;
        }
    }

    public static List<PropertyDataDto> toPersist(DataSimple data, MetadataPropertyVo propDef){
        val customPropertyValue = (NestPropertyValue) data.getProperties().get(propDef.getProperty());
        if(null != customPropertyValue){
            return customPropertyValue.toPropertyData(propDef, null);
        }
        return Lists.newArrayList();
    }

    private List<PropertyDataDto> toPropertyData(MetadataPropertyVo propDef, String parentPath){
        List<PropertyDataDto> result = Lists.newArrayList();
        propDef.getObjDef().forEach(subPropDef->{
            if(subPropDef.getDataType().equals(PropertyDataType.Object)){
                val subPropertyValue = (NestPropertyValue) this.get(subPropDef.getProperty());
                result.addAll(subPropertyValue.toPropertyData(subPropDef, parentPath == null? propDef.getProperty():parentPath + "."+propDef.getProperty()));
            }else{
                val subPropertyValue = (SimplePropertyValue) this.get(subPropDef.getProperty());
                if(null != subPropertyValue){
                    result.add(subPropertyValue.toPropertyData(subPropDef, parentPath == null? propDef.getProperty() + ".":parentPath + "."+propDef.getProperty() + "."));
                }
            }
        });
        return result;
    }

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {

    }

    @Override
    public String toText() {
        throw new ServerException("Function not supported");
    }

}
