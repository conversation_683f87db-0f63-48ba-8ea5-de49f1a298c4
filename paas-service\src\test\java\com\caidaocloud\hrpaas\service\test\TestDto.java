package com.caidaocloud.hrpaas.service.test;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.infrastructure.util.RandomUtil;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormBasicsDto;
import com.caidaocloud.util.FastjsonUtil;

import java.util.List;
import java.util.Map;

public class TestDto {
    private final static TestDto td = new TestDto();

    private TestDto(){
        System.out.println("我执行了");
    }

    public static void testUiFormBasicsDto(){
        String str = "{\"formType\":\"manage\",\"joinModel\":\"entity.hr.ceshi123\",\"pageTemplate\":\"GENERAL_LIST_PAGE\",\"name\":\"测试页面6\",\"menuName\":\"测试页面-6\",\"showNav\":true,\"schema\":{\"key\":\"Page_edZxKjzWJdBA6DPrgHAFyb\",\"type\":\"Page\",\"label\":\"页面\",\"props\":{\"padding\":\"12px 12px 12px 12px\"},\"childList\":[{\"key\":\"Search_cgeCdKdS9SCLy23w4Ui2dc\",\"type\":\"Search\",\"label\":\"筛选\",\"props\":{\"cols\":2,\"labelWidth\":\"100px\",\"options\":{\"mock\":\"模拟数据\",\"mock4\":\"2021-11-04 10:37:22\",\"mock5\":[\"2021-11-04 06:25:22\",\"2021-11-30 06:25:22\"]}},\"action\":[{\"name\":\"onSearch\",\"list\":[{\"key\":\"bhFjWoGr49R3j3tLYnhfpV\",\"type\":[\"script\"],\"value\":\"refreshTable\"}]},{\"name\":\"onReset\",\"list\":[{\"key\":\"iHoWhjecevtv9K5sdBgFKT\",\"type\":[\"script\"],\"value\":\"refreshTable\"}]}],\"childList\":[{\"key\":\"Input_mock\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"模拟1\",\"prop\":\"mock\",\"placeholder\":\"请输入模拟1\",\"dataType\":\"string\",\"clearable\":true}},{\"key\":\"Textarea_mock2\",\"type\":\"Textarea\",\"label\":\"单行文本\",\"props\":{\"label\":\"模拟2\",\"prop\":\"mock2\",\"placeholder\":\"请输入模拟2\",\"dataType\":\"string\",\"clearable\":true}},{\"key\":\"InputNumber_mock3\",\"type\":\"InputNumber\",\"label\":\"数值\",\"props\":{\"label\":\"模拟3\",\"prop\":\"mock3\",\"placeholder\":\"请输入模拟3\",\"dataType\":\"number\",\"clearable\":true}},{\"key\":\"DatePicker_mock4\",\"type\":\"DatePicker\",\"label\":\"日期\",\"props\":{\"label\":\"模拟4\",\"prop\":\"mock4\",\"placeholder\":\"请选择模拟4\",\"dataType\":\"datetime\",\"clearable\":true}},{\"key\":\"DateRangePicker_mock5\",\"type\":\"DateRangePicker\",\"label\":\"日期区间\",\"props\":{\"label\":\"模拟5\",\"prop\":\"mock5\",\"placeholder\":[\"开始时间\",\"结束时间\"],\"dataType\":\"datetime\",\"clearable\":true}},{\"key\":\"Select_mock6\",\"type\":\"Select\",\"label\":\"下拉单选\",\"props\":{\"label\":\"模拟6\",\"prop\":\"mock6\",\"placeholder\":\"请输入模拟6\",\"dataType\":\"string\",\"clearable\":true,\"options\":[{\"label\":\"下拉1\",\"value\":\"1\"},{\"label\":\"下拉2\",\"value\":\"2\"},{\"label\":\"下拉3\",\"value\":\"3\"}]}},{\"key\":\"EmployeePicker_mock7\",\"type\":\"EmployeePicker\",\"label\":\"成员\",\"props\":{\"label\":\"模拟7\",\"prop\":\"mock7\",\"placeholder\":\"请输入模拟7\",\"dataType\":\"string\",\"clearable\":true}}]},{\"key\":\"Panel_eF1nBQrvaqKVJPjMM12Vrn\",\"type\":\"Panel\",\"label\":\"容器\",\"props\":{\"direction\":\"column\",\"padding\":\"0px 15px 15px 15px\"},\"childList\":[{\"key\":\"Panel_sUfzoCLRRL28GBCGpx3U9B\",\"type\":\"Panel\",\"label\":\"容器\",\"props\":{\"display\":\"flex\",\"justify\":\"center\",\"margin\":\"15px 0px 15px 0px\"},\"childList\":[{\"key\":\"Button_dfdbpY6ybR4y3crgWw26Bu\",\"type\":\"Button\",\"label\":\"按钮\",\"props\":{\"type\":\"primary\",\"content\":\"新增\"},\"action\":[{\"name\":\"onClick\",\"list\":[{\"key\":\"kSBisuDXPhpUV2SCW8e3aZ\",\"type\":[\"script\"],\"value\":\"showDialog\"}]}]}]},{\"key\":\"Table_rBYsNqWtuJbuEHr2FukBRp\",\"type\":\"Table\",\"label\":\"表格\",\"props\":{\"formId\":\"tUVGVE6SBitbt65W7C3KUq\",\"pagination\":true,\"border\":true,\"stripe\":true,\"size\":\"mini\",\"pageSize\":10,\"pageSizes\":[10,20,50,100,200],\"options\":{\"url\":\"/api/hrpaas/uiform/data/v1/list\",\"params\":[{\"key\":\"fXmZB8QupavAabEYwBYk9W\",\"name\":\"mock\",\"dataType\":\"string\",\"operator\":\"=\",\"fieldType\":[\"page\",\"Page_edZxKjzWJdBA6DPrgHAFyb\",\"Search_cgeCdKdS9SCLy23w4Ui2dc\",\"Input_mock\"]},{\"key\":\"rRQ8DbYKkAxyEZ6vTNKjU2\",\"name\":\"mock2\",\"dataType\":\"string\",\"operator\":\"=\",\"fieldType\":[\"page\",\"Page_edZxKjzWJdBA6DPrgHAFyb\",\"Search_cgeCdKdS9SCLy23w4Ui2dc\",\"Textarea_mock2\"]},{\"key\":\"rUfBH3w39aviqDvHeoi7EH\",\"name\":\"mock3\",\"dataType\":\"number\",\"operator\":\"=\",\"fieldType\":[\"page\",\"Page_edZxKjzWJdBA6DPrgHAFyb\",\"Search_cgeCdKdS9SCLy23w4Ui2dc\",\"InputNumber_mock3\"]},{\"key\":\"2Q6PvmrvR99A2bDKhbfRgf\",\"name\":\"mock4\",\"dataType\":\"datetime\",\"operator\":\"=\",\"fieldType\":[\"page\",\"Page_edZxKjzWJdBA6DPrgHAFyb\",\"Search_cgeCdKdS9SCLy23w4Ui2dc\",\"DatePicker_mock4\"]},{\"key\":\"61nYPZAwbyt68evWrkCFWj\",\"name\":\"mock5\",\"dataType\":\"datetime\",\"operator\":\"=\",\"fieldType\":[\"page\",\"Page_edZxKjzWJdBA6DPrgHAFyb\",\"Search_cgeCdKdS9SCLy23w4Ui2dc\",\"DateRangePicker_mock5\"]},{\"key\":\"jXAUJzHRrc7uXskJfQRzUA\",\"name\":\"mock6\",\"dataType\":\"string\",\"operator\":\"=\",\"fieldType\":[\"page\",\"Page_edZxKjzWJdBA6DPrgHAFyb\",\"Search_cgeCdKdS9SCLy23w4Ui2dc\",\"Select_mock6\"]},{\"key\":\"cfTQd1jm1qyMQncS88kAKW\",\"name\":\"mock7\",\"dataType\":\"string\",\"operator\":\"=\",\"fieldType\":[\"page\",\"Page_edZxKjzWJdBA6DPrgHAFyb\",\"Search_cgeCdKdS9SCLy23w4Ui2dc\",\"EmployeePicker_mock7\"]}]}},\"childList\":[{\"key\":\"TableColumn_3pQVAXWkVoNYLnGxfuuUVs\",\"type\":\"TableColumn\",\"label\":\"数据列\",\"isConfig\":false,\"props\":{\"title\":\"模拟1\",\"dataType\":\"string\",\"showOverflowTooltip\":true,\"resizable\":true,\"sortable\":true}},{\"key\":\"TableColumn_iRPmEmrHpEdi7jC6dXVRia\",\"type\":\"TableColumn\",\"label\":\"数据列\",\"isConfig\":false,\"props\":{\"title\":\"模拟2\",\"dataType\":\"string\",\"showOverflowTooltip\":true,\"resizable\":true,\"sortable\":true}},{\"key\":\"TableColumn_whiJY1SuqKvUNtQSHrWYdk\",\"type\":\"TableColumn\",\"label\":\"数据列\",\"isConfig\":false,\"props\":{\"title\":\"模拟3\",\"dataType\":\"number\",\"showOverflowTooltip\":true,\"resizable\":true,\"sortable\":true}},{\"key\":\"TableColumn_7oerMwQsgGRY7hcTH9bNZH\",\"type\":\"TableColumn\",\"label\":\"数据列\",\"isConfig\":false,\"props\":{\"title\":\"模拟4\",\"dataType\":\"datetime\",\"showOverflowTooltip\":true,\"resizable\":true,\"sortable\":true}},{\"key\":\"TableColumn_e23GH1wigvcsmtydDJ5k9W\",\"type\":\"TableColumn\",\"label\":\"数据列\",\"isConfig\":false,\"props\":{\"title\":\"模拟5\",\"dataType\":\"datetime\",\"showOverflowTooltip\":true,\"resizable\":true,\"sortable\":true}},{\"key\":\"TableColumn_7WsXZHbzNLEPmNkyiKVdKK\",\"type\":\"TableColumn\",\"label\":\"数据列\",\"isConfig\":false,\"props\":{\"title\":\"模拟6\",\"dataType\":\"string\",\"showOverflowTooltip\":true,\"resizable\":true,\"sortable\":true,\"options\":[{\"label\":\"下拉1\",\"value\":\"1\"},{\"label\":\"下拉2\",\"value\":\"2\"},{\"label\":\"下拉3\",\"value\":\"3\"}]}},{\"key\":\"TableColumn_5cYCuag94Z4Mx2k1M4pFYs\",\"type\":\"TableColumn\",\"label\":\"数据列\",\"isConfig\":false,\"props\":{\"title\":\"模拟7\",\"dataType\":\"string\",\"showOverflowTooltip\":true,\"resizable\":true,\"sortable\":true}},{\"key\":\"TableColumn_operator\",\"type\":\"TableColumn\",\"label\":\"操作列\",\"isConfig\":false,\"slotScope\":true,\"props\":{\"title\":\"操作\",\"dataIndex\":\"operator\"},\"childList\":[{\"key\":\"Button_edit\",\"type\":\"Button\",\"label\":\"按钮\",\"isConfig\":false,\"props\":{\"type\":\"text\",\"margin\":\"0 8px 0 0\",\"size\":\"small\"},\"action\":[{\"name\":\"onClick\",\"list\":[{\"key\":\"pMPGbydMWJcMtKJj9wdyf7\",\"type\":[\"script\"],\"value\":\"editTable\"}]}],\"childList\":[\"编辑\"]},{\"key\":\"Button_delete\",\"type\":\"Button\",\"label\":\"按钮\",\"isConfig\":false,\"props\":{\"type\":\"text\",\"size\":\"small\",\"confirm\":true,\"confirmText\":\"是否确认删除？\"},\"action\":[{\"name\":\"onClick\",\"list\":[{\"key\":\"rAMhNQgYbhHB7WqtWCQ5uj\",\"type\":[\"script\"],\"value\":\"deleteRowData\"}]}],\"childList\":[\"删除\"]}]}]}]},{\"key\":\"Dialog_8o35oYqgh8gE4v9Svj9odB\",\"type\":\"Dialog\",\"label\":\"对话框\",\"props\":{\"title\":\"编辑人员信息\",\"width\":\"700px\",\"padding\":\"15px 0px 0px 0px\",\"visible\":false,\"appendToBody\":true,\"modal\":true},\"action\":[{\"name\":\"onClose\",\"list\":[{\"key\":\"bqYwCm9L3PHxDFPvNWLtWN\",\"type\":[\"script\"],\"value\":\"closeDialog\"}]},{\"name\":\"onOk\",\"list\":[{\"key\":\"7wStrCentsyWwwSDW8Kg2N\",\"type\":[\"script\"],\"value\":\"submitForm\"}]}],\"childList\":[{\"key\":\"Form_4xWmkDtVucGrvqusdxGbTz\",\"type\":\"Form\",\"label\":\"表单\",\"props\":{\"labelWidth\":\"100px\",\"cols\":2,\"formId\":\"3EdiVad9Y8LAMCRKpkGa8m\",\"options\":{\"mock\":\"模拟数据\",\"mock4\":\"2021-11-04 10:37:22\",\"mock5\":[\"2021-11-04 06:25:22\",\"2021-11-30 06:25:22\"]}},\"action\":[{\"name\":\"onSubmitSuccess\",\"list\":[{\"key\":\"bs76FXUqnyfvXrh8Z4BXp8\",\"type\":[\"script\"],\"value\":\"formSubmitSuccess\"}]}],\"childList\":[{\"key\":\"Input_3FcnT8V7AbNGnK2NyTDvSQ\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"模拟1\",\"prop\":\"mock\",\"placeholder\":\"请输入模拟1\",\"dataType\":\"String\",\"required\":true,\"value\":\"模拟数据\"}},{\"key\":\"Textarea_noogxsGkZErrpYiZTYWGRH\",\"type\":\"Textarea\",\"label\":\"单行文本\",\"props\":{\"label\":\"模拟2\",\"prop\":\"mock2\",\"placeholder\":\"请输入模拟2\",\"dataType\":\"String\",\"required\":false}},{\"key\":\"InputNumber_r26Z7AXNVTe18APeETDDff\",\"type\":\"InputNumber\",\"label\":\"数值\",\"props\":{\"label\":\"模拟3\",\"prop\":\"mock3\",\"placeholder\":\"请输入模拟3\",\"dataType\":\"Number\",\"required\":false}},{\"key\":\"DatePicker_unN5jpwBjLM6e4c3YGnMKP\",\"type\":\"DatePicker\",\"label\":\"日期\",\"props\":{\"label\":\"模拟4\",\"prop\":\"mock4\",\"placeholder\":\"请选择模拟4\",\"dataType\":\"Timestamp\",\"required\":true,\"value\":\"2021-11-04 10:37:22\"}},{\"key\":\"DateRangePicker_qBi5WPofDjaojXuhpWWteQ\",\"type\":\"DateRangePicker\",\"label\":\"日期区间\",\"props\":{\"label\":\"模拟5\",\"prop\":\"mock5\",\"placeholder\":[\"开始时间\",\"结束时间\"],\"dataType\":\"Timestamp_Array\",\"required\":true,\"value\":[\"2021-11-04 06:25:22\",\"2021-11-30 06:25:22\"]}},{\"key\":\"Select_wfdMVQPJAx9KGAozQ5a6WY\",\"type\":\"Select\",\"label\":\"下拉单选\",\"props\":{\"label\":\"模拟6\",\"prop\":\"mock6\",\"placeholder\":\"请输入模拟6\",\"dataType\":\"String\",\"required\":false,\"options\":[{\"label\":\"下拉1\",\"value\":\"1\"},{\"label\":\"下拉2\",\"value\":\"2\"},{\"label\":\"下拉3\",\"value\":\"3\"}]}},{\"key\":\"EmployeePicker_bPKqxh4oP7LsN7Ud3r6soD\",\"type\":\"EmployeePicker\",\"label\":\"成员\",\"props\":{\"label\":\"模拟7\",\"prop\":\"mock7\",\"placeholder\":\"请输入模拟7\",\"dataType\":\"String\",\"required\":false}}]}]}],\"script\":\"\"}}";
        UiFormBasicsDto uiFormBasicsDto = FastjsonUtil.toObject(str, UiFormBasicsDto.class);
        System.out.println(FastjsonUtil.toJson(uiFormBasicsDto));
    }

    public static void taoge() {
        String[] a = new String[2];
        Object[] b = a;
        a[0] = "hi";
        b[1] = 88;
        //b[1] = Integer.valueOf(42);

        System.out.println("---------------------------");
        System.out.println(FastjsonUtil.toJson(a));
        System.out.println(FastjsonUtil.toJson(b));
        System.out.println("---------------------------");
    }

    public static void main(String[] args) {
        String [] strings = "relations$province".split("\\$");
        System.out.println(strings[1]);
        PageType ft = PageType.valueOf("NormalPage");
        System.out.println(ft);

        testUiFormBasicsDto();

        String funType = "Cus%s%s";
        funType = String.format(funType, System.currentTimeMillis(), RandomUtil.randomString(6));
        System.out.println(funType);

        String userStr = "{\"empname\":\"erha(二号人员)\",\"thirdPart\":\"TENCENT\",\"empid\":5,\"upduser\":5,\"corpid\":11651,\"thirdId\":\"21:tencent-test3\",\"belongOrgId\":56594,\"globalid\":\"2\",\"mobnum\":\"***********\",\"userid\":35132,\"clockType\":1,\"extMap\":{\"globalid\":\"2\",\"corpkey\":\"tencent-test3\",\"userName\":\"tencent-test3\",\"corpcode\":\"TENCENT\"},\"passwd\":\"f6c82ad030b7b500df7ec605f4cfd9a4\",\"tenantId\":\"tencent-test3\",\"crttime\":**********,\"crtuser\":0,\"issuperadmin\":false,\"account\":\"t_wx_tencent-test3_21\",\"email\":\"<EMAIL>\",\"status\":1}";

        System.out.println(userStr);
        UserInfo ui = FastjsonUtil.toObject(userStr, UserInfo.class);
        System.out.println(FastjsonUtil.toJson(ui));

        Long id  = null;
        System.out.println("" + null);
        System.out.println();

        String str = String.format("%s$%s", "userName", "_ABC");
        System.out.println(str);

        String json = "[{\"travelId\":****************,\"workNo\":\"ershanli\",\"empName\":\"二号人员\",\"orgName\":\"健康保险\",\"fullPath\":\"健康保险\",\"travelType\":\"外出天\",\"timeDuration\":0.5,\"timeUnitName\":\"天\",\"startDate\":\"2021-10-01 13:00:00\",\"endDate\":\"2021-10-01 18:00:00\",\"travelModeName\":null,\"provinceName\":\"北京市\",\"cityName\":\"北京市\",\"countyName\":null,\"reason\":\"11111\",\"createTime\":**********,\"statusName\":\"已通过\",\"lastApprovalTime\":**********,\"status\":2,\"funType\":45,\"businessKey\":\"****************_121\"},{\"travelId\":1264707622017027,\"workNo\":\"ershanli\",\"empName\":\"二号人员\",\"orgName\":\"健康保险\",\"fullPath\":\"健康保险\",\"travelType\":\"外出天\",\"timeDuration\":0.5,\"timeUnitName\":\"天\",\"startDate\":\"2021-10-01 09:00:00\",\"endDate\":\"2021-10-01 12:00:00\",\"travelModeName\":null,\"provinceName\":\"北京市\",\"cityName\":\"北京市\",\"countyName\":null,\"reason\":\"11111\",\"createTime\":1634549720,\"statusName\":\"已撤销\",\"lastApprovalTime\":1634549720,\"status\":9,\"funType\":45,\"businessKey\":\"1264707622017027_121\"},{\"travelId\":1264706624280577,\"workNo\":\"ershanli\",\"empName\":\"二号人员\",\"orgName\":\"健康保险\",\"fullPath\":\"健康保险\",\"travelType\":\"外出天\",\"timeDuration\":1.0,\"timeUnitName\":\"天\",\"startDate\":\"2021-10-01\",\"endDate\":\"2021-10-01\",\"travelModeName\":null,\"provinceName\":\"北京市\",\"cityName\":\"北京市\",\"countyName\":null,\"reason\":\"111111\",\"createTime\":1634549598,\"statusName\":\"已撤销\",\"lastApprovalTime\":1634549598,\"status\":9,\"funType\":45,\"businessKey\":\"1264706624280577_121\"},{\"travelId\":1264619414910979,\"workNo\":\"yihaorenyuan\",\"empName\":\"一号人员\",\"orgName\":\"健康保险\",\"fullPath\":\"健康保险\",\"travelType\":\"外出小时2\",\"timeDuration\":8.0,\"timeUnitName\":\"小时\",\"startDate\":\"2021-10-23 08:00:00\",\"endDate\":\"2021-10-23 18:00:00\",\"travelModeName\":null,\"provinceName\":\"北京市\",\"cityName\":\"北京市\",\"countyName\":null,\"reason\":\"22222\",\"createTime\":1634538952,\"statusName\":\"已通过\",\"lastApprovalTime\":1634538952,\"status\":2,\"funType\":45,\"businessKey\":\"1264619414910979_121\"},{\"travelId\":1264614122027009,\"workNo\":\"yihaorenyuan\",\"empName\":\"一号人员\",\"orgName\":\"健康保险\",\"fullPath\":\"健康保险\",\"travelType\":\"外出小时2\",\"timeDuration\":8.0,\"timeUnitName\":\"小时\",\"startDate\":\"2021-10-22 08:00:00\",\"endDate\":\"2021-10-22 18:00:00\",\"travelModeName\":null,\"provinceName\":null,\"cityName\":null,\"countyName\":null,\"reason\":\"11111\",\"createTime\":1634538306,\"statusName\":\"已通过\",\"lastApprovalTime\":1634538306,\"status\":2,\"funType\":45,\"businessKey\":\"1264614122027009_121\"},{\"travelId\":1261853312989189,\"workNo\":\"ershanli\",\"empName\":\"二号人员\",\"orgName\":\"健康保险\",\"fullPath\":\"健康保险\",\"travelType\":\"测试外出类型\",\"timeDuration\":1.0,\"timeUnitName\":\"天\",\"startDate\":\"2021-10-14\",\"endDate\":\"2021-10-14\",\"travelModeName\":null,\"provinceName\":\"北京市\",\"cityName\":\"北京市\",\"countyName\":null,\"reason\":\"12\",\"createTime\":1634201293,\"statusName\":\"已通过\",\"lastApprovalTime\":1634201293,\"status\":2,\"funType\":45,\"businessKey\":\"1261853312989189_121\"},{\"travelId\":1261850762827779,\"workNo\":\"yihaorenyuan\",\"empName\":\"一号人员\",\"orgName\":\"健康保险\",\"fullPath\":\"健康保险\",\"travelType\":\"1111\",\"timeDuration\":0.5,\"timeUnitName\":\"天\",\"startDate\":\"2021-10-21 09:00:00\",\"endDate\":\"2021-10-21 12:00:00\",\"travelModeName\":null,\"provinceName\":null,\"cityName\":null,\"countyName\":null,\"reason\":\"1111\",\"createTime\":1634200982,\"statusName\":\"已通过\",\"lastApprovalTime\":1634200982,\"status\":2,\"funType\":45,\"businessKey\":\"1261850762827779_121\"},{\"travelId\":1261849938917377,\"workNo\":\"yihaorenyuan\",\"empName\":\"一号人员\",\"orgName\":\"健康保险\",\"fullPath\":\"健康保险\",\"travelType\":\"外出天\",\"timeDuration\":1.0,\"timeUnitName\":\"天\",\"startDate\":\"2021-10-14\",\"endDate\":\"2021-10-14\",\"travelModeName\":null,\"provinceName\":\"北京市\",\"cityName\":\"北京市\",\"countyName\":null,\"reason\":\"1111\",\"createTime\":1634200882,\"statusName\":\"已通过\",\"lastApprovalTime\":1634200882,\"status\":2,\"funType\":45,\"businessKey\":\"1261849938917377_121\"},{\"travelId\":1261159294326789,\"workNo\":\"ershanli\",\"empName\":\"二号人员\",\"orgName\":\"健康保险\",\"fullPath\":\"健康保险\",\"travelType\":\"外出天\",\"timeDuration\":1.0,\"timeUnitName\":\"天\",\"startDate\":\"2021-10-02\",\"endDate\":\"2021-10-02\",\"travelModeName\":null,\"provinceName\":null,\"cityName\":null,\"countyName\":null,\"reason\":\"kkk\",\"createTime\":1634116574,\"statusName\":\"已撤销\",\"lastApprovalTime\":1634116574,\"status\":9,\"funType\":45,\"businessKey\":\"1261159294326789_121\"},{\"travelId\":1261157384476675,\"workNo\":\"ershanli\",\"empName\":\"二号人员\",\"orgName\":\"健康保险\",\"fullPath\":\"健康保险\",\"travelType\":\"外出天\",\"timeDuration\":1.0,\"timeUnitName\":\"天\",\"startDate\":\"2021-10-07\",\"endDate\":\"2021-10-07\",\"travelModeName\":null,\"provinceName\":null,\"cityName\":null,\"countyName\":null,\"reason\":\"edd\",\"createTime\":1634116341,\"statusName\":\"已撤销\",\"lastApprovalTime\":1634116341,\"status\":9,\"funType\":45,\"businessKey\":\"1261157384476675_121\"}]";

        List<Map> list = FastjsonUtil.toObject(json, new TypeReference<List<Map>>(){});

        for (Map map : list) {
            System.out.println(FastjsonUtil.toJson(map));
        }

        /*UserInfo userInfo = null;
        userInfo.getCorpid();*/

        taoge();
    }
}
