package com.caidaocloud.hrpaas.metadata.sdk.feign;

import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataAddressVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SchemaDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MetadataFeignClientFallBack implements IMetadataFeign {
    @Override
    public Result one(String identifier) {
        return Result.fail();
    }

    @Override
    public Result upgrade(List<SchemaDto> schemaList) {
        return Result.fail();
    }

    @Override
    public Result<List<MetadataAddressVo>> getAddressByStr(List<String> list) {
        return Result.fail();
    }
}
