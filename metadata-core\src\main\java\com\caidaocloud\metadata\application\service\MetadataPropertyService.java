package com.caidaocloud.metadata.application.service;

import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.domain.entity.PropertyDefaultValue;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequence;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MetadataPropertyService {
    @Autowired
    private MetadataSyncService metadataSyncService;

    @Transactional(rollbackFor=Exception.class)
    public void insertStandardProps(String identifier, Sequence<PropertyDef> props,
                                  DefChannel defChannel){
        EntityDef.insertStandardProps(identifier, props, defChannel);
        metadataSyncService.sync2Md(identifier);
    }

    @Transactional(rollbackFor=Exception.class)
    public void insertCustomProps(String identifier, Sequence<PropertyDef> props,
                                         DefChannel defChannel){
        EntityDef.insertCustomProps(identifier, props, defChannel);
        metadataSyncService.sync2Md(identifier);
    }
    @Transactional(rollbackFor=Exception.class)
    public void saveCustomProps(String identifier, Sequence<PropertyDef> props, DefChannel defChannel){
        EntityDef.saveCustomProps(identifier, props, defChannel);
    }
    @Transactional(rollbackFor=Exception.class)
    public void updateCustomProps(String identifier, Sequence<Pair<String, PropertyDef>> props,
                                         DefChannel defChannel){
        EntityDef.updateCustomProps(identifier, props, defChannel);
    }
    @Transactional(rollbackFor=Exception.class)
    public void removeCustomProps(String identifier, Sequence<String> props,
                                         DefChannel defChannel){
        EntityDef.removeCustomProps(identifier, props, defChannel);
    }
    @Transactional(rollbackFor=Exception.class)
    public void recoverCustomProps(String identifier, Sequence<Pair<String, PropertyDefaultValue>> propsWithDefaultValue,
                                          DefChannel defChannel){
        EntityDef.recoverCustomProps(identifier, propsWithDefaultValue, defChannel);
    }
    @Transactional(rollbackFor=Exception.class)
    public void physicalDeleteCustomProps(String identifier, Sequence<String> props,
                                                 DefChannel defChannel) {
        EntityDef.physicalDeleteCustomProps(identifier, props, defChannel);
    }
    @Transactional(rollbackFor=Exception.class)
    public void physicalDeleteStandardProps(String identifier, Sequence<String> props,
                                          DefChannel defChannel) {
        EntityDef.physicalDeleteStandardProps(identifier, props, defChannel);
    }

}
