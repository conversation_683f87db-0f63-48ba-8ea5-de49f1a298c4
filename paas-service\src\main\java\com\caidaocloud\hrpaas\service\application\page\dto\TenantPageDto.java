package com.caidaocloud.hrpaas.service.application.page.dto;

import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageTemplate;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Map;

@Data
@ApiModel("菜单dto")
public class TenantPageDto {

    private String id;

    @ApiModelProperty("名称")
    @NotNull
    private Map<String, String> i18nName;

    @ApiModelProperty("图标")
    private String icon;

    @NotBlank
    @ApiModelProperty("页面类型")
    private PageType type;

    @Size(max = 255, min = 1)
    @ApiModelProperty("描述")
    private String desc;

    @ApiModelProperty("导航显示：true显示，false不显示")
    private Boolean showNav;

    @ApiModelProperty("父ID")
    private String parentId;

    @ApiModelProperty("是否标准")
    private boolean isStandard;

    @ApiModelProperty("关联模型")
    private String joinModel;

    @ApiModelProperty("页面模板")
    private PageTemplate pageTemplate;

    @ApiModelProperty("页面schema")
    private String schema;
}
