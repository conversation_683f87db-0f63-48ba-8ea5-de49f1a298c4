package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.googlecode.totallylazy.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class DataTable implements PropertyValue{

    private List<DataSlave> dataList = Lists.list();

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {

    }

    @Override
    public String toText() {
        return null;
    }
}
