package com.caidaocloud.hrpaas.service.application.rules;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022-01-04
 */
public class CheckRuleFactory {
    private static Map<String, ICheckRule> ruleFactory = new ConcurrentHashMap();

    // 按类别分组
    private static Map<String, List<String>> ruleGroupFactory = new ConcurrentHashMap();

    public static void registerCheckRule(String ruleType, ICheckRule checkRule){
        ruleFactory.put(ruleType, checkRule);
    }

    public static ICheckRule getCheckRule(String ruleType){
        return ruleFactory.get(ruleType);
    }

    public static ICheckRule getCheckRule(IRule checkRuleDto){
        ICheckRule checkRule = ruleFactory.get(checkRuleDto.type());
        if(null != checkRule){
            return checkRule;
        }

        if("inputType".equals(checkRuleDto.type())){
            checkRule = ruleFactory.get(checkRuleDto.rule());
        }

        return checkRule;
    }

    public synchronized static void registerCheckRuleGroup(String group, String type){
        final List<String> typeList = ruleGroupFactory.getOrDefault(group, new ArrayList<>(20));
        typeList.add(type);
        ruleGroupFactory.put(group, typeList);
    }

    public static List<String> getCheckRuleList(String group){
        return ruleGroupFactory.getOrDefault(group, new ArrayList<>(20));
    }
}
