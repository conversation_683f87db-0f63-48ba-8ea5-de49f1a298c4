{"identifier": "entity.common.BaseModel", "isBuiltIn": true, "name": "通用基础模型", "isAbstract": true, "owner": "metadata", "parent": null, "isSharable": true, "isInheritable": true, "mainKey": ["bid"], "standardProperties": [{"property": "id", "persistProperty": "id", "name": "id", "dataType": "String", "isVisible": false, "isModifiable": false, "isRequired": true}, {"property": "identifier", "persistProperty": "identifier", "name": "数据所属模型", "dataType": "String", "isVisible": false, "isModifiable": false, "isRequired": true}, {"property": "bid", "persistProperty": "bid", "name": "业务Id", "dataType": "String", "unique": true, "isModifiable": false, "isRequired": true}, {"property": "tenantId", "persistProperty": "tenant_id", "name": "租户ID", "dataType": "String", "isModifiable": false, "isRequired": true}, {"property": "createTime", "persistProperty": "create_time", "name": "创建时间", "dataType": "Timestamp", "isModifiable": false, "isRequired": true}, {"property": "createBy", "persistProperty": "create_by", "name": "创建人", "dataType": "String", "isModifiable": false, "isRequired": true}, {"property": "updateTime", "persistProperty": "update_time", "name": "更新时间", "dataType": "Timestamp"}, {"property": "updateBy", "persistProperty": "update_by", "name": "更新人", "dataType": "String"}, {"property": "dataStartTime", "persistProperty": "data_start_time", "name": "数据开始时间", "dataType": "Timestamp"}, {"property": "dataEndTime", "persistProperty": "data_end_time", "name": "数据结束时间", "dataType": "Timestamp"}, {"property": "deleted", "persistProperty": "deleted", "name": "是否已删除", "dataType": "Boolean"}]}