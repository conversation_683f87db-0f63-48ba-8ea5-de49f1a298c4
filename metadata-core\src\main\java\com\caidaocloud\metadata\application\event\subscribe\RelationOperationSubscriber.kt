package com.caidaocloud.metadata.application.event.subscribe

import com.caidaocloud.hrpaas.metadata.sdk.enums.EventDefStatus
import com.caidaocloud.hrpaas.metadata.sdk.enums.RelationOperationType
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil
import com.caidaocloud.metadata.application.event.publish.EntityEventSender
import com.caidaocloud.metadata.application.event.publish.EntityRelationOperationEvent
import com.caidaocloud.metadata.application.event.publish.RELATION_MODIFICATION
import com.caidaocloud.metadata.domain.service.EntityEventDefDomainService
import com.caidaocloud.metadata.domain.entity.DataModification
import com.caidaocloud.metadata.domain.enums.OperationTarget
import com.caidaocloud.metadata.domain.enums.OperationType
import com.caidaocloud.msg.handler.MessageHandler
import com.caidaocloud.security.util.SecurityUserUtil
import com.caidaocloud.util.FastjsonUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class RelationOperationSubscriber : MessageHandler<EntityRelationOperationEvent> {

    @Autowired
    private lateinit var entityEventDefDomainService : EntityEventDefDomainService

    @Autowired
    private lateinit var entityEventSender: EntityEventSender

    override fun topic(): String {
        return RELATION_MODIFICATION
    }

    override fun handle(event: EntityRelationOperationEvent) {
        if(event.allowAnonymous){
            try{
                ThreadLocalUtil.allowAnonymous()
                handleEvent(event)
            }finally {
                ThreadLocalUtil.anonymousEnd()
            }
        }else{
            handleEvent(event)
        }
    }

    fun handleEvent(event: EntityRelationOperationEvent) {
        DataModification(
            event.identifier,
            event.targetId,
            OperationTarget.EntityRelation,
            FastjsonUtil.toJson(event.data),
            event.eventTime,
            SecurityUserUtil.getSecurityUserInfo().userId?.toString()?:"",
            when(event.operation){
                RelationOperationType.ADD-> OperationType.ADD
                RelationOperationType.DELETE-> OperationType.DELETE
                RelationOperationType.REPLACE_ALL-> OperationType.UPDATE
                RelationOperationType.DELETE_ALL-> OperationType.DELETE
            },
            event.startTime
        ).insert()
        entityEventDefDomainService.loadByModelRef(event.identifier)
            .filter { entityEventDef->
                entityEventDef.trackRelation && EventDefStatus.OPEN == entityEventDef.status
            }.forEach { entityEventDef ->
                val entityEvent = entityEventDefDomainService.initRelationEvent(event.targetId, event.data, entityEventDef, event.operation)
                entityEventSender.publish(entityEvent, entityEventDef.consumerList)
            }
    }
}