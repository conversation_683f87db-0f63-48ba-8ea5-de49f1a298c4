package com.caidaocloud.hrpaas.service.interfaces.dto.metadata;

import com.caidaocloud.hrpaas.service.application.metadata.dto.MetadataSchemaInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MetadataSchemaDto {
    @ApiModelProperty("脚本ID")
    private String id;

    /**
     * 模块名，如：payroll、hr、attendance，ach 等
     */
    @ApiModelProperty("模块名")
    private String schemaModel;

    /**
     * 脚本文件夹名
     */
    @ApiModelProperty("脚本文件夹名")
    private Integer folder;

    /**
     * 版本
     */
    @ApiModelProperty("脚本版本")
    private Long version;

    /**
     * 脚本文件名称
     */
    @ApiModelProperty("脚本文件名称")
    private String schemaName;

    /**
     * 脚本内容
     */
    @ApiModelProperty("脚本内容")
    private List<MetadataSchemaInfoDto> content;
}
