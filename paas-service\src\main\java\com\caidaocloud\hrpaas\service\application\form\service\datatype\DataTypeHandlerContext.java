package com.caidaocloud.hrpaas.service.application.form.service.datatype;

import com.caidaocloud.hrpaas.service.application.form.dto.WfRegistrySeqDto;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/11/8
 **/
@Slf4j
public class DataTypeHandlerContext {

    public static Optional<WfRegistrySeqDto> generateSeq(FormPropDef formDef, String formDefId) {
        IDataTypeHandler.dataTypeHandlers.get(formDef.getDataType());
        if (IDataTypeHandler.dataTypeHandlers.containsKey(formDef.getDataType())) {
            var wfRegistrySeqDto = IDataTypeHandler.dataTypeHandlers
                    .get(formDef.getDataType()).generateSeqDto(formDef, formDefId);
            return Optional.of(wfRegistrySeqDto);
        }
        log.warn("not found handler for {} type", formDef.getDataType());
        return Optional.empty();
    }

}
