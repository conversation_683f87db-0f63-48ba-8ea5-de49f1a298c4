package com.caidaocloud.hrpaas.service.application.metadata.service;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.service.interfaces.dto.file.AttachmentInfoDto;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.security.service.ISessionService;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 * @date  2021/12/2
 **/
@Service
public class FileService {

    @Autowired
    private OssService ossService;

    @Autowired
    private ISessionService sessionService;

    @Value("${oss.provider:}")
    private String provider;

    /**
     * 上传文件
     *
     * @param multipartFile
     * @param bucketName
     * @return
     */
    public AttachmentInfoDto upload(MultipartFile multipartFile, String bucketName){
        if(!"nas_provider".equals(provider) && StringUtils.isBlank(bucketName)){
            UserInfo userInfo = sessionService.getUserInfo();
            bucketName = userInfo.getCorpid() + "-" + userInfo.getBelongOrgId();
        }
        val result = ossService.upload(bucketName, multipartFile);
        val attachment = new AttachmentInfoDto();
        attachment.setFileName(result.getFileName());
        attachment.setPath(result.getObjectPath());
        return attachment;
    }

    /**
     * 上传文件
     *
     * @param multipartFile
     * @return
     */
    public AttachmentInfoDto upload(MultipartFile multipartFile){
        return upload(multipartFile, "");
    }

    public void download(String path, String fileName, HttpServletResponse response){
        ossService.downloadFile(path, fileName, response);
    }

    public List<AttachmentInfoDto> batchUpload(List<MultipartFile> fileList) {
        List<AttachmentInfoDto> result = new ArrayList<>();
        for (MultipartFile file : fileList) {
            result.add(upload(file));
        }
        return result;
    }

}
