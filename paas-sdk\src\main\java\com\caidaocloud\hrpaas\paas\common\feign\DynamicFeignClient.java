package com.caidaocloud.hrpaas.paas.common.feign;

import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.paas.common.dto.UserDynamicConfig;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicColumnConfigDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        value = "${feign.rename.caidaocloud-hr-paas-service:caidaocloud-hr-paas-service}",
        fallback = DynamicFeignClientFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "dynamicFeignClient"
)
public interface DynamicFeignClient {

    @GetMapping("/api/hrpaas/v1/dynamicTable/set/user/load")
    Result<UserDynamicConfig> userDynamicTableLoad(@RequestParam String code);

    @GetMapping("/api/hrpaas/v1/dynamicTable/set/load/onlyProperties")
    Result<List<MetadataPropertyDto>> dynamicTableLoad(@RequestParam String code);


    @PostMapping("/api/hrpaas/v1/dynamicTable/set")
    public Result<Boolean> dynamicTableSet(@RequestBody DynamicColumnConfigDto config);
}
