package com.caidaocloud.hrpaas.service.domain.dynamic.entity;

import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.paas.common.dto.UserDynamicConfig;
import com.caidaocloud.hrpaas.service.domain.dynamic.repository.DynamicColumnConfigRepository;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicPropertyDto;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequence;
import lombok.Data;

import java.util.List;

@Data
public class DynamicColumnConfig {
    private Long id;
    private String code;
    private String name;
    private List<DynamicPropertyDto> properties = Lists.list();

    public static DynamicColumnConfig loadByCode(String code) {
        return SpringUtil.getBean(DynamicColumnConfigRepository.class).loadByCode(code);
    }

    public static List<DynamicColumnConfig> list() {
        return SpringUtil.getBean(DynamicColumnConfigRepository.class).list();
    }

    public static UserDynamicConfig loadUser(String code, String userId) {
        return SpringUtil.getBean(DynamicColumnConfigRepository.class).loadUser(code, userId);
    }

    public static void addUser(String code, String userId, UserDynamicConfig userConfig) {
        SpringUtil.getBean(DynamicColumnConfigRepository.class).addUser(code, userId, userConfig);
    }

    public static void updateUser(String code, String userId, UserDynamicConfig userConfig) {
        SpringUtil.getBean(DynamicColumnConfigRepository.class).updateUser(code, userId, userConfig);
    }

    public void create() {
        this.id = SnowUtil.createId();
        SpringUtil.getBean(DynamicColumnConfigRepository.class).create(this);
    }

    public void updateById(Long id) {
        this.id = id;
        SpringUtil.getBean(DynamicColumnConfigRepository.class).update(this);
    }

    public static List<DynamicColumnConfig> loadByCode(List<String> codes) {
        Sequence<DynamicColumnConfig> sequence = SpringUtil.getBean(DynamicColumnConfigRepository.class).loadByCodes(codes);
        if (sequence.isEmpty()) {
            return Lists.list();
        }
        return sequence.toList();
    }
}
