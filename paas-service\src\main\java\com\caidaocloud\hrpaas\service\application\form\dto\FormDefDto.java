package com.caidaocloud.hrpaas.service.application.form.dto;

import com.aliyuncs.utils.StringUtils;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormHeader;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormTarget;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormWidgetType;
import com.caidaocloud.hrpaas.service.domain.form.factory.FormFactory;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.Data;
import lombok.val;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

@Data
public class FormDefDto {

    private String id;

    private String name;

    private String code;

    private Map<String, String> i18nName;

    private FormTarget target;

    private boolean workflowNodeAvailable;

    private boolean workflowAvailable;

    private String description;

    private boolean implantable = false;

    //private FormType formType;

    private List<FormHeader> headers = Lists.list();

    private List<FormPropDefDto> properties = Lists.list();

    private boolean onlyBasic;

    private boolean standard = false;

    private String mappingId;

    public FormDef toEntity(FormDef exist){
        val result = FastjsonUtil.convertObject(this, FormDef.class);
        result.getProperties().clear();
        for(int i = 0; i < properties.size(); i++){
            val propDefDto = properties.get(i);
            if(FormWidgetType.withNoPersist(propDefDto.getWidgetType())){
                propDefDto.setDataType(PropertyDataType.String);
            }
            val propDef = FormFactory.convertPropertyDef(propDefDto, exist);
            propDef.setSort(i);
            if(StringUtils.isEmpty(propDef.getName()) && MapUtils.isEmpty(propDef.getI18nName())){
                propDef.setName(propDef.getProperty());
                propDef.setI18nName(Maps.map("default", propDef.getProperty()));
            }
            propDef.handleComponentInfo();
            result.getProperties().add(propDef);
        }
        return result;
    }



}
