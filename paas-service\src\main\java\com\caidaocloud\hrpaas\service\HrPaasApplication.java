package com.caidaocloud.hrpaas.service;

import com.caidaocloud.record.core.annotation.EnableLogRecord;
import com.google.common.collect.Lists;
import lombok.val;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Jarvis
 * @Desc:
 * @Date: 1/3/2021 10:59 PM
 */
@EnableFeignClients
@EnableDiscoveryClient
@EnableCircuitBreaker
@SpringBootApplication
@EnableLogRecord(name = "hrpaas")
@ComponentScan(basePackages = {"com.caidaocloud.hrpaas.service",
        "com.caidaocloud.config", "com.caidaocloud.metadata", "com.caidaocloud.util"},
        excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
                classes = {}))
@MapperScan(basePackages = {"com.caidaocloud.metadata.infrastructure.repository.impl",
        "com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl"})
public class HrPaasApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        val applicationContext = SpringApplication.run(HrPaasApplication.class, appendArgs(args));
    }

    private static String[] appendArgs(String[] args){
        List<String> springParams =
                Lists.newArrayList(
                        "spring.cloud.nacos.discovery.server-addr",
                        "spring.cloud.nacos.discovery.username",
                        "spring.cloud.nacos.discovery.password",
                        "spring.cloud.nacos.discovery.namespace",

                        "nacos.config.server-addr",
                        "nacos.config.username",
                        "nacos.config.password",
                        "nacos.config.namespace",
                        "logging.config");
        val allArgs = springParams.stream().map(it->{
            String value = System.getProperty(it);
            if(value == null){
                return null;
            }else{
                return "--" + it + "=" + value;
            }
        }).filter(it->it != null).collect(Collectors.toList());
        if(null != args && args.length > 0){
            allArgs.addAll(Lists.newArrayList(args));
        }
        return allArgs.toArray(new String[allArgs.size()]);
    }

}
