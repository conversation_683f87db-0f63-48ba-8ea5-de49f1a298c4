package com.caidaocloud.hrpaas.service.application.metadata.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataTenantDo;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.IMetadataTenantRepository;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataTenantDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataTenantLogoDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataTenantRuleDto;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class MetadataTenantService {
    private final static String TENANT_INFO_KEY = "t_info_%s";
    @Resource
    private MetadataSchemaService metadataSchemaService;
    @Resource
    private IMetadataTenantRepository metadataTenantRepository;
    @Resource
    private CacheService cacheService;

    public void init(MetadataTenantDto metadataTenantDto){
        log.info("init tenant info. MetadataTenantDto = {}", FastjsonUtil.toJson(metadataTenantDto));
        MetadataTenantDo tenantDo = ObjectConverter.convert(metadataTenantDto, MetadataTenantDo.class);
        tenantDo.setPcTenantLogo(FastjsonUtil.toJson(initEmptyAttachment()));
        tenantDo.setAppTenantLogo(FastjsonUtil.toJson(initEmptyAttachment()));

        tenantDo.save();

    }

    public void saveMetadataTenant(MetadataTenantDto metadataTenantDto){
        log.info("saveMetadataTenant. MetadataTenantDto = {}", FastjsonUtil.toJson(metadataTenantDto));
        MetadataTenantDo tenantDo = ObjectConverter.convert(metadataTenantDto, MetadataTenantDo.class);
        tenantDo.save();
    }

    public void logoSet(MetadataTenantLogoDto tenantLogoDto) {
        MetadataTenantDo tenantData = metadataTenantRepository.loadById(SecurityUserUtil.getSecurityUserInfo().getTenantId()).get();
        if(null == tenantData || StringUtil.isEmpty(tenantData.getTenantId())){
            log.info("The tenant information does not exist or the reply has expired. userInfo={}", FastjsonUtil.toJson(SecurityUserUtil.getSecurityUserInfo().getTenantId()));
            PreCheck.preCheckArgument(true, "操作的数据不存在!");
        }

        if(null == tenantLogoDto.getPcTenantLogo()){
            tenantLogoDto.setPcTenantLogo(initEmptyAttachment());
        }
        if(null == tenantLogoDto.getAppTenantLogo()){
            tenantLogoDto.setAppTenantLogo(initEmptyAttachment());
        }

        // logo 支持删除，因此这里无需判空
        tenantData.setPcTenantLogo(FastjsonUtil.toJson(tenantLogoDto.getPcTenantLogo()));
        tenantData.setAppTenantLogo(FastjsonUtil.toJson(tenantLogoDto.getAppTenantLogo()));

        tenantData.update();

        cacheTenantInfo(tenantData);
    }

    public void orgRuleGuide(Integer manageSystem) {
        MetadataTenantDo tenantData = metadataTenantRepository.loadById(SecurityUserUtil.getSecurityUserInfo().getTenantId()).get();
        checkMetadataTenant(tenantData, manageSystem);

        tenantData.setManageSystem(manageSystem);
        tenantData.update();

        cacheTenantInfo(tenantData);
    }

    public void orgRuleSet(MetadataTenantRuleDto metadataTenantRuleDto) {
        MetadataTenantDo tenantData = metadataTenantRepository.loadById(SecurityUserUtil.getSecurityUserInfo().getTenantId()).get();
        checkMetadataTenant(tenantData, metadataTenantRuleDto.getManageSystem());

        BeanUtil.copyWithNoValue(metadataTenantRuleDto, tenantData);
        tenantData.update();

        cacheTenantInfo(tenantData);
    }

    private void checkMetadataTenant(MetadataTenantDo tenantData, Integer manageSystem){
        if(null == tenantData || StringUtil.isEmpty(tenantData.getTenantId())){
            log.info("The tenant information does not exist or the reply has expired. userInfo={}", FastjsonUtil.toJson(SecurityUserUtil.getSecurityUserInfo()));
            PreCheck.preCheckArgument(true, "操作的数据不存在！");
        }

        if(null != tenantData.getManageSystem() &&
                !(tenantData.getManageSystem().equals(manageSystem))){
            log.info("Post/position management system cannot be modified after initial selection. manageSystem={}", FastjsonUtil.toJson(tenantData.getManageSystem()));
            PreCheck.preCheckArgument(true, "岗位/职位管理体系初始选择后,不可修改!");
        }
    }

    public List<MetadataTenantDo> getOrgSet() {
        List<MetadataTenantDo> listByPage = MetadataTenantDo.getListByPage(SecurityUserUtil.getSecurityUserInfo().getTenantId(), 1, 1);
        if(null == listByPage || listByPage.isEmpty()){
            log.error("The tenant information does not exist. The tenantID={}", SecurityUserUtil.getSecurityUserInfo().getTenantId());
            PreCheck.preCheckArgument(true, "未查到数据！");
        }
        return listByPage;
    }

    private synchronized void cacheTenantInfo(MetadataTenantDo tenantData){
        cacheService.cacheValue(String.format(TENANT_INFO_KEY, tenantData.getTenantId()), FastjsonUtil.toJson(tenantData));
    }

    private Attachment initEmptyAttachment(){
        Attachment attachment = new Attachment();
        attachment.setNames(Lists.newArrayList());
        attachment.setUrls(Lists.newArrayList());
        return attachment;
    }
}
