package com.caidaocloud.hrpaas.service.domain.page.entity;

import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.hrpaas.service.domain.page.repository.ITenantPageDetailRepository;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import lombok.Data;

@Data
public class TenantPageDetail extends BaseEntity<ITenantPageDetailRepository> {

    private String id;

    private PageType type;

    private String standardPageConfig;

    private long createTime;

    private String createBy;

    private long updateTime;

    private String updateBy;

    public static TenantPageDetail loadById(String id) {
        return repository(TenantPageDetail.class).load(id).getOrNull();
    }

    public static void deleteById(String id) {
        ((ITenantPageDetailRepository)repository(TenantPageDetail.class)).deleteById(id);
    }
}
