package com.caidaocloud.hrpaas.service.application.uiform.service.convert;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.service.application.uiform.service.SysDictService;
import com.caidaocloud.hrpaas.service.application.uiform.service.hook.HookConstant;
import com.caidaocloud.hrpaas.service.application.uiform.service.render.SysDictDataSourceRender;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiDataSourceDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiGeneralDataSourceDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-09-07
 */
@Service
public class SysDictFieldValueAssembleStrategy implements FieldValueAssembleStrategy{
    @Resource
    private SysDictService sysDictService;
    @Resource
    private SysDictDataSourceRender sysDictDataSourceRender;

    @Override
    public void packageFieldValue(Map fieldMap, String fieldKey, String fieldVal, UiDataSourceDto fieldDataSource) {
        String fieldKeyTxtVal = sysDictService.getSysDictById(fieldVal);
        if(Objects.equals(fieldKeyTxtVal, fieldVal)){
            fieldKeyTxtVal = doPackageFieldValue(fieldKeyTxtVal, fieldDataSource.getId(), fieldDataSource.getConfig());
        }

        fieldMap.put(String.format(HookConstant.FIELD_TXT_FORMAT_TEMPLATE, fieldKey), fieldKeyTxtVal);
    }

    private String doPackageFieldValue(String fieldKeyTxtVal, String cacheKey, UiGeneralDataSourceDto config){
        List<Map> dataList =  (List<Map>) HolderUtil.get(cacheKey);
        if(null != dataList && !dataList.isEmpty()){
            return defaultPackageFieldValue(fieldKeyTxtVal, dataList);
        }

        PageResult<Map> pageResult = sysDictDataSourceRender.doRender(config, 0 , 0, null);
        if(null == pageResult || null == pageResult.getItems() || pageResult.getItems().isEmpty()){
            return fieldKeyTxtVal;
        }

        dataList = pageResult.getItems();
        HolderUtil.set(cacheKey, dataList);
        return defaultPackageFieldValue(fieldKeyTxtVal, dataList);
    }

    private String defaultPackageFieldValue(String fieldKeyTxtVal, List<Map> dictList){
        int len = dictList.size();
        for (int i = 0; i < len; i++) {
            Map data = dictList.get(i);
            if(Objects.equals(data.getOrDefault(HookConstant.CONVERT_SELECT_FEILD_VALUE, ""), fieldKeyTxtVal)){
                return (String) data.getOrDefault(HookConstant.CONVERT_SELECT_FEILD_TEXT, "");
            }
        }

        return fieldKeyTxtVal;
    }
}
