package com.caidaocloud.hrpaas.service.application.rules;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MinLengthCheckRule implements ICheckRule{
    private static String MIN_LENGTH_TIPS = "最小长度不合法";

    @Override
    public String getRuleType() {
        return "minLength";
    }

    @Override
    public String[] getRuleGroup() {
        return new String[] {"Number"};
    }

    @Override
    public void check(IRule checkRuleDto) {
        if(null == checkRuleDto){
            return;
        }

        String tips = StringUtil.isBlank(checkRuleDto.tips()) ? MIN_LENGTH_TIPS : checkRuleDto.tips();
        try {
            if(StringUtil.isNotEmpty(checkRuleDto.value()) && checkRuleDto.value().length() < Integer.parseInt(checkRuleDto.rule())){
                throw new ServerException(tips);
            }
        } catch (Exception e){
            log.error("MinLength Validation exception, {}", e.getMessage(), e);
            throw new ServerException(tips);
        }
    }
}
