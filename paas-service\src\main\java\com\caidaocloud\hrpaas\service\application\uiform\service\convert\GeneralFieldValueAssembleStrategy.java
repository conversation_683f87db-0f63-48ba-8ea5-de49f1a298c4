package com.caidaocloud.hrpaas.service.application.uiform.service.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.hrpaas.service.application.uiform.service.hook.HookConstant;
import com.caidaocloud.hrpaas.service.application.uiform.service.hook.HookTemplate;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiDataSourceDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiGeneralDataSourceDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-09-07
 */
@Slf4j
@Service
public class GeneralFieldValueAssembleStrategy implements FieldValueAssembleStrategy{

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private HookTemplate hookTemplate;

    @Override
    public void packageFieldValue(Map fieldMap, String fieldKey, String fieldVal, UiDataSourceDto fieldDataSource) {

        List<Map> dataList  = loadDataByDataSource(fieldKey, fieldDataSource);

        dataList.forEach(dataMap -> {
            Object obj = null;
            if(null != dataMap && null != (obj = dataMap.getOrDefault(fieldKey, dataMap.get(HookConstant.CONVERT_SELECT_FEILD_VALUE))) && Objects.equals(obj, fieldVal)){
                fieldMap.put(String.format(HookConstant.FIELD_TXT_FORMAT_TEMPLATE, fieldKey), dataMap.getOrDefault(HookConstant.CONVERT_SELECT_FEILD_TEXT, obj));
            }
        });

    }

    private List<Map> loadDataByDataSource(String fieldKey, UiDataSourceDto fieldDataSource){
        log.info("fieldDataSource={}", JSON.toJSONString(fieldDataSource));
        if(Objects.isNull(fieldDataSource) || Objects.isNull(fieldDataSource.getConfig())){
            return Lists.newArrayList();
        }

        UiGeneralDataSourceDto generalDataSource = fieldDataSource.getConfig();
        if(Objects.isNull(generalDataSource) || Objects.isNull(generalDataSource.getUrl())){
            return Lists.newArrayList();
        }

        fieldKey = String.format(HookConstant.FIELD_FORMAT_TEMPLATE, fieldKey, HookConstant.GENERAL_DATASOURCE_REQUEST_SCOPE_CACHE_KEY);
        List<Map> dataList =  (List<Map>) HolderUtil.get(fieldKey);
        if(generalDataSource.isRequestScope() && null != dataList){
            return dataList;
        }

        String rData = null;
        switch (generalDataSource.getRequestMethod()){
            case GET:
                rData = doGet(generalDataSource.getUrl(), generalDataSource.getRequestParams());
                break;
            case POST:
                rData = doPost(generalDataSource.getUrl(), generalDataSource.getRequestParams());
                break;
            default:
                log.error("Unsupported http request type");
                break;
        }

        dataList = JSON.parseObject(rData, new TypeReference<List<Map>>(){});

        if(!Objects.isNull(generalDataSource.getPostHook())){
            Map<String, Object> binding = new HashMap<>();
            binding.put("sourceList", dataList);
            binding.put("convertMap", generalDataSource.getPostOption());
            // 调用后置处理
            dataList = (List<Map>) hookTemplate.executeObject(generalDataSource.getPostHook(), binding);
        }

        HolderUtil.set(fieldKey, dataList);
        return null == dataList ? Lists.newArrayList() : dataList;
    }

    /**
     * get 请求
     * @param url
     * @param requestParams
     * @return
     */
    private String doGet(String url, Map<String, Object> requestParams){
        String responseData = restTemplate.getForObject(url, String.class, requestParams);
        log.info("do get request url={}, responseData = {}", responseData);
        return responseData;
    }

    /**
     * post请求
     * @param url
     * @param requestParams
     * @return
     */
    private String doPost(String url, Map<String, Object> requestParams){
        MultiValueMap<String, Object> paramMap = new LinkedMultiValueMap();
        if(null != requestParams && !requestParams.isEmpty()){
            requestParams.forEach((key, value) -> {
                paramMap.add(key, value);
            });
        }

        String responseData = restTemplate.postForObject(url, paramMap, String.class);
        log.info("do post request url={}, responseData = {}", responseData);
        return responseData;
    }
}
