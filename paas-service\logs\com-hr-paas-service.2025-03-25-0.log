2025-03-25 10:35:59.453 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-25 10:35:59.477 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-25 10:36:26.431 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-25 10:36:26.437 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-25 10:36:28.499 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='172.16.0.138:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-25 10:36:32.917 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2025-03-25 10:36:37.513 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2025-03-25 10:36:40.433 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-25 10:36:40.438 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-25 10:36:40.571 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 112ms. Found 0 repository interfaces.
2025-03-25 10:36:43.203 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-25 10:36:43.211 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-25 10:36:45.697 [main] ERROR c.j.c.a.AutoloadCacheManageConfiguration - Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1092)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1065)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:865)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:340)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration.createRedisCacheManager(AutoloadCacheManageConfiguration.java:163)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration.autoloadCacheCacheManager(AutoloadCacheManageConfiguration.java:157)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$8e801315.CGLIB$autoloadCacheCacheManager$0(<generated>)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$8e801315$$FastClassBySpringCGLIB$$8e8a799e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:363)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$8e801315.autoloadCacheCacheManager(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:620)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:605)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1239)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1166)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:855)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:758)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1239)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1166)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:855)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:758)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1239)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1166)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:855)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:758)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:109)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1064)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:487)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:391)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:109)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1064)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:487)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:391)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:109)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1064)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:487)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.registerBeanPostProcessors(PostProcessorRegistrationDelegate.java:240)
	at org.springframework.context.support.AbstractApplicationContext.registerBeanPostProcessors(AbstractApplicationContext.java:707)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:531)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:204)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:113)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:113)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1085)
	... 154 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:6379
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:340)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:616)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:563)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:480)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:442)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: no further information
	... 12 common frames omitted
2025-03-25 10:36:46.860 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-25 10:36:46.987 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-25 10:37:40.296 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-25 10:37:40.300 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-25 10:37:41.813 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='172.16.0.138:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-25 10:37:47.671 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2025-03-25 10:37:48.513 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2025-03-25 10:37:50.776 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-25 10:37:50.780 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-25 10:37:50.898 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 99ms. Found 0 repository interfaces.
2025-03-25 10:37:53.252 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-25 10:37:53.257 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-25 10:37:55.689 [main] ERROR c.j.c.a.AutoloadCacheManageConfiguration - Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1092)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1065)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:865)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:340)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration.createRedisCacheManager(AutoloadCacheManageConfiguration.java:163)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration.autoloadCacheCacheManager(AutoloadCacheManageConfiguration.java:157)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$8e801315.CGLIB$autoloadCacheCacheManager$0(<generated>)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$8e801315$$FastClassBySpringCGLIB$$8e8a799e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:363)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$8e801315.autoloadCacheCacheManager(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:620)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:605)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1239)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1166)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:855)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:758)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1239)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1166)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:855)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:758)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1239)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1166)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:855)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:758)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:109)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1064)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:487)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:391)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:109)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1064)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:487)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:391)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:109)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1064)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:487)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.registerBeanPostProcessors(PostProcessorRegistrationDelegate.java:240)
	at org.springframework.context.support.AbstractApplicationContext.registerBeanPostProcessors(AbstractApplicationContext.java:707)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:531)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:204)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:113)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:113)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1085)
	... 154 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:6379
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:340)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:616)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:563)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:480)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:442)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: no further information
	... 12 common frames omitted
2025-03-25 10:37:55.886 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-25 10:37:55.887 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-25 10:38:19.449 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-25 10:38:19.453 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-25 10:38:20.916 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-25 10:38:21.249 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2025-03-25 10:38:22.852 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2025-03-25 10:38:25.586 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-25 10:38:25.593 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-25 10:38:25.776 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 159ms. Found 0 repository interfaces.
2025-03-25 10:38:28.289 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-25 10:38:28.294 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-25 10:38:29.047 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2025-03-25 10:38:29.507 [main] INFO  org.reflections.Reflections - Reflections took 429 ms to scan 2 urls, producing 16 keys and 376 values 
2025-03-25 10:38:29.586 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2025-03-25 10:38:34.296 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-25 10:38:34.296 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-25 10:38:34.320 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@7ef8899d
2025-03-25 10:38:36.995 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:36.999 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:37.000 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:37.087 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:37.212 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:37.507 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:37.603 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:37.634 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:37.749 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:37.896 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:38.681 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:39.130 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-25 10:38:39.430 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-25 10:38:40.282 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-25 10:38:40.343 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2025-03-25 10:38:40.343 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2025-03-25 10:38:40.660 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:45.144 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-25 10:38:45.144 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-25 10:38:46.391 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:46.480 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:46.920 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:46.971 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:47.018 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:49.298 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4cda8d3b[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2025-03-25 10:38:49.300 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@73e054c0[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$f3238da7#prepareRecentTask]
2025-03-25 10:38:49.345 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:49.384 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:49.434 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:49.471 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:49.487 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:49.533 [main] WARN  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback config fail, adminAddresses is null.
2025-03-25 10:38:49.537 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-25 10:38:49.561 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-25 10:38:49.575 [Thread-38] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-25 10:38:49.577 [Thread-38] WARN  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry config fail, adminAddresses is null.
2025-03-25 10:38:49.592 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-25 10:38:50.028 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-25 10:38:50.754 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2025-03-25 10:38:50.918 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-25 10:38:50.974 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-25 10:38:50.998 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2025-03-25 10:38:51.017 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2025-03-25 10:38:51.473 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2025-03-25 10:38:51.508 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-03-25 10:38:51.560 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-03-25 10:38:51.562 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-25 10:38:51.659 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2025-03-25 10:38:51.661 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2025-03-25 10:38:51.662 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2025-03-25 10:38:51.672 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2025-03-25 10:38:51.689 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-03-25 10:38:51.820 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2025-03-25 10:38:51.893 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-25 10:38:52.729 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-25 10:38:52.810 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-25 10:38:52.817 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-25 10:38:52.972 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@343a8b18
2025-03-25 10:38:52.998 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:38:53.037 [Thread-42] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-25 10:38:53.039 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-25 10:38:53.039 [Thread-35] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-25 10:38:53.041 [Thread-35] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-25 10:38:53.041 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-25 10:38:53.052 [Thread-38] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-25 10:38:53.052 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-25 10:38:53.053 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-25 10:38:53.209 [Thread-40] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-25 10:39:36.268 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-25 10:39:36.275 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-25 10:39:39.914 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-25 10:39:41.380 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2025-03-25 10:39:42.726 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2025-03-25 10:39:47.500 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-25 10:39:47.503 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-25 10:39:47.618 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 99ms. Found 0 repository interfaces.
2025-03-25 10:39:49.914 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-25 10:39:49.919 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-25 10:39:50.695 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2025-03-25 10:39:50.911 [main] INFO  org.reflections.Reflections - Reflections took 186 ms to scan 2 urls, producing 16 keys and 376 values 
2025-03-25 10:39:51.020 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2025-03-25 10:39:55.758 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-25 10:39:55.759 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-25 10:39:55.782 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@31887330
2025-03-25 10:39:59.463 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:39:59.468 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:39:59.469 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:39:59.524 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:39:59.684 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:39:59.740 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:39:59.764 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:39:59.802 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:39:59.846 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:39:59.979 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:00.387 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:01.290 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-25 10:40:01.631 [redisson-netty-5-11] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-25 10:40:02.713 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-25 10:40:03.382 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2025-03-25 10:40:03.383 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2025-03-25 10:40:04.011 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:09.185 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-25 10:40:09.186 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-25 10:40:10.625 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:10.666 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:11.173 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:11.243 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:11.294 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:13.328 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@17aa06eb[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2025-03-25 10:40:13.329 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@370c9681[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$557aa31d#prepareRecentTask]
2025-03-25 10:40:13.371 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:13.387 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:13.410 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:13.425 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:13.436 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:13.469 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-25 10:40:13.490 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-25 10:40:13.508 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-25 10:40:13.519 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-25 10:40:13.877 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-25 10:40:14.296 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2025-03-25 10:40:14.648 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2025-03-25 10:40:14.815 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2025-03-25 10:40:14.818 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2025-03-25 10:40:14.821 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2025-03-25 10:40:14.824 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2025-03-25 10:40:14.827 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2025-03-25 10:40:14.830 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2025-03-25 10:40:14.833 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2025-03-25 10:40:14.836 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2025-03-25 10:40:14.948 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-25 10:40:15.005 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-25 10:40:15.028 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2025-03-25 10:40:15.044 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2025-03-25 10:40:15.157 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2025-03-25 10:40:15.195 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2025-03-25 10:40:15.205 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2025-03-25 10:40:15.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-03-25 10:40:15.284 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-25 10:40:15.306 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2025-03-25 10:40:15.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2025-03-25 10:40:15.349 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2025-03-25 10:40:15.363 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-03-25 10:40:15.365 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-25 10:40:15.486 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-25 10:40:15.489 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2025-03-25 10:40:15.492 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2025-03-25 10:40:15.507 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2025-03-25 10:40:15.509 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2025-03-25 10:40:15.517 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2025-03-25 10:40:15.522 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2025-03-25 10:40:15.541 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2025-03-25 10:40:15.547 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2025-03-25 10:40:15.553 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2025-03-25 10:40:15.559 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2025-03-25 10:40:15.570 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2025-03-25 10:40:15.575 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2025-03-25 10:40:15.610 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2025-03-25 10:40:15.628 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2025-03-25 10:40:15.630 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2025-03-25 10:40:15.632 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2025-03-25 10:40:15.637 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2025-03-25 10:40:15.640 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2025-03-25 10:40:15.661 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-03-25 10:40:15.736 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2025-03-25 10:40:15.820 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-25 10:40:16.602 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-25 10:40:16.692 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-25 10:40:16.700 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-25 10:40:16.788 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-25 10:40:16.790 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@20ebc899
2025-03-25 10:40:16.825 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2025-03-25 10:40:17.271 [main] INFO  org.reflections.Reflections - Reflections took 122 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-25 10:40:17.711 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-25 10:40:17.758 [Thread-35] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-25 10:40:17.758 [Thread-35] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-25 10:40:17.758 [Thread-44] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-25 10:40:17.761 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-25 10:40:17.761 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-25 10:40:17.769 [Thread-41] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-25 10:40:17.780 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://192.168.130.64:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-25 10:40:17.780 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-25 10:40:17.780 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-25 10:40:17.782 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-25 10:40:17.783 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-25 10:40:17.783 [Thread-40] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-25 10:40:17.879 [Thread-43] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
