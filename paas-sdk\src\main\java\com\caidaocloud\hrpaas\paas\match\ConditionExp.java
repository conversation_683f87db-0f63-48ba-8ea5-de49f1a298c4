package com.caidaocloud.hrpaas.paas.match;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;

/**
 * <AUTHOR>
 * @date 2022/3/25
 **/
@Data
public class ConditionExp {

    private String name;

    private ConditionOperatorEnum symbol;

    private Object value;

    private String simpleValue;

    private ConditionComponentEnum componentType;

    public boolean match(Map<String, String> empInfo) {
        beforeMatch();
        switch (componentType){
            case ADDRESS:
                Address address = FastjsonUtil.toObject(simpleValue, Address.class);
                simpleValue = String.format(Address.ADDRESS_VALUE_FORMAT, address.getProvince(),
                        address.getCity(), address.getArea(), address.getAddress());
                break;
            case DICT_SELECTOR:
                if(!name.contains(".") && empInfo.containsKey(name + ".dict.value")){
                    empInfo.put(name, empInfo.get(name + ".dict.value"));
                }
                break;
        }
        switch (symbol) {
            case EQ:
                return StringUtils.equals(empInfo.get(name), simpleValue);
            case NE:
                return !StringUtils.equals(empInfo.get(name), simpleValue);
            case IN:
                if (StringUtils.isEmpty(simpleValue)) {
                    return false;
                } else {
                    return Lists.list(simpleValue.split(",")).contains(empInfo.get(name));
                }
            case NOT_CONTAIN:
                if(StringUtils.isEmpty(simpleValue)){
                    return !Objects.equals(empInfo.get(name), simpleValue);
                }
                return !Lists.list(simpleValue.split(",")).contains(empInfo.get(name));
            case IS_NULL: return StringUtils.isEmpty(empInfo.get(name));
            case IS_NOT_NULL: return StringUtils.isNotEmpty(empInfo.get(name));
            case LT: return StringUtils.isEmpty(empInfo.get(name))?false: new BigDecimal(empInfo.get(name)).compareTo(new BigDecimal(simpleValue)) < 0;
            case LE: return StringUtils.isEmpty(empInfo.get(name))?false: new BigDecimal(empInfo.get(name)).compareTo(new BigDecimal(simpleValue)) <= 0;
            case GT: return StringUtils.isEmpty(empInfo.get(name))?false: new BigDecimal(empInfo.get(name)).compareTo(new BigDecimal(simpleValue)) > 0;
            case GE: return StringUtils.isEmpty(empInfo.get(name))?false: new BigDecimal(empInfo.get(name)).compareTo(new BigDecimal(simpleValue)) >= 0;
            case CONTAIN_CHILD: return containChild(empInfo.get(name), simpleValue);
            default:
                throw new ServerException("匹配条件不支持的比较符");
        }
    }

    private void beforeMatch() {
        if (value == null) {
            return;
        }
        if (simpleValue != null) {
            return;
        }
        if (symbol == ConditionOperatorEnum.IN || symbol == ConditionOperatorEnum.NOT_CONTAIN) {
            simpleValue = StringUtils.join(FastjsonUtil.convertObject(value,List.class), ",");
        }else {
            simpleValue=(String) value;
        }
    }

    public boolean matchExp(Map<String, String> preEmp,
        BiFunction<Map<String, String>, ConditionExp, Boolean> function) {
        return function.apply(preEmp, this);
    }


    public boolean containChild(String organize, String simpleValue, Long dataTime){
        if(Objects.equals(organize, simpleValue)){
            return true;
        }

        if(null != simpleValue && null != organize && simpleValue.contains(organize)){
            return true;
        }

        if(null != simpleValue){
            String [] sValues = simpleValue.split(",");
            for (String sValue : sValues) {
                if(doContainChild(organize, sValue, dataTime)){
                    return true;
                }
            }
        }
        return false;
    }

    private boolean doContainChild(String organize, String simpleValue, Long dataTime){
        DataFilter filter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
                .and(DataFilter.eq("bid", simpleValue).orRegex("pid$path", simpleValue))
                .andEq("deleted", Boolean.FALSE.toString());
        PageResult<DataSimple> pageResult = DataQuery.identifier("entity.hr.Org")
                .decrypt().specifyLanguage()
                .queryInvisible().limit(5000, 1)
                .filter(filter, DataSimple.class, dataTime);
        if(null == pageResult || null == pageResult.getItems() || pageResult.getItems().isEmpty()){
            return false;
        }

        return null != pageResult.getItems().stream()
                .filter(it -> it.getBid().equals(organize))
                .findFirst().orElse(null);
    }

    public boolean containChild(String organize, String simpleValue){
        return containChild(organize, simpleValue, System.currentTimeMillis());
    }
}
