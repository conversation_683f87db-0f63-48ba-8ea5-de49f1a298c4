package com.caidaocloud.metadata.domain.entity

import com.caidaocloud.hrpaas.metadata.sdk.enums.RelationOperationType
import com.caidaocloud.metadata.domain.entity.EntityDataChange
import com.caidaocloud.metadata.domain.enums.OperationType

class EntityEvent {
//    var preData : Map<String, Any?>? = null
//    var postData : Map<String, Any?>? = null
    var data : List<EntityDataChange> = listOf()
    var dataOperationType : OperationType? = null
    var relationOperationType : RelationOperationType? = null
    var isRelationOperation : Boolean = false
    var eventIdentifier : String = ""
    var dataId : String? = null
}