package com.caidaocloud.hrpaas.service.application.form.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormWidgetType;
import com.caidaocloud.hrpaas.service.interfaces.vo.form.FormPropDefVo;
import com.caidaocloud.metadata.domain.entity.PropertyEnumDef;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FormPropDefDto {

    private String property;

    private String name;

    private Map<String, String> i18nName;

    private PropertyDataType dataType;

    private FormWidgetType widgetType;

    private List<PropertyEnumDef> enumDef= Lists.list();

    private Map componentDetail = Maps.map();

    private boolean required = false;

    private boolean encrypted = false;

    private String styleExtras;

    private List<FormPropRuleDto> rules = Lists.list();

    private String onEvent;

    // 表单字段说明
    private String desc;

    private Map<String, String> i18nDesc;

    private List<FormPropDefDto> slaveProperties;
}
