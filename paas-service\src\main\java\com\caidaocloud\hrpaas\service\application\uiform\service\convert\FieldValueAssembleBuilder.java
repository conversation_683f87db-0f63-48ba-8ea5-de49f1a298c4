package com.caidaocloud.hrpaas.service.application.uiform.service.convert;

import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceType;
import com.caidaocloud.util.SpringUtil;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class FieldValueAssembleBuilder {
    /**
     * TokenGranter缓存池
     */
    private static Map<DataSourceType, FieldValueAssembleStrategy> AssemblePool = new ConcurrentHashMap<>();

    static {
        AssemblePool.put(DataSourceType.SYSTEM_DICT, SpringUtil.getBean(SysDictFieldValueAssembleStrategy.class));
        AssemblePool.put(DataSourceType.EXTERNAL_SYSTEM, SpringUtil.getBean(GeneralFieldValueAssembleStrategy.class));
    }

    public static FieldValueAssembleStrategy getFieldValueAssembleStrategy(DataSourceType dataSourceType){
        FieldValueAssembleStrategy fieldValueAssemble = AssemblePool.getOrDefault(dataSourceType, AssemblePool.get(DataSourceType.SYSTEM_DICT));
        return fieldValueAssemble;
    }
}
