package com.caidaocloud.hrpaas.service.test;

import com.caidaocloud.hrpaas.service.application.uiform.dto.UiFormDto;

/**
 * <AUTHOR>
 * @Date 2022/1/5
 */
public class PageSchemaParseTest {

    public static void main(String[] args) throws Exception {

        //Test-模型页面-普通列表页
//        String schema = "{\"key\":\"Page_8zSsKgFGjhfNocjBcUyRYG\",\"type\":\"Page\",\"label\":\"页面\",\"props\":{\"padding\":\"12px 12px 12px 12px\",\"backgroundColor\":\"#ffffff\"},\"childList\":[{\"key\":\"Filter_13PyJ3AehTKgRXQ2XWYLLy\",\"type\":\"Search\",\"label\":\"筛选\",\"props\":{\"cols\":2,\"action\":true,\"labelWidth\":\"100px\",\"options\":{}},\"action\":[{\"name\":\"onSearch\",\"list\":[{\"key\":\"hgZNtiRSrr62zN95GSY7Ku\",\"type\":[\"script\"],\"value\":\"refreshTable\"}]},{\"name\":\"onReset\",\"list\":[{\"key\":\"6xeryZonH7BTVBNLPCTZc4\",\"type\":[\"script\"],\"value\":\"refreshTable\"}]}],\"childList\":[{\"key\":\"Input_name\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"名称\",\"prop\":\"name\",\"placeholder\":\"请输入名称\",\"dataType\":\"string\",\"clearable\":true}}]},{\"key\":\"Panel_by653oQdZn9tQ5VSK77rsU\",\"type\":\"Panel\",\"label\":\"容器\",\"props\":{\"direction\":\"column\",\"padding\":\"0px 15px 15px 15px\"},\"childList\":[{\"key\":\"Toolbar_7vQBWx4yrqRsG1qbx3hAZi\",\"type\":\"Panel\",\"label\":\"标题栏\",\"props\":{\"display\":\"flex\",\"justify\":\"center\",\"align\":\"between\",\"margin\":\"15px 0px 15px 0px\"},\"childList\":[{\"key\":\"Panel_aJqN5aizzxK5BPA22bJdXx\",\"type\":\"Panel\",\"label\":\"容器\",\"props\":{\"display\":\"block\",\"direction\":\"row\",\"width\":\"auto\",\"height\":\"auto\"},\"childList\":[{\"key\":\"Button_create_mPy3is1FnWBeJvfhx2GFYq\",\"type\":\"Button\",\"label\":\"新增\",\"props\":{\"type\":\"primary\",\"content\":\"新增\"},\"action\":[{\"name\":\"onClick\",\"list\":[{\"key\":\"mzX36Xh58abMcJmFeSivR1\",\"type\":[\"script\"],\"value\":\"showEditorCreate\"}]}]}]},{\"key\":\"Panel_aEgz4UiX9MhYgixdZoome2\",\"type\":\"Panel\",\"label\":\"容器\",\"props\":{\"display\":\"flex\",\"direction\":\"row\",\"width\":\"auto\",\"height\":\"auto\"},\"childList\":[{\"key\":\"Button_import_qPmYJ97Pf7RnbxgdpDzVSN\",\"type\":\"Button\",\"label\":\"按钮\",\"props\":{\"content\":\"导入\",\"margin\":\"0 8px 0 0\"},\"action\":[{\"name\":\"onClick\",\"list\":[{\"key\":\"aS2zhPocm5qTNWxLz291oe\",\"type\":[\"script\"],\"value\":\"importData\"}]}]},{\"key\":\"Button_export_9WsVboRwCn6YZvWdnETnVe\",\"type\":\"Button\",\"label\":\"按钮\",\"props\":{\"content\":\"导出\"},\"action\":[{\"name\":\"onClick\",\"list\":[{\"key\":\"gzQWwcxrnWSwbEby8BdDo9\",\"type\":[\"script\"],\"value\":\"exportData\"}]}]}]}]},{\"key\":\"Table_3HbwBisa7niNZ7YvyckNwj\",\"type\":\"Table\",\"label\":\"列表\",\"props\":{\"formId\":\"nMjg4nPUFP9Y7jGbQNDmM7\",\"pagination\":true,\"border\":true,\"stripe\":true,\"size\":\"mini\",\"pageSize\":10,\"pageSizes\":[10,20,50,100,200],\"options\":{\"url\":\"/api/hrpaas/uiform/data/v1/list\",\"params\":[{\"key\":\"uz8q626VxqMiaGNGisXUB5\",\"name\":\"name\",\"dataType\":\"string\",\"operator\":\"=\",\"fieldType\":[\"page\",null,\"Filter_13PyJ3AehTKgRXQ2XWYLLy\",\"Input_name\"]},{\"key\":\"no3V1PZfHv2TrgSeVxR3vg\",\"name\":\"st\",\"dataType\":\"string\",\"operator\":\"=\",\"fieldType\":[\"page\",null,\"Filter_13PyJ3AehTKgRXQ2XWYLLy\",\"EmbeddedModel_st\"]}]}},\"childList\":[{\"key\":\"TableColumn_s4VkHuTw5udjWhnMb8jGab\",\"type\":\"TableColumn\",\"label\":\"数据列\",\"isConfig\":false,\"props\":{\"dataIndex\":\"name\",\"title\":\"名称\",\"dataType\":\"string\",\"showOverflowTooltip\":true,\"resizable\":true,\"sortable\":true}},{\"key\":\"TableColumn_operator\",\"type\":\"TableColumn\",\"label\":\"操作列\",\"isConfig\":false,\"slotScope\":true,\"props\":{\"title\":\"操作\",\"dataIndex\":\"operator\"},\"childList\":[{\"key\":\"Button_edit\",\"type\":\"Button\",\"label\":\"按钮\",\"isConfig\":false,\"props\":{\"type\":\"text\",\"margin\":\"0 8px 0 0\",\"size\":\"small\"},\"action\":[{\"name\":\"onClick\",\"list\":[{\"key\":\"aLo8dQpzrUBiPhs5ePFMfB\",\"type\":[\"script\"],\"value\":\"editTable\"}]}],\"childList\":[\"编辑\"]},{\"key\":\"Button_delete\",\"type\":\"Button\",\"label\":\"按钮\",\"isConfig\":false,\"props\":{\"type\":\"text\",\"size\":\"small\",\"confirm\":true,\"confirmText\":\"是否确认删除？\"},\"action\":[{\"name\":\"onClick\",\"list\":[{\"key\":\"cs4U8sBBkozxdgNzvQvsUp\",\"type\":[\"script\"],\"value\":\"deleteRowData\"}]}],\"childList\":[\"删除\"]}]}]}]},{\"key\":\"Editor_create_uqLqWRezYPRiGfgPjJ5L6b\",\"type\":\"Dialog\",\"label\":\"新增\",\"props\":{\"width\":\"700px\",\"padding\":\"15px 0px 0px 0px\",\"visible\":false,\"appendToBody\":true,\"modal\":true},\"action\":[{\"name\":\"onClose\",\"list\":[{\"key\":\"f2cr9J3TvzUfcJY8Zaae2c\",\"type\":[\"script\"],\"value\":\"closeEditorCreate\"}]},{\"name\":\"onOk\",\"list\":[{\"key\":\"kqshf2Qu6CZCxwqFuJtV3P\",\"type\":[\"script\"],\"value\":\"submitCreateForm\"}]}],\"childList\":[{\"key\":\"Form_create_v6eGhFmDrhdHqasMvhnEKj\",\"type\":\"Form\",\"label\":\"表单\",\"props\":{\"labelWidth\":\"100px\",\"cols\":1,\"labelPosition\":\"left\",\"layout\":\"vertical\",\"formId\":\"gbeKg2eDYg8Skov3XZXVqq\",\"options\":{}},\"childList\":[{\"key\":\"Input_r6s1LDHi2aQFYcVJQcV9Bj\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"名称\",\"prop\":\"name\",\"placeholder\":\"请输入名称\",\"dataType\":\"string\",\"required\":false,\"width\":\"50%\"}},{\"key\":\"EmbeddedModel_suHmxaBvExgK29dFJ5WLEu\",\"type\":\"EmbeddedModel\",\"label\":\"可编辑关联模型\",\"props\":{\"label\":\"st\",\"prop\":\"st\",\"placeholder\":\"请输入st\",\"dataType\":\"string\",\"required\":false,\"width\":\"50%\",\"options\":{\"dataSourceId\":\"entity.hr.st\"},\"content\":[{\"key\":\"Input_stname\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"st名称\",\"prop\":\"stname\",\"placeholder\":\"请输入st名称\",\"dataType\":\"string\",\"clearable\":true,\"required\":false}}]}}],\"action\":[{\"name\":\"onSubmitSuccess\",\"list\":[{\"key\":\"9sQd2MseNXeKHrfyuu7Yii\",\"type\":[\"script\"],\"value\":\"onEditorCreateSubmitSuccess\"}]}]}]},{\"key\":\"Editor_edit_vZQM9ewHEKkthXdUEGzo4G\",\"type\":\"Drawer\",\"label\":\"编辑\",\"props\":{\"width\":\"700px\",\"padding\":\"15px 0px 0px 0px\",\"visible\":false,\"appendToBody\":true,\"modal\":true},\"action\":[{\"name\":\"onClose\",\"list\":[{\"key\":\"4hE8BiPkA2HHoD57rFQ619\",\"type\":[\"script\"],\"value\":\"closeEditorEdit\"}]},{\"name\":\"onOk\",\"list\":[{\"key\":\"25ZY2Mc7KJ5dXU8vDQP2Dk\",\"type\":[\"script\"],\"value\":\"submitEditForm\"}]}],\"childList\":[{\"key\":\"Form_edit_1pdDd6NKNoQj9e5PoJZHHD\",\"type\":\"Form\",\"label\":\"表单\",\"props\":{\"labelWidth\":\"100px\",\"cols\":1,\"labelPosition\":\"left\",\"layout\":\"vertical\",\"formId\":\"6uvsE8ipni1iFT5jNSTJRJ\",\"options\":{},\"initApi\":\"/api/hrpaas/uiform/data/v1/detail?dataId=${dataId}&formId=${formId}\"},\"childList\":[{\"key\":\"Input_7qaxd3wJsSL6csSAXnqGs1\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"名称\",\"prop\":\"name\",\"placeholder\":\"请输入名称\",\"dataType\":\"string\",\"required\":false,\"width\":\"50%\"}},{\"key\":\"EmbeddedModel_w8XPyH3PRnCb5PYCy6cQ3D\",\"type\":\"EmbeddedModel\",\"label\":\"可编辑关联模型\",\"props\":{\"label\":\"st\",\"prop\":\"st\",\"placeholder\":\"请输入st\",\"dataType\":\"string\",\"required\":false,\"width\":\"50%\",\"options\":{\"dataSourceId\":\"entity.hr.st\"},\"content\":[{\"key\":\"Input_stname\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"st名称\",\"prop\":\"stname\",\"placeholder\":\"请输入st名称\",\"dataType\":\"string\",\"clearable\":true,\"required\":false}}]}}],\"action\":[{\"name\":\"onSubmitSuccess\",\"list\":[{\"key\":\"pHJgAn3j9pqsXaARKgo8JC\",\"type\":[\"script\"],\"value\":\"onEditorEditSubmitSuccess\"}]}]}]}],\"script\":\"/**\\\\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系才到平台的技术支持获得服务（可能收费）。\\\\n* 我们可以用 JS 面板来开发一些定制度高功能。\\\\n* 你可以点击面板上方的 「使用帮助」了解。\\\\n*/\\\\n\\\\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\\\\nexport function didMount() {\\\\n\\\\tconsole.log(`「页面 JS」：当前页面地址 ${location.href}`);\\\\n // 更多相关 API 请参考：https://aliwork.com/developer/API\\\\n}\\\\n\\\\n//刷新表格\\\\nexport function refreshTable () {\\\\n\\\\tlet table = ck.getComponent('Table_3HbwBisa7niNZ7YvyckNwj'); \\\\n\\\\ttable.refresh();\\\\n}\\\\n\\\\n//显示弹窗\\\\nexport function showEditorCreate () {\\\\n\\\\tck.setProps({\\\\n\\\\t\\\\t['Form_create_v6eGhFmDrhdHqasMvhnEKj']: {options: {}},\\\\n\\\\t\\\\t['Editor_create_uqLqWRezYPRiGfgPjJ5L6b']: {visible: true}\\\\n\\\\t});\\\\n \\\\n}\\\\n\\\\n//关闭弹窗\\\\nexport function closeEditorCreate () {\\\\n\\\\tck.setProps('Editor_create_uqLqWRezYPRiGfgPjJ5L6b', 'visible', false); \\\\n}\\\\n\\\\n//关闭弹窗\\\\nexport function closeEditorEdit () {\\\\n\\\\tck.setProps('Editor_edit_vZQM9ewHEKkthXdUEGzo4G', 'visible', false); \\\\n}\\\\n\\\\n//编辑表单\\\\nexport function editTable (e, data) {\\\\n\\\\n\\\\nconst {id} = data || {};\\\\n\\\\n\\\\nif (!id) return;\\\\n\\\\tck.setProps({\\\\n\\\\t\\\\t['Form_edit_1pdDd6NKNoQj9e5PoJZHHD']: {dataId: id},\\\\n\\\\t\\\\t['Editor_edit_vZQM9ewHEKkthXdUEGzo4G']: {visible: true}\\\\n\\\\t});\\\\n \\\\n}\\\\n\\\\n//提交表单\\\\nexport function submitCreateForm () {\\\\n\\\\tlet form = ck.getComponent('Form_create_v6eGhFmDrhdHqasMvhnEKj'); \\\\n\\\\tform.submit();\\\\n}\\\\n\\\\n//提交表单\\\\nexport function submitEditForm () {\\\\n\\\\tlet form = ck.getComponent('Form_edit_1pdDd6NKNoQj9e5PoJZHHD'); \\\\n\\\\tform.submit();\\\\n}\\\\n\\\\n//新增表单提交成功\\\\nexport function onEditorCreateSubmitSuccess () {\\\\n\\\\t\\\\t\\\\tlet table = ck.getComponent('Table_3HbwBisa7niNZ7YvyckNwj'); \\\\n\\\\t\\\\t\\\\ttable.refresh();\\\\n\\\\tck.setProps({\\\\n\\\\t\\\\t['Editor_create_uqLqWRezYPRiGfgPjJ5L6b']: {visible: false}\\\\n\\\\t});\\\\n \\\\n}\\\\n\\\\n//编辑表单提交成功\\\\nexport function onEditorEditSubmitSuccess () {\\\\n\\\\t\\\\t\\\\tlet table = ck.getComponent('Table_3HbwBisa7niNZ7YvyckNwj'); \\\\n\\\\t\\\\t\\\\ttable.refresh();\\\\n\\\\tck.setProps({\\\\n\\\\t\\\\t['Editor_edit_vZQM9ewHEKkthXdUEGzo4G']: {visible: false}\\\\n\\\\t});\\\\n \\\\n}\\\\n\\\\n//删除表格行数据\\\\nexport function deleteRowData (e, data) {\\\\n\\\\tck.http.post('/api/hrpaas/uiform/data/v1/remove', {id: data.id, formId: data.formId}).then(res=>{\\\\n\\\\t\\\\tif(res) {\\\\n\\\\t\\\\t\\\\tlet table = ck.getComponent('Table_3HbwBisa7niNZ7YvyckNwj'); \\\\n\\\\t\\\\t\\\\ttable.refresh();\\\\n\\\\t\\\\t}\\\\n\\\\t})\\\\n\\\\n}\\\\n\\\\n//列表数据导入\\\\nexport function importData () {\\\\n\\\\tlet table = ck.getComponent('Table_3HbwBisa7niNZ7YvyckNwj'); \\\\n\\\\ttable.refresh();\\\\n}\\\\n\\\\n//列表数据导入\\\\nexport function exportData () {\\\\n\\\\tlet table = ck.getComponent('Table_3HbwBisa7niNZ7YvyckNwj'); \\\\n\\\\ttable.refresh();\\\\n}\"}";
        //Test-模型页面-表单提交页
//        String schema = "{\"key\":\"Page_f3Q42b9n3bHEfybEychURS\",\"type\":\"Page\",\"label\":\"页面\",\"props\":{\"padding\":\"12px 12px 12px 12px\"},\"childList\":[{\"key\":\"Form_p2jaxrDQvmJUrDBaLiXUMv\",\"type\":\"Form\",\"label\":\"表单\",\"props\":{\"labelWidth\":\"100px\",\"cols\":1,\"labelPosition\":\"left\",\"layout\":\"vertical\",\"formId\":\"gz6S9Bfp4QmiG6wDRZ5gCn\",\"options\":{},\"hasFooter\":true},\"childList\":[{\"key\":\"Input_2Et9NR3CwY8ESarF5xMekp\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"名称\",\"prop\":\"name\",\"placeholder\":\"请输入名称\",\"dataType\":\"string\",\"required\":false,\"width\":\"50%\"}},{\"key\":\"EmbeddedModel_tcN8A9tTc1AKsr641y67WZ\",\"type\":\"EmbeddedModel\",\"label\":\"可编辑关联模型\",\"props\":{\"label\":\"st\",\"prop\":\"st\",\"placeholder\":\"请输入st\",\"dataType\":\"string\",\"required\":false,\"width\":\"50%\",\"options\":{\"dataSourceId\":\"entity.hr.st\"},\"content\":[{\"key\":\"Input_stname\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"st名称\",\"prop\":\"stname\",\"placeholder\":\"请输入st名称\",\"dataType\":\"string\",\"clearable\":true,\"required\":false}}]}}]}],\"script\":\"/**\\\\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系才到平台的技术支持获得服务（可能收费）。\\\\n* 我们可以用 JS 面板来开发一些定制度高功能。\\\\n* 你可以点击面板上方的 「使用帮助」了解。\\\\n*/\\\\n\\\\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\\\\nexport function didMount() {\\\\n\\\\tconsole.log(`「页面 JS」：当前页面地址 ${location.href}`);\\\\n // 更多相关 API 请参考：https://aliwork.com/developer/API\\\\n}\"}";
        //Test-模型页面-表单编辑页
//        String schema ="{\"key\":\"Page_qLDYjFBwpW54GrBeNLCfNT\",\"type\":\"Page\",\"label\":\"编辑表单\",\"props\":{\"padding\":\"12px 12px 12px 12px\"},\"childList\":[{\"key\":\"Form_vTsXZqQqUWjkCbErDnKCZE\",\"type\":\"Form\",\"label\":\"表单\",\"props\":{\"labelWidth\":\"100px\",\"cols\":1,\"labelPosition\":\"left\",\"layout\":\"vertical\",\"formId\":\"3HcUYTtoXPUBB35hNRE92u\",\"options\":{},\"hasFooter\":true,\"initApi\":\"/api/hrpaas/uiform/data/v1/detail?dataId=${dataId}&formId=${formId}\"},\"childList\":[{\"key\":\"Input_wRRzbsRaRUjK9UGfJKVf5s\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"名称\",\"prop\":\"name\",\"placeholder\":\"请输入名称\",\"dataType\":\"string\",\"required\":false,\"width\":\"50%\"}},{\"key\":\"EmbeddedModel_i9L3sFZquuYi8spY4XGnnc\",\"type\":\"EmbeddedModel\",\"label\":\"可编辑关联模型\",\"props\":{\"label\":\"st\",\"prop\":\"st\",\"placeholder\":\"请输入st\",\"dataType\":\"string\",\"required\":false,\"width\":\"50%\",\"options\":{\"dataSourceId\":\"entity.hr.st\"},\"content\":[{\"key\":\"Input_stname\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"st名称\",\"prop\":\"stname\",\"placeholder\":\"请输入st名称\",\"dataType\":\"string\",\"clearable\":true,\"required\":false}}]}}]}],\"action\":[{\"name\":\"didMount\",\"list\":[{\"key\":\"eQADNWQVFo4qPhpkrrEPer\",\"type\":[\"script\"],\"value\":\"didMount\"}]}],\"script\":\"\\\\n\\\\n//页面初始化\\\\nexport function didMount () {\\\\n\\\\n\\\\nconst {id} = ck.locationUtil.parseSearch(location);\\\\n\\\\tck.setProps({\\\\n\\\\t\\\\t['Form_vTsXZqQqUWjkCbErDnKCZE']: {dataId: id}\\\\n\\\\t});\\\\n \\\\n}\"}";
        //Test-普通页面-表单
//        String schema ="{\"key\":\"Page_vwZCrCaoJcCief1vRx1czX\",\"type\":\"Page\",\"label\":\"页面\",\"props\":{\"display\":\"flex\",\"direction\":\"column\",\"padding\":\"12px 12px 12px 12px\"},\"childList\":[{\"label\":\"表单\",\"type\":\"Form\",\"props\":{\"cols\":3,\"layout\":\"horizontal\",\"labelPosition\":\"right\",\"hasFooter\":true,\"labelWidth\":\"80px\",\"prop\":\"prop_hvdv6BKPXPWVfk2HFgym13\"},\"childList\":[{\"key\":\"Form_Input_1\",\"type\":\"Input\",\"label\":\"单行文本\",\"props\":{\"label\":\"姓名\",\"prop\":\"title\",\"dataType\":\"string\",\"placeholder\":\"请输入姓名\",\"clearable\":true}},{\"type\":\"InputNumber\",\"label\":\"数值\",\"icon\":\"square-o\",\"props\":{\"label\":\"数值\",\"dataType\":\"number\",\"placeholder\":\"数值\",\"prop\":\"prop_3i1WLExHc7wa7Y5B8CFQ5v\"},\"key\":\"InputNumber_i7tQnxAgPfyVsGnDiMdsSv\"},{\"label\":\"下拉单选\",\"icon\":\"caret-square-o-down\",\"type\":\"Select\",\"props\":{\"label\":\"下拉单选\",\"dataType\":\"number\",\"placeholder\":\"下拉单选\",\"options\":[{\"value\":1,\"label\":\"选项一\"},{\"value\":2,\"label\":\"选项二\"},{\"value\":3,\"label\":\"选项三\"}],\"prop\":\"prop_gsbwSkGXSicD739w5UwD89\"},\"key\":\"Select_kWjqESHrw9fBEi8kLZJrHw\"}],\"key\":\"Form_6DD93Gf5J2UsrLNYRcQ5ZP\"}],\"script\":\"/**\\\\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系才到平台的技术支持获得服务（可能收费）。\\\\n* 我们可以用 JS 面板来开发一些定制度高功能。\\\\n* 你可以点击面板上方的 「使用帮助」了解。\\\\n*/\\\\n\\\\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\\\\nexport function didMount() {\\\\n\\\\tconsole.log(`「页面 JS」：当前页面地址 ${location.href}`);\\\\n // 更多相关 API 请参考：https://aliwork.com/developer/API\\\\n}\"}";
        //Test-普通页面
        String schema ="{\"key\":\"Page_dqp4KTkqm5cTQF8Xazhcxf\",\"type\":\"Page\",\"label\":\"页面\",\"props\":{\"display\":\"flex\",\"direction\":\"column\",\"padding\":\"12px 12px 12px 12px\"},\"childList\":[{\"label\":\"单行文本\",\"icon\":\"square-o\",\"type\":\"Input\",\"props\":{\"label\":\"单行文本\",\"dataType\":\"string\",\"placeholder\":\"单行文本\",\"prop\":\"prop_1ekVJ5dXK6x7e6bASo7wKX\"},\"key\":\"Input_imwdhSvEp2BNTJWzpBTWQo\"},{\"type\":\"InputNumber\",\"label\":\"数值\",\"icon\":\"square-o\",\"props\":{\"label\":\"数值\",\"dataType\":\"number\",\"placeholder\":\"数值\",\"prop\":\"prop_pEFpb6K5VH7Bgkm6eMwoFX\"},\"key\":\"InputNumber_wqxgStceE7xnc4uB2tpLUn\"},{\"label\":\"下拉单选\",\"icon\":\"caret-square-o-down\",\"type\":\"Select\",\"props\":{\"label\":\"下拉单选\",\"dataType\":\"number\",\"placeholder\":\"下拉单选\",\"options\":[{\"value\":1,\"label\":\"选项一\"},{\"value\":2,\"label\":\"选项二\"},{\"value\":3,\"label\":\"选项三\"}],\"prop\":\"prop_iYN5xM6hLobkDE4ZyGzFGZ\"},\"key\":\"Select_wUh3yaYPemrvihq2Pf1FBc\"}],\"script\":\"/**\\\\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系才到平台的技术支持获得服务（可能收费）。\\\\n* 我们可以用 JS 面板来开发一些定制度高功能。\\\\n* 你可以点击面板上方的 「使用帮助」了解。\\\\n*/\\\\n\\\\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\\\\nexport function didMount() {\\\\n\\\\tconsole.log(`「页面 JS」：当前页面地址 ${location.href}`);\\\\n // 更多相关 API 请参考：https://aliwork.com/developer/API\\\\n}\"}";
        UiFormDto uiFormDto = new UiFormDto();
        uiFormDto.setSchema(schema);
//        PageDto pageDto = FastjsonUtil.toObject(schema, PageDto.class);
        uiFormDto.doParseSchema();
        System.out.println(uiFormDto.toString());
    }
}
