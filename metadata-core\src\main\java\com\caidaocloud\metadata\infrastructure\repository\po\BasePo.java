package com.caidaocloud.metadata.infrastructure.repository.po;

import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.annotation.TableId;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import lombok.val;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Data
public abstract class BasePo<P extends BasePo, E extends BaseEntity>{

    @TableId
    public Long id;

    public static <P extends BasePo, E extends BaseEntity> P fromEntity(Class<P> clazz, E entity){
        return JsonEnhanceUtil.toObject(entity, clazz);
    }

    public E toEntity(Class<E> clazz){
        val poClazz = this.getClass();

        val dataMap = JsonEnhanceUtil.toObject(this, Map.class);

        val classFields = poClazz.getDeclaredFields();
        Arrays.stream(classFields)
                .filter(field -> field.isAnnotationPresent(DisplayAsArray.class)
                        && dataMap.get(field.getName()) != null && dataMap.get(field.getName()) instanceof String)
                .forEach(field ->{
                            String value = (String) dataMap.get(field.getName());
                            dataMap.put(field.getName(), FastjsonUtil.toObject(value, new TypeReference<List>(){}));
                        }
                );
        Arrays.stream(classFields)
                .filter(field -> field.isAnnotationPresent(DisplayAsObject.class)
                        && dataMap.get(field.getName()) != null && dataMap.get(field.getName()) instanceof String)
                .forEach(field ->{
                            String value = (String) dataMap.get(field.getName());
                            dataMap.put(field.getName(), FastjsonUtil.toObject(value, HashMap.class));
                        }
                );
        return JsonEnhanceUtil.toObject(dataMap, clazz);
    }

    public static <P extends BasePo, E extends BaseEntity> List<E> toEntityList(Class<E> clazz, List<P> poList) {
        return CollectionUtils.isEmpty(poList) ? new ArrayList<>() : poList.stream().map(po ->(E) po.toEntity(clazz)).collect(Collectors.toList());
    }

}
