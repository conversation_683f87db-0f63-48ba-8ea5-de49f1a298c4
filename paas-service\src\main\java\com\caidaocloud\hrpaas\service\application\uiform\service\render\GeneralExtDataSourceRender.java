package com.caidaocloud.hrpaas.service.application.uiform.service.render;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.service.application.uiform.dto.UiDataSourceSysDictRenderDto;
import com.caidaocloud.hrpaas.service.application.uiform.service.hook.HookTemplate;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceAuthMode;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceRequestType;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiDataSourceRenderDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiGeneralDataSourceDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-10-21
 */
@Slf4j
@Service
public class GeneralExtDataSourceRender implements DataSourceRender {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private HookTemplate hookTemplate;

    @Override
    public PageResult<Map> doRender(String dataSourceConfig, Integer pageNo, Integer pageSize, Map<String, String[]> parameterMap) {
        log.info("General Ext system request parameters. dataSourceConfig={}, pageNo={}, pageSize={}", dataSourceConfig, pageNo, pageSize);
        UiDataSourceSysDictRenderDto dataSourceRenderDto = FastjsonUtil.toObject(dataSourceConfig, UiDataSourceSysDictRenderDto.class);
        return doRender(dataSourceRenderDto, pageNo, pageSize, parameterMap);
    }

    @Override
    public PageResult<Map> doRender(UiDataSourceRenderDto dataSourceConfig, Integer pageNo, Integer pageSize, Map<String, String[]> parameterMap) {
        if(null == dataSourceConfig){
            return PageResult.pageResultEmpty();
        }

        UiGeneralDataSourceDto renderDto = FastjsonUtil.toObject(FastjsonUtil.toJson(dataSourceConfig), UiGeneralDataSourceDto.class);
        return this.defaultRender(renderDto, parameterMap);
    }

    private PageResult<Map> defaultRender(UiGeneralDataSourceDto dataSourceRenderDto, Map<String, String[]> parameterMap){
        if(null != dataSourceRenderDto.getRequestWay() && DataSourceAuthMode.HTTP_USER_PWD.equals(dataSourceRenderDto.getRequestWay())){
            PreCheck.preCheckArgument(null == dataSourceRenderDto.getRequestParams(), "http账号密码，用户名和密码不能为空！");

            String requestName = (String) dataSourceRenderDto.getRequestParams().get("requestName"),
                requestPassword = (String) dataSourceRenderDto.getRequestParams().get("requestPassword");
            PreCheck.preCheckArgument(StringUtil.isEmpty(requestName) || StringUtil.isEmpty(requestPassword),
                     "http账号密码，用户名和密码不能为空！");
        }

        if(null != parameterMap && !parameterMap.isEmpty()){
            Map<String, Object> requestParams = null == dataSourceRenderDto.getRequestParams() ?
                    new HashMap<>() : dataSourceRenderDto.getRequestParams();
            parameterMap.forEach((k, v) -> {
                requestParams.put(k, v);
            });
            dataSourceRenderDto.setRequestParams(requestParams);
        }

        // 暂不支持的请求 Unsupported request
        String rData = null;
        switch (dataSourceRenderDto.getRequestMethod()){
            case POST:
                rData = doPost(dataSourceRenderDto.getContentType(),
                        dataSourceRenderDto.getUrl(), null);
                break;
            case GET:
                rData = doGet(dataSourceRenderDto.getUrl(), dataSourceRenderDto.getRequestParams());
                break;
            default:
                log.error("Unsupported http request type");
                break;
        }

        List<Map> dataList = null;
        if(null != rData && rData.indexOf("\"code\":200") > -1 && rData.indexOf("\"msg\":\"success\",") > -1){
            Result result = FastjsonUtil.toObject(rData, new TypeReference<Result<List<Map>>>(){});
            dataList = null != result && null != result.getData() ? (List<Map>) result.getData() : null;
        } else {
            dataList = FastjsonUtil.toObject(rData, new TypeReference<List<Map>>(){});
        }

        if(!Objects.isNull(dataSourceRenderDto.getPostHook())){
            Map<String, Object> binding = new HashMap<>();
            binding.put("convertMap", dataSourceRenderDto.getPostOption());
            binding.put("sourceList", dataList);
            // 调用后置处理
            dataList = (List<Map>) hookTemplate.executeObject(dataSourceRenderDto.getPostHook(), binding);
        }

        if(null == dataList || dataList.isEmpty()){
            return PageResult.pageResultEmpty();
        }

        PageResult pageResult = new PageResult(dataList, 1, dataList.size(), dataList.size());
        return pageResult;
    }

    /**
     * get 请求
     * @param url
     * @param requestParams
     * @return
     */
    public String doGet(String url, Map<String, Object> requestParams){
        UriComponentsBuilder uri = UriComponentsBuilder.fromUriString(url);
        requestParams.forEach((key, value) -> {
            if(value instanceof String[]){
                uri.queryParam(key, String.join(",", (String[]) value));
                return;
            }
            uri.queryParam(key, value);
        });
        url = uri.build().toUri().toString();
        String responseData = restTemplate.getForObject(url, String.class, requestParams);
        log.info("do get request url={}, requestParams = {}, responseData = {}",
                url, FastjsonUtil.toJson(requestParams), responseData);
        return responseData;
    }

    /**
     * post请求
     * @param url
     * @param requestParams
     * @return
     */
    public String doPost(DataSourceRequestType contentType, String url, Map<String, Object> requestParams){
        if(null == contentType){
            return doPostFormSubmit(url, requestParams);
        }

        switch (contentType){
            case JSON:
                return doPostJsonSubmit(url, requestParams, MediaType.APPLICATION_JSON);
            case URLENCODED:
                // url 编码，form 提交
                return doPostFormSubmit(url, requestParams);
            case APP_JSON:
                return doPostJsonSubmit(url, requestParams, MediaType.APPLICATION_JSON_UTF8);
            default:
                // 默认按表单提交
                return doPostFormSubmit(url, requestParams);
        }
    }

    /**
     * post json 提交
     * @param url
     * @param requestParams
     * @return
     */
    private String doPostJsonSubmit(String url, Map<String, Object> requestParams, MediaType mediaType){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(mediaType);
        HttpEntity<String> request = new HttpEntity<>(requestParams.toString(), headers);
        String responseData = restTemplate.postForObject(url, request, String.class);
        log.info("do post json[type={}] Submit request url={}, responseData = {}", mediaType, responseData);
        return responseData;
    }

    /**
     * form 表单提交
     * @param url
     * @param requestParams
     * @return
     */
    private String doPostFormSubmit(String url, Map<String, Object> requestParams){
        MultiValueMap<String, Object> paramMap = new LinkedMultiValueMap();
        if(null != requestParams && !requestParams.isEmpty()){
            requestParams.forEach((key, value) -> {
                paramMap.add(key, value);
            });
        }

        String responseData = restTemplate.postForObject(url, paramMap, String.class);
        log.info("do post Form Submit request url={}, responseData = {}", responseData);
        return responseData;
    }
}
