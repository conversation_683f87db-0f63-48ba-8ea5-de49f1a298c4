package com.caidaocloud.hrpaas.service.interfaces.facade.metadata;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.service.application.metadata.service.MetadataTenantService;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataTenantDo;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataTenantDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataTenantLogoDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataTenantRuleDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataTenantRuleGuideDto;
import com.caidaocloud.hrpaas.service.interfaces.vo.orgSet.MetadataTenantVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/hrpaas/metadata/tenant/v1")
@Api(value = "元数据租户维护", tags = "v1.6", description = "元数据--租户接口")
public class MetadataTenantController {
    @Autowired
    private MetadataTenantService metadataTenantService;

    @ApiOperation("初始化租户信息")
    @PostMapping("/init")
    public void init(@RequestBody MetadataTenantDto metadataTenantDto){
        metadataTenantService.init(metadataTenantDto);
    }

    @PostMapping(value = "/logo")
    @ApiOperation(value = "Logo设置", tags = "v1.6")
    public Result logoSet(@RequestBody MetadataTenantLogoDto tenantLogoDto){
        metadataTenantService.logoSet(tenantLogoDto);
        return Result.ok(true);
    }

    @PostMapping(value = "/orgRule")
    @ApiOperation(value = "组织设置规则", tags = "v1.6")
    public Result orgRuleSet(@RequestBody MetadataTenantRuleDto metadataTenantRuleDto){
        metadataTenantService.orgRuleSet(metadataTenantRuleDto);
        return Result.ok(true);
    }

    @PostMapping(value = "/orgRuleGuide")
    @ApiOperation(value = "组织设置规则引导", tags = "v1.6")
    public Result orgRuleGuide(@RequestBody MetadataTenantRuleGuideDto tenantRuleDto){
        metadataTenantService.orgRuleGuide(tenantRuleDto.getManageSystem());
        return Result.ok(true);
    }

    @GetMapping("/getOrg")
    @ApiOperation(value = "查询组织设置详情", tags = "v1.6")
    public Result<MetadataTenantVo> getOrg(){
        List<MetadataTenantDo> pageResult = metadataTenantService.getOrgSet();
        MetadataTenantDo tenantDo = pageResult.get(0);
        MetadataTenantVo tenantVo = ObjectConverter.convert(tenantDo, MetadataTenantVo.class);
        tenantVo.setPcTenantLogo(FastjsonUtil.toObject(tenantDo.getPcTenantLogo(), Attachment.class));
        tenantVo.setAppTenantLogo(FastjsonUtil.toObject(tenantDo.getAppTenantLogo(), Attachment.class));
        return Result.ok(tenantVo);
    }

}
