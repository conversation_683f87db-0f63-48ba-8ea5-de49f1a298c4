package com.caidaocloud.hrpaas.service.test;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.hrpaas.service.application.uiform.service.SysDictService;
import com.caidaocloud.hrpaas.service.infrastructure.util.SerializeUtil;
import io.lettuce.core.RedisAsyncCommandsImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class SysDictServiceTest {
    @Resource
    private SysDictService sysDictService;
    @Resource
    private CacheService cacheService;
    @Resource
    public RedisTemplate<String, Object> serializableRedisTemplate;

    @Test
    public void getSysDictByIdTest(){
        Integer dictId = 75481;
        for (int i = 0; i < 1000; i ++ ) {
            String dictName = sysDictService.getSysDictById(dictId.toString());
            System.out.println(dictName);
        }
    }


    @Test
    public void testCacheValue(){
        afterPropertiesSet();
        //String sr = getJedis().get("SYS_PARM_DICT_KEY_75396");
        //System.out.println(sr);

        byte [] bk = SerializeUtil.serialize("SYS_PARM_DICT_KEY_test_9999");
        byte [] hello = SerializeUtil.serialize("hello world");
        cacheService.cacheValue("SYS_PARM_DICT_KEY_test_9999", "hello world");
        //byte [] data = redisTemplate.opsForValue().get(bk);
        //Object object = SerializeUtil.unserialize(data);
        Object object = cacheService.getValue("SYS_PARM_DICT_KEY_test_9999");
        System.out.println(object);
    }

    public void afterPropertiesSet() {
        //Field poolField = ReflectionUtils.findField(JedisConnectionFactory.class, "pool");
        //ReflectionUtils.makeAccessible(poolField);
        RedisConnectionFactory redisConnectionFactory = serializableRedisTemplate.getConnectionFactory();
        Object object = redisConnectionFactory.getConnection().getNativeConnection();
        RedisAsyncCommandsImpl redisAsyncCommands = (RedisAsyncCommandsImpl) object;

        Object str = redisAsyncCommands
                .getStatefulConnection()
                .sync().get(SerializeUtil.serialize("SYS_PARM_DICT_KEY_75396"));

        System.out.println(str);
        System.out.println("----------");
        Object objectDict = SerializeUtil.unOldSerialize((byte[]) str);
        System.out.println(objectDict);

        System.out.println(JSON.toJSONString(objectDict));


        System.out.println(redisConnectionFactory);
        //ReflectionUtils.getField(poolField, serializableRedisTemplate.getConnectionFactory());
    }
}
