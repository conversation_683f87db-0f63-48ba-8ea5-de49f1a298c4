<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>caidao-hr-paas-service</artifactId>
        <groupId>com.caidaocloud</groupId>
        <version>2.0.2-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>metadata-sdk</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-msg</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.4.1</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
<!--            <plugin>-->
<!--                <groupId>com.github.wvengen</groupId>-->
<!--                <artifactId>proguard-maven-plugin</artifactId>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>proguard</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <libs>-->
<!--                        <lib>${java.home}/lib/rt.jar</lib>-->
<!--                        <lib>${java.home}/lib/jce.jar</lib>-->
<!--                    </libs>-->
<!--                    <obfuscate>true</obfuscate>-->
<!--                    <injar>${project.build.finalName}.jar</injar>-->
<!--                    <outjar>${project.build.finalName}.jar</outjar>-->
<!--                    <options>-->
<!--                        <option>-dontshrink</option>-->
<!--                        <option>-dontoptimize</option>-->
<!--                        &lt;!&ndash;						&ndash;&gt;-->
<!--                        &lt;!&ndash;						<option>-printconfiguration</option>&ndash;&gt;-->
<!--                        &lt;!&ndash;						保留所有core的class&ndash;&gt;-->
<!--&lt;!&ndash;                        <option>-keep public class com.caidaocloud.hrpaas.metadata.sdk.**</option>&ndash;&gt;-->
<!--                        &lt;!&ndash;						保留所有public的属性和方法&ndash;&gt;-->
<!--&lt;!&ndash;                        <option>-keepclassmembers class com.caidaocloud.hrpaas.metadata.sdk.** {&ndash;&gt;-->
<!--&lt;!&ndash;                            public <![CDATA[<methods>]]>;&ndash;&gt;-->
<!--&lt;!&ndash;                            public <![CDATA[<fields>]]>;&ndash;&gt;-->
<!--&lt;!&ndash;                            }&ndash;&gt;-->
<!--&lt;!&ndash;                        </option>&ndash;&gt;-->
<!--                        &lt;!&ndash;						<option>-keepclassmembers enum com.caidaocloud.hrpaas.core.** {&ndash;&gt;-->
<!--                        &lt;!&ndash;							public <![CDATA[<methods>]]>;&ndash;&gt;-->
<!--                        &lt;!&ndash;							public <![CDATA[<fields>]]>;&ndash;&gt;-->
<!--                        &lt;!&ndash;							}&ndash;&gt;-->
<!--                        &lt;!&ndash;						</option>&ndash;&gt;-->
<!--                        <option>-allowaccessmodification</option>-->
<!--                        &lt;!&ndash;						<option>-keepparameternames</option>&ndash;&gt;-->
<!--                        <option>-renamesourcefileattribute SourceFile</option>-->
<!--                        <option>-keepattributes Signature,Exceptions,*Annotation*,InnerClasses,PermittedSubclasses,EnclosingMethod,Deprecated,SourceFile,LineNumberTable</option>-->
<!--                        &lt;!&ndash;						<option>-keepclasseswithmembernames,includedescriptorclasses class * { native <![CDATA[<methods>]]>; }</option>&ndash;&gt;-->
<!--                        &lt;!&ndash;						<option>-keepclassmembers,allowoptimization enum * { public static **[] values(); public static ** valueOf(java.lang.String); }</option>&ndash;&gt;-->
<!--                        <option>-keepclassmembers class * implements java.io.Serializable { static final long serialVersionUID; private static final java.io.ObjectStreamField[] serialPersistentFields; private void writeObject(java.io.ObjectOutputStream); private void readObject(java.io.ObjectInputStream); java.lang.Object writeReplace(); java.lang.Object readResolve(); }</option>-->
<!--                    </options>-->
<!--                </configuration>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>