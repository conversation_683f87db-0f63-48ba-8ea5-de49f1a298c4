package com.caidaocloud.hrpaas.metadata.sdk.feign;

import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.TxFeignConfiguration;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "caidaocloud-hr-paas-service",
        fallback = EntityDataFeignFallback.class,
        configuration = {FeignConfiguration.class, TxFeignConfiguration.class},
        contextId = "entityDataFeign")
public interface IEntityDataFeign {

    @PostMapping("/api/hrpaas/v1/data")
    Result insert(EntityDataDto data);

    @PostMapping("/api/hrpaas/v1/data/withRelation")
    Result insertWithRelation(DataWithRelationDTO data);

    @PutMapping("/api/hrpaas/v1/data")
    Result update(EntityDataDto data);

    @PutMapping("/api/hrpaas/v1/data/withRelation")
    Result updateWithRelation(DataWithRelationDTO data);

    @PostMapping("/api/hrpaas/v1/data/one")
    Result one(DataQueryDto query, @RequestParam("queryTime") long queryTime);

    @PostMapping("/api/hrpaas/v1/data/one/:nonnull")
    Result oneOrNull(DataQueryDto query, @RequestParam("queryTime") long queryTime);

    @PostMapping("/api/hrpaas/v1/data/filter")
    Result filter(DataQueryDto query, @RequestParam("queryTime") long queryTime);

    @DeleteMapping("/api/hrpaas/v1/data")
    Result delete(@RequestParam("identifier") String identifier, @RequestParam("bid") String bid, @RequestParam("startTime") long startTime);

    @DeleteMapping("/api/hrpaas/v1/data/soft")
    Result softDelete(@RequestParam("identifier") String identifier, @RequestParam("bid") String bid, @RequestParam("startTime") long startTime);

    @PutMapping("/api/hrpaas/v1/data/relation")
    Result operateRelation(RelationOperationDto operation);

    @GetMapping("/api/hrpaas/v1/data/range")
    Result range(@RequestParam("identifier") String identifier, @RequestParam("bid") String bid, @RequestParam("startTime") long startTime, @RequestParam("endTime") long endTime);

    @PostMapping("/api/hrpaas/v1/data/batchUpdate")
    Result batchUpdate(DataBatchUpdateDto query, @RequestParam("queryTime") long queryTime);

    @DeleteMapping("/api/hrpaas/v1/data/:batch")
    Result batchDeleteWithoutRecord(@RequestBody BatchDeleteDto deleteDto);

    @PostMapping("/api/hrpaas/v1/data/:batch")
    Result batchInsertWithoutRecord(@RequestParam("identifier") String identifier, @RequestBody List<EntityDataDto> dataList);

    @GetMapping("/api/hrpaas/v1/data/job/grade/all")
    Result allJobGrade();

    @GetMapping("/api/hrpaas/v1/data/job/grade/channel/all")
    Result allJobGradeChannel();

    @PostMapping("/api/hrpaas/v1/data/count")
    Result count(DataQueryDto query, @RequestParam("queryTime") long queryTime);

    @PostMapping("/api/hrpaas/v1/data/max")
    Result max(MaxQueryDto query);

    @PostMapping("/api/hrpaas/v1/data/count/grouped")
    Result countByGroup(GroupedCountDto query);

    @PostMapping("/api/hrpaas/v1/data/indicate/grouped")
    Result indicateByGroup(GroupedIndicateDto query);

    @PostMapping("/api/hrpaas/v1/data/join")
    Result join(DataJoin dataJoin);

    @PostMapping("/api/hrpaas/v1/data/conditon/load")
    Result<List<EmpSimple>> condition(DataQueryDto query, @RequestParam("queryTime") Long queryTime);
}
