package com.caidaocloud.metadata.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("paas_property_event")
public class PropertyEventPo {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String identifier;

    private String property;

    private String event;

    public PropertyEventPo(){

    }

    public PropertyEventPo(String identifier, String property, String event){
        this.identifier = identifier;
        this.property = property;
        this.event = event;
    }

}
