package com.caidaocloud.hrpaas.metadata.sdk.query;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.util.PatternMatchUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataAddressVo;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IDictFeignClient;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMetadataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class QueryInfoCache {

    private static final ThreadLocal<QueryInfoCache> queryInfoCache = new ThreadLocal<>();

    public static void init(){
        if(queryInfoCache.get() != null){
            queryInfoCache.get().initCount++;
        }else{
            queryInfoCache.set(new QueryInfoCache());
        }
    }

    private int initCount = 1;

    public static void clear(){
        queryInfoCache.get().initCount--;
        if(queryInfoCache.get().initCount <= 0){
            queryInfoCache.remove();
        }
    }

    private Map<String, LabelData> jobGradeChannel = Maps.map();

    private Map<String, LabelData> jobGrade = Maps.map();

    private Map<String, SysParamDictDto> dict = Maps.map();

    private Map<String, String> province = Maps.map();

    private Map<String, String> city = Maps.map();
    // 地址缓存
    private Map<Long, String> address = Maps.map();
    // 地址编码缓存
    private Map<String, Long> addressMap = Maps.map();
    // 员工缓存
    private Map<String, EmployeeDto> empMap = Maps.map();

    private boolean jobGradeCached = false;

    public static SysParamDictDto getDict(String key, String dictId){
        String dict = String.format(key, dictId);
        val cache = queryInfoCache.get();
        if(null == cache){
            return getDictInfo(dict, dictId);
        }
        if(!cache.dict.containsKey(dict)){
            val dictObj = getDictInfo(dict, dictId);
            cache.dict.put(dict, dictObj);
        }
        return cache.dict.get(dict);
    }

    public static SysParamDictDto getDict(String dict){
        return getDictInfo(dict, null);
    }

    public static SysParamDictDto getDictInfo(String dict, String dictId){
        val dictValue = SpringUtil.getBean(CacheService.class).getValue(dict);
        if(StringUtil.isNotEmpty(dictValue)){
            return FastjsonUtil.toObject(dictValue, SysParamDictDto.class);
        }

        if(StringUtil.isEmpty(dictId) || "null".equals(dictId) || !PatternMatchUtil.isNumber(dictId)){
            return null;
        }

        Result<SysParamDictDto> result = SpringUtil.getBean(IDictFeignClient.class).getDict(Long.valueOf(dictId));
        SysParamDictDto dictObj = null == result || !result.isSuccess() || null == result.getData() ? null : result.getData();
        if(null != dictObj){
            SpringUtil.getBean(CacheService.class).cacheValue(dict, FastjsonUtil.toJson(dictObj));
        }
        return dictObj;
    }

    public static SysParamDictDto code2Dict(String dict, String dictCode){
        dict = String.format(dict, dictCode);
        val cache = queryInfoCache.get();
        if(null == cache){
            return getDictByCode(dictCode);
        }

        if(!cache.dict.containsKey(dict)){
            val dictObj = getDictByCode(dictCode);
            cache.dict.put(dict, dictObj);
        }
        return cache.dict.get(dict);
    }

    public static SysParamDictDto code2Dict(String dict, String dictType, String dictCode){
        dict = String.format(dict, dictType, dictCode);
        val cache = queryInfoCache.get();
        if(null == cache){
            return getDictByCode(dictType, dictCode);
        }

        if(!cache.dict.containsKey(dict)){
            val dictObj = getDictByCode(dictType, dictCode);
            cache.dict.put(dict, dictObj);
        }
        return cache.dict.get(dict);
    }

    private static SysParamDictDto getDictByCode(String dictCode){
        Result<SysParamDictDto> result = SpringUtil.getBean(IDictFeignClient.class).one(dictCode);
        return null == result || !result.isSuccess() || null == result.getData() ? null : result.getData();
    }

    private static SysParamDictDto getDictByCode(String dictType, String dictCode){
        Result<SysParamDictDto> result = SpringUtil.getBean(IDictFeignClient.class).getDictByTypeAndCode(dictType, dictCode);
        return null == result || !result.isSuccess() || null == result.getData() ? null : result.getData();
    }

    public static String getCity(String cityId){
        val cache = queryInfoCache.get();
        if(!cache.city.containsKey(cityId)){
            val city = DataQuery.identifier("entity.hrpaas.City").
                    one(cityId, LabelData.class).getLabel();
            cache.city.put(cityId, city);
        }
        return cache.city.get(cityId);
    }

    public static String getProvince(String provinceId){
        val cache = queryInfoCache.get();
        if(!cache.province.containsKey(provinceId)){
            val province = DataQuery.identifier("entity.hrpaas.Province").
                    one(provinceId, LabelData.class).getLabel();
            cache.province.put(provinceId, province);
        }
        return cache.province.get(provinceId);
    }

    public static LabelData getJobGrade(String bid){
        val cache = queryInfoCache.get();
        if(!cache.jobGradeCached){
            cache();
        }
        LabelData labelData = cache.jobGrade.get(bid);
        if (labelData == null) {
            log.info("not found data, bid={}", bid);
            labelData = new LabelData("");
        }
        return labelData;
    }

    public static LabelData getJobGradeChannel(String bid){
        val cache = queryInfoCache.get();
        if(!cache.jobGradeCached){
            cache();
        }
        LabelData labelData = cache.jobGradeChannel.get(bid);
        if (labelData == null) {
            log.info("not found data, bid={}", bid);
            labelData = new LabelData("");
        }
        return labelData;
    }

    private static void cache(){
        val cache = queryInfoCache.get();
        cache.jobGrade.putAll(
                DataQuery.allJobGrade().stream()
                        .collect(Collectors.toMap(DataSimple::getBid, Function.identity()))
        );
        cache.jobGradeChannel.putAll(
                DataQuery.allJobGradeChannel().stream()
                        .collect(Collectors.toMap(DataSimple::getBid, Function.identity()))
        );
        cache.jobGradeCached = true;
    }

    public static String getAddress(Long addressId){
        val cache = queryInfoCache.get();
        return getAddress(addressId, cache);
    }

    public static String getAddress(Long addressId, QueryInfoCache cache){
        String addr = "";
        if(null == addressId || null == cache){
            return addr;
        }

        if(!cache.address.containsKey(addressId)){
            String cacheKey = String.format("address_%s", addressId);
            val cacheValue = SpringUtil.getBean(CacheService.class).getValue(cacheKey);
            if(null != cacheValue){
                val data = FastjsonUtil.toObject(cacheValue, Map.class);
                addr = (String) data.get("address");
                cache.address.put(addressId, addr);
            }

            return addr;
        }
        return cache.address.get(addressId);
    }

    public static Map<String, Long> getAddressCode(List<String> list, String cacheKey){
        val cacheService = SpringUtil.getBean(CacheService.class);
        Map<String, Long> cacheMap = new HashMap<>();
        val cache = queryInfoCache.get();
        if(null == cache){
            val newCache = new QueryInfoCache();
            getCacheAddress(list, newCache, cacheKey, cacheService, cacheMap);
            if(!list.isEmpty()){
                getPaasAddress(list, newCache, cacheKey, cacheService, cacheMap);
            }
            return cacheMap;
        }

        Iterator<String> iterator = list.iterator();
        while (iterator.hasNext()){
            String next = iterator.next();
            if(cache.addressMap.containsKey(next)){
                cacheMap.put(next, cache.addressMap.get(next));
                // 如果本地缓存中存在，则删除元素，不再从redisCache和DB中查数据
                iterator.remove();
            }
        }

        getCacheAddress(list, cache, cacheKey, cacheService, cacheMap);

        if(!list.isEmpty()){
            getPaasAddress(list, cache, cacheKey, cacheService, cacheMap);
        }

        return cacheMap;
    }

    private static void getCacheAddress(List<String> list, QueryInfoCache cache,
        String cacheKey, CacheService cacheService, Map<String, Long> cacheMap){
        Iterator<String> iterator = list.iterator();
        StringBuilder sb = new StringBuilder();
        while (iterator.hasNext()) {
            String next = iterator.next();
            sb.append(next);
            String addrKey = String.format(cacheKey, sb.toString());
            String cacheValue = cacheService.getValue(addrKey);
            if(StringUtil.isNotEmpty(cacheValue)){
                Long value = Long.valueOf(cacheValue);
                cacheMap.put(addrKey, value);
                cache.addressMap.put(addrKey, value);
                // redis cache 中存在，则删除list中的元素，不用查db
                // iterator.remove();
            }
        }
    }

    private static void getPaasAddress(List<String> list, QueryInfoCache cache,
        String cacheKey, CacheService cacheService, Map<String, Long> cacheMap){
        List<MetadataAddressVo> addressList = getAddressList(list);
        PreCheck.preCheckArgument(null == addressList || addressList.isEmpty()
                || addressList.size() < list.size(), "Address mismatch");

        if(addressList.size() != list.size()){
            Iterator<MetadataAddressVo> iterator = addressList.iterator();
            while (iterator.hasNext()){
                MetadataAddressVo next = iterator.next();
                String [] ids = next.getPidPath().split("/");
                if(ids.length > 1){
                    for (String id : ids) {
                        String address = QueryInfoCache.getAddress(Long.valueOf(id), cache);
                        if(!list.contains(address)){
                            iterator.remove();
                            break;
                        }
                    }
                }
            }
        }

        Map<String, List<Long>> collect = new HashMap<>();
        addressList.forEach(addr -> {
            String address = addr.getAddress();
            if(collect.containsKey(address)){
                List<Long> addrList = collect.get(address);
                addrList.add(Long.valueOf(addr.getId()));
                List<Long> sortList = addrList.stream().sorted().collect(Collectors.toList());
                collect.put(address, sortList);
                return;
            }

            collect.put(address, Lists.newArrayList(Long.valueOf(addr.getId())));
        });

        StringBuilder sb = new StringBuilder();
        list.forEach(txt -> {
            List<Long> addrList = collect.get(txt);
            if(null == addrList || addrList.isEmpty()){
                throw new ServerException("Address mismatch");
            }

            sb.append(txt);
            addrList.forEach(addressId -> {
                String format = String.format(cacheKey, sb);
                if(cacheMap.containsKey(format)){
                    return;
                }
                cacheMap.put(format, addressId);
                cache.addressMap.put(format, addressId);
                // 缓存 3 个小时
                cacheService.cacheValue(format, addressId.toString(), 10800L);
            });
            if(addrList.size() > 1){
                addrList.remove(0);
            }
        });
    }

    private static List<MetadataAddressVo> getAddressList(List<String> list){
        Result<List<MetadataAddressVo>> result = SpringUtil.getBean(IMetadataFeign.class).getAddressByStr(list);
        return null == result || !result.isSuccess() || null == result.getData() ? null : result.getData();
    }

    public static EmployeeDto loadEmpById(String cacheKey, String empId){
        val cache = queryInfoCache.get();
        if(null == cache){
            return getEmpById(cacheKey, empId);
        }

        if(!cache.empMap.containsKey(cacheKey)){
            val dictObj = getEmpById(cacheKey, empId);
            cache.empMap.put(cacheKey, dictObj);
        }

        return cache.empMap.get(cacheKey);
    }

    private static EmployeeDto getEmpById(String cacheKey, String empId){
        CacheService cacheService = SpringUtil.getBean(CacheService.class);
        String empJson = cacheService.getValue(cacheKey);
        EmployeeDto employeeDto = FastjsonUtil.toObject(empJson, EmployeeDto.class);
        if(null == employeeDto || StringUtil.isEmpty(employeeDto.getWorkno())){
            Object objData = ThreadLocalUtil.LOCAL_REQUEST_DATA.get();
            long dataTime = System.currentTimeMillis();
            if(null != objData && objData instanceof DataQuery){
                dataTime = ((DataQuery) objData).getQueryTime();
            }

            List<DataSimple> dbList = DataQuery.identifier("entity.hr.EmpWorkInfo").decrypt().dept().specifyLanguage()
                    .queryInvisible()
                    .filter(DataFilter.eq("empId", empId), DataSimple.class, dataTime).getItems();
            DataSimple dataSimple =  null == dbList || dbList.isEmpty() ? null : dbList.get(0);

            if(null != dataSimple){
                NestPropertyValue properties = dataSimple.getProperties();
                employeeDto = new EmployeeDto();
                employeeDto.setEmpId(empId);
                employeeDto.setName(((SimplePropertyValue) properties.get("name")).getValue());
                employeeDto.setWorkno(((SimplePropertyValue) properties.get("workno")).getValue());
                employeeDto.setEnName(((SimplePropertyValue) properties.get("enName")).getValue());
                // 缓存 3 个小时
                cacheService.cacheValue(cacheKey, FastjsonUtil.toJson(employeeDto), 10800L);
            }
        }
        return employeeDto;
    }
}
