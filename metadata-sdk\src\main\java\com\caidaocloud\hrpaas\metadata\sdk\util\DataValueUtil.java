package com.caidaocloud.hrpaas.metadata.sdk.util;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataValueFunction;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public final class DataValueUtil {
    private final static String UIFORM_RELATION_FORMAT_TXT = "%s_txt";

    public static void loadDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, Map<String, Map> joinValues, List<DataValueFunction<Object>> dataVals){
        if(null == dataVal){
            return;
        }

        if(dataVal instanceof SimplePropertyValue){
            SimplePropertyValue pValue = (SimplePropertyValue) dataVal;

            switch (pValue.getType()){
                case String:
                    dataValue.fitDataValue(pValue.getValue());
                    break;
                case Boolean:
                    dataValue.fitDataValue(Boolean.valueOf(pValue.getValue()));
                    break;
                case String_Array:
                    List<String> valueList = pValue.getArrayValues();
                    dataValue.fitDataValue(null == valueList ? "" : valueList.stream().collect(Collectors.joining(",")));

                    String [] joinArr = dataValue.loadDataProp().split("\\.");
                    if(joinArr.length > 1){
                        dataValue.fitDataProp("bid".equals(joinArr[1]) ? joinArr[0] : String.format(UIFORM_RELATION_FORMAT_TXT, joinArr[0]));
                    }
                    break;
                case Number:
                    dataValue.fitDataValue(pValue.getValue());
                    break;
                default:
                    dataValue.fitDataValue(pValue.getValue());
                    break;
            }

            return;
        }

        dataVal.convertDataValue(dataValue, dataVal, dataVals);
    }
}
