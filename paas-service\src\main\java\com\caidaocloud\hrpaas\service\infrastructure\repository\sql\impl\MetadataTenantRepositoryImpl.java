package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.metadata.domain.dto.BaseSearchDto;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataTenantDo;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.IMetadataTenantRepository;
import org.springframework.stereotype.Repository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.MetadataTenantPo;
import com.caidaocloud.hrpaas.service.infrastructure.util.SqlUtil;
import com.googlecode.totallylazy.Option;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Repository
public class MetadataTenantRepositoryImpl implements IMetadataTenantRepository {

    @Autowired
    MetadataTenantMapper mapper;

    @Override
    public String insert(MetadataTenantDo tenantDo) {
        Option<MetadataTenantDo> option = this.load(tenantDo.getTenantId());
        if (!option.isEmpty()) {
            return option.get().getId();
        }
        val po = MetadataTenantPo.fromEntity(MetadataTenantPo.class, tenantDo);
        mapper.insert(po);
        return po.getId().toString();
    }

    @Override
    public Option<MetadataTenantDo> loadById(String tenantId) {
        LambdaQueryWrapper<MetadataTenantPo> queryWrapper = new QueryWrapper<MetadataTenantPo>().lambda();
        queryWrapper.eq(MetadataTenantPo::getTenantId, tenantId);
        val po = mapper.selectOne(queryWrapper);
        if (po == null) {
            return Option.none();
        }
        return Option.option(po.toEntity(MetadataTenantDo.class));
    }

    @Override
    public Option<MetadataTenantDo> load(String id) {
        return loadById(id);
    }

    @Override
    public void update(MetadataTenantDo tenantDo) {
        val po = MetadataTenantPo.fromEntity(MetadataTenantPo.class, tenantDo);
        mapper.updateById(po);
    }

    @Override
    public List<MetadataTenantDo> getListByPage(BaseSearchDto query, int pageNo, int pageSize) {
        Page<MetadataTenantPo> page = new Page<>(pageNo, pageSize);
        QueryWrapper<MetadataTenantPo> queryWrapper = new QueryWrapper<>();
        query.getEqCondition().forEach((key,value)->{
            queryWrapper.eq(SqlUtil.humpToLine(key), value);
        });

        Page<MetadataTenantPo> poPage = mapper.selectPage(page, queryWrapper);
        return MetadataTenantPo.toEntityList(MetadataTenantDo.class, poPage.getRecords());
    }

}
