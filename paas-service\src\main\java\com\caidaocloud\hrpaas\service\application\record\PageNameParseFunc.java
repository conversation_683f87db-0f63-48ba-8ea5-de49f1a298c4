package com.caidaocloud.hrpaas.service.application.record;

import com.caidaocloud.hrpaas.service.application.page.service.TenantPageService;
import com.caidaocloud.hrpaas.service.interfaces.vo.page.TenantPageVo;
import com.caidaocloud.record.core.service.IParseFunction;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * created by: FoAng
 * create time: 29/7/2024 5:44 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class PageNameParseFunc implements IParseFunction {

    private TenantPageService tenantPageService;

    @Override
    public String functionName() {
        return "pageName";
    }

    @Override
    public boolean executeBefore() {
        return true;
    }

    @Override
    public String apply(String pageId) {
        return Optional.ofNullable(pageId).map(it -> {
            TenantPageVo pageVo = tenantPageService.viewPage(pageId);
            return pageVo != null ? pageVo.getName() : null;
        }).orElse("");
    }
}
