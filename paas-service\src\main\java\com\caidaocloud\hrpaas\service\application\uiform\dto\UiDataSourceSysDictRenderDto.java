package com.caidaocloud.hrpaas.service.application.uiform.dto;

import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiDataSourceRenderDto;
import lombok.Data;

@Data
public class UiDataSourceSysDictRenderDto extends UiDataSourceRenderDto {
    private SysDictRequestDto requestDto;

    public SysDictRequestDto getRequest(){
        if(null == this.getRequestParams()){
            return null;
        }

        return SysDictRequestDto.bulid((String) getRequestParams().get("typeCode"),
                (Boolean) getRequestParams().getOrDefault("sysConstant", false));
    }
}
