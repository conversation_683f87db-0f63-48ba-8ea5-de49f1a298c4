package com.caidaocloud.hrpaas.service.domain.metadata.entity;

import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.IMetadataKvRepository;
import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import lombok.Data;

@Data
public class MetadataKvDo extends BaseEntity<IBaseRepository> {
    public final static String ROOT_PID = "0", ADDRESS_KEY = "address_%s";
    private String property;
    private String content;
    private String i18n;
    /**
     * 是否删除。0 默认值，未删除，1 已删除
     */
    private boolean deleted;

    public void save(){
        this.insert();
    }

    @Override
    public void update() {
        repository(MetadataKvDo.class).update(this);
    }

    public static MetadataKvDo getDetailByKey(String key){
        return ((IMetadataKvRepository) repository(MetadataKvDo.class)).loadByKey(key);
    }

    public static void deleteByKey(String key){
        ((IMetadataKvRepository) repository(MetadataKvDo.class)).deleteByKey(key);
    }
}
