package com.caidaocloud.hrpaas.service.expression;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Map;

import javax.annotation.meta.When;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropAndRelationUpdateDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyInsertDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.DataQueryType;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SimpleDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.service.DataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.infrastructure.repository.impl.EntityDefMapper;
import com.caidaocloud.metadata.infrastructure.repository.po.EntityDefPo;
import com.caidaocloud.metadata.interfaces.facade.EntityDataApiController;
import com.caidaocloud.metadata.interfaces.facade.MetadataApiController;
import com.caidaocloud.metadata.interfaces.facade.MetadataWebController;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.service.SessionServiceImpl;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHelper;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.val;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/3/6
 */
@SpringBootTest(classes = HrPaasApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class EntityExpPropDefTest {
	@Autowired
	private MetadataWebController metadataWebController;
	@Autowired
	private EntityDataApiController entityDataApiController;
	@SpyBean
	private DataOperatorService dataOperatorService;
	@Autowired
	private MetadataApiController metadataApiController;
	@Autowired
	private JdbcTemplate jdbcTemplate;

	@Before
	public void bf() {
		SecurityUserInfo info = new SecurityUserInfo();
		info.setTenantId("11");
		info.setEmpId(0L);
		info.setUserId(0L);
		SecurityUserUtil.setSecurityUserInfo(info);

		RequestHelper.getRequest()
				.setAttribute("_caidaocloud_userinfo_request_threadlocal_", ObjectConverter.convert(info, UserInfo.class));
		SessionServiceImpl service = (SessionServiceImpl) SpringUtil.getBean(ISessionService.class);
		try {
			Field field = SessionServiceImpl.class.getDeclaredField("threadLocalCache");
			field.setAccessible(true);
			field.set(service, true);
		}
		catch (NoSuchFieldException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	private static String identifier = "entity.hr.EmpWorkInfo";

	@Autowired
	private EntityDefMapper mapper;


	@Test
	public void EXP_PROPERTY_DEFINITION_TEST() {
		MetadataPropertyInsertDto dto = new MetadataPropertyInsertDto();
		dto.setIdentifier(identifier);
		dto.setProps(new ArrayList<>());

		MetadataPropertyDto metadataPropertyDto = new MetadataPropertyDto();
		metadataPropertyDto.setProperty("exp01");
		metadataPropertyDto.setName("exp01");
		metadataPropertyDto.setDataType(PropertyDataType.Number);
		metadataPropertyDto.setExpEnable(true);
		metadataPropertyDto.setExpression("1+1");
		dto.getProps().add(metadataPropertyDto);

		metadataWebController.insertCustomProps(dto);

		Wrapper<EntityDefPo> wrapper = new LambdaUpdateWrapper<EntityDefPo>()
				.eq(EntityDefPo::getIdentifier, identifier);
		val entityDef = this.mapper.selectOne(wrapper);

		Map<String, String> propertyToFieldMapping = FastjsonUtil.toObject(entityDef.getPropertyToFieldMapping(), Map.class);


		Assert.assertTrue(Sequences.sequence(entityDef.toEntity(Maps.map()).getCustomProperties())
				.find(p -> p.getProperty().equals("exp01"))
				.isDefined());
		Assert.assertTrue(Sequences.sequence(propertyToFieldMapping.keySet()).find(p -> p.equals("exp01")).isEmpty());

	}

	@Test
	public void EXP_PROPERTY_DEFINITION_TEST2() {
		MetadataPropAndRelationUpdateDto dto = new MetadataPropAndRelationUpdateDto();
		dto.setRelations(new ArrayList<>());
		dto.setStandardProps(new ArrayList<>());
		dto.setIdentifier(identifier);
		dto.setProps(new ArrayList<>());

		MetadataPropertyDto metadataPropertyDto = new MetadataPropertyDto();
		metadataPropertyDto.setProperty("exp02");
		metadataPropertyDto.setName("exp02");
		metadataPropertyDto.setDataType(PropertyDataType.Number);
		metadataPropertyDto.setExpEnable(false);
		metadataPropertyDto.setExpression("1+1");
		dto.getProps().add(metadataPropertyDto);

		metadataWebController.updatePropsAndRelations(dto);

		metadataPropertyDto.setExpEnable(true);
		metadataWebController.updatePropsAndRelations(dto);

		Wrapper<EntityDefPo> wrapper = new LambdaUpdateWrapper<EntityDefPo>()
				.eq(EntityDefPo::getIdentifier, identifier);
		val entityDef = this.mapper.selectOne(wrapper);

		Map<String, String> propertyToFieldMapping = FastjsonUtil.toObject(entityDef.getPropertyToFieldMapping(), Map.class);


		Assert.assertTrue(Sequences.sequence(entityDef.toEntity(Maps.map()).getCustomProperties())
				.find(p -> p.getProperty().equals("exp02"))
				.isDefined());
		Assert.assertTrue(Sequences.sequence(propertyToFieldMapping.keySet()).find(p -> p.equals("exp02")).isDefined());

	}

	@Test
	public void EXP_PROPERTY_DEFINITION_TEST3() {
		MetadataPropAndRelationUpdateDto dto = new MetadataPropAndRelationUpdateDto();
		dto.setRelations(new ArrayList<>());
		dto.setStandardProps(new ArrayList<>());
		dto.setIdentifier(identifier);
		dto.setProps(new ArrayList<>());

		MetadataPropertyDto metadataPropertyDto = new MetadataPropertyDto();
		metadataPropertyDto.setProperty("exp03");
		metadataPropertyDto.setName("exp03");
		metadataPropertyDto.setDataType(PropertyDataType.Number);
		metadataPropertyDto.setExpEnable(true);
		metadataPropertyDto.setExpression("1+1");
		dto.getProps().add(metadataPropertyDto);

		metadataWebController.updatePropsAndRelations(dto);

		metadataPropertyDto.setExpEnable(false);
		metadataWebController.updatePropsAndRelations(dto);

		Wrapper<EntityDefPo> wrapper = new LambdaUpdateWrapper<EntityDefPo>()
				.eq(EntityDefPo::getIdentifier, identifier);
		val entityDef = this.mapper.selectOne(wrapper);

		jdbcTemplate.queryForList("select exp03 from entity_hr_exp_test_11", Map.class);
	}

	@Test
	public void DATA_INSERT_TEST() {
		DataSimple dataSimple = new DataSimple();
		dataSimple.getProperties().add("exp03", "3");
		val metadata = metadataApiController.one(identifier).getData();
		val entityData = dataSimple.toPersistData(metadata);
		entityData.setIdentifier(identifier);
		entityDataApiController.insert(entityData);
	}

	@Test
	public void DATA_QUERY_TEST(){
		DataQueryDto dto = new DataQueryDto();
		dto.setIdentifier(identifier);
		dto.setType(DataQueryType.PAGE);
		dto.setFilter(FastjsonUtil.toJson(DataFilter.eq("tenantId", "11")));
		Result<PageResult<EntityDataDto>> result = entityDataApiController.filter(dto, -1);
		System.out.println(FastjsonUtil.toJson(result.getData().getItems()));
		Assert.assertTrue(result.getData().getItems().size() > 0);
		Assert.assertTrue(Sequences.sequence(result.getData().getItems().get(0).getProperties())
				.find(p -> p.getProperty().equals("exp01")).isEmpty());
		Assert.assertTrue(Sequences.sequence(result.getData().getItems().get(0).getProperties())
				.find(p -> p.getProperty().equals("exp02")).isEmpty());
		Assert.assertTrue(Sequences.sequence(result.getData().getItems().get(0).getProperties())
				.find(p -> p.getProperty().equals("exp03")).isDefined());
	}


	@Test
	public void DATA_QUERY_AND_EXP_TEST(){
		DataQueryDto dto = new DataQueryDto();
		dto.setIdentifier(identifier);
		dto.setType(DataQueryType.PAGE);
		dto.setFilter(FastjsonUtil.toJson(DataFilter.eq("tenantId", "11")));
		PageResult<DataSimple> result = DataQuery.identifier(identifier)
				.filter(DataFilter.eq("tenantId", "11"), DataSimple.class);
		System.out.println(FastjsonUtil.toJson(result));
	}

	@Test
	public void WORK_INFO_TEST(){
		DataSimple one = DataQuery.identifier("entity.hr.EmpWorkInfo").one("1699922882926622", DataSimple.class);
		Assert.assertNotNull(one.getProperties().get("siLing"));
		System.out.println(FastjsonUtil.toJson(one.getProperties().get("siLing")));
	}

	@Test
	public void WORK_INFO_SELECT_PROPERTY_TEST(){
		PageResult<Map<String, String>> result = DataQuery.identifier("entity.hr.EmpWorkInfo").exp()
				.filterProperties(DataFilter.eq("bid", "1699922882926622"), Lists.list("empId", "hireDate", "siLing"), System.currentTimeMillis());
		Map<String, String> data = result.getItems().get(0);
		Assert.assertNotNull(data);
		Assert.assertNotNull(data.get("siLing"));
		System.out.println(FastjsonUtil.toJson(data.get("siLing")));
	}

}
