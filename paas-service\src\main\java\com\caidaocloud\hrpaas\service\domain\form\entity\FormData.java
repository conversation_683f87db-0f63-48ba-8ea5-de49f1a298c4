package com.caidaocloud.hrpaas.service.domain.form.entity;

import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.em.OpEnum;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.util.AuthScopeUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDataStatus;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormTarget;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormWidgetType;
import com.caidaocloud.metadata.domain.entity.PropertyEnumDef;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Slf4j
public class FormData {

    private String id;
    private String formId;
    //private String formName;
    private long createTime;
    private String createBy;
    private long updateTime;
    private String updateBy;
    private List<FormPropertyData> properties = Lists.list();
    private String workflowTaskId;//表单所属节点任务ID

    private String targetId;
    private FormDataStatus status;
    private String sysProcessCode;

    public String create(FormDef formDef) {
        val id = SnowUtil.nextId();
        this.setId(id);
        val dataSimple = new FormDataEntity();
        dataSimple.setDataStartTime(0);
        dataSimple.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        dataSimple.setIdentifier("entity.form." + this.formId);
        dataSimple.setCreateBy(this.createBy);
        dataSimple.setUpdateBy(this.updateBy);
        dataSimple.setCreateTime(this.createTime);
        dataSimple.setUpdateTime(this.updateTime);
        dataSimple.setBid(id);
        dataSimple.setFormTargetId(targetId);
        dataSimple.setWorkflowTaskId(workflowTaskId);
        dataSimple.setStatus(this.status);
        this.properties.forEach(it -> {
            val value = it.getValue();
            val dataType = it.getDataType();
            PropertyValue propertyValue;
            if (dataType.equals(PropertyDataType.Data_Table)) {
                val mappedPropertyDef = formDef.getProperties().stream().filter(propertyDef -> propertyDef.getProperty()
                        .equals(it.getProperty())).findFirst().get();
                propertyValue = new DataTable();
                List<Map> dataList = FastjsonUtil.toList(value, Map.class);
                val slaves = dataList.stream().map(data -> {
                    DataSlave slave = new DataSlave();
                    data.entrySet().forEach(dataProperty -> {
                        val mappedSlaveProperty = mappedPropertyDef.getSlaveProperties().stream().filter(slaveProperty ->
                                slaveProperty.getProperty().equals(((Map.Entry) dataProperty).getKey())
                        ).findFirst().get();
                        if (PropertyDataType.Enum.equals(mappedSlaveProperty.getDataType())) {
                            EnumSimple value0 = new EnumSimple();
                            val dataValue = ((Map.Entry) dataProperty).getValue();
                            val dataPropertyKey = ((Map.Entry) dataProperty).getKey();
                            value0.setValue(null == dataValue ? null : dataValue.toString());
                            slave.getProperties().add(dataPropertyKey.toString(), value0);
                        } else if (Lists.list(PropertyDataType.Number, PropertyDataType.Integer,
                                PropertyDataType.String, PropertyDataType.Timestamp).contains(mappedSlaveProperty.getDataType())) {
                            SimplePropertyValue value0 = new SimplePropertyValue();
                            val dataValue = ((Map.Entry) dataProperty).getValue();
                            val dataPropertyKey = ((Map.Entry) dataProperty).getKey();
                            value0.setValue(null == dataValue ? null : dataValue.toString());
                            value0.setType(mappedSlaveProperty.getDataType());
                            slave.getProperties().add(dataPropertyKey.toString(), value0);
                        }

                    });
                    return slave;
                }).collect(Collectors.toList());
                ((DataTable) propertyValue).setDataList(slaves);
            } else if (dataType.equals(PropertyDataType.Attachment)) {
                propertyValue = FastjsonUtil.toObject(value, Attachment.class);
            } else if (dataType.equals(PropertyDataType.Emp)) {
                propertyValue = FastjsonUtil.toObject(value, EmpSimple.class);
                val propertyDef = formDef.getProperties().stream().filter(propDef ->
                        StringUtils.equals(propDef.getProperty(), it.getProperty())).findFirst().get();
                if (!propertyDef.getComponentDetail().isEmpty() && value != null) {
                    val empDef = FastjsonUtil.convertObject(propertyDef.getComponentDetail(), FormPropEmpDef.class);
                    empDef.getEmpProperty().forEach((empPropertyType, properties) -> {
                        val empProperties = FastjsonUtil.toObject(value, Map.class).get(empPropertyType.toString());
                        if (null != empProperties) {
                            ((Map) empProperties).forEach((empPropName, empPropValue) -> {
                                if (null != empPropValue) {

                                    String persistValue = null;
                                    if (empPropValue == null) {

                                    } else if (empPropValue instanceof String) {
                                        persistValue = "string:" + empPropValue;
                                    } else if (empPropValue instanceof Boolean) {
                                        persistValue = "boolean:" + empPropValue;
                                    } else if (empPropValue instanceof Integer || empPropValue instanceof Long) {
                                        persistValue = "long:" + empPropValue;
                                    } else if (empPropValue instanceof Float || empPropValue instanceof Double) {
                                        persistValue = "double:" + empPropValue;
                                    } else if (empPropValue instanceof BigDecimal) {
                                        persistValue = "bigDecimal:" + ((BigDecimal) empPropValue).toPlainString();
                                    } else {
                                        persistValue = "json:" + FastjsonUtil.toJson(empPropValue);
                                    }
                                    dataSimple.getProperties()
                                            .add(it.getProperty() + "_" + empPropertyType.toString().toLowerCase()
                                                            + "_" + empPropName,
                                                    persistValue);
                                }
                            });
                        }
//                        val identifier = empPropertyType.toIdentifier();
//                        val emp = DataQuery.identifier(identifier).filter(
//                                DataFilter.eq("empId", ((EmpSimple)propertyValue).getEmpId()), DataSimple.class).getItems()
//                                .stream().findAny().orElseThrow(()->new ServerException("员工不存在"));
//                        properties.forEach(property-> dataSimple.getProperties()
//                                .put(it.getProperty() + "_" + empPropertyType.toString().toLowerCase()
//                                                + "_" + property,
//                                emp.getProperties().get(property)));
                    });
                }
            } else {
                propertyValue = new SimplePropertyValue();
                ((SimplePropertyValue) propertyValue).setValue(it.getValue());
                if (dataType.isArray()) {
                    ((SimplePropertyValue) propertyValue).setArrayValues(FastjsonUtil.toList(value, String.class));
                }
                ((SimplePropertyValue) propertyValue).setType(dataType);
            }
            String suffix = StringUtils.trimToEmpty(it.getSuffix());
            if (StringUtils.isNotEmpty(suffix)) {
                suffix = "_" + suffix;
            }
            dataSimple.getProperties().put(it.getProperty() + suffix, propertyValue);
        });
        DataInsert.identifier("entity.form." + this.formId).insert(dataSimple);
        return id;
    }

    public void update(FormDef formDef) {
        val dataSimple = new FormDataEntity();
        dataSimple.setDataStartTime(0);
        dataSimple.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        dataSimple.setIdentifier("entity.form." + this.formId);
        dataSimple.setCreateBy(this.createBy);
        dataSimple.setUpdateBy(this.updateBy);
        dataSimple.setCreateTime(this.createTime);
        dataSimple.setUpdateTime(this.updateTime);
        dataSimple.setBid(this.id);
        dataSimple.setFormTargetId(targetId);
        dataSimple.setStatus(this.status);
        this.properties.forEach(it -> {
            val value = it.getValue();
            val dataType = it.getDataType();
            PropertyValue propertyValue;
            if (dataType.equals(PropertyDataType.Data_Table)) {
                val mappedPropertyDef = formDef.getProperties().stream().filter(propertyDef -> propertyDef.getProperty()
                        .equals(it.getProperty())).findFirst().get();
                propertyValue = new DataTable();
                List<Map> dataList = FastjsonUtil.toList(value, Map.class);
                val slaves = dataList.stream().map(data -> {
                    DataSlave slave = new DataSlave();
                    data.entrySet().forEach(dataProperty -> {
                        val mappedSlaveProperty = mappedPropertyDef.getSlaveProperties().stream().filter(slaveProperty ->
                                slaveProperty.getProperty().equals(((Map.Entry) dataProperty).getKey())
                        ).findFirst().get();
                        if (PropertyDataType.Enum.equals(mappedSlaveProperty.getDataType())) {
                            EnumSimple value0 = new EnumSimple();
                            val dataValue = ((Map.Entry) dataProperty).getValue();
                            val dataPropertyKey = ((Map.Entry) dataProperty).getKey();
                            value0.setValue(null == dataValue ? null : dataValue.toString());
                            slave.getProperties().add(dataPropertyKey.toString(), value0);
                        } else if (Lists.list(PropertyDataType.Number, PropertyDataType.Integer,
                                PropertyDataType.String, PropertyDataType.Timestamp).contains(mappedSlaveProperty.getDataType())) {
                            SimplePropertyValue value0 = new SimplePropertyValue();
                            val dataValue = ((Map.Entry) dataProperty).getValue();
                            val dataPropertyKey = ((Map.Entry) dataProperty).getKey();
                            value0.setValue(null == dataValue ? null : dataValue.toString());
                            value0.setType(mappedSlaveProperty.getDataType());
                            slave.getProperties().add(dataPropertyKey.toString(), value0);
                        }

                    });
                    return slave;
                }).collect(Collectors.toList());
                ((DataTable) propertyValue).setDataList(slaves);
            } else if (dataType.equals(PropertyDataType.Attachment)) {
                propertyValue = FastjsonUtil.toObject(value, Attachment.class);
            } else if (dataType.equals(PropertyDataType.Emp)) {
                propertyValue = FastjsonUtil.toObject(value, EmpSimple.class);
                val propertyDef = formDef.getProperties().stream().filter(propDef ->
                        StringUtils.equals(propDef.getProperty(), it.getProperty())).findFirst().get();
                if (!propertyDef.getComponentDetail().isEmpty() && value != null) {
                    val empDef = FastjsonUtil.convertObject(propertyDef.getComponentDetail(), FormPropEmpDef.class);
                    empDef.getEmpProperty().forEach((empPropertyType, properties) -> {

                        val empProperties = FastjsonUtil.toObject(value, Map.class).get(empPropertyType.toString());
                        if (null != empProperties) {
                            ((Map) empProperties).forEach((empPropName, empPropValue) -> {
                                if (null != empPropValue) {
                                    String persistValue = null;
                                    if (empPropValue == null) {

                                    } else if (empPropValue instanceof String) {
                                        persistValue = "string:" + empPropValue;
                                    } else if (empPropValue instanceof Boolean) {
                                        persistValue = "boolean:" + empPropValue;
                                    } else if (empPropValue instanceof Integer || empPropValue instanceof Long) {
                                        persistValue = "long:" + empPropValue;
                                    } else if (empPropValue instanceof Float || empPropValue instanceof Double) {
                                        persistValue = "double:" + empPropValue;
                                    } else if (empPropValue instanceof BigDecimal) {
                                        persistValue = "bigDecimal:" + ((BigDecimal) empPropValue).toPlainString();
                                    } else {
                                        persistValue = "json:" + FastjsonUtil.toJson(empPropValue);
                                    }
                                    dataSimple.getProperties()
                                            .add(it.getProperty() + "_" + empPropertyType.toString().toLowerCase()
                                                            + "_" + empPropName,
                                                    persistValue);
                                }
                            });
                        }

//                        val identifier = empPropertyType.toIdentifier();
//                        val emp = DataQuery.identifier(identifier).filter(
//                                DataFilter.eq("empId", ((EmpSimple)propertyValue).getEmpId()), DataSimple.class).getItems()
//                                .stream().findAny().orElseThrow(()->new ServerException("员工不存在"));
//                        properties.forEach(property-> dataSimple.getProperties()
//                                .put(it.getProperty() + "_" + empPropertyType.toString().toLowerCase()
//                                                + "_" + property,
//                                        emp.getProperties().get(property)));
                    });
                }
            } else {
                propertyValue = new SimplePropertyValue();
                ((SimplePropertyValue) propertyValue).setValue(it.getValue());
                if (dataType.isArray()) {
                    ((SimplePropertyValue) propertyValue).setArrayValues(FastjsonUtil.toList(value, String.class));
                }
                ((SimplePropertyValue) propertyValue).setType(dataType);
            }
            String suffix = StringUtils.trimToEmpty(it.getSuffix());
            if (StringUtils.isNotEmpty(suffix)) {
                suffix = "_" + suffix;
            }
            dataSimple.getProperties().put(it.getProperty() + suffix, propertyValue);
        });
        DataUpdate.identifier("entity.form." + this.formId).update(dataSimple);
    }

    public static String updateApproveStatusAndReturnTaskId(String formId, String id, FormDataStatus status) {
        val dataSimple = DataQuery.identifier("entity.form." + formId).one(id, FormDataEntity.class);
        dataSimple.setStatus(status);
        DataUpdate.identifier("entity.form." + formId).update(dataSimple);
        return dataSimple.getWorkflowTaskId();
    }

    public static FormData loadById(String formId, String id, FormDef formDef, boolean self) {
        return loadById(formId, id, formDef, self, self ? String.valueOf(SecurityUserUtil.getSecurityUserInfo().getEmpId()) : null, false);
    }

    public static FormData loadById(String formId, String id, FormDef formDef, boolean self, String empId, boolean isSub) {
        FormDataEntity dataSimple;
        if (self) {
            DataFilter filter = DataFilter.eq("bid", id);
            filter = filter.andEqIf("form_owner$empId", empId,
                    () -> FormTarget.EMP.equals(formDef.getTarget()));
            filter = filter.andEqIf("createBy", empId,
                    () -> !FormTarget.EMP.equals(formDef.getTarget()));
            dataSimple = DataQuery.identifier("entity.form." + formId).filter(filter, FormDataEntity.class)
                    .getItems().stream().findFirst().orElseThrow(() -> new ServerException("data not exist"));
        } else if (isSub && StringUtils.isEmpty(id)) {
            DataFilter filter = DataFilter.eq("form_owner$empId", empId);
            dataSimple = DataQuery.identifier("entity.form." + formId).filter(filter, FormDataEntity.class)
                    .getItems().stream().findFirst().orElseThrow(() -> new ServerException("data not exist"));
        } else {
            dataSimple = DataQuery.identifier("entity.form." + formId)
                    .one(id, FormDataEntity.class);
        }
        val formData = new FormData();
        formData.setFormId(formId);
        formData.setId(dataSimple.getBid());
        formData.setTargetId(dataSimple.getFormTargetId());
        formData.setStatus(dataSimple.getStatus());
        formData.setCreateTime(dataSimple.getCreateTime());
        formData.setCreateBy(dataSimple.getCreateBy());
        formData.setUpdateTime(dataSimple.getUpdateTime());
        formData.setUpdateBy(dataSimple.getUpdateBy());
        formData.setSysProcessCode(dataSimple.getSysProcessCode());
        formDef.getProperties().forEach(propDef -> {
            PropertyValue propertyValue = dataSimple.getProperties().get(propDef.getProperty());
            String value = null;
            if (propertyValue instanceof SimplePropertyValue) {
                if (((SimplePropertyValue) propertyValue).getType().isArray()) {
                    value = FastjsonUtil.toJson(((SimplePropertyValue) propertyValue).getArrayValues());
                } else {
                    value = ((SimplePropertyValue) propertyValue).getValue();
                }
            } else if (propertyValue instanceof EmpSimple) {
                val emp = FastjsonUtil.convertObject(propertyValue, Map.class);
                if (!propDef.getComponentDetail().isEmpty()) {
                    val empDef = FastjsonUtil.convertObject(propDef.getComponentDetail(), FormPropEmpDef.class);
                    empDef.getEmpProperty().forEach((identifier, properties) -> {
                        emp.put(identifier, Maps.map());
                        properties.forEach(empProperty -> {
                            val empPropertyValue = ((SimplePropertyValue) dataSimple.getProperties()
                                    .get(propDef.getProperty() + "_" + identifier.toString().toLowerCase() + "_" + empProperty)).getValue();
                            if (StringUtils.isEmpty(empPropertyValue)) {
                                ((Map) emp.get(identifier)).put(empProperty, null);
                            } else if (empPropertyValue.startsWith("string:")) {
                                ((Map) emp.get(identifier)).put(empProperty, empPropertyValue.substring("string:".length()));
                            } else if (empPropertyValue.startsWith("long:")) {
                                ((Map) emp.get(identifier)).put(empProperty, Long.valueOf(empPropertyValue.substring("long:".length())));
                            } else if (empPropertyValue.startsWith("double:")) {
                                ((Map) emp.get(identifier)).put(empProperty, Double.valueOf(empPropertyValue.substring("double:".length())));
                            } else if (empPropertyValue.startsWith("boolean:")) {
                                ((Map) emp.get(identifier)).put(empProperty, Boolean.valueOf(empPropertyValue.substring("boolean:".length())));
                            } else if (empPropertyValue.startsWith("bigDecimal:")) {
                                ((Map) emp.get(identifier)).put(empProperty, new BigDecimal(empPropertyValue.substring("bigDecimal:".length())));
                            } else if (empPropertyValue.startsWith("json:")) {
                                if (empPropertyValue.startsWith("json:[")) {
                                    ((Map) emp.get(identifier)).put(empProperty, FastjsonUtil.toList(empPropertyValue.substring("json:".length()), Map.class));
                                } else {
                                    ((Map) emp.get(identifier)).put(empProperty, FastjsonUtil.toObject(empPropertyValue.substring("json:".length()), Map.class));
                                }
                            }
                        });
                    });
                }
                value = FastjsonUtil.toJson(emp);
            } else if (propertyValue instanceof DataTable) {
                val slaves = ((DataTable) propertyValue).getDataList();
                val slavesMap = slaves.stream().map(slave -> {
                    Map<String, Object> slaveDataMap = Maps.map();
                    slave.getProperties().forEach((property, value0) -> {
                        if (value0 instanceof SimplePropertyValue) {
                            val valueString = ((SimplePropertyValue) value0).getValue();
                            if (PropertyDataType.Timestamp
                                    .equals(((SimplePropertyValue) value0).getType()) && !StringUtils.isEmpty(valueString)) {
                                slaveDataMap.put(property, Long.valueOf(valueString));
                            } else {
                                slaveDataMap.put(property, valueString);
                            }

                        } else if (value0 instanceof EnumSimple) {
                            slaveDataMap.put(property, ((EnumSimple) value0).getValue());
                        }
                    });
                    return slaveDataMap;
                }).collect(Collectors.toList());
                value = FastjsonUtil.toJson(slavesMap);
            } else {
                value = FastjsonUtil.toJson(propertyValue);
            }
            FormPropertyData propertyData = new FormPropertyData();
            propertyData.setProperty(propDef.getProperty());
            propertyData.setDataType(propDef.getDataType());
            propertyData.setValue(value);
            propertyData.setSuffix("");
            formData.getProperties().add(propertyData);
            loadTxtData(dataSimple, formData, propDef);
        });

        return formData;
    }

    public static void delete(String formId, String id) {
        DataDelete.identifier("entity.form." + formId).delete(id);
    }

    @Data
    public static class FormDataEntity extends DataSimple {
        private String formTargetId;
        private String workflowTaskId;
        private FormDataStatus status;
        private String sysProcessCode;
    }

    public static <T extends DataSimple> PageResult<T> page(String keywords, FormDataStatus status,
            String formId, int pageSize, int pageNo, FormDef formDef,
            boolean selfOwn, boolean selfCreate, List<FilterElement> filters, String empId, Class<T> clazz, boolean emptyStatus) {
        DataFilter filter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId());
        if (null != status) {
            if (emptyStatus) {
                filter = filter.and(DataFilter.eq("status", status.toString()).orEq("status", null));
            }
            else {
                filter = filter.andEq("status", status.toString());
            }
        }
        filter = filter.andEqIf("form_owner$empId",
                empId,
                () -> selfOwn);
        filter = filter.andEqIf("createBy",
                empId,
                () -> selfCreate);
        if (null != filters) {
            if (StringUtils.isEmpty(keywords)) {
                keywords = filters.stream().filter(it -> "keywords".equals(it.getProp())).map(it -> it.getValue())
                        .filter(it -> null != it).map(it -> it.toString()).findFirst().orElse(null);
            }
            if (null == status) {
                String filterStatus = filters.stream().filter(it -> "status".equals(it.getProp())).map(it -> it.getValue())
                        .filter(it -> null != it).map(it -> it.toString()).findFirst().orElse(null);
                if (StringUtils.isNotEmpty(filterStatus)) {
                    filter = filter.andEq("status", filterStatus);
                }
            }

            String ownerOrgId = filters.stream().filter(it -> "ownerOrgId".equals(it.getProp())).map(it -> it.getValue())
                    .filter(it -> null != it).map(it -> it.toString()).findFirst().orElse(null);
            filter = filter.andEqIf("form_owner_organize", ownerOrgId, ()->StringUtils.isNotEmpty(ownerOrgId));
            for (FilterElement filterElement : filters) {
                if ("keywords".equals(filterElement.getProp())) {
                    continue;
                }
                if ("status".equals(filterElement.getProp())) {
                    continue;
                }
                if("ownerOrgId".equals(filterElement.getProp())){
                    continue;
                }
                if("sysProcessCode".equals(filterElement.getProp())){
                    filter.andEq("sysProcessCode", String.valueOf(filterElement.getValue()));
                    continue;
                }
                for (FormPropDef it : formDef.getProperties()) {
                    if (it.getProperty().equals(filterElement.getProp())) {
                        PropertyDataType dataType = it.getDataType();
                        if (Lists.list(PropertyDataType.String,
                                PropertyDataType.Number,
                                PropertyDataType.Enum,
                                PropertyDataType.Integer,
                                PropertyDataType.Boolean).contains(dataType)){
                            filter = filter.andEqIf(filterElement.getProp(), String.valueOf(filterElement.getValue()), ()-> null!= filterElement.getValue() && OpEnum.eq.equals(filterElement.getOp()));
                        }else if(PropertyDataType.Emp.equals(dataType)){
                            filter = filter.andRegexIf(filterElement.getProp()+"$name", String.valueOf(filterElement.getValue()), ()-> null!= filterElement.getValue() && OpEnum.eq.equals(filterElement.getOp()));
                        }else if(PropertyDataType.Timestamp.equals(dataType)){
                            filter = filter.andEqIf(filterElement.getProp(), String.valueOf(filterElement.getValue()), ()-> null!= filterElement.getValue() && OpEnum.eq.equals(filterElement.getOp()));
                            if(OpEnum.bt.equals(filterElement.getOp()) && null != filterElement.getValue()){
                                val times = FastjsonUtil.toList(FastjsonUtil.toJson(filterElement.getValue()), String.class);
                                if (times.size() > 0 && null != times.get(0)) {
                                    filter = filter.andGe(filterElement.getProp(), times.get(0));
                                }
                                if (times.size() > 1 && null != times.get(1)) {
                                    filter = filter.andLe(filterElement.getProp(), times.get(1));
                                }
                            }
                        } else {
                            throw new ServerException("not supported");
                        }
                    }
                }
            }
        }
        if (StringUtils.isNotEmpty(keywords)) {
            val empIdList = DataQuery.identifier("entity.hr.EmpWorkInfo").limit(-1, 1)
                    .filterProperties(DataFilter.regex("workno", keywords).orRegex("name", keywords), Lists.list("empId"),
                            System.currentTimeMillis()).getItems().stream().map(it -> it.get("empId")).collect(Collectors.toList());
            if (empIdList.isEmpty()) {
                return new PageResult<>(Lists.list(), pageNo, pageSize, 0);
            } else {
                filter = filter.and(DataFilter.in("formTargetId", empIdList).orIn("form_owner$empId",empIdList));
            }
        }
        try {
            AuthScopeFilterUtil.addIdentifiers(Lists.list("entity.form." + formId));
            return DataQuery.identifier("entity.form." + formId)
                    .limit(pageSize, pageNo).filter(filter, clazz);
        }finally {
            AuthScopeFilterUtil.clearIdentifier();
        }

    }

    public static PageResult<FormData> page(String keywords, FormDataStatus status,
                                            String formId, int pageSize, int pageNo, FormDef formDef,
                                            boolean selfOwn, boolean selfCreate, List<FilterElement> filters, String empId,boolean emptyStatus) {
        val page = page(keywords, status, formId, pageSize, pageNo, formDef, selfOwn, selfCreate, filters, empId, FormDataEntity.class,emptyStatus);
        val list = page.getItems().stream().map(it -> {
            FormData formData = new FormData();
            formData.setCreateBy(it.getCreateBy());
            formData.setUpdateBy(it.getUpdateBy());
            formData.setCreateTime(it.getCreateTime());
            formData.setUpdateTime(it.getUpdateTime());
            formData.setId(it.getBid());
            formData.setFormId(it.getIdentifier().replace("entity.form.", ""));
            formData.setTargetId(it.getFormTargetId());
            formData.setStatus(it.getStatus());
            formData.setSysProcessCode(it.getSysProcessCode());
            //formData.setStatus(FormDataStatus.APPROVED);
            formDef.getProperties().forEach(formPropDef -> {
                String value = null;
                val propertyValue = it.getProperties().get(formPropDef.getProperty());
                if (null != propertyValue) {
                    if (propertyValue instanceof SimplePropertyValue) {
                        if (((SimplePropertyValue) propertyValue).getType().isArray()) {
                            value = FastjsonUtil.toJson(((SimplePropertyValue) propertyValue).getArrayValues());
                        } else {
                            value = ((SimplePropertyValue) propertyValue).getValue();
                        }
                    } else {
                        value = FastjsonUtil.toJson(propertyValue);
                    }
                }
                val formPropertyData = new FormPropertyData();
                formPropertyData.setProperty(formPropDef.getProperty());
                formPropertyData.setDataType(formPropDef.getDataType());
                if (!formPropDef.getEnumDef().isEmpty() && value != null) {
                    if (formPropertyData.getDataType().equals(PropertyDataType.Enum_Array)) {
                        List<EnumSimple> valueList = FastjsonUtil.toList(value, String.class).stream().map(enumValue -> {
                            val enumSimple = new EnumSimple();
                            enumSimple.setValue(enumValue);
                            for (PropertyEnumDef enumDef : formPropDef.getEnumDef()) {
                                if (enumValue.equals(enumDef.getValue())) {
                                    enumSimple.setText(enumDef.getDisplay());
                                }
                            }
                            return enumSimple;
                        }).collect(Collectors.toList());
                        value = FastjsonUtil.toJson(valueList);
//                        value = StringUtils.join(valueList.stream().map(item->{
//                            for(PropertyEnumDef enumDef : formPropDef.getEnumDef()){
//                                if(item.equals(enumDef.getValue())){
//                                    return enumDef.getDisplay();
//                                }
//                            }
//                            return item;
//                        }).collect(Collectors.toList()), ",");
                    }
//                    else{
//                        for(PropertyEnumDef enumDef : formPropDef.getEnumDef()){
//                            if(value.equals(enumDef.getValue())){
//                                value = enumDef.getDisplay();
//                                break;
//                            }
//                        }
//                    }
                }
                formPropertyData.setValue(value);
                formPropertyData.setSuffix("");
                formData.getProperties().add(formPropertyData);
                loadTxtData(it, formData, formPropDef);
            });
            return formData;
        }).collect(Collectors.toList());
        return new PageResult<>(list, pageNo, pageSize, page.getTotal());
    }

    private static void loadTxtData(FormDataEntity it, FormData formData, FormPropDef formPropDef) {
        if (FormWidgetType.getSelectType().contains(formPropDef.getWidgetType())) {
            FormPropertyData txtData = ObjectConverter.convert(formPropDef, FormSelectPropDef.class)
                    .loadTxtData(it);
            formData.getProperties().add(txtData);
        }
    }

}
