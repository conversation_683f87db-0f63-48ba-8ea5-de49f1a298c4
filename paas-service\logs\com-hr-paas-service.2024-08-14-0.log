2024-08-14 14:43:06.682 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 14:43:06.694 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 14:43:08.709 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 14:43:09.063 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 14:43:14.868 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 14:43:18.014 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 14:43:18.019 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 14:43:18.201 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 157ms. Found 0 repository interfaces.
2024-08-14 14:43:21.856 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 14:43:21.864 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 14:43:23.937 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 14:43:25.128 [main] INFO  org.reflections.Reflections - Reflections took 894 ms to scan 2 urls, producing 16 keys and 350 values 
2024-08-14 14:43:25.221 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 14:43:29.762 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:29.768 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:29.769 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:29.855 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:30.044 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:30.116 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:30.580 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:30.636 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:30.892 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:31.693 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 14:43:32.017 [redisson-netty-5-11] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 14:43:32.210 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 14:43:32.281 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 14:43:32.281 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 14:43:32.789 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:36.784 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 14:43:36.784 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 14:43:36.798 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 14:43:36.799 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 14:43:38.538 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:38.684 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:38.726 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:39.641 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:39.679 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:39.708 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:41.997 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2116e9cf[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 14:43:41.998 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7a88e6a9[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$add6659b#prepareRecentTask]
2024-08-14 14:43:42.054 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:42.068 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:42.095 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:42.135 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:42.158 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:42.171 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:43:42.207 [main] WARN  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback config fail, adminAddresses is null.
2024-08-14 14:43:42.208 [main] INFO  com.xxl.job.core.util.NetUtil - >>>>>>>>>>> xxl-rpc, port[9999] is in use.
2024-08-14 14:43:42.211 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 14:43:42.238 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 14:43:42.254 [Thread-33] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10000
2024-08-14 14:43:42.256 [Thread-33] WARN  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry config fail, adminAddresses is null.
2024-08-14 14:43:42.268 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 14:43:42.643 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 14:43:43.077 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 14:43:43.269 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 14:43:43.319 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 14:43:43.341 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 14:43:43.362 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 14:43:43.555 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 14:43:43.621 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 14:43:43.631 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 14:43:43.634 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 14:43:43.784 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 14:43:43.788 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 14:43:43.793 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 14:43:43.808 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 14:43:43.832 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 14:43:43.998 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 14:43:44.485 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 14:43:44.485 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 14:43:44.486 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 14:43:44.487 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 14:43:44.496 [Thread-33] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 14:43:44.496 [Thread-34] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 14:43:44.498 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 14:43:44.534 [Thread-34] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 14:43:44.534 [Thread-34] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 14:43:44.646 [Thread-34] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 14:51:01.027 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 14:51:01.033 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 14:51:05.278 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 14:51:07.033 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 14:51:09.309 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 14:51:15.329 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 14:51:15.342 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 14:51:16.358 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 937ms. Found 0 repository interfaces.
2024-08-14 14:51:24.556 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 14:51:24.572 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 14:51:26.008 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 14:51:26.369 [main] INFO  org.reflections.Reflections - Reflections took 319 ms to scan 2 urls, producing 16 keys and 350 values 
2024-08-14 14:51:26.480 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 14:51:33.841 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:33.848 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:33.849 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:33.955 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:34.186 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:34.269 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:34.818 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:34.870 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:35.170 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:36.156 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 14:51:36.499 [redisson-netty-5-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 14:51:36.720 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 14:51:36.824 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 14:51:36.824 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 14:51:38.568 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:42.990 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 14:51:42.991 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 14:51:43.006 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 14:51:43.006 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 14:51:44.717 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:44.755 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:44.789 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:46.386 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:46.438 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:46.471 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:49.027 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@665fb1d8[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 14:51:49.028 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@142ac60e[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$abd402bc#prepareRecentTask]
2024-08-14 14:51:49.079 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:49.095 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:49.113 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:49.135 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:49.152 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:49.164 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 14:51:49.194 [main] WARN  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback config fail, adminAddresses is null.
2024-08-14 14:51:49.197 [main] INFO  com.xxl.job.core.util.NetUtil - >>>>>>>>>>> xxl-rpc, port[9999] is in use.
2024-08-14 14:51:49.201 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 14:51:49.225 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 14:51:49.240 [Thread-33] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10000
2024-08-14 14:51:49.242 [Thread-33] WARN  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry config fail, adminAddresses is null.
2024-08-14 14:51:49.257 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 14:51:49.740 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 14:51:50.335 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 14:51:50.511 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 14:51:50.613 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 14:51:50.643 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 14:51:50.663 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 14:51:50.996 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 14:51:51.055 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 14:51:51.066 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 14:51:51.068 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 14:51:51.244 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 14:51:51.247 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 14:51:51.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 14:51:51.261 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 14:51:51.278 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 14:51:51.385 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 14:51:51.956 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 14:51:51.956 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 14:51:51.957 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 14:51:51.960 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 14:51:51.972 [Thread-34] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 14:51:51.973 [Thread-33] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 14:51:51.977 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 14:51:52.048 [Thread-34] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 14:51:52.048 [Thread-34] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 14:51:52.188 [Thread-34] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 15:00:36.160 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 15:00:36.165 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 15:00:38.347 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 15:00:38.760 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 15:00:40.615 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 15:00:43.475 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 15:00:43.480 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 15:00:43.604 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 104ms. Found 0 repository interfaces.
2024-08-14 15:00:47.113 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 15:00:47.120 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 15:00:47.835 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 15:00:48.106 [main] INFO  org.reflections.Reflections - Reflections took 237 ms to scan 2 urls, producing 16 keys and 350 values 
2024-08-14 15:00:48.182 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 15:00:53.132 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:00:53.138 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:00:53.139 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:00:53.253 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:00:53.503 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:00:53.584 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:00:54.042 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:00:54.099 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:00:54.374 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:00:55.048 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 15:00:55.293 [redisson-netty-5-11] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 15:00:55.468 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 15:00:55.555 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 15:00:55.555 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 15:00:56.122 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:01.235 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 15:01:01.237 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 15:01:01.252 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 15:01:01.253 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 15:01:03.129 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:03.174 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:03.206 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:03.812 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:03.849 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:03.881 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:08.548 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@37e99c0f[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 15:01:08.550 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@29ae939c[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$6a66edf5#prepareRecentTask]
2024-08-14 15:01:08.620 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:08.641 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:08.669 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:08.712 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:08.744 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:08.782 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:01:08.902 [main] INFO  com.xxl.job.core.util.NetUtil - >>>>>>>>>>> xxl-rpc, port[9999] is in use.
2024-08-14 15:01:08.910 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 15:01:08.945 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 15:01:08.962 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10000
2024-08-14 15:01:08.990 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 15:01:11.217 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 15:01:11.790 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 15:01:12.239 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 15:01:12.396 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-14 15:01:12.398 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-14 15:01:12.400 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-14 15:01:12.402 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-14 15:01:12.404 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-14 15:01:12.406 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-14 15:01:12.407 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-14 15:01:12.409 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-14 15:01:12.478 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 15:01:12.525 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 15:01:12.557 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 15:01:12.583 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-14 15:01:12.717 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 15:01:12.759 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-14 15:01:12.771 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-14 15:01:12.779 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 15:01:12.812 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-14 15:01:12.835 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-14 15:01:12.848 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-14 15:01:12.889 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-14 15:01:12.901 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 15:01:12.903 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 15:01:13.022 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-14 15:01:13.027 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-14 15:01:13.030 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-14 15:01:13.047 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-14 15:01:13.051 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-14 15:01:13.062 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-14 15:01:13.070 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-14 15:01:13.087 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-14 15:01:13.091 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-14 15:01:13.095 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-14 15:01:13.100 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-14 15:01:13.107 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-14 15:01:13.110 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-14 15:01:13.140 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-14 15:01:13.158 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 15:01:13.161 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 15:01:13.163 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 15:01:13.168 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 15:01:13.171 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-14 15:01:13.189 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 15:01:13.274 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 15:01:13.767 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 15:01:13.768 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 15:01:13.768 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 15:01:13.773 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 15:01:13.789 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 15:01:13.802 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service', registryValue='http://*************2:10000/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-14 15:01:13.803 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-14 15:01:13.803 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 15:01:13.805 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 15:01:13.805 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-14 15:01:13.806 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-14 15:01:13.860 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 15:01:13.860 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 15:01:14.030 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 15:03:50.882 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 15:03:50.893 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 15:03:55.605 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 15:03:56.961 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 15:04:00.736 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 15:04:04.816 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 15:04:04.823 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 15:04:05.128 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 276ms. Found 0 repository interfaces.
2024-08-14 15:04:12.928 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 15:04:12.949 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 15:04:14.352 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 15:04:15.343 [main] INFO  org.reflections.Reflections - Reflections took 918 ms to scan 2 urls, producing 16 keys and 350 values 
2024-08-14 15:04:15.714 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 15:04:23.062 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:23.108 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:23.109 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:23.231 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:23.689 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:23.827 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:24.335 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:24.393 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:24.703 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:27.646 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 15:04:27.869 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 15:04:28.028 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 15:04:28.097 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 15:04:28.097 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 15:04:29.320 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:33.357 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 15:04:33.358 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 15:04:33.371 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 15:04:33.372 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 15:04:34.997 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:35.042 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:35.077 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:35.960 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:35.990 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:36.023 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:38.376 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@20b56542[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 15:04:38.377 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@35ec675e[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$4bd3e31b#prepareRecentTask]
2024-08-14 15:04:38.438 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:38.452 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:38.471 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:38.495 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:38.513 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:38.531 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 15:04:38.577 [main] INFO  com.xxl.job.core.util.NetUtil - >>>>>>>>>>> xxl-rpc, port[9999] is in use.
2024-08-14 15:04:38.579 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 15:04:38.611 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 15:04:38.627 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 10000
2024-08-14 15:04:38.649 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 15:04:39.192 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 15:04:39.763 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 15:04:40.183 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 15:04:40.505 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-14 15:04:40.508 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-14 15:04:40.511 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-14 15:04:40.515 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-14 15:04:40.519 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-14 15:04:40.522 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-14 15:04:40.526 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-14 15:04:40.530 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-14 15:04:40.664 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 15:04:40.730 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 15:04:40.767 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 15:04:40.806 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-14 15:04:40.983 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 15:04:41.042 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-14 15:04:41.057 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-14 15:04:41.073 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 15:04:41.125 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-14 15:04:41.161 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-14 15:04:41.176 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-14 15:04:41.224 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-14 15:04:41.250 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 15:04:41.253 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 15:04:41.408 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-14 15:04:41.412 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-14 15:04:41.415 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-14 15:04:41.431 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-14 15:04:41.436 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-14 15:04:41.446 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-14 15:04:41.453 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-14 15:04:41.466 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-14 15:04:41.471 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-14 15:04:41.474 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-14 15:04:41.478 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-14 15:04:41.483 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-14 15:04:41.488 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-14 15:04:41.530 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-14 15:04:41.554 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 15:04:41.557 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 15:04:41.562 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 15:04:41.608 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 15:04:41.611 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-14 15:04:41.637 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 15:04:41.834 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 15:04:42.826 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 15:04:42.826 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 15:04:42.827 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 15:04:42.828 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 15:04:42.853 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 15:04:42.870 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service', registryValue='http://*************2:10000/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-14 15:04:42.871 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-14 15:04:42.871 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 15:04:42.872 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 15:04:42.872 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-14 15:04:42.873 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-14 15:04:42.917 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 15:04:42.917 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 15:04:43.061 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 17:24:21.817 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 17:24:21.824 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 17:24:24.174 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 17:24:24.625 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 17:24:28.302 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 17:24:30.772 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 17:24:30.779 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 17:24:31.092 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 280ms. Found 0 repository interfaces.
2024-08-14 17:24:35.472 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 17:24:35.477 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 17:24:36.147 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 17:24:37.605 [main] INFO  org.reflections.Reflections - Reflections took 1380 ms to scan 2 urls, producing 15 keys and 351 values 
2024-08-14 17:24:38.153 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 17:24:44.445 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:44.450 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:44.451 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:44.549 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:44.840 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:44.929 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:44.995 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:45.689 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:45.983 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:46.740 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 17:24:46.981 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 17:24:47.158 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 17:24:47.346 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 17:24:47.347 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 17:24:48.159 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:53.357 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:24:53.357 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:24:53.373 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:24:53.373 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:24:57.634 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:57.719 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:57.771 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:58.798 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:58.841 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:24:58.885 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:25:01.260 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@340b7fca[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 17:25:01.262 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4ab8c3c0[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$b3b00c2f#prepareRecentTask]
2024-08-14 17:25:01.315 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:25:01.327 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:25:01.346 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:25:01.377 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:25:01.412 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:25:01.430 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:25:01.468 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 17:25:01.493 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 17:25:01.508 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-14 17:25:01.525 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 17:25:02.049 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 17:25:02.853 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 17:25:03.277 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 17:25:03.547 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-14 17:25:03.550 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-14 17:25:03.552 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-14 17:25:03.556 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-14 17:25:03.559 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-14 17:25:03.564 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-14 17:25:03.568 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-14 17:25:03.572 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-14 17:25:03.745 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 17:25:03.820 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 17:25:03.867 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 17:25:03.906 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-14 17:25:04.112 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 17:25:04.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-14 17:25:04.182 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-14 17:25:04.198 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 17:25:04.259 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-14 17:25:04.303 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-14 17:25:04.322 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-14 17:25:04.385 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-14 17:25:04.415 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 17:25:04.421 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 17:25:04.756 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-14 17:25:04.764 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-14 17:25:04.770 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-14 17:25:04.788 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-14 17:25:04.796 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-14 17:25:04.813 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-14 17:25:04.821 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-14 17:25:04.846 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-14 17:25:04.852 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-14 17:25:04.856 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-14 17:25:04.863 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-14 17:25:04.869 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-14 17:25:04.874 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-14 17:25:04.933 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-14 17:25:04.967 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 17:25:04.974 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 17:25:04.985 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 17:25:05.035 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 17:25:05.045 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-14 17:25:05.110 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 17:25:05.426 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 17:25:06.932 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:25:07.131 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-14 17:25:07.148 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-14 17:25:07.317 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@6ffac1f3
2024-08-14 17:25:07.395 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:25:07.529 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 17:25:07.529 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-14 17:25:07.531 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 17:25:07.531 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 17:25:07.531 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 17:25:07.547 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 17:25:07.557 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service', registryValue='http://*************2:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-14 17:25:07.557 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-14 17:25:07.561 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 17:25:07.563 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 17:25:07.564 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-14 17:25:07.564 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-14 17:25:07.623 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:25:07.623 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:25:07.741 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 17:26:12.533 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 17:26:12.544 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 17:26:17.241 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 17:26:17.887 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 17:26:19.963 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 17:26:22.623 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 17:26:22.630 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 17:26:22.842 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 180ms. Found 0 repository interfaces.
2024-08-14 17:26:26.262 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 17:26:26.268 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 17:26:26.985 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 17:26:27.329 [main] INFO  org.reflections.Reflections - Reflections took 301 ms to scan 2 urls, producing 15 keys and 350 values 
2024-08-14 17:26:27.451 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 17:26:35.081 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:35.120 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:35.126 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:35.458 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:35.996 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:36.146 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:36.227 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:37.028 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:37.331 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:38.633 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 17:26:39.225 [redisson-netty-5-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 17:26:39.532 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 17:26:39.689 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 17:26:39.690 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 17:26:42.156 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:49.560 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:26:49.561 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:26:49.579 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:26:49.579 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:26:52.038 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:52.089 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:52.140 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:53.334 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:53.371 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:53.426 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:56.914 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4ef6177c[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 17:26:56.917 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@45b4d2ff[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$fc1ae93a#prepareRecentTask]
2024-08-14 17:26:56.986 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:57.002 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:57.028 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:57.063 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:57.075 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:57.085 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:26:57.122 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 17:26:57.145 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 17:26:57.163 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-14 17:26:57.177 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 17:26:57.537 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 17:26:58.074 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 17:26:58.387 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 17:26:58.608 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-14 17:26:58.612 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-14 17:26:58.616 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-14 17:26:58.619 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-14 17:26:58.624 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-14 17:26:58.628 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-14 17:26:58.632 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-14 17:26:58.634 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-14 17:26:58.758 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 17:26:58.837 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 17:26:58.881 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 17:26:58.908 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-14 17:26:59.118 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 17:26:59.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-14 17:26:59.180 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-14 17:26:59.188 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 17:26:59.228 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-14 17:26:59.253 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-14 17:26:59.264 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-14 17:26:59.335 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-14 17:26:59.375 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 17:26:59.378 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 17:26:59.646 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-14 17:26:59.652 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-14 17:26:59.658 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-14 17:26:59.682 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-14 17:26:59.687 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-14 17:26:59.700 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-14 17:26:59.708 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-14 17:26:59.725 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-14 17:26:59.731 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-14 17:26:59.737 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-14 17:26:59.742 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-14 17:26:59.748 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-14 17:26:59.751 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-14 17:26:59.786 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-14 17:26:59.807 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 17:26:59.811 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 17:26:59.815 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 17:26:59.825 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 17:26:59.829 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-14 17:26:59.858 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 17:26:59.968 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 17:27:00.726 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:27:00.894 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-14 17:27:00.914 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-14 17:27:01.044 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@6d3e720e
2024-08-14 17:27:01.110 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:27:01.244 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 17:27:01.243 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-14 17:27:01.246 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 17:27:01.246 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 17:27:01.247 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 17:27:01.262 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 17:27:01.276 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service', registryValue='http://*************2:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-14 17:27:01.277 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-14 17:27:01.278 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 17:27:01.280 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 17:27:01.280 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-14 17:27:01.281 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-14 17:27:01.336 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:27:01.337 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:27:01.440 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 17:27:43.613 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 17:27:43.624 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 17:27:48.456 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 17:27:49.379 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 17:27:51.296 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 17:27:55.851 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 17:27:55.857 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 17:27:56.049 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 164ms. Found 0 repository interfaces.
2024-08-14 17:28:00.183 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 17:28:00.190 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 17:28:00.863 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 17:28:01.135 [main] INFO  org.reflections.Reflections - Reflections took 232 ms to scan 2 urls, producing 15 keys and 349 values 
2024-08-14 17:28:01.238 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 17:28:08.535 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:08.542 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:08.543 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:08.629 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:08.828 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:08.903 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:08.958 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:09.423 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:09.612 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:10.447 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 17:28:10.694 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 17:28:10.863 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 17:28:10.951 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 17:28:10.952 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 17:28:11.583 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:15.995 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:28:15.996 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:28:16.010 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:28:16.010 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:28:19.307 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:19.452 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:19.560 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:21.601 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:21.669 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:21.729 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:25.778 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@9fe1e14[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 17:28:25.782 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@d4e17ec[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$f4478f15#prepareRecentTask]
2024-08-14 17:28:25.860 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:25.876 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:25.899 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:25.927 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:25.941 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:25.958 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:26.001 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 17:28:26.029 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 17:28:26.046 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-14 17:28:26.063 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 17:28:26.511 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 17:28:27.048 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 17:28:27.404 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 17:28:27.623 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-14 17:28:27.627 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-14 17:28:27.629 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-14 17:28:27.633 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-14 17:28:27.635 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-14 17:28:27.638 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-14 17:28:27.641 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-14 17:28:27.644 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-14 17:28:27.784 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 17:28:27.840 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 17:28:27.871 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 17:28:27.899 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-14 17:28:28.044 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 17:28:28.087 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-14 17:28:28.103 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-14 17:28:28.114 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 17:28:28.164 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-14 17:28:28.194 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-14 17:28:28.208 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-14 17:28:28.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-14 17:28:28.274 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 17:28:28.276 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 17:28:28.564 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-14 17:28:28.570 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-14 17:28:28.576 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-14 17:28:28.591 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-14 17:28:28.595 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-14 17:28:28.604 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-14 17:28:28.610 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-14 17:28:28.627 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-14 17:28:28.632 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-14 17:28:28.636 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-14 17:28:28.640 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-14 17:28:28.643 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-14 17:28:28.647 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-14 17:28:28.680 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-14 17:28:28.706 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 17:28:28.710 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 17:28:28.713 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 17:28:28.723 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 17:28:28.727 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-14 17:28:28.755 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 17:28:28.915 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 17:28:29.959 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-14 17:28:29.960 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-14 17:28:30.150 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-14 17:28:30.611 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:28:30.759 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-14 17:28:30.770 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-14 17:28:30.889 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@667b3972
2024-08-14 17:28:30.946 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:28:31.028 [main] ERROR c.c.h.metadata.sdk.query.DataQuery - 请求异常，identifier:entity.hr.EmpWorkInfo filter: null
2024-08-14 17:28:31.028 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-08-14 17:28:31.039 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=HRPaas140开发DEV环境报错日志, canAlarm = true
2024-08-14 17:28:31.068 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 17:28:31.069 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 17:28:31.069 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 17:28:31.070 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-14 17:28:31.070 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 17:28:31.093 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 17:28:31.101 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service', registryValue='http://*************2:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-14 17:28:31.101 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-14 17:28:31.107 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 17:28:31.109 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 17:28:31.110 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-14 17:28:31.111 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-14 17:28:31.161 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:28:31.162 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:28:31.258 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 17:28:31.384 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-14 17:28:31.398 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-08-14 17:29:44.271 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 17:29:44.278 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 17:29:46.729 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 17:29:47.175 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 17:29:50.154 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 17:29:54.019 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 17:29:54.036 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 17:29:54.347 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 265ms. Found 0 repository interfaces.
2024-08-14 17:29:59.597 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 17:29:59.607 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 17:30:00.399 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 17:30:01.947 [main] INFO  org.reflections.Reflections - Reflections took 1422 ms to scan 2 urls, producing 15 keys and 351 values 
2024-08-14 17:30:02.024 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 17:30:06.960 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:06.975 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:06.976 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:07.092 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:07.556 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:07.677 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:07.778 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:09.329 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:09.899 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:11.094 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 17:30:11.382 [redisson-netty-5-12] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 17:30:11.534 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 17:30:11.668 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 17:30:11.669 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 17:30:12.345 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:17.646 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:30:17.648 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:30:17.661 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:30:17.661 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:30:21.366 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:21.449 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:21.500 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:22.930 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:22.988 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:23.049 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:27.828 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2aafd790[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 17:30:27.829 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@340b7fca[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$835d131a#prepareRecentTask]
2024-08-14 17:30:27.884 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:27.900 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:27.919 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:27.941 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:27.960 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:27.973 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:28.013 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 17:30:28.040 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 17:30:28.065 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-14 17:30:28.074 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 17:30:28.531 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 17:30:29.195 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 17:30:29.649 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 17:30:30.198 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-14 17:30:30.207 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-14 17:30:30.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-14 17:30:30.218 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-14 17:30:30.224 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-14 17:30:30.229 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-14 17:30:30.232 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-14 17:30:30.237 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-14 17:30:31.204 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 17:30:31.428 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 17:30:31.449 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 17:30:31.464 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-14 17:30:32.242 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 17:30:32.272 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-14 17:30:32.283 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-14 17:30:32.288 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 17:30:32.315 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-14 17:30:32.332 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-14 17:30:32.354 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-14 17:30:32.392 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-14 17:30:32.403 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 17:30:32.405 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 17:30:32.605 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-14 17:30:32.609 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-14 17:30:32.614 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-14 17:30:32.630 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-14 17:30:32.635 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-14 17:30:32.646 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-14 17:30:32.652 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-14 17:30:32.666 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-14 17:30:32.670 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-14 17:30:32.674 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-14 17:30:32.679 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-14 17:30:32.684 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-14 17:30:32.689 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-14 17:30:32.731 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-14 17:30:32.761 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 17:30:32.764 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 17:30:32.767 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 17:30:32.777 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 17:30:32.780 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-14 17:30:32.803 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 17:30:32.921 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 17:30:33.942 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:30:34.155 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-14 17:30:34.173 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-14 17:30:34.519 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:30:34.522 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@bdc5584
2024-08-14 17:30:34.581 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:30:35.038 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-14 17:30:35.038 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-14 17:30:35.193 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:30:35.225 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-14 17:30:35.777 [main] ERROR c.c.h.m.s.t.aspect.TransactionAspect - Rolling back paas transaction by exception,tx id=1994435542284288
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:659)
	at java.util.ArrayList.get(ArrayList.java:435)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService.create(FormDataService.java:99)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService.create(FormDataService.java:70)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$FastClassBySpringCGLIB$$8117d76f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect.doTransaction(TransactionAspect.java:64)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$EnhancerBySpringCGLIB$$231c3cd8.create(<generated>)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$FastClassBySpringCGLIB$$8117d76f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$EnhancerBySpringCGLIB$$eb13e9ac.create(<generated>)
	at com.caidaocloud.hrpaas.service.form.FormDefServiceTest.save(FormDefServiceTest.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:74)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:251)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-08-14 17:30:35.779 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-08-14 17:30:35.801 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=HRPaas140开发DEV环境报错日志, canAlarm = true
2024-08-14 17:30:35.906 [caidaocloud-mail-log-pool-0] INFO  com.caidaocloud.config.MailConfig - AsyncEmailWork run ...
2024-08-14 17:30:36.039 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-14 17:30:36.040 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 17:30:36.040 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 17:30:36.045 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 17:30:36.041 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 17:30:36.074 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 17:30:36.101 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service', registryValue='http://*************2:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-14 17:30:36.102 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-14 17:30:36.103 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 17:30:36.107 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 17:30:36.113 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-14 17:30:36.116 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-14 17:30:36.185 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:30:36.185 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:30:36.391 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 17:30:36.554 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-14 17:30:36.585 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-08-14 17:31:01.728 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 17:31:01.738 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 17:31:04.375 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 17:31:04.781 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 17:31:07.599 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 17:31:09.729 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 17:31:09.737 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 17:31:09.939 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 145ms. Found 0 repository interfaces.
2024-08-14 17:31:13.333 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 17:31:13.339 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 17:31:14.013 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 17:31:14.323 [main] INFO  org.reflections.Reflections - Reflections took 265 ms to scan 2 urls, producing 15 keys and 351 values 
2024-08-14 17:31:14.469 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 17:31:20.191 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:20.198 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:20.200 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:20.305 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:20.555 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:20.628 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:20.719 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:21.279 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:21.498 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:22.501 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 17:31:23.530 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 17:31:23.708 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 17:31:23.824 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 17:31:23.825 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 17:31:24.374 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:31.238 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:31:31.250 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:31:31.275 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:31:31.275 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:31:36.035 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:36.100 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:36.143 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:37.814 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:37.843 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:37.879 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:42.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@340b7fca[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 17:31:42.457 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4ab8c3c0[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$835d131a#prepareRecentTask]
2024-08-14 17:31:42.615 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:42.640 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:42.689 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:42.777 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:42.828 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:42.839 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:43.199 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 17:31:43.298 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 17:31:43.360 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 17:31:43.363 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-14 17:31:43.822 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 17:31:45.144 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 17:31:46.211 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 17:31:47.357 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-14 17:31:47.363 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-14 17:31:47.366 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-14 17:31:47.370 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-14 17:31:47.372 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-14 17:31:47.375 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-14 17:31:47.377 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-14 17:31:47.380 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-14 17:31:47.528 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 17:31:47.616 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 17:31:47.662 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 17:31:47.702 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-14 17:31:47.948 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 17:31:48.147 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-14 17:31:48.162 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-14 17:31:48.175 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 17:31:48.233 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-14 17:31:48.288 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-14 17:31:48.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-14 17:31:48.374 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-14 17:31:48.402 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 17:31:48.406 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 17:31:48.666 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-14 17:31:48.674 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-14 17:31:48.683 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-14 17:31:48.718 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-14 17:31:48.721 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-14 17:31:48.747 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-14 17:31:48.762 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-14 17:31:48.799 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-14 17:31:48.817 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-14 17:31:48.835 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-14 17:31:48.844 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-14 17:31:48.852 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-14 17:31:48.868 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-14 17:31:48.923 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-14 17:31:49.048 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 17:31:49.054 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 17:31:49.062 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 17:31:49.081 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 17:31:49.088 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-14 17:31:49.131 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 17:31:49.297 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 17:31:52.159 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:31:52.483 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-14 17:31:52.502 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-14 17:31:52.793 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:31:52.798 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@5b2728db
2024-08-14 17:31:52.877 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:31:53.327 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-14 17:31:53.327 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-14 17:31:53.521 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:31:53.773 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-14 17:33:31.902 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m38s14ms484µs800ns).
2024-08-14 17:33:38.565 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - null
java.net.SocketTimeoutException: null
	at java.net.SocksSocketImpl.remainingMillis(SocksSocketImpl.java:111)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1228)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:95)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:750)
2024-08-14 17:33:38.580 [xxl-job, executor ExecutorRegistryThread] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-08-14 17:33:38.896 [xxl-job, executor ExecutorRegistryThread] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=HRPaas140开发DEV环境报错日志, canAlarm = true
2024-08-14 17:35:36.244 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m4s329ms959µs700ns).
2024-08-14 17:35:36.618 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-14 17:35:36.626 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 17:35:36.627 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 17:35:36.896 [caidaocloud-mail-log-pool-0] INFO  com.caidaocloud.config.MailConfig - AsyncEmailWork run ...
2024-08-14 17:35:36.898 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 17:35:36.900 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service', registryValue='http://*************2:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(null), for url : http://***************:10006/xxl-job-admin/api/registry, content=null]
2024-08-14 17:35:36.910 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 17:35:36.991 [main] INFO  org.reflections.Reflections - Reflections took 314 ms to scan 1 urls, producing 9 keys and 30 values 
2024-08-14 17:35:37.078 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 17:35:37.107 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service', registryValue='http://*************2:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-14 17:35:37.108 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-14 17:35:37.108 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 17:35:37.111 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 17:35:37.113 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-14 17:35:37.113 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-14 17:35:37.199 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:35:37.200 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:35:37.207 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@2c9cdbc9
2024-08-14 17:35:37.374 [main] ERROR c.c.h.m.s.t.aspect.TransactionAspect - Rolling back paas transaction by exception,tx id=1994436183119872
feign.FeignException: status 500 reading IMetadataFeign#one(String)
	at feign.FeignException.errorStatus(FeignException.java:78)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:93)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:149)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:78)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:103)
	at com.sun.proxy.$Proxy239.one(Unknown Source)
	at com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorServiceFeignImpl.load(MetadataOperatorServiceFeignImpl.java:25)
	at com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert.insert(DataInsert.java:63)
	at com.caidaocloud.hrpaas.service.domain.form.entity.FormData.create(FormData.java:162)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService.create(FormDataService.java:138)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService.create(FormDataService.java:70)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$FastClassBySpringCGLIB$$8117d76f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect.doTransaction(TransactionAspect.java:64)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$EnhancerBySpringCGLIB$$231c3cd8.create(<generated>)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$FastClassBySpringCGLIB$$8117d76f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$EnhancerBySpringCGLIB$$eb13e9ac.create(<generated>)
	at com.caidaocloud.hrpaas.service.form.FormDefServiceTest.save(FormDefServiceTest.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:74)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:251)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-08-14 17:35:37.382 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-08-14 17:35:37.403 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=HRPaas140开发DEV环境报错日志, canAlarm = true
2024-08-14 17:35:37.406 [caidaocloud-mail-log-pool-1] INFO  com.caidaocloud.config.MailConfig - AsyncEmailWork run ...
2024-08-14 17:35:37.552 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 17:35:37.748 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-14 17:35:37.763 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-08-14 17:36:03.646 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 17:36:03.654 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 17:36:06.684 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 17:36:07.366 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 17:36:09.437 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 17:36:12.520 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 17:36:12.525 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 17:36:12.721 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 166ms. Found 0 repository interfaces.
2024-08-14 17:36:16.420 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 17:36:16.430 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 17:36:17.240 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 17:36:17.566 [main] INFO  org.reflections.Reflections - Reflections took 292 ms to scan 2 urls, producing 15 keys and 351 values 
2024-08-14 17:36:17.672 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 17:36:26.320 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:26.373 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:26.381 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:26.619 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:26.997 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:27.187 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:27.651 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:31.380 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:32.137 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:33.612 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 17:36:34.980 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 17:36:35.142 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 17:36:35.215 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 17:36:35.215 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 17:36:35.692 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:43.943 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:36:43.965 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:36:43.989 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:36:43.989 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:36:47.965 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:48.032 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:48.113 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:50.389 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:50.442 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:50.497 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:55.574 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@37e99c0f[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 17:36:55.577 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@29ae939c[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$88c1e801#prepareRecentTask]
2024-08-14 17:36:55.634 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:55.650 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:55.671 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:55.692 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:55.706 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:55.718 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:36:55.757 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 17:36:55.778 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 17:36:55.799 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-14 17:36:55.810 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 17:36:56.207 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 17:36:56.996 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 17:36:57.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 17:36:57.498 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-14 17:36:57.501 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-14 17:36:57.503 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-14 17:36:57.505 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-14 17:36:57.507 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-14 17:36:57.510 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-14 17:36:57.512 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-14 17:36:57.514 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-14 17:36:57.606 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 17:36:57.667 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 17:36:57.695 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 17:36:57.722 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-14 17:36:57.892 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 17:36:57.934 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-14 17:36:57.948 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-14 17:36:57.959 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 17:36:57.991 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-14 17:36:58.014 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-14 17:36:58.027 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-14 17:36:58.064 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-14 17:36:58.085 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 17:36:58.088 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 17:36:58.242 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-14 17:36:58.246 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-14 17:36:58.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-14 17:36:58.268 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-14 17:36:58.271 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-14 17:36:58.279 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-14 17:36:58.285 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-14 17:36:58.298 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-14 17:36:58.305 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-14 17:36:58.308 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-14 17:36:58.311 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-14 17:36:58.314 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-14 17:36:58.318 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-14 17:36:58.351 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-14 17:36:58.371 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 17:36:58.374 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 17:36:58.376 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 17:36:58.382 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 17:36:58.385 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-14 17:36:58.405 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 17:36:58.500 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 17:36:59.327 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:36:59.496 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-14 17:36:59.510 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-14 17:36:59.806 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:36:59.810 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@585a2ad9
2024-08-14 17:36:59.868 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:37:00.323 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-14 17:37:00.324 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-14 17:37:00.516 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:37:00.527 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-14 17:37:01.088 [main] INFO  org.reflections.Reflections - Reflections took 126 ms to scan 1 urls, producing 9 keys and 30 values 
2024-08-14 17:37:01.302 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-14 17:37:01.302 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 17:37:01.303 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 17:37:01.305 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 17:37:01.307 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 17:37:01.319 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 17:37:01.329 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service', registryValue='http://*************2:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-14 17:37:01.335 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-14 17:37:01.336 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 17:37:01.336 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 17:37:01.337 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-14 17:37:01.339 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-14 17:37:01.388 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:37:01.388 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:37:01.498 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 17:37:01.637 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-14 17:37:01.684 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-08-14 17:42:47.448 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 17:42:47.494 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 17:42:50.662 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 17:42:51.190 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 17:42:53.468 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 17:42:57.475 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 17:42:57.479 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 17:42:57.583 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 86ms. Found 0 repository interfaces.
2024-08-14 17:42:59.993 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 17:43:00.000 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 17:43:00.668 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 17:43:00.900 [main] INFO  org.reflections.Reflections - Reflections took 204 ms to scan 2 urls, producing 15 keys and 351 values 
2024-08-14 17:43:00.987 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 17:43:05.083 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:05.089 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:05.090 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:05.207 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:05.397 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:05.680 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:05.730 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:06.377 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:06.570 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:07.325 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 17:43:07.565 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 17:43:07.721 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 17:43:07.798 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 17:43:07.798 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 17:43:08.373 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:13.041 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:43:13.042 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:43:13.055 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:43:13.056 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:43:15.179 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:15.226 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:15.276 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:16.901 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:16.944 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:16.989 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:20.445 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@20b56542[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 17:43:20.447 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@35ec675e[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$d737300e#prepareRecentTask]
2024-08-14 17:43:20.520 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:20.539 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:20.585 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:20.656 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:20.732 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:20.747 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:20.835 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 17:43:20.870 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 17:43:20.890 [Thread-34] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-14 17:43:20.908 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 17:43:21.262 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 17:43:21.703 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 17:43:22.058 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 17:43:22.398 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-14 17:43:22.401 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-14 17:43:22.404 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-14 17:43:22.408 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-14 17:43:22.410 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-14 17:43:22.414 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-14 17:43:22.417 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-14 17:43:22.420 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-14 17:43:22.515 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 17:43:22.575 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 17:43:22.606 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 17:43:22.630 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-14 17:43:22.778 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 17:43:22.811 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-14 17:43:22.821 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-14 17:43:22.832 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 17:43:22.871 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-14 17:43:22.894 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-14 17:43:22.903 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-14 17:43:22.934 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-14 17:43:22.946 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 17:43:22.948 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 17:43:23.078 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-14 17:43:23.083 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-14 17:43:23.087 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-14 17:43:23.107 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-14 17:43:23.110 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-14 17:43:23.117 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-14 17:43:23.123 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-14 17:43:23.136 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-14 17:43:23.142 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-14 17:43:23.147 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-14 17:43:23.152 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-14 17:43:23.158 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-14 17:43:23.163 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-14 17:43:23.204 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-14 17:43:23.223 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 17:43:23.225 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 17:43:23.227 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 17:43:23.234 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 17:43:23.237 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-14 17:43:23.257 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 17:43:23.354 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 17:43:24.048 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:43:24.204 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-14 17:43:24.216 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-14 17:43:24.437 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:43:24.440 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@5b989e1d
2024-08-14 17:43:24.491 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:43:24.871 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-14 17:43:24.872 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-14 17:43:25.029 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-14 17:43:25.230 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:43:25.609 [main] INFO  org.reflections.Reflections - Reflections took 105 ms to scan 1 urls, producing 9 keys and 30 values 
2024-08-14 17:43:25.867 [main] ERROR c.c.h.metadata.sdk.dto.DataSimple - 组件转换失败
java.lang.NumberFormatException: For input string: "{"code":"FullTime","text":"正式员工","value":"104"}"
	at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.lang.Long.parseLong(Long.java:589)
	at java.lang.Long.valueOf(Long.java:803)
	at com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache.getDictInfo(QueryInfoCache.java:90)
	at com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache.getDict(QueryInfoCache.java:67)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple.doDictSimple(DictSimple.java:207)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple.toPersist(DictSimple.java:141)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple.extractPersist(DictSimple.java:94)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple.initPropertyData(DataSimple.java:180)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple.lambda$toPersistData$8(DataSimple.java:169)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple.toPersistData(DataSimple.java:164)
	at com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert.insert(DataInsert.java:64)
	at com.caidaocloud.hrpaas.service.domain.form.entity.FormData.create(FormData.java:162)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService.create(FormDataService.java:138)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService.create(FormDataService.java:70)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$FastClassBySpringCGLIB$$8117d76f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect.doTransaction(TransactionAspect.java:64)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$EnhancerBySpringCGLIB$$4891f1e0.create(<generated>)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$FastClassBySpringCGLIB$$8117d76f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$EnhancerBySpringCGLIB$$3eee06a0.create(<generated>)
	at com.caidaocloud.hrpaas.service.form.FormDefServiceTest.save(FormDefServiceTest.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:74)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:251)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-08-14 17:43:25.868 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-08-14 17:43:25.882 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=HRPaas140开发DEV环境报错日志, canAlarm = true
2024-08-14 17:43:25.954 [caidaocloud-mail-log-pool-0] INFO  com.caidaocloud.config.MailConfig - AsyncEmailWork run ...
2024-08-14 17:43:25.955 [main] ERROR c.c.h.m.s.t.aspect.TransactionAspect - Rolling back paas transaction by exception,tx id=1994441849165824
com.caidaocloud.excption.ServerException: 组件转换失败
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple.initPropertyData(DataSimple.java:183)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple.lambda$toPersistData$8(DataSimple.java:169)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple.toPersistData(DataSimple.java:164)
	at com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert.insert(DataInsert.java:64)
	at com.caidaocloud.hrpaas.service.domain.form.entity.FormData.create(FormData.java:162)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService.create(FormDataService.java:138)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService.create(FormDataService.java:70)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$FastClassBySpringCGLIB$$8117d76f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect.doTransaction(TransactionAspect.java:64)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$EnhancerBySpringCGLIB$$4891f1e0.create(<generated>)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$FastClassBySpringCGLIB$$8117d76f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.hrpaas.service.application.form.service.FormDataService$$EnhancerBySpringCGLIB$$3eee06a0.create(<generated>)
	at com.caidaocloud.hrpaas.service.form.FormDefServiceTest.save(FormDefServiceTest.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:74)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:251)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
Caused by: java.lang.NumberFormatException: For input string: "{"code":"FullTime","text":"正式员工","value":"104"}"
	at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.lang.Long.parseLong(Long.java:589)
	at java.lang.Long.valueOf(Long.java:803)
	at com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache.getDictInfo(QueryInfoCache.java:90)
	at com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache.getDict(QueryInfoCache.java:67)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple.doDictSimple(DictSimple.java:207)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple.toPersist(DictSimple.java:141)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple.extractPersist(DictSimple.java:94)
	at com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple.initPropertyData(DataSimple.java:180)
	... 61 common frames omitted
2024-08-14 17:43:25.956 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-08-14 17:43:25.957 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=HRPaas140开发DEV环境报错日志, canAlarm = false
2024-08-14 17:43:26.009 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-14 17:43:26.011 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 17:43:26.011 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 17:43:26.013 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 17:43:26.015 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 17:43:26.028 [Thread-34] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 17:43:26.040 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service', registryValue='http://*************2:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-14 17:43:26.040 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-14 17:43:26.040 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 17:43:26.042 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 17:43:26.042 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-14 17:43:26.043 [Thread-33] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-14 17:43:26.104 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:43:26.104 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:43:26.279 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 17:43:26.428 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-14 17:43:26.455 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-08-14 17:44:37.495 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-14 17:44:37.502 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-14 17:44:39.699 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-14 17:44:40.086 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-14 17:44:41.779 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-14 17:44:45.803 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-14 17:44:45.810 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-14 17:44:45.979 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 142ms. Found 0 repository interfaces.
2024-08-14 17:44:50.570 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-14 17:44:50.589 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-14 17:44:51.956 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-14 17:44:52.501 [main] INFO  org.reflections.Reflections - Reflections took 481 ms to scan 2 urls, producing 15 keys and 351 values 
2024-08-14 17:44:52.637 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-14 17:44:59.301 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:44:59.315 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:44:59.317 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:44:59.423 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:44:59.677 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:44:59.781 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:44:59.860 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:01.475 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:01.744 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:02.676 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-14 17:45:02.935 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-14 17:45:03.156 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-14 17:45:03.459 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-14 17:45:03.459 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-14 17:45:05.022 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:10.706 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:45:10.712 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:45:10.747 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:45:10.747 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:45:14.218 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:14.292 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:14.360 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:15.693 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:15.719 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:15.743 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:18.596 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@591be976[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-14 17:45:18.598 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@520f1862[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$88c1e801#prepareRecentTask]
2024-08-14 17:45:18.642 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:18.653 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:18.668 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:18.684 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:18.696 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:18.705 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:18.736 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-14 17:45:18.757 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-14 17:45:18.773 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-14 17:45:18.786 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-14 17:45:19.312 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-14 17:45:20.071 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-14 17:45:20.665 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-14 17:45:20.847 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-14 17:45:20.850 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-14 17:45:20.854 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-14 17:45:20.856 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-14 17:45:20.858 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-14 17:45:20.860 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-14 17:45:20.862 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-14 17:45:20.864 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-14 17:45:20.962 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-14 17:45:21.009 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-14 17:45:21.035 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-14 17:45:21.053 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-14 17:45:21.197 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-14 17:45:21.238 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-14 17:45:21.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-14 17:45:21.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-14 17:45:21.302 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-14 17:45:21.328 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-14 17:45:21.340 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-14 17:45:21.386 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-14 17:45:21.400 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-14 17:45:21.402 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-14 17:45:21.537 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-14 17:45:21.542 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-14 17:45:21.547 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-14 17:45:21.566 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-14 17:45:21.572 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-14 17:45:21.585 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-14 17:45:21.594 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-14 17:45:21.611 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-14 17:45:21.616 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-14 17:45:21.621 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-14 17:45:21.626 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-14 17:45:21.629 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-14 17:45:21.632 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-14 17:45:21.666 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-14 17:45:21.688 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-14 17:45:21.691 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-14 17:45:21.693 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-14 17:45:21.698 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-14 17:45:21.701 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-14 17:45:21.723 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-14 17:45:21.818 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-14 17:45:22.663 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:45:22.807 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-14 17:45:22.823 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-14 17:45:23.083 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:45:23.086 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@2cf62699
2024-08-14 17:45:23.140 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-14 17:45:23.604 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-14 17:45:23.606 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-14 17:45:23.844 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-14 17:45:23.885 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-14 17:45:24.607 [main] INFO  org.reflections.Reflections - Reflections took 148 ms to scan 1 urls, producing 9 keys and 30 values 
2024-08-14 17:45:24.932 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-14 17:45:24.934 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-14 17:45:24.935 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-14 17:45:24.935 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-14 17:45:24.938 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-14 17:45:24.949 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-14 17:45:24.958 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service', registryValue='http://*************2:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-14 17:45:24.959 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-14 17:45:24.959 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-14 17:45:24.961 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-14 17:45:24.962 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-14 17:45:24.962 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-14 17:45:25.000 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-14 17:45:25.000 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-14 17:45:25.126 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-14 17:45:25.262 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-14 17:45:25.277 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
