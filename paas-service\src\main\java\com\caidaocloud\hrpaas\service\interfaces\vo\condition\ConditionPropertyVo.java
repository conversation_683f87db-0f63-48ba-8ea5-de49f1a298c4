package com.caidaocloud.hrpaas.service.interfaces.vo.condition;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2022/11/2
 */
@ApiModel("匹配条件可选字段vo")
@Data
public class ConditionPropertyVo {
	@ApiModelProperty("模型identifier")
	private String identifier;
	@ApiModelProperty("模型名称")
	private String name;
	@ApiModelProperty("可选字段")
	private List<ConditionDataVo> properties;
}
