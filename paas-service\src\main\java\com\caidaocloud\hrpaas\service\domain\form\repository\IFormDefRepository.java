package com.caidaocloud.hrpaas.service.domain.form.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import com.googlecode.totallylazy.Option;

import java.util.List;

public interface IFormDefRepository extends IBaseRepository<FormDef> {

    void publish(FormDef entity);

    void disable(FormDef entity);

    PageResult<FormDef> page(int pageNo, int pageSize);

    PageResult<FormDef> pageByKeyWorks(int pageNo, int pageSize, String keyWords);

    List<FormDef> list();

    Option<FormDef> loadByName(String name);

	Option<FormDef> loadByCode(String code);
}
