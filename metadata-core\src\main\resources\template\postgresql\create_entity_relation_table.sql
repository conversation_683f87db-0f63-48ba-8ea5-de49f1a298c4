create table if not exists $tableName  (
    "id" bigserial primary key,
    "identifier" varchar(50) NOT NULL,
    "property" varchar(50) NOT NULL,
    "source_id" varchar(50) NOT NULL,
    "target_id" varchar(50) NOT NULL,
    "create_time" bigint NOT NULL,
    "create_by" varchar(50) default NULL,
    "data_start_time" bigint default NULL,
    "data_end_time" bigint default NULL
);
create index if not exists "$source_index_name" on $tableName ("source_id");
create index if not exists "$target_index_name" on $tableName ("target_id");
COMMENT on table $tableName is '$table_desc';