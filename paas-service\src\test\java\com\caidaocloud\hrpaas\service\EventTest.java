package com.caidaocloud.hrpaas.service;

import com.caidaocloud.metadata.application.event.factory.EventFactory;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR>
 * @date 2024/3/12
 */
@SpringBootTest(classes = HrPaasApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class EventTest {
	@Before
	public void bf(){
		SecurityUserInfo info = new SecurityUserInfo();
		info.setTenantId("11");
		info.setEmpId(0L);
		info.setUserId(0L);
		SecurityUserUtil.setSecurityUserInfo(info);
	}


	@Test
	public void syncMsg(){
		EntityDef def = new EntityDef();
		EventFactory.createEntityPropertySyncEvent(def).publish();
	}
}
