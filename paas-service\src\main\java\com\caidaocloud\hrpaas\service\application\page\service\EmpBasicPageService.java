package com.caidaocloud.hrpaas.service.application.page.service;

import java.io.IOException;
import java.util.List;

import javax.annotation.Resource;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.paas.common.event.PageDetailChangeEvent;
import com.caidaocloud.hrpaas.service.application.page.dto.TenantPageDetailDto;
import com.caidaocloud.hrpaas.service.domain.page.entity.TenantPageDetail;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.interfaces.vo.page.TenantPageDetailVo;
import com.caidaocloud.metadata.application.service.MetadataService;
import com.caidaocloud.metadata.interfaces.convertor.MetadataConvertor;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import lombok.SneakyThrows;
import lombok.val;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import static com.caidaocloud.hrpaas.service.application.page.constant.PageConstant.EMP_BASIC_PAGE_ID;

@Service
public class EmpBasicPageService {

	@Resource
	private MetadataService metadataService;

	private static String empBasicPageConfig = null;

	public List<MetadataVo> loadBasicMetadata() {
		return Lists.list(loadMetadata("entity.hr.EmpWorkInfo"), loadMetadata("entity.hr.EmpPrivateInfo"), loadMetadata("entity.hr.Contract"));
	}

	public MetadataVo loadMetadata(String identifier) {
		val def = metadataService.one(identifier);
		val inherited = metadataService.loadHierarchyProperties(def);
		return MetadataConvertor.toVo(def, inherited);
	}

	public void alterDetail(TenantPageDetailDto pageDetail) {
		val pageId = pageDetail.getId();
		if (StringUtils.isEmpty(pageDetail.getStandardPageConfig()) || !pageDetail.getId().equals(EMP_BASIC_PAGE_ID)) {
			throw new ServerException("标准页面配置错误");
		}
		TenantPageDetail existed = TenantPageDetail.loadById(pageId);
		if (null == existed) {
			existed = new TenantPageDetail();
			existed.setId(pageId);
			existed.setType(PageType.StandardPage);
			existed.setStandardPageConfig(pageDetail.getStandardPageConfig());
			existed.setCreateTime(System.currentTimeMillis());
			existed.setUpdateTime(existed.getCreateTime());
			existed.setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
			existed.setUpdateBy(existed.getCreateBy());
			existed.insert();
		}
		else {
			BeanUtils.copyProperties(pageDetail, existed);
			existed.setUpdateTime(System.currentTimeMillis());
			existed.setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
			existed.update();
		}

		new PageDetailChangeEvent(existed.getId(), SecurityUserUtil.getSecurityUserInfo().getTenantId()).publish();
	}

	public TenantPageDetailVo basicPageDetail() {
		TenantPageDetail pageDetail = TenantPageDetail.loadById(EMP_BASIC_PAGE_ID);
		if (null == pageDetail) {
			String empBasicPageConfig = empBasicPageConfig();
			pageDetail = new TenantPageDetail();
			pageDetail.setId(EMP_BASIC_PAGE_ID);
			pageDetail.setType(PageType.StandardPage);
			pageDetail.setStandardPageConfig(empBasicPageConfig);
		}
		return FastjsonUtil.convertObject(pageDetail, TenantPageDetailVo.class);
	}


	@SneakyThrows
	public String empBasicPageConfig() {
		if (empBasicPageConfig == null) {
			synchronized (this) {
				if (empBasicPageConfig == null) {
					empBasicPageConfig = IOUtils.toString(
							new ClassPathResource("page/standard_emp_basic_page.json").getInputStream(),
							"UTF-8"
					);
				}
			}
		}
		return empBasicPageConfig;
	}

}
