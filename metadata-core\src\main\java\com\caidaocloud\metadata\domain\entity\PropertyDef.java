package com.caidaocloud.metadata.domain.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.DefStatus;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;

@Data
public class PropertyDef {

    private String property;

    private String persistProperty;

    private PropertyDefaultValue defaultValue;

    private String name;
    private Map<String, String> i18nName;

    private PropertyDataType dataType;

    private String datasource;

    private String widgetType;

    private String format;

    private List<PropertyEnumDef> enumDef;

    private List<PropertyDef> objDef;

    private DefStatus status;

    private boolean unique = false;

    private boolean visible = true;

    private boolean modifiable = true;

    private boolean required = false;

    private boolean encrypted = false;

    private boolean showOnApp = true;

    private boolean availableOnWorkflow = true;

    private String onEvent;

    private String placeholder;

    private Map<String, String> i18nPlaceholder;

    private List<PropertyDefExt> extList = Lists.list();

    private List<PropertyDef> slaveProperties;

    private List<PropertyRule> rules = Lists.list();

    private boolean expEnable = false;
    private String expression;

    private boolean sync = false;

    protected boolean conflict(Sequence<PropertyDef> props){
        return props.find(prop ->{
            if(this.property.equals(prop.property)){
                return true;
            }
            if(null != this.i18nName && !this.i18nName.isEmpty()){
                if(this.i18nName.get("default").equals(prop.name)){
                    return true;
                }
            }else{
                if(this.name.equals(prop.name)){
                    return true;
                }
            }
            return false;

        }).isDefined();
    }

    protected boolean conflictRelation(Sequence<EntityRelationDef> relations){
        return relations.find(relation ->
                this.property.equals(relation.getProperty()) ||
                        (null!=this.i18nName && !this.i18nName.isEmpty()
                                && this.i18nName.get("default").equals(relation.getPropertyName())) ||
                        (null != this.name && this.name.equals(relation.getPropertyName()))
        ).isDefined();
    }

    protected boolean conflict(String excludeCheckProp, Sequence<PropertyDef> props) {
        val thisName = StringUtils.isNotEmpty(this.getName())?this.getName():this.getI18nName().get("default");
        return props.find(prop ->
                !excludeCheckProp.equals(prop.property) &&
                        (this.property.equals(prop.property) ||
                                this.property.equals(prop.persistProperty) ||
                                thisName.equals(StringUtils.isNotEmpty(prop.getName())?prop.getName():prop.getI18nName().get("default")))
        ).isDefined();
    }

    protected Sequence<PropertyDef> replace(String property, Sequence<PropertyDef> props){
        val replacedCount = new AtomicInteger(0);
        val replace = props.map(prop->{
            if(property.equals(prop.property)){
                this.validateUpdate(prop);
                replacedCount.incrementAndGet();
                return this;
            }else{
                return prop;
            }
        });
        val result = Sequences.sequence(replace.toList());
        if(replacedCount.get() < 1){
            throw new ServerException("属性不存在");
        }
        return result;
    }

    protected void validateWhenModelInit(){
        validate(this.property, false);
    }
    protected void validateInsert(){
        validate(this.property, true);
    }
    protected void validateUpdate(PropertyDef toBeReplaced){
        if(DefStatus.DISABLED.equals(toBeReplaced.status)){
            throw new ServerException("属性已禁用");
        }
        if(!toBeReplaced.dataType.equals(this.dataType)){
            // throw new ServerException("数据类型不允许变化");
        }
        validate(toBeReplaced.persistProperty, true);
    }
    protected void validateRecover(){
        validate(this.persistProperty, true);
    }

    private void validate(String persistProperty, boolean validateRequired){
        this.status = DefStatus.ENABLED;
        this.persistProperty = persistProperty;
        if(PropertyDataType.PID.equals(dataType)){
            if(!Constant.PROPERTY_PID.equals(property)){
                throw new ServerException("上级节点命名property必须为pid");
            }
        }else if(Constant.PROPERTY_PID.equals(property)){
            throw new ServerException("非上级节点命名property不允许为pid");
        }

        if(validateRequired){
            checkRequiredProperty();
        }
        checkDefaultValueDataType();
        checkExpression();
        if(PropertyDataType.Enum.equals(this.dataType)){
            if(null == enumDef || enumDef.isEmpty()){
                throw new ServerException("缺少枚举定义");
            }
        }
        if(PropertyDataType.Object.equals(this.dataType)){
            if(null == objDef || objDef.isEmpty()){
                throw new ServerException("缺少对象定义");
            }
        }
    }

    private void checkExpression() {
        if (expEnable) {
            if (dataType != PropertyDataType.String && dataType != PropertyDataType.Number) {
                throw new ServerException("属性类型不支持表达式:" + dataType);
            }
            if (StringUtils.isBlank(expression)) {
                throw new ServerException("表达式未配置");
            }
            try {
                AviatorEvaluator.validate(expression);
            }
            catch (Exception e) {
                throw new ServerException("表达式配置错误:" + e.getMessage(), e);
            }
        }

    }

    private void checkRequiredProperty(){
        if(required){
            if(PropertyDataType.PID.equals(this.dataType)){
                throw new ServerException("上级节点不可定义为必填");
            }
        }
    }

    private void checkDefaultValueDataType(){
        if(null == defaultValue){
            return;
        }
        if(PropertyDataType.PID.equals(dataType)){
            throw new ServerException("上级节点不允许定义默认值");
        }
        if(!dataType.equals(defaultValue.getType())){
            throw new ServerException("非法的默认值");
        }
        if(null == defaultValue.getValue()){
            return;
        }
        switch (dataType){
            case String:break;
            case Number:
                try{
                    Double.parseDouble(defaultValue.getValue());
                }catch(Exception e){
                    throw new ServerException("非法的默认值");
                }
                break;
            case Timestamp:
                try{
                    val i = Long.parseLong(defaultValue.getValue());
                    if(i < 0){
                        throw new Exception();
                    }
                }catch(Exception e){
                    throw new ServerException("非法的默认值");
                }
                break;
            case Enum :
                Sequences.sequence(enumDef).find(enumDefCheck->enumDefCheck.getValue().equals(defaultValue.getValue()))
                        .getOrThrow(new ServerException("非法的默认值"));
                break;
            case Integer:
                try{
                    Long.parseLong(defaultValue.getValue());
                }catch(Exception e){
                    throw new ServerException("非法的默认值");
                }
                break;
            case Object:
            case String_Array:
            case Number_Array:
            case Timestamp_Array:
            case Integer_Array:
        }
    }

    public void clearOnEvent(){
        this.onEvent = null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PropertyDef that = (PropertyDef) o;
        return Objects.equals(property, that.property);
    }

    @Override
    public int hashCode() {
        return Objects.hash(property);
    }

    public void appendMasterBidSlaveProperty() {
        if(slaveProperties == null){
            slaveProperties = Lists.list();
        }
        if(slaveProperties.stream().filter(it->"masterBid".equals(it.getProperty())).findFirst().isPresent()){
            return;
        }
        val master = new PropertyDef();
        master.setProperty("masterBid");
        master.setName("主模型BID");
        master.setDataType(PropertyDataType.String);
        slaveProperties.add(master);
    }
}
