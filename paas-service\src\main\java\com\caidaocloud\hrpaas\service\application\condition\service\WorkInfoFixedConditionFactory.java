package com.caidaocloud.hrpaas.service.application.condition.service;

import java.util.List;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;

/**
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
public abstract class WorkInfoFixedConditionFactory implements FixedConditionFactory {
	@NacosValue("${caidaocloud.condition.fixed.work.identifiers:entity.onboarding.EmpWorkInfo,entity.hr.EmpWorkInfo}")
	public List<String> identifiers;

	public boolean isWorkInfo(String identifier) {
		return identifiers.contains(identifier);
	}

	@Override
	public boolean apply(EntityDef identifier, PropertyDef property) {
		return isWorkInfo(identifier.getIdentifier()) && getProperty().equals(property.getProperty());
	}

	protected abstract String getProperty();
}
