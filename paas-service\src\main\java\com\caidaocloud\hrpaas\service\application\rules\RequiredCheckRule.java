package com.caidaocloud.hrpaas.service.application.rules;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.service.application.uiform.enums.CheckRuleGroup;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Service;

@Service
public class RequiredCheckRule implements ICheckRule {
    private static String REQUIRED_TIPS = "%s不能为空";

    @Override
    public String getRuleType() {
        return "required";
    }

    @Override
    public String[] getRuleGroup() {
        return new String [] {CheckRuleGroup.STRING.getRuleGrouType()};
    }

    @Override
    public void check(IRule checkRuleDto) {
        if(StringUtil.isEmpty(checkRuleDto.value())){
            String tips = StringUtil.isBlank(checkRuleDto.tips()) ?
                    String.format(REQUIRED_TIPS, checkRuleDto.name()) : checkRuleDto.tips();
            throw new ServerException(tips);
        }
    }
}
