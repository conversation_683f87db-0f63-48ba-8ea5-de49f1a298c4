package com.caidaocloud.metadata.infrastructure.repository.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil;
import com.caidaocloud.metadata.domain.entity.DataModification;
import com.caidaocloud.metadata.domain.repository.IDataModificationRepository;
import com.caidaocloud.metadata.infrastructure.repository.po.DataModificationPo;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.StringUtil;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class DataModificationRepositoryImpl implements IDataModificationRepository {

    @Autowired
    DataModificationMapper mapper;

    @NacosValue("${dbType:mysql}")
    private String dbType;

    @NacosValue("${anonymous.tenant:}")
    private String anonymousTenant;

    private String getTenantId() {
        String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        if(StringUtil.isEmpty(tenantId)){
            if(ThreadLocalUtil.isAnonymousAllowed()){
                tenantId = anonymousTenant;
            }
        }
        return tenantId;
    }

    @Override
    public String insert(DataModification entity) {
        val po = DataModificationPo.fromEntity(DataModificationPo.class, entity);
        mapper.insert(po);
        return po.getId().toString();
    }

    @Override
    public void update(DataModification entity) {
        val po = DataModificationPo.fromEntity(DataModificationPo.class, entity);
        mapper.updateById(po);
    }

    @Override
    public DataModification getLastUpdated(String identifier, String bid, long startTime) {
        LambdaQueryWrapper<DataModificationPo> queryWrapper = new QueryWrapper<DataModificationPo>().lambda();
        queryWrapper.eq(DataModificationPo::getTargetId, bid)
                .eq(DataModificationPo::getIdentifier, identifier)
                .le(DataModificationPo::getStartTime, startTime)
                .orderByDesc(DataModificationPo::getStartTime)
                .last("limit 1");
         DataModificationPo po = mapper.selectOne(queryWrapper);
        if (po == null) {
            return null;
        }
        return po.toEntity(DataModification.class);
    }

//    @SneakyThrows
//    @Override
//    public void createTable() {
//        String sqlTemplate = IOUtils.toString(new ClassPathResource("template/" + dbType + "/create_data_modification_table.sql").getInputStream(), "UTF-8");
//        sqlTemplate = sqlTemplate.replaceAll("\\$tenantId", getTenantId());
//        mapper.exec(sqlTemplate);
//    }
}
