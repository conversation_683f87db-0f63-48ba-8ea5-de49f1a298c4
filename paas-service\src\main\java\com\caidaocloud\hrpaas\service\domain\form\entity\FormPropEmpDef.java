package com.caidaocloud.hrpaas.service.domain.form.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.Data;
import lombok.val;

import java.util.List;
import java.util.Map;

@Data
public class FormPropEmpDef {

    private Map<EmpPropertyType, List<String>> empProperty = Maps.map();

    private boolean formOwner;

    private OwnerAutoSet ownerAutoSet = OwnerAutoSet.NONE;

    public List<PropertyDef> appendEntityProperties(FormPropDef formPropDef) {
        List<PropertyDef> result = Lists.list();
        empProperty.keySet().forEach(identifier->{
            val propertiesMetadata = FastjsonUtil.convertList(
                    SpringUtil.getBean(MetadataOperatorService.class).load(identifier.toIdentifier()).fetchAllProperties(),
                    PropertyDef.class
            );
            val properties = empProperty.get(identifier);
            properties.forEach(property->{
                val propertyDef = propertiesMetadata.stream().filter(it->it.getProperty().equals(property))
                        .findAny().get();
                propertyDef.setProperty(formPropDef.getProperty() + "_" + identifier.toString().toLowerCase() + "_" + property);
                propertyDef.setName(formPropDef.getName() + "_" + identifier.toString().toLowerCase() + "_" + propertyDef.getName());
                propertyDef.setI18nName(Maps.map());
                propertyDef.setRequired(false);
                propertyDef.setUnique(false);
                propertyDef.setExpEnable(false);
                propertyDef.setDataType(PropertyDataType.String);
                propertyDef.setDefaultValue(null);
                result.add(propertyDef);
            });
        });
        return result;
    }

    public enum OwnerAutoSet{
        NONE, MOBILE, PC
    }

    public enum EmpPropertyType{
        WORK_INFO, PRIVATE_INFO, WORK_OVERVIEW;

        public String toIdentifier() {
            switch (this){
                case WORK_INFO: return "entity.hr.EmpWorkInfo";
                case PRIVATE_INFO: return "entity.hr.EmpPrivateInfo";
                case WORK_OVERVIEW: return "entity.hr.EmpWorkOverview";
                default : throw new ServerException("Not supported yet");
            }
        }
    }

}
