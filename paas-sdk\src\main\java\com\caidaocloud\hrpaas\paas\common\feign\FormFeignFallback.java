package com.caidaocloud.hrpaas.paas.common.feign;

import java.util.List;

import com.caidaocloud.hrpaas.paas.common.dto.FormDataDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDataMapDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefDto;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

@Component
public class FormFeignFallback implements FormFeignClient {

    @Override
    public Result<List<FormDefDto>> getFormDefList(String formType, boolean implantable) {
        return Result.fail();
    }

    @Override
    public Result<List<FormDefDto>> getFormDefList(String formType) {
        return Result.fail();
    }

    @Override
    public Result<FormDefDto> getFormDef(String name) {
        return Result.fail();
    }

    @Override
    public Result<FormDefDto> getFormDefById(String id) {
        return Result.fail();
    }

    @Override
    public Result<FormDefDto> getFormDefByCode(String code) {
        return Result.fail();
    }

    @Override
    public Result<FormDataMapDto> getFormDataMap(String formId, String id) {
        return Result.fail();
    }

    @Override
    public Result<String> saveFormData(String formId, FormDataDto formDataDto) {
        return Result.fail();
    }

    @Override
    public Result update(String formId, FormDataDto formDataDto) {
        return Result.fail();
    }
}
