package com.caidaocloud.hrpaas.service.application.auth.feign;

import com.caidaocloud.hrpaas.service.application.auth.dto.TenantDto;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MaintenanceFeignFallBack implements MaintenanceFeignClient {
    @Override
    public Result<List<TenantDto>> tenantList() {
        return Result.ok(Lists.newArrayList());
    }
}
