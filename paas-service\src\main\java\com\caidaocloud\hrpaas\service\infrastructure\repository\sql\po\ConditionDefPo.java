package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.service.application.condition.enums.ConditionPlatform;
import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionDef;
import com.caidaocloud.metadata.infrastructure.configuration.handler.BooleanTypeHandler;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@TableName("paas_condition_def")
@Data
public class ConditionDefPo extends BasePo<ConditionDefPo, ConditionDef> {
	private String name;
	private String module;
	private String code;
	@DisplayAsArray
	private String identifiers;
	@DisplayAsArray
	private String properties;
	private String remark;
	@TableField(typeHandler = BooleanTypeHandler.class)
	private boolean deleted = false;
	private String tenantId;
	private long createTime;
	private String createBy;
	private long updateTime;
	private String updateBy;
	private ConditionPlatform platform;

	// @Override
	// public ConditionDef toEntity(Class<ConditionDef> clazz) {
	// 	ConditionDef entity = ObjectConverter.convert(this, ConditionDef.class);
	// 	entity.setProperties(FastjsonUtil.toList(properties, ConditionData.class));
	// 	return entity;
	// }
}
