package com.caidaocloud.hrpaas.service.application.uiform.dto;

import lombok.Data;

/**
 * 表单引擎Dto
 * <AUTHOR>
 * @date 2021-10-29
 */
@Data
public class UiFormMenuDto {
    /**
     * 菜单ID
     */
    private String id;

    /**
     * 租户Id
     */
    private String tenantId;

    /**
     * 菜单名称
     */
    private String code;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单icon
     */
    private String icon;

    /**
     * 父菜单 code
     */
    private String parent;

    /**
     * 菜单类型
     */
    private String type;

    /**
     * 菜单url
     */
    private String url;

    /**
     * 菜单所属模块
     * hrpaas 模块，attendance 考勤模块，payroll 薪资模块
     */
    private String model;

    /**
     * 挂载点id
     */
    private Long mountId;

    /**
     * 菜单排序
     */
    private Integer sortNum;

    /**
     * 菜单 share，是否分享
     */
    private boolean share;

    /**
     * 菜单 share 相关的配置
     */
    private String shareConfig;
}
