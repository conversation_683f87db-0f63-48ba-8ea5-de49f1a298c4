package com.caidaocloud.hrpaas.service.interfaces.facade.schedule;

import com.caidaocloud.hrpaas.service.application.page.dto.TenantPageDetailDto;
import com.caidaocloud.hrpaas.service.application.page.dto.TenantPageDto;
import com.caidaocloud.hrpaas.service.application.page.service.TenantPageService;
import com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService;
import com.caidaocloud.hrpaas.service.application.schedule.dto.ScheduleTaskDto;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.interfaces.vo.page.TenantPageDetailVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.page.TenantPageVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/hrpaas/v1")
@Api(value = "/api/hrpaas/v1", description = "定时任务管理接口", tags = "定时任务管理接口")
public class ScheduleController {

    @Autowired
    private ScheduleTaskService scheduleTaskService;

    @ApiOperation("新增定时任务")
    @PostMapping("/schedule/add")
    public Result<Boolean> addSchedule(@RequestBody ScheduleTaskDto scheduleTaskDto){
        scheduleTaskService.addScheduleTask(scheduleTaskDto);
        return Result.ok();
    }

    @ApiOperation("删除定时任务")
    @DeleteMapping("/schedule/delete")
    public Result<Boolean> deletePage(@RequestParam("taskTopic") String taskTopic,
                                      @RequestParam("taskId") String taskId){
        scheduleTaskService.deleteScheduleTask(taskTopic, taskId);
        return Result.ok();
    }

}
