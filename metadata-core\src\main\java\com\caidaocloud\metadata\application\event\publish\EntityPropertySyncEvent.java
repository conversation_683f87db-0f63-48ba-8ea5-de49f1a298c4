package com.caidaocloud.metadata.application.event.publish;

import com.caidaocloud.hrpaas.metadata.sdk.event.AbstractInteriorEvent;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class EntityPropertySyncEvent extends AbstractInteriorEvent {
	private String identifier;
	private String tenantId;
	private EventType eventType;


	public final static String topic = "ENTITY_PROPERTY_SYNC";

	public EntityPropertySyncEvent() {
		super(topic);
	}

	public EntityPropertySyncEvent(String identifier, String tenantId) {
		super(topic);
		this.identifier = identifier;
		this.tenantId = tenantId;
		this.eventType = EventType.INIT;
	}


	@Override
	public void doPublish() {
		MqMessageProducer producer = SpringUtil.getBean(MqMessageProducer.class);
		RabbitBaseMessage message = new RabbitBaseMessage();
		message.setBody(FastjsonUtil.toJson(this));
		message.setExchange("caidao.hrpaas");
		message.setRoutingKey("caidao.hrpaas.model.property.sync");
		producer.publish(message);
	}

	public static enum EventType {
		INIT,REFRESH
	}
}
