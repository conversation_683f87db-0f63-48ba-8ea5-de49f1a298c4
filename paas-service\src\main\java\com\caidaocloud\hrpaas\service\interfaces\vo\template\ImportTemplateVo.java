package com.caidaocloud.hrpaas.service.interfaces.vo.template;

import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ImportTemplateVo extends BaseEntity<IBaseRepository> {
    private String tenantId;

    @ApiModelProperty("模版code")
    private String templateCode;

    @ApiModelProperty("模版名称")
    private String templateName;

    @ApiModelProperty("模版路径")
    private String path;

    @ApiModelProperty("菜单所属模块")
    private String model;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新时间")
    private Long updateTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("是否删除")
    private Integer deleted;
}
