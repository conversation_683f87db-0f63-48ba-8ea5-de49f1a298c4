package com.caidaocloud.hrpaas.service.application.form.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 审批任务DTO
 *
 * <AUTHOR>
 * @date 2022/3/8
 **/
@Data
@ApiModel(value = "任务审批")
public class WfTaskApproveDTO  {

    public WfTaskApproveDTO(){

    }

    public WfTaskApproveDTO(String taskId){
        this.taskId = taskId;
        this.choice = "APPROVE";
    }

    public WfTaskApproveDTO(String taskId, String choice){
        this.taskId = taskId;
        this.choice = choice;
    }

    public WfTaskApproveDTO(String taskId, String choice, String comment){
        this.taskId = taskId;
        this.choice = choice;
        this.comment = comment;
    }

    private String choice;

    private String taskId;

    private String comment;

}
