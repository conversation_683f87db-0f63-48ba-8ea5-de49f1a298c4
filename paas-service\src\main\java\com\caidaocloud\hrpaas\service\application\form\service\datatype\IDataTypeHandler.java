package com.caidaocloud.hrpaas.service.application.form.service.datatype;

import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.service.application.form.dto.WfRegistrySeqDto;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Map;

public interface IDataTypeHandler {
    Map<PropertyDataType, IDataTypeHandler> dataTypeHandlers = Maps.newHashMap();

    PropertyDataType getType();

    default WfRegistrySeqDto generateSeqDto(FormPropDef formDef, String formDefId) {
        if (formDef == null || StringUtils.isBlank(formDefId)) {
            throw ServerException.globalException(ErrorMessage.fromCode("form.workflow.generate.seq.parameter"));
        }
        return generateSeq(formDef, formDefId);
    }

    WfRegistrySeqDto generateSeq(FormPropDef formDef, String formDefId);

    @PostConstruct
    default void registry() {
        dataTypeHandlers.put(getType(), this);
    }

}
