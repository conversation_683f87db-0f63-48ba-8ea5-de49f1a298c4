package com.caidaocloud.hrpaas.service.test;

import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.hrpaas.service.application.uiform.dto.UiFormDataEventDto;
import com.caidaocloud.hrpaas.service.application.uiform.event.publish.UiFormDataPushEvent;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.WorkflowAction;
import com.caidaocloud.util.FastjsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class UiFormDataPushEventTest {
    @Resource
    private UiFormDataPushEvent uiFormDataPushEvent;

    @Test
    public void testUiFormDataPushEvent(){
        UiFormDataEventDto eventDto = UiFormDataEventDto.builder().
                formId("formId").
                formDataId("dataId").
                functionType("functionType").
                tenantId("tenantId")
                .userId(10000L)
                /**
                 * 默认发起新流程
                 */
                .action(WorkflowAction.apply)
                .empId(20000L).build();

        uiFormDataPushEvent.publish(FastjsonUtil.toJson(eventDto));
    }
}
