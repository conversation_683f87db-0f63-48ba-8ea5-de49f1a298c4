package com.caidaocloud.hrpaas.metadata.sdk.filter;

import java.util.List;

import com.caidaocloud.dto.FilterFunction;
import com.caidaocloud.hrpaas.metadata.sdk.enums.ConditionIdentifier;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/7/26
 */
@Data
public class SpecifiedSimpleDataFilter extends SimpleDataFilter implements FilterFunction {
	private ConditionIdentifier identifier;

	private SpecifiedSimpleDataFilter() {
		super();
	}

	public SpecifiedSimpleDataFilter(FilterOperator operator, String property, String value, ConditionIdentifier identifier) {
		super(operator, property, value);
		this.identifier = identifier;
	}

	public SpecifiedSimpleDataFilter(FilterOperator operator, String property, List<String> values, ConditionIdentifier identifier) {
		super(operator, property, values);
		this.identifier = identifier;
	}
}
