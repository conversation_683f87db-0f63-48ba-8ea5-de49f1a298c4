package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.DataSourceDo;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceType;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;

@Data
@TableName("paas_data_source")
public class DataSourcePo extends BasePo<DataSourcePo, DataSourceDo> {
    /**
     * 数据源名字
     */
    private Long tenantId;

    /**
     * 数据源名字
     */
    private String sourceName;

    /**
     * 数据源名字
     */
    private String description;

    /**
     * 数据源类型;0: 本地 key、value数组类型;1: 通过 api 获取远程接口数据的模式
     */
    private DataSourceType type;

    /**
     * 数据源配置
     */
    private String config;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * UT 更新时间
     */
    private Long updateTime;

    /**
     * UT 更新人
     */
    private Long updateBy;

    /**
     * 是否删除。0 默认值，未删除，1 已删除
     */
    private Integer deleted;
}