package com.caidaocloud.metadata.infrastructure.repository.utils;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.hrpaas.metadata.sdk.dto.ComponentPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import org.reflections.Reflections;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.util.Arrays;
import java.util.Map;

public class EntityAnalyzeTool {
    public static void main(String[] args) throws Exception {
        val reader = new BufferedReader(new FileReader("D:\\workspace\\caidao-hr-paas-service\\metadata-core\\src\\main\\java\\com\\caidaocloud\\metadata\\infrastructure\\repository\\utils\\a.txt"));
        String a;
        reader.readLine();
        Map<PropertyDataType, ComponentPropertyValue> components = Maps.map();
        new Reflections("com.caidaocloud.hrpaas.metadata.sdk.dto")
                .getSubTypesOf(ComponentPropertyValue.class).stream()
                .forEach(it->{
                    try {
                        components.put(it.getAnnotation(DataComponent.class).dataType(), it.newInstance());
                    } catch (Exception e) {
                    }
                }
        );
        while((a=reader.readLine()) != null){
            val strs = a.split("\\t");
            String identifier = strs[0];
            String name = strs[1];
            val sProperties = FastjsonUtil.toList(strs[2], PropertyDef.class);
            val cProperties = FastjsonUtil.toList(strs[3], PropertyDef.class);
            Map<String, String> mapping = FastjsonUtil.toObject(strs[4], Map.class);
            if(name.startsWith("{")){
                name = (String) FastjsonUtil.toObject(name, Map.class).get("default");
            }
            System.out.print(SnakeCaseConvertor.toSnake(identifier)+"_66     ");
            System.out.println(name);
            System.out.println("-----------------------------------------");
            Sequences.sequence(sProperties).join(cProperties).forEach(it->{
                String name0 = it.getName();
                if(name0.startsWith("{")){
                    name0 = (String) FastjsonUtil.toObject(name0, Map.class).get("default");
                }
                String prop = it.getProperty();
                if(it.getDataType().isComponent()){
                    val suffixes = components.get(it.getDataType()).propertySuffixToWhetherLong().keySet();
                    for (String suffix : suffixes) {
                        System.out.print(mapping.get(prop + suffix) + "     ");
                        System.out.println(name0 + suffix + "     ");
                    }
                }else{
                    System.out.print(mapping.get(prop) + "     ");
                    System.out.println(name0 + "     ");
                }

            });
            System.out.println("========================================");
        }
    }
}
