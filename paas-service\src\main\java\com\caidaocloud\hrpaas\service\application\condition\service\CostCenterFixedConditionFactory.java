package com.caidaocloud.hrpaas.service.application.condition.service;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.hrpaas.paas.match.ConditionComponentEnum;
import com.caidaocloud.hrpaas.paas.match.ConditionOperator;
import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionData;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/08/21
 */
@Component
public class CostCenterFixedConditionFactory extends WorkInfoFixedConditionFactory {
    @NacosValue("${caidaocloud.condition.fixed.work.costCenter:costCenters}")
    public String property;


    @Override
    protected String getProperty() {
        return property;
    }

    @Override
    public ConditionData fixedConditionData(String identifier, String identifierName, String property, String name) {
        ConditionData data = new ConditionData();
        data.setIdentifier(identifier);
        data.setIdentifierName(identifierName);
        data.setProperty(property);
        data.setOperators(Lists.list(ConditionOperator.EQ, ConditionOperator.NE, ConditionOperator.IN));
        data.setComponent(ConditionComponentEnum.COSTCENTER);
        data.setDataSourceParams(Maps.map());
        data.setName(name);
        data.setCode(property);
        return data;
    }
}
