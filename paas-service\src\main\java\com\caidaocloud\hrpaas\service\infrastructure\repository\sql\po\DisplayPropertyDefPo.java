package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.service.domain.display.entity.DisplayPropertyDef;
import com.caidaocloud.metadata.infrastructure.configuration.handler.BooleanTypeHandler;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;

@TableName("paas_display_property")
@Data
public class DisplayPropertyDefPo extends BasePo<DisplayPropertyDefPo, DisplayPropertyDef> {
    private String code;
    @DisplayAsArray
    private String properties;
    private String remark;
    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean deleted = false;
    private String tenantId;
    private long createTime;
    private String createBy;
    private long updateTime;
    private String updateBy;
}
