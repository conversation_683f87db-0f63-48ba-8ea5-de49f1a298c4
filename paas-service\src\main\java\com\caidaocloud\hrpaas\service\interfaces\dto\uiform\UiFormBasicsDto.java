package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageTemplate;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 表单引擎Dto
 * <AUTHOR>
 * @date 2021-07-02
 */
@Data
public class UiFormBasicsDto {
    @ApiModelProperty("表单ID")
    private String bid;

    @NotBlank
    @Size(max = 20, min = 1)
    @ApiModelProperty("表单名称")
    private String name;

    @NotBlank
    @ApiModelProperty("页面类型")
    private PageType type;

    @ApiModelProperty("关联表单ID")
    private String sourceId;

    @Size(max = 255, min = 1)
    @ApiModelProperty("描述")
    private String desc;

    @Size(max = 32, min = 1)
    @ApiModelProperty("菜单名称")
    private String menuName;

    @ApiModelProperty("导航显示：true显示，false不显示")
    private Boolean showNav;

    @ApiModelProperty("关联模型")
    private String joinModel;

    @ApiModelProperty("页面模板")
    private PageTemplate pageTemplate;

    @ApiModelProperty("页面schema")
    private String schema;
}
