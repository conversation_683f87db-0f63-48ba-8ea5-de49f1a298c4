package com.caidaocloud.hrpaas.service.application.uiform.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.CheckRuleDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormFieldDto;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单引擎Dto
 *
 * <AUTHOR>
 * @date 2021-07-02
 */
@Data
public class UiFormBasicDataDto extends DataSimple {
    /**
     * 表单名称
     */
    private String name;

    /**
     * 表单类型
     */
    private EnumSimple type;

    /**
     * 管理页面关联的表单ID
     */
    private String sourceId;

    /**
     * 表单组件列表配置
     */
    private String config;

    /**
     * 表单组件字段列表配置
     */
    private String fieldConfig;

    /**
     * 表单组件字段校验规则配置
     */
    private String checkRuleConfig;

    /**
     * 描述
     */
    private String desc;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 是否导航显示
     * true显示，false不显示
     */
    private Boolean showNav;

    /**
     * 关联模型
     */
    private String joinModel;

    /**
     * 页面模板
     */
    private EnumSimple pageTemplate;

    /**
     * 流程功能类型
     */
    private String functionType;

    /**
     * 页面schema
     */
    private String schema;

    /**
     * 是否内置模型
     */
    private Boolean builtIn;

    public UiFormBasicDataDto fillFeild(UiFormBasicDataDto oldData, String userId) {
        this.setUpdateBy(userId);
        this.setUpdateTime(System.currentTimeMillis());
        this.setCreateBy(oldData.getCreateBy());
        this.setCreateTime(oldData.getCreateTime());
        this.setShowNav(replaceFieldValue(this.getShowNav(), oldData.getShowNav()));
        this.setTenantId(replaceFieldValue(this.getTenantId(), oldData.getTenantId()));
        this.setName(replaceFieldValue(this.getName(), oldData.getName()));
        this.setType(replaceFieldValue(this.getType(), oldData.getType()));
        this.setBid(replaceFieldValue(this.getBid(), oldData.getBid()));
        this.setJoinModel(replaceFieldValue(this.getJoinModel(), oldData.getJoinModel()));
        this.setPageTemplate(replaceFieldValue(this.getPageTemplate(), oldData.getPageTemplate()));
        this.setFunctionType(replaceFieldValue(this.getFunctionType(), oldData.getFunctionType()));
        this.setMenuName(replaceFieldValue(this.getMenuName(), oldData.getMenuName()));
        this.setBuiltIn(replaceFieldValue(this.getBuiltIn(), oldData.getBuiltIn()));
        return this;
    }

    private <T> T replaceFieldValue(T original, T replace) {
        return StringUtils.isEmpty(original) ? replace : original;
    }

    @SuppressWarnings("unchecked")
    private void lookupSchemaFormNode(Map nodeData, List<Map> fieldList) {
        if (MapUtils.isEmpty(nodeData) || !nodeData.containsKey("childList")) {
            return;
        }

        List childList = (List) nodeData.get("childList");

        if (CollectionUtils.isEmpty(childList)) {
            return;
        }

        if ("Form".equals(nodeData.get("type"))) {
            fieldList.addAll(childList);
            return;
        }

        for (Object node : childList) {
            if (node instanceof Map) {
                lookupSchemaFormNode((Map) node, fieldList);
            }
        }
    }

    private void clearFieldConfigAndCheckRuleConfig() {
        this.setFieldConfig("");
        this.setCheckRuleConfig("");
    }

    /**
     * schema 字段信息解析
     */
    public void doParseSchema() {
        if (StringUtils.isEmpty(this.schema)) {
            this.clearFieldConfigAndCheckRuleConfig();
            return;
        }

        Map schemaMap = FastjsonUtil.toObject(this.schema, Map.class);

        // 查找type=form的节点
        List<Map> fieldList = new ArrayList<>();
        lookupSchemaFormNode(schemaMap, fieldList);

        List<PageElementItemFieldDto> elementItemFieldDtos = FastjsonUtil.toArrayList(FastjsonUtil.toJson(fieldList), PageElementItemFieldDto.class);

        // 剔除 field type=EmbeddedModel字段 并 根据prop去重
        elementItemFieldDtos = elementItemFieldDtos.stream().filter(o -> !"EmbeddedModel".equals(o.getType())).
                collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getProps().getProp()))), ArrayList::new));

        if (CollectionUtils.isEmpty(elementItemFieldDtos)) {
            this.clearFieldConfigAndCheckRuleConfig();
            return;
        }

        // db数据对象组装
        List<UiFormFieldDto> fieldConfig = new ArrayList<>();
        List<CheckRuleDto> checkRuleConfig = new ArrayList<>();

        elementItemFieldDtos.forEach(fieldDto -> {
            UiFormFieldDto uiFormFieldDto = new UiFormFieldDto();
            uiFormFieldDto.setType(fieldDto.getType());

            PageElementItemFieldPropDto propDto = fieldDto.getProps();
            uiFormFieldDto.setProp(propDto.getProp());
            uiFormFieldDto.setDataType(propDto.getDataType());
            uiFormFieldDto.setLabel(propDto.getLabel());

            Object dataSourceObj = propDto.getOptions();
            if (dataSourceObj != null && dataSourceObj instanceof Map) {
                String dataSourceId = (String) ((Map) dataSourceObj).get("dataSourceId");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(dataSourceId)) {
                    uiFormFieldDto.setDataSourceId(dataSourceId);
                }
            }
            fieldConfig.add(uiFormFieldDto);

            if (!CollectionUtils.isEmpty(propDto.getRules())) {
                List<CheckRuleDto> ruleDtoList = propDto.getRules().stream().map(ruleDto -> {
                    CheckRuleDto checkRuleDto = new CheckRuleDto();
                    checkRuleDto.setRuleType(ruleDto.getType());
                    checkRuleDto.setFieldRule(ruleDto.getValue());
                    checkRuleDto.setRuleTips(ruleDto.getMessage());
                    checkRuleDto.setFieldName(propDto.getLabel());
                    checkRuleDto.setFieldProp(propDto.getProp());
                    return checkRuleDto;
                }).collect(Collectors.toList());
                checkRuleConfig.addAll(ruleDtoList);
            }
        });
        this.setFieldConfig(FastjsonUtil.toJson(fieldConfig));
        this.setCheckRuleConfig(FastjsonUtil.toJson(checkRuleConfig));
    }
}
