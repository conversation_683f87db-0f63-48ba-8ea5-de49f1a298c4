package com.caidaocloud.metadata.interfaces.facade;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.hrpaas.metadata.sdk.dto.ComponentPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
 import com.caidaocloud.metadata.application.service.MetadataSyncService;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.infrastructure.repository.impl.EntityDefMapper;
import com.caidaocloud.metadata.infrastructure.repository.impl.EntityDefRepositoryImpl;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.RestUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.val;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.reflections.Reflections;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/${service.name.short:hrpaas}/v1/metadata")
public class SqlRepositoryToolController {

    @Autowired
    private EntityDefRepositoryImpl defRepository;

    @Autowired
    private ISessionService sessionService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private MetadataSyncService metadataSyncService;

    @NacosValue("${dbType:mysql}")
    private String dbType;

    private Map<String, String> escapeChar = Maps.map(
            "mysql", "`",
            "postgresql", "\""
    );

    private String colEscapeChar(){
        return escapeChar.get(dbType);
    }

    @PostMapping("test")
    public void aa(){
        val a = DataQuery.identifier("entity.hr.EmpWorkInfo")
                .one("1544238315943965", DataSimple.class);
        a.getProperties().add("empsubtype", DictSimple.doDictSimple("1488308438114631"));
        DataUpdate.identifier("entity.hr.EmpWorkInfo").update(a);
    }


    @DeleteMapping("/cache")
    public void removeCache(@RequestParam("identifier") String identifier){
        defRepository.clearDefPoCache(identifier, sessionService.getTenantId());
    }

    @PutMapping("/property/:informal")
    @ApiOperation("非正式接口，更新属性定义，只需要传入有变动的属性")
    @SneakyThrows
    public void alterProperty(@RequestParam("identifier") String identifier, @RequestBody List<PropertyDef> properties){
        String sqlTemplate = IOUtils
                .toString(new ClassPathResource("template/" + dbType + "/alter_entity_column.sql")
                        .getInputStream(), "UTF-8");
        String tenantIdFinal = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        properties.forEach(property->{
            val po = defRepository.getDefPo(identifier, tenantIdFinal);
            val def = po.toEntity(EntityDef.class);
            val oldProperty = def.fetchProperties().stream()
                    .filter(it->it.getProperty().equals(property.getProperty())).findFirst().get();
            Map<String, String> propertyToFieldMapping = FastjsonUtil.toObject(po.getPropertyToFieldMapping(), Map.class);
            val encryptedChanged = property.isEncrypted() && !oldProperty.isEncrypted();
            if(null == property.getRules()){
                property.setRules(Lists.list());
            }
            if(null == oldProperty.getRules()){
                oldProperty.setRules(Lists.list());
            }
            val maxLengthChanged = property.getRules().stream().filter(it->
                    "maxLength".equals(it.getType())
                            && StringUtils.isNotEmpty(it.getValue()) && Long.valueOf(it.getValue()) > 200).findFirst().isPresent()
                    && !oldProperty.getRules().stream().filter(it->
                    "maxLength".equals(it.getType())
                            && StringUtils.isNotEmpty(it.getValue()) && Long.valueOf(it.getValue()) > 200).findFirst().isPresent();

            if(encryptedChanged||maxLengthChanged){
                String fieldType = maxLengthChanged ? " text " : " varchar(600) ";
                val field = propertyToFieldMapping.get(property.getProperty());

                if(null == field){
                    propertyToFieldMapping.keySet().stream()
                            .filter(it->it.startsWith(property.getProperty()+".")).forEach(it->{
                        val subField = propertyToFieldMapping.get(it);
                        val sql = sqlTemplate.replaceAll("\\$table_name",SnakeCaseConvertor.toSnake(identifier) + "_" + tenantIdFinal)
                                .replaceAll("\\$field_name",subField)
                                .replaceAll("\\$field_type", fieldType);
                        jdbcTemplate.execute(sql);
                    });
                }else{
                    val sql = sqlTemplate.replaceAll("\\$table_name",SnakeCaseConvertor.toSnake(identifier) + "_" + tenantIdFinal)
                            .replaceAll("\\$field_name",field)
                            .replaceAll("\\$field_type", fieldType);
                    jdbcTemplate.execute(sql);
                }

            }
            BeanUtils.copyProperties(property, oldProperty);
            defRepository.update(def);
        });
        metadataSyncService.sync2Md(identifier);
    }

    @PostMapping("/property/:informal")
    @ApiOperation("非正式接口，新增属性定义，只需要传入有变动的属性")
    public void addProperty(@RequestParam("identifier") String identifier, @RequestBody List<PropertyDef> properties){
        String tenantIdFinal = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        val po = defRepository.getDefPo(identifier, tenantIdFinal);
        val def = po.toEntity(EntityDef.class);
        checkProperty(def, properties);
        Map<String, String> propertyToFieldMapping = FastjsonUtil.toObject(po.getPropertyToFieldMapping(), Map.class);
        List<PropertyDef> propertyDefList = propertiesMapFields(properties, propertyToFieldMapping);
        po.setPropertyToFieldMapping(FastjsonUtil.toJson(propertyToFieldMapping));
        def.getCustomProperties().addAll(properties);
        po.setCustomProperties(FastjsonUtil.toJson(def.getCustomProperties()));
        SpringUtil.getBean(EntityDefMapper.class).updateById(po);
        SpringUtil.getBean(EntityDefRepositoryImpl.class)
                .clearDefPoCache(identifier, tenantIdFinal);
        addTableColumn(propertyDefList, identifier, tenantIdFinal);
    }

    private void checkProperty(EntityDef def, List<PropertyDef> properties) {
        // 检查自定义属性是否已存在
        for (PropertyDef propertyDef : properties) {
            if (def.fetchAllProperties().stream()
                    .anyMatch(p -> p.getProperty().equals(propertyDef.getProperty()))) {
                throw new ServerException("属性已存在:" + propertyDef.getProperty());
            }
        }
    }



    @SneakyThrows
    private List<PropertyDef> propertiesMapFields(List<PropertyDef> properties, Map<String, String> propertyToFieldMapping){
        List<PropertyDef> list = new ArrayList<>();
        for(PropertyDef propertyDef : properties){
            if (propertyDef.isExpEnable()) {
                continue;
            }
            val property = propertyDef.getProperty();
            if(!propertyToFieldMapping.containsKey(property)){
                val dataType = propertyDef.getDataType();
                if(dataType.isComponent()){
                    if(!ComponentPropertyValue.componentHolder.containsKey(dataType)){
                        Class<? extends ComponentPropertyValue> componentClass =new Reflections("com.caidaocloud.hrpaas.metadata.sdk.dto").getSubTypesOf(ComponentPropertyValue.class).stream().filter(
                                it->dataType.equals(it.getAnnotation(DataComponent.class).dataType())
                        ).findFirst().get();
                        ComponentPropertyValue.componentHolder.put(dataType, componentClass.newInstance());
                    }
                    Map<String, Boolean> suffixToLongMap = ComponentPropertyValue.componentHolder.get(dataType).propertySuffixToWhetherLong();
                    for(Map.Entry<String, Boolean> suffixToLong : suffixToLongMap.entrySet()){
                        propertyToFieldMapping.put(property + suffixToLong.getKey(), SnakeCaseConvertor.toSnake(property + suffixToLong.getKey()));
                    }
                    list.add(propertyDef);
                }else{
                    if(!propertyToFieldMapping.containsKey(property)){
                        propertyToFieldMapping.put(property, SnakeCaseConvertor.toSnake(property));
                        list.add(propertyDef);
                    }
                }
            }
        }
        return list;
    }


    private void addTableColumn(List<PropertyDef> entityDef, String identifier, String tenantId){
        List<String> varcharFields = Lists.list();
        List<String> encryptVarcharFields = Lists.list();
        List<String> textFields = Lists.list();
        Sequences.sequence(entityDef)
                .forEach(property->{
                    if(property.getDataType().isComponent()){
                        ComponentPropertyValue.componentHolder.get(property.getDataType())
                                .propertySuffixToWhetherLong().forEach((suffix, isLong) ->{
                            if(property.isEncrypted()){
                                encryptVarcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty() + suffix));
                            }else if(isLong){
                                textFields.add(SnakeCaseConvertor.toSnake(property.getProperty() + suffix));
                            }else{
                                varcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty() + suffix));
                            }
                        });
                    }else{
                        boolean isLong = false;
                        if(property.isEncrypted()){
                            encryptVarcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty()));
                            if(null != property.getRules()){
                                property.getRules().stream().filter(it->
                                        "maxLength".equals(it.getType())
                                                && StringUtils.isNotEmpty(it.getValue()) && Long.valueOf(it.getValue()) > 30)
                                        .findFirst().ifPresent(it->{
                                    throw new ServerException("加密字段长度不能超过30");
                                });

                            }
                        }else{
                            if(null != property.getRules()){
                                isLong = property.getRules().stream().filter(it->
                                        "maxLength".equals(it.getType())
                                                && StringUtils.isNotEmpty(it.getValue()) && Long.valueOf(it.getValue()) > 200).findFirst().isPresent();

                            }
                            if(isLong){
                                textFields.add(SnakeCaseConvertor.toSnake(property.getProperty()));
                            }else{
                                varcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty()));
                            }
                        }
                    }
                });
        varcharFields.forEach(it->{
            jdbcTemplate.execute("alter table " +
                    SnakeCaseConvertor.toSnake(identifier) + "_" + tenantId +
                    " add column " + colEscapeChar() + it + colEscapeChar() + " varchar(200) default null;");
        });
        textFields.forEach(it->{
            jdbcTemplate.execute("alter table " +
                    SnakeCaseConvertor.toSnake(identifier) + "_" + tenantId +
                    " add column " + colEscapeChar() + it + colEscapeChar() + " varchar(600) default null;");
        });
        encryptVarcharFields.forEach(it->{
            jdbcTemplate.execute("alter table " +
                    SnakeCaseConvertor.toSnake(identifier) + "_" + tenantId +
                    " add column " + colEscapeChar() + it + colEscapeChar() + " text default null;");
        });
    }


    @DeleteMapping("/property/custom/:clear")
    @SneakyThrows
    @ApiOperation("清空自定义属性")
    public void clearCustom(@RequestParam("identifier") String identifier){
        String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        val po = defRepository.getDefPo(identifier, tenantId);
        po.setCustomProperties("[]");
        val mapping = FastjsonUtil.toObject(po.getPropertyToFieldMapping(), Map.class);
        val properties = FastjsonUtil.toList(po.getStandardProperties(), PropertyDef.class);
        val newMapping = Maps.map();
        for(PropertyDef propertyDef: properties){
            val property = propertyDef.getProperty();
            val dataType = propertyDef.getDataType();
            if(dataType.isComponent()){
                if(!ComponentPropertyValue.componentHolder.containsKey(dataType)){
                    Class<? extends ComponentPropertyValue> componentClass =new Reflections("com.caidaocloud.hrpaas.metadata.sdk.dto").getSubTypesOf(ComponentPropertyValue.class).stream().filter(
                            it->dataType.equals(it.getAnnotation(DataComponent.class).dataType())
                    ).findFirst().get();
                    ComponentPropertyValue.componentHolder.put(dataType, componentClass.newInstance());
                }
                Map<String, Boolean> suffixToLongMap = ComponentPropertyValue.componentHolder.get(dataType).propertySuffixToWhetherLong();
                for(Map.Entry<String, Boolean> suffixToLong : suffixToLongMap.entrySet()){
                    newMapping.put(property + suffixToLong.getKey(), mapping.get(property + suffixToLong.getKey()));
                }
            }else{
                if(!newMapping.containsKey(property)){
                    newMapping.put(property, mapping.get(property));
                }
            }
        }
        po.setPropertyToFieldMapping(FastjsonUtil.toJson(newMapping));
        SpringUtil.getBean(EntityDefMapper.class).updateById(po);
        SpringUtil.getBean(EntityDefRepositoryImpl.class)
                .clearDefPoCache(identifier, tenantId);

    }

    @PutMapping("def/refreshFieldMapping")
    @ApiOperation("刷新属性mapping")
    public Result refreshFieldMapping(@RequestParam("identifier") String identifier) {
        SpringUtil.getBean(EntityDefRepositoryImpl.class).removeDictTextMapping(identifier);
        return Result.ok(true);
    }

    @GetMapping("/def/mapping")
    @ApiOperation("获取属性mapping")
    public Result<Map<String, String>> getIdentifierFieldMapping(@RequestParam("identifier") String identifier,
                                            @RequestParam("tenantId") String tenantId){//1006   token11
        SecurityUserInfo user = new SecurityUserInfo();
        user.setTenantId(tenantId);
        SecurityUserUtil.setSecurityUserInfo(user);
        val mapping = SpringUtil.getBean(EntityDefRepositoryImpl.class).getDefPo(identifier, tenantId).getPropertyToFieldMapping();
        return Result.ok(FastjsonUtil.toObject(mapping, Map.class));
    }
}
