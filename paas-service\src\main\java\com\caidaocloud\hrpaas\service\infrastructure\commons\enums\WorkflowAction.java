package com.caidaocloud.hrpaas.service.infrastructure.commons.enums;

/**
 * 工作流，发起流程相关状态
 */
public enum WorkflowAction {
    /**
     * 发起的新流程
     */
    apply("发起流程", 1),
    /**
     * 工作流流程启动失败
     */
    repulse("流程启动失败", 2),
    /**
     * 审批拒绝
     */
    refuse("拒绝", 3),
    /**
     * 审批驳回
     */
    reject("驳回", 4),
    /**
     * 发起人撤销
     */
    revoke("撤销", 5);

    private String name;
    private int value;

    WorkflowAction(String name, int value){
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public int getValue() {
        return value;
    }
}
