package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataValueFunction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UiFormDataValueDto<T> extends UiFormDataValueBaseDto<T> {
    /**
     * 字符串，字段ID，必填，最大长度16位
     */
    @ApiModelProperty("字段ID")
    private String prop;

    @ApiModelProperty("字段类型")
    private String propType;

    @ApiModelProperty("字段回显值")
    private String propText;

    /**
     * 当前字段 ID 对应的字段值
     */
    @ApiModelProperty("字段值")
    private T value;

    @Override
    public DataValueFunction dataValueBulid() {
        return new UiFormDataValueDto();
    }

    @Override
    public void fitDataValue(T dataValue) {
        this.setValue(dataValue);
    }

    @Override
    public void fitDataProp(String dataProp) {
        this.setProp(dataProp);
    }

    @Override
    public String loadDataProp() {
        return this.getProp();
    }

    @Override
    public T loadDataValue() {
        return this.getValue();
    }
}
