package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 元数据模型定义
 * <AUTHOR>
 * @date 2021-06-17
 */
@Data
public class MetadataBasicInfoDto {

    @ApiModelProperty("业务模型全限定名")
    private String identifier;
    @ApiModelProperty("业务模型名称")
    private String name;

    private Map<String, String> i18nName;


    /**
     * 所属微服务，模型中的数据即存储在名为{owner}_entity_{tenantId}的collection中
     */
    @ApiModelProperty("所属微服务")
    private String owner;

    /**
     * 父模型，父模型中的所有属性都会被本模型继承。父模型的owner必须与本模型相同（entity.common.BaseModel例外）。
     * 所有模型实际上最终都会继承entity.common.BaseModel模型
     */
    @ApiModelProperty("父模型")
    private String parent = "entity.common.BaseModel";

    @ApiModelProperty("标题属性")
    private String label;

    @ApiModelProperty("是否开启时间轴")
    private boolean timelineEnabled = true;

    @ApiModelProperty("是否树结构")
    private boolean tree = false;

    @ApiModelProperty("业务主键对应的属性")
    private List<String> mainKey = Lists.newArrayList();

    public String getI18Name() {
        return Optional.ofNullable(name).orElseGet(() -> i18nName != null ? i18nName.getOrDefault("default", "") : "");
    }

}
