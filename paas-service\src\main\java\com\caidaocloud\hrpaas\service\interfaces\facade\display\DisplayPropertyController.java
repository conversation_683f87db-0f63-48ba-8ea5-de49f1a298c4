package com.caidaocloud.hrpaas.service.interfaces.facade.display;

import com.caidaocloud.hrpaas.service.application.display.dto.DisplayPropertyDto;
import com.caidaocloud.hrpaas.service.application.display.service.DisplayPropertyService;
import com.caidaocloud.hrpaas.paas.match.vo.display.DisplayPropertyVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/hrpaas/display/v1")
@Api(value = "/api/hrpaas/display/v1",  tags = "显示字段配置接口")
@Slf4j
public class DisplayPropertyController {
    @Resource
    private DisplayPropertyService displayPropertyService;

    @GetMapping("/property")
    public DisplayPropertyVo getProperty() {
        return ObjectConverter.convert(displayPropertyService.loadByCode("emp"), DisplayPropertyVo.class);
    }

    @PostMapping("/save")
    public Result<Boolean> save(@RequestBody DisplayPropertyDto dto) {
        displayPropertyService.saveByCode(dto);
        return Result.ok();
    }
}
