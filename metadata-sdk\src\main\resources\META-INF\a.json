{"data": {"pageNo": 1, "pageSize": 1000, "total": 65, "items": [{"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 5, "payslip_name": "效益工资", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016005", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 89, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 6, "payslip_name": "岗位工资", "paramTax": 1, "pre_show": false, "paramR": 1, "valid": true, "pay_item_code": "PY016006", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 90, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "固定项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 7, "payslip_name": "技能工资", "paramTax": 1, "pre_show": false, "paramR": 1, "valid": true, "pay_item_code": "PY016007", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 91, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "固定项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 10, "payslip_name": "年功工资", "paramTax": 1, "pre_show": false, "paramR": 1, "valid": true, "pay_item_code": "PY016010", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 94, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "固定项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 11, "payslip_name": "津贴", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016011", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 95, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 12, "payslip_name": "津贴天数", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016012", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 96, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 15, "payslip_name": "生产补助", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016015", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 99, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "text", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 18, "payslip_name": "病假工资", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016018", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 102, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 19, "payslip_name": "应发合计", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016019", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 103, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 21, "payslip_name": "事假扣款", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016021", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 105, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 22, "payslip_name": "请假合计", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016022", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 106, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 23, "payslip_name": "试用期扣款", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016023", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 107, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 24, "payslip_name": "离职扣款基数", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016024", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 108, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 26, "payslip_name": "离职扣款", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016026", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 110, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 28, "payslip_name": "养老保险-个人", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016028", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 112, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 32, "payslip_name": "大额医疗公司", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016032", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 116, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 34, "payslip_name": "代扣税", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016034", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 118, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 35, "payslip_name": "实发合计", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016035", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 119, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 36, "payslip_name": "计件工资", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016036", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 140, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 38, "payslip_name": "岗位津贴", "paramTax": 1, "pre_show": false, "paramR": 1, "valid": true, "pay_item_code": "PY016038", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 142, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "固定项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 39, "payslip_name": "绩效工资", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016039", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 143, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 40, "payslip_name": "生产补助", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016040", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 144, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 41, "payslip_name": "其他补发工资", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016041", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 145, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 42, "payslip_name": "地区最低基本标准", "paramTax": 1, "pre_show": false, "paramR": 1, "valid": true, "pay_item_code": "PY016042", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 146, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "固定项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 43, "payslip_name": "年功工资 （转正月份）", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016043", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 147, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 44, "payslip_name": "节日福利费", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016044", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 148, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 45, "payslip_name": "清凉费", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016045", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 149, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 46, "payslip_name": "生产补助", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016046", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 150, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 48, "payslip_name": "急离职", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016048", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 152, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "text", "paramMi": 1}, {"param_op": "", "param_tax": "社保项目", "payslip_report": true, "display_order": 52, "payslip_name": "医疗保险-个人", "paramTax": 3, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016052", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 156, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 53, "payslip_name": "在职天数", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016053", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 158, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 54, "payslip_name": "学历职称工资", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016054", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 159, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 55, "payslip_name": "大额医疗调整项", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016055", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 160, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 58, "payslip_name": "年功工资（2）", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016058", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 167, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 59, "payslip_name": "年功工资 ", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016059", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 168, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 60, "payslip_name": "医疗保险公司", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016060", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 175, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 61, "payslip_name": "失业保险-个人", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016061", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 176, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 63, "payslip_name": "长期照护保险-个人", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016063", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 178, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 64, "payslip_name": "大额医疗-个人", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016064", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 179, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 65, "payslip_name": "住房公积金-个人", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016065", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 180, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 66, "payslip_name": "住房公积金-公司", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016066", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 181, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 67, "payslip_name": "其他扣款", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016067", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 182, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 68, "payslip_name": "长期照护保险-公司", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016068", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 183, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 69, "payslip_name": "累计减免费用", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016069", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 184, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 70, "payslip_name": "累计3岁婴幼儿照护附加扣除", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016070", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 185, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 71, "payslip_name": "3岁以下婴幼儿照护附加扣除", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016071", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 186, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 72, "payslip_name": "累计应发收入", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016072", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 187, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 73, "payslip_name": "累计赡养老人附加扣除", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016073", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 188, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 74, "payslip_name": "赡养老人附加扣除", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016074", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 189, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 75, "payslip_name": "累计预缴税额", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016075", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 190, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 76, "payslip_name": "继续教育附加扣除", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016076", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 191, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 77, "payslip_name": "累计继续教育附加扣除", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016077", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 192, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 78, "payslip_name": "住房贷款利息附加扣除", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016078", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 193, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 79, "payslip_name": "累计住房贷款利息附加扣除", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016079", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 194, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 80, "payslip_name": "住房租金附加扣除", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016080", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 195, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 81, "payslip_name": "累计住房租金附加扣除", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016081", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 196, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 82, "payslip_name": "子女教育附加扣除", "paramTax": 1, "pre_show": false, "paramR": 2, "valid": true, "pay_item_code": "PY016082", "checked": true, "param_mi": "系统规则计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 197, "is_xml": false, "allow_update": false, "struc_main_id": 16, "param_r": "月度项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 1}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 83, "payslip_name": "累计子女教育附加扣除", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016083", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 198, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 84, "payslip_name": "个人社保公积金合计", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016084", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 199, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 85, "payslip_name": "个人社保合计", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016085", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 200, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 86, "payslip_name": "养老保险-公司", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016086", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 201, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 87, "payslip_name": "当期累计税率", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016087", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 202, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 88, "payslip_name": "累计应纳税额", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016088", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 203, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 89, "payslip_name": "累计个人社保公积金合计", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016089", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 204, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}, {"param_op": "", "param_tax": "工资项目", "payslip_report": true, "display_order": 90, "payslip_name": "累计附加扣除", "paramTax": 1, "pre_show": false, "paramR": 3, "valid": true, "pay_item_code": "PY016090", "checked": true, "param_mi": "自定义公式计算", "zero_show": true, "sum_show": false, "detail_report": false, "level": 1, "detail_id": 205, "is_xml": true, "allow_update": false, "struc_main_id": 16, "param_r": "计算项目", "cc_report": false, "tax_report": false, "data_type": "num", "paramMi": 2}]}, "code": 0, "msg": "OK", "serverTime": 1700726305604, "success": true}