package com.caidaocloud.hrpaas.service.application.feildMapping.feign;

import com.caidaocloud.hrpaas.service.application.feildMapping.dto.ChangeDefDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "${feign.rename.caidaocloud-hr-service:caidaocloud-hr-service}",
        fallback = HrFeignClientFallBack.class,
        configuration = FeignConfiguration.class)
public interface HrFeignClient {
    /**
     *
     * @param mappingConfigId
     * @return
     */
    @GetMapping("/api/hr/transfer/config/v1/changeDef/getByMappingConfigId")
    Result<List<ChangeDefDto>> getByMappingConfigId(@RequestParam(value = "mappingConfigId") String mappingConfigId);

}
