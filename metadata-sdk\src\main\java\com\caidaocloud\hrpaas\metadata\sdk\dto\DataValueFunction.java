package com.caidaocloud.hrpaas.metadata.sdk.dto;

public interface DataValueFunction<T> {
    /**
     * 设置 value 值
     * @param value
     */
    void fitDataValue(T value);

    T loadDataValue();

    void fitDataProp(String dataProp);

    String loadDataProp();

    /**
     * 构建一个新的 DataValue 对象
     * @return
     */
    DataValueFunction dataValueBulid();

    /**
     * 获取 DataValue 模型的 Identifier
     * @return
     */
    default String loadSourceId(){
        return "";
    }

    default void fitSourceId(String sourceId){}

    /**
     * 获取 DataValue 模型的 Identifier
     * @return
     */
    default String loadTargetId(){
        return "";
    }

    default void fitTargetId(String targetId){}
}
