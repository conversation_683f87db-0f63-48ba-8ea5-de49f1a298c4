package com.caidaocloud.hrpaas.service.domain.metadata.entity;

import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.IMetadataSchemaRepository;
import com.googlecode.totallylazy.Option;
import lombok.Data;

import java.util.List;

@Data
public class MetadataSchemaDo extends BaseEntity<IMetadataSchemaRepository> {
    private String schemaModel;

    /**
     * 脚本文件夹名
     */
    private Integer folder;

    /**
     * 版本
     */
    private Long version;

    /**
     * 脚本文件名称
     */
    private String schemaName;

    /**
     * 脚本内容
     */
    private String schemaInfo;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * UT 更新时间
     */
    private Long updateTime;

    /**
     * UT 更新人
     */
    private Long updateBy;

    public void saveOrUpdate(){
        this.insert();
    }

    public static void batchSave(List<MetadataSchemaDo> schemaDoList){
        ((IMetadataSchemaRepository) repository(MetadataSchemaDo.class)).batchSave(schemaDoList);
    }

    public static Option<List<MetadataSchemaDo>> findAllSchema(){
       return  ((IMetadataSchemaRepository) repository(MetadataSchemaDo.class)).findAllSchema();
    }

    public static List<MetadataSchemaDo> findSchemaByIds(String [] scriptIds){
        return  ((IMetadataSchemaRepository) repository(MetadataSchemaDo.class)).findSchemaByIds(scriptIds);
    }

    public static List<MetadataSchemaDo> findSchemaByNameOrVersion(String name, Long version){
        return  ((IMetadataSchemaRepository) repository(MetadataSchemaDo.class)).findSchemaByNameOrVersion(name, version);
    }

    public static List<MetadataSchemaDo> findSchemaGtVersion(Long version){
        return  ((IMetadataSchemaRepository) repository(MetadataSchemaDo.class)).findSchemaGtVersion(version);
    }

}
