package com.caidaocloud.hrpaas.metadata.sdk.filter;

import com.caidaocloud.dto.FilterFunction;
import com.googlecode.totallylazy.Lists;
import lombok.Data;

import java.util.List;

@Data
public class MultiDataFilter extends DataFilter implements FilterFunction {

    private final String type = "multi";

    private FilterOperator operator;

    private List<DataFilter> filters;

    protected MultiDataFilter() {

    }

    public MultiDataFilter(FilterOperator operator, DataFilter... filters) {
        this.operator = operator;
        this.filters = Lists.list(filters);
    }

}
