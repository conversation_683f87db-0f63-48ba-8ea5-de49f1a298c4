package com.caidaocloud.hrpaas.service.infrastructure.commons.enums;

public enum DataSourceType {
    CUSTOM("自定义数据源", 1),
    SYSTEM_DICT("系统字典数据源", 2),
    SYSTEM_FORM_ENGINE("其他表单数据源", 3),
    SYSTEM_STANDARD_COMPONENT("系统标准组件数据源", 4),
    SYSTEM_LIST("系统功能列表数据源", 5),
    METADATA("元数据数据源", 6),
    EXTERNAL_SYSTEM("外部系统数据源", 7);

    private String name;
    private int value;

    DataSourceType(String name, int value){
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public int getValue() {
        return value;
    }

}
