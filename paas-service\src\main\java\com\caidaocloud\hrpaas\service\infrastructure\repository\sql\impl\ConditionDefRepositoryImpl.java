package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.service.domain.condition.entity.ConditionDef;
import com.caidaocloud.hrpaas.service.domain.condition.repository.IConditionDefRepository;
import org.springframework.stereotype.Repository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.ConditionDefPo;
import com.caidaocloud.hrpaas.service.interfaces.dto.condition.ConditionQueryDto;
import com.caidaocloud.metadata.infrastructure.repository.impl.util.TenantMapperUtil;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;

import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@Repository
public class ConditionDefRepositoryImpl implements IConditionDefRepository {

	@Autowired
	private ConditionDefMapper mapper;

	@Override
	public String insert(ConditionDef entity) {
		throw new UnsupportedOperationException("not supported");
	}

	@Override
	public void update(ConditionDef entity) {
		if (entity.getProperties() == null || entity.getProperties().isEmpty()) {
			// todo
		}
		ConditionDefPo po = ConditionDefPo.fromEntity(ConditionDefPo.class, entity);
		mapper.updateById(po);
	}

	@Override
	public PageResult<ConditionDef> page(ConditionQueryDto query) {
		QueryWrapper<ConditionDefPo> queryWrapper = new QueryWrapper<ConditionDefPo>();
		queryWrapper.eq("deleted", 0);
		Page<ConditionDefPo> page = TenantMapperUtil.selectTenantPage(mapper, new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper);
		List<ConditionDef> list = Sequences.sequence(page.getRecords())
				.map(po -> po.toEntity(ConditionDef.class)).toList();
		return new PageResult<ConditionDef>(list, query.getPageNo(), query.getPageSize(), (int) page.getTotal());
	}

	@Override
	public Option<ConditionDef> loadByCode(String code) {
		QueryWrapper<ConditionDefPo> queryWrapper = new QueryWrapper<ConditionDefPo>();
		queryWrapper.eq("code", code);
		queryWrapper.eq("deleted", 0);
		return Option.option(mapper.selectOne(queryWrapper)).map(po -> po.toEntity(ConditionDef.class));
	}

	@Override
	public Option<ConditionDef> load(String id) {
		return Option.option(mapper.selectById(Long.valueOf(id)).toEntity(ConditionDef.class));
	}
}
