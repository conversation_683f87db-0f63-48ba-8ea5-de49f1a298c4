package com.caidaocloud.hrpaas.metadata.sdk.dto;

import java.io.Serializable;
import java.util.List;

public interface PropertyValue extends Serializable {
    /**
     * 组件数据转换
     * @param dataValue
     * @param dataVal
     * @param dataVals
     */
    void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals);

    String toText();
}
