package com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration;

import com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.ribbon.PaasTxRibbonClientConfiguration;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.netflix.ribbon.RibbonAutoConfiguration;
import org.springframework.cloud.netflix.ribbon.RibbonClient;
import org.springframework.cloud.netflix.ribbon.RibbonClients;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 *
 * <AUTHOR>
 * @date 2022/10/10
 */
@Configuration
@EnableAspectJAutoProxy
@EnableFeignClients
@AutoConfigureAfter(RibbonAutoConfiguration.class)
@RibbonClients(value = {@RibbonClient(value = "caidaocloud-hr-paas-service", configuration = PaasTxRibbonClientConfiguration.class)})
public class TransactionConfiguration {

	@Bean
	@ConditionalOnMissingBean(TransactionAspect.class)
	public TransactionAspect transactionalAspect(){
		return new TransactionAspect();
	}
}
