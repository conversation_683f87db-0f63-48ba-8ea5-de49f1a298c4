package com.caidaocloud.hrpaas.service.address;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.metadata.application.service.MetadataAddressService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class WorkplaceTest {
    @Resource
    private MetadataAddressService metadataAddressService;

    @Test
    public void testSave(){
        WorkplaceDo dataDo = new WorkplaceDo();
        dataDo.setNationality(DictSimple.doDictSimple("1488308438114292"));
        dataDo.setCode("modu");
        dataDo.setName("魔都");
        dataDo.setAddress("魔都");
        Address address = new Address();
        address.setProvince(110000L);
        address.setCity(null);
        address.setArea(110101L);
        dataDo.setProvince(address);
        DataInsert.identifier("entity.hr.workplace").insert(dataDo);
    }

    @Test
    public void testLoadCache(){
        metadataAddressService.loadCache();
    }

    @Test
    public void testQuery(){
        /*WorkplaceDo one = DataQuery.identifier("entity.hr.workplace").one("1571818977187841", WorkplaceDo.class);
        System.out.println(FastjsonUtil.toJson(one));*/
        System.out.println();
        System.out.println();
        System.out.println();
        WorkplaceDo newone = DataQuery.identifier("entity.hr.Workplace").one("1572379072788481", WorkplaceDo.class);
        System.out.println(FastjsonUtil.toJson(newone));
    }



    @Before
    public void bf() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setUserId(0L);
        userInfo.setTenantId("8");
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }
}
