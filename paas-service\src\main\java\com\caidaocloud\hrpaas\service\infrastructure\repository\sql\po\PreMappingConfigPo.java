package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.application.form.dto.FormPropertyMapping;
import com.caidaocloud.hrpaas.service.domain.feildMapping.entity.ConverterType;
import com.caidaocloud.hrpaas.service.domain.feildMapping.entity.PreMappingConfig;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;

@Data
@TableName("pre_mapping_config")
public class PreMappingConfigPo {
    @TableId
    private String id;

    private long createTime;

    private String createBy;

    private long updateTime;

    private String updateBy;
    /**
     * 映射类型
     */
    private ConverterType type;
    /**
     * 映射源identifier
     */
    private String source;
    /**
     * 映射源名称
     */
    private String sourceName;
    /**
     * 映射目标identifier
     */
    private String target;
    /**
     * 映射目标名称
     */
    private String targetName;
    /**
     * 属性映射
     */
    private String mapping;
    /**
     * 名称
     */
    private String name;
    /**
     * 模型字段映射类别
     */
    private String category;

    public static PreMappingConfigPo fromEntity(PreMappingConfig entity) {
        PreMappingConfigPo po = ObjectConverter.convert(entity, PreMappingConfigPo.class);
        po.setMapping(FastjsonUtil.toJson(entity.getMapping()));
        return po;
    }

    public PreMappingConfig toEntity(){
        PreMappingConfig entity = ObjectConverter.convert(this, PreMappingConfig.class);
        entity.setMapping(FastjsonUtil.toList(mapping, FormPropertyMapping.class));
        return entity;
    }
}
