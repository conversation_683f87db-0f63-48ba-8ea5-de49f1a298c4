package com.caidaocloud.metadata.domain.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.EntityRelationType;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.caidaocloud.metadata.domain.repository.IEntityRelationDefRepository;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;

import java.util.Collection;

@Data
public class EntityRelationDef extends BaseEntity<IEntityRelationDefRepository> {

    private String identifier;

    private boolean builtIn = false;

    private String relatedIdentifier;

    private String property;

    private String propertyName;

    private EntityRelationType relationType;

    private boolean multiple;


    protected boolean conflict(Sequence<EntityRelationDef> relations){
        return relations.find(relation ->
                this.property.equals(relation.property) ||
                        this.propertyName.equals(relation.getPropertyName())
        ).isDefined();
    }

    protected boolean conflictProperty(Collection<PropertyDef>... propSequences){
        Sequence<PropertyDef> props = Sequences.join(propSequences);
        return props.find(prop ->
                this.property.equals(prop.getProperty()) ||
                        this.propertyName.equals(prop.getName())
        ).isDefined();
    }

    protected static Sequence<EntityRelationDef> loadByIdentifier(String identifier){
        return ((IEntityRelationDefRepository)repository(EntityRelationDef.class)).loadByIdentifier(identifier);
    }

    protected void addToEntity() {
        repository().insert(this);
    }

    protected void initInsert(DefChannel defChannel){
        if(defChannel.equals(DefChannel.SCRIPT)){
            this.setBuiltIn(true);
        }else{
            this.setBuiltIn(false);
        }
    }

    protected void checkDelete(DefChannel defChannel){
        if(builtIn && !DefChannel.SCRIPT.equals(defChannel)){
            throw new ServerException("标准关联请通过script维护");
        }
        if(!builtIn && DefChannel.SCRIPT.equals(defChannel)){
            throw new ServerException("自定义关联不允许通过script维护");
        }
    }

    protected void delete() {
        repository().delete(identifier, property);
    }


    public static long checkEntityDefRelatedCount(String identifier) {
        return ((IEntityRelationDefRepository)repository(EntityRelationDef.class)).checkEntityDefRelatedCount(identifier);
    }

    public boolean isEmbedded(){
        return EntityRelationType.EMBEDDED.equals(this.getRelationType());
    }
}
