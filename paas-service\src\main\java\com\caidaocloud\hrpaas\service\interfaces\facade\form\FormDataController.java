package com.caidaocloud.hrpaas.service.interfaces.facade.form;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.hrpaas.service.application.form.dto.FormDataApprovalDto;
import com.caidaocloud.hrpaas.service.application.form.dto.FormDataDeleteDto;
import com.caidaocloud.hrpaas.service.application.form.dto.FormDataDto;
import com.caidaocloud.hrpaas.service.application.form.service.FormDataService;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDataStatus;
import com.caidaocloud.hrpaas.service.interfaces.dto.form.FormDefIdDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.form.FormPageQueryDto;
import com.caidaocloud.hrpaas.service.interfaces.vo.form.FormDataMapVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.form.FormDataVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackRequestDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/hrpaas/v1/form/data")
@Api(value = "/api/hrpaas/v1/form/data", description = "表单接口", tags = "表单接口")
public class FormDataController {
    @Autowired
    private FormDataService formDataService;

    @ApiOperation("新增表单")
    @PostMapping("/create/{formId}")
    public Result<String> create(@PathVariable String formId, @RequestBody FormDataDto formDataDto){
        return Result.ok(formDataService.create(formId, formDataDto, false));
    }

    @ApiOperation("新增表单")
    @PostMapping("/createSelf/{formId}")
    public Result<String> createSelf(@PathVariable String formId, @RequestBody FormDataDto formDataDto){
        return Result.ok(formDataService.create(formId, formDataDto, true));
    }

    @ApiOperation("新增员工子集表单")
    @PostMapping("/create/sub/{formId}")
    public Result<String> createSub(@PathVariable String formId, @RequestBody FormDataDto formDataDto){
        return Result.ok(formDataService.create(formId, formDataDto, false,formDataDto.getEmpId(),true));
    }



    @ApiOperation("表单工作流回调")
    @PostMapping("/workflow/callback")
    public Result<Boolean> workflowCallback(@RequestBody WfCallbackRequestDto callback){
        formDataService.workflowCallback(callback.getBusinessKey(), callback.getTenantId());
        return Result.ok();
    }

    @ApiOperation("表单工作流回调(驳回)")
    @PostMapping("/workflow/callback/reject")
    public Result<Boolean> workflowRejectCallback(@RequestBody WfCallbackRequestDto callback){
        formDataService.workflowRejectCallback(callback.getBusinessKey(), callback.getTenantId());
        return Result.ok();
    }

    @ApiOperation("更新表单")
    @PutMapping("/update/{formId}")
    public Result<Boolean> update(@PathVariable String formId, @RequestBody FormDataDto formDataDto){
        formDataService.update(formId, formDataDto, false);
        return Result.ok();
    }

    @ApiOperation("更新表单")
    @PutMapping("/updateSelf/{formId}")
    public Result<Boolean> updateSelf(@PathVariable String formId, @RequestBody FormDataDto formDataDto){
        formDataService.update(formId, formDataDto, true);
        return Result.ok();
    }


    @ApiOperation("更新员工子集表单")
    @PutMapping("/update/sub/{formId}")
    public Result<Boolean> updateSub(@PathVariable String formId, @RequestBody FormDataDto formDataDto){
        formDataService.saveOrUpdateSub(formId, formDataDto,formDataDto.getEmpId());
        return Result.ok();
    }

    @ApiOperation("审批表单")
    @PutMapping("/approve/{formId}")
    public Result<Boolean> approve(@PathVariable String formId, @RequestBody FormDataApprovalDto formDataApprovalDto){
        formDataService.approve(formId, formDataApprovalDto);
        return Result.ok();
    }

    @ApiOperation("删除表单")
    @PostMapping("/delete/{formId}")
    public Result<Boolean> delete(@PathVariable String formId, @RequestBody FormDataDeleteDto delete){
        formDataService.delete(formId, delete.getId(), false);
        return Result.ok();
    }

    @ApiOperation("删除表单")
    @PostMapping("/deleteSelf/{formId}")
    public Result<Boolean> deleteSelf(@PathVariable String formId, @RequestBody FormDataDeleteDto delete){
        formDataService.delete(formId, delete.getId(), true);
        return Result.ok();
    }

    @ApiOperation("删除表单")
    @PostMapping("/delete/sub/{formId}")
    public Result<Boolean> deleteSub(@PathVariable String formId, @RequestBody FormDataDeleteDto delete){
        formDataService.delete(formId, delete.getId(), false, delete.getEmpId(), true);
        return Result.ok();
    }

    @ApiOperation("撤回表单")
    @PutMapping("/revoke/{formId}")
    public Result<Boolean> revoke(@PathVariable String formId, @RequestBody FormDataDto data){
        formDataService.revoke(formId, data.getId(), false);
        return Result.ok();
    }

    @ApiOperation("撤回表单")
    @PutMapping("/revokeSelf/{formId}")
    public Result<Boolean> revokeSelf(@PathVariable String formId, @RequestBody FormDataDto data){
        formDataService.revoke(formId, data.getId(), true);
        return Result.ok();
    }

    @ApiOperation("表单列表")
    @GetMapping("/page/{formId}")
    public Result<PageResult<Map<String, Object>>> page(
            @PathVariable String formId,
            @RequestParam("pageNo")int pageNo,
            @RequestParam("pageSize")int pageSize,
            @RequestParam(value = "keywords", required = false)String keywords,
            @RequestParam(value = "status", required = false)FormDataStatus status
            ){
        return Result.ok(formDataService.page(formId, pageSize, pageNo, keywords, status, false, new ArrayList<>()).getDataList());
    }

    @ApiOperation("表单列表")
    @PostMapping("/page/withFilter/{formId}")
    public Result<PageResult<Map<String, Object>>> page(
            @PathVariable String formId, @RequestBody FormPageQueryDto query){
        return Result.ok(formDataService.page(formId, query.getPageSize(), query.getPageNo(),
                query.getKeywords(), query.getStatus(), false, query.getFilters()).getDataList());
    }

    @ApiOperation("表单列表")
    @GetMapping("/pageSelf/{formId}")
    public Result<PageResult<Map<String, Object>>> pageSelf(
            @PathVariable String formId,
            @RequestParam("pageNo")int pageNo,
            @RequestParam("pageSize")int pageSize
    ){
        return Result.ok(formDataService.page(formId, pageSize, pageNo, null, null, true, new ArrayList<>()).getDataList());
    }

    @ApiOperation("表单列表")
    @GetMapping("/page/sub/{formId}")
    public Result<PageResult<Map<String, Object>>> pageSelf(
            @PathVariable String formId,
            @RequestParam("pageNo")int pageNo,
            @RequestParam("pageSize")int pageSize,
            @RequestParam("empId")String empId,
            @RequestParam(value = "isPlain", required = false, defaultValue = "false") boolean isPlain
    ){
        return Result.ok(formDataService.pageSub(formId, pageSize, pageNo,  empId, isPlain)
                .getDataList());
    }

    @ApiOperation("查看表单详情（属性自描述结构）")
    @GetMapping("/one/{formId}/:self_described")
    public Result<FormDataVo> oneWithSelfDescribed(@PathVariable String formId, @RequestParam String id){
        return Result.ok(FastjsonUtil.convertObject(formDataService.loadById(formId,id), FormDataVo.class));
    }

    @ApiOperation("查看表单详情（属性map结构）")
    @GetMapping("/one/{formId}")
    public Result<FormDataMapVo> one(@PathVariable String formId, @RequestParam String id){
        return Result.ok(FormDataMapVo.fromFormData(formDataService.loadById(formId,id)));
    }

    @ApiOperation("查看表单详情（属性map结构）")
    @GetMapping("/oneSelf/{formId}")
    public Result<FormDataMapVo> oneSelf(@PathVariable String formId, @RequestParam String id){
        return Result.ok(FormDataMapVo.fromFormData(formDataService.loadMineById(formId,id)));
    }

    @ApiOperation("查看员工子集表单详情（属性map结构）")
    @GetMapping("/one/sub/{formId}")
    public Result<FormDataMapVo> oneSub(@PathVariable String formId, @RequestParam String empId, @RequestParam(required = false) String id){
        return Result.ok(FormDataMapVo.fromFormData(formDataService.loadSubByEmpId(formId, empId,id)));
    }


    @ApiOperation("表单导出")
    @PostMapping("/export/{formId}")
    public Result export(@PathVariable String formId, HttpServletResponse response){
        formDataService.export(formId, response);
        return Result.ok();
    }


    @ApiOperation("表单导出")
    @PostMapping("/export/withFilter/{formId}")
    public Result export(@PathVariable String formId, @RequestBody FormPageQueryDto query, HttpServletResponse response) throws IOException {
        formDataService.export(formId, query.getKeywords(), query.getStatus(), false, query.getFilters(), response);
        return Result.ok();
    }
}