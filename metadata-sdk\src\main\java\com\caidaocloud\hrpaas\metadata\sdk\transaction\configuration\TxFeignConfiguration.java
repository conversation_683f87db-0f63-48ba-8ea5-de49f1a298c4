package com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration;

import com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect;
import feign.RequestInterceptor;
import feign.RequestTemplate;

import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 * @date 2022/10/11
 */
@Configuration
public class TxFeignConfiguration implements RequestInterceptor {
	/**
	 * Called for every request. Add data using methods on the supplied {@link RequestTemplate}.
	 * @param template
	 */
	@Override
	public void apply(RequestTemplate template) {
		// header添加paas事务id
		template.header("Paas-Tx-Id", TransactionAspect.getTransactionHolder().getTransactionId());
	}
}
