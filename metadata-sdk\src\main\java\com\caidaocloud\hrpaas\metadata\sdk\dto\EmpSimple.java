package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

import org.springframework.beans.BeanUtils;

/**
 * 选人组件
 *
 * <AUTHOR>
 * @date 2022-02-24
 */
@Data
@DataComponent(dataType = PropertyDataType.Emp)
@Slf4j
public class EmpSimple implements ComponentPropertyValue {
    private final static String EMP_BASIC_INFO_KEY = "EMP_B_I_%s";
    private long dataStartTime;

    private String empId;

    private String workno;

    private String name;

    private String enName;

    private String deptDesc;

    // 强制使用传入值
    private boolean force;

    @Override
    public Map<String, Boolean> propertySuffixToWhetherLong() {
        return Maps.map(
                Sequences.sequence(
                        Pair.pair(".empId", false),
                        Pair.pair(".name", false),
                        Pair.pair(".workno", false),
                        Pair.pair(".enName", false)
                )
        );
    }

    /**
     * 存储属性转换成对象
     */
    @Override
    public PropertyValue extractDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties) {
        return EmpSimple.toDisplay(propDef.getProperty(), properties);
    }

    public static EmpSimple toDisplay(String fieldProp, List<PropertyDataDto> propList) {
        if (null == propList || StringUtil.isEmpty(fieldProp) || propList.isEmpty()) {
            return null;
        }

        EmpSimple empSimple = new EmpSimple();
        for (PropertyDataDto prop : propList) {
            if (null == prop || !prop.getProperty().startsWith(fieldProp + ".")) {
                continue;
            }

            if (prop.getProperty().endsWith(".empId")) {
                empSimple.setEmpId(prop.getValue());
            } else if (prop.getProperty().endsWith(".workno")) {
                empSimple.setWorkno(prop.getValue());
            } else if (prop.getProperty().endsWith(".name")) {
                empSimple.setName(prop.getValue());
            } else if (prop.getProperty().endsWith(".enName")) {
                empSimple.setEnName(prop.getValue());
            }
        }

        if (StringUtil.isEmpty(empSimple.getEmpId())) {
            return null;
        }

        Object objData = ThreadLocalUtil.LOCAL_REQUEST_DATA.get();
        if (null != objData && objData instanceof DataQuery && ((DataQuery) objData).isShowDept()) {
            DataSimple dataSimple = DataQuery.identifier("entity.hr.EmpWorkInfo")
                    .oneOrNull(empSimple.getEmpId(), DataSimple.class, ((DataQuery) objData).getQueryTime());
            PropertyValue organizeTxt = null;
            if (null == dataSimple || null == (organizeTxt = dataSimple.getProperties().get("organizeTxt"))) {
                return empSimple;
            }

            empSimple.setDeptDesc(((SimplePropertyValue) organizeTxt).getValue());
            empSimple.setDataStartTime(((DataQuery) objData).getQueryTime());
        }

        return empSimple;
    }

    @Override
    public List<PropertyDataDto> extractPersist(DataSimple data, MetadataPropertyVo propDef) {
        return EmpSimple.toPersist(data, propDef);
    }

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {
        /*dataValue.fitDataValue(((EmpSimple) dataVal).getEmpId());
        DataValueFunction dataValueDto = dataValue.dataValueBulid();
        dataValueDto.fitDataProp(String.format(UIFORM_RELATION_FORMAT_TXT, dataValue.loadDataProp()));
        dataValueDto.fitDataValue(((EmpSimple) dataVal).getWorkno());
        dataVals.add(dataValueDto);*/
    }

    @Override
    public String toText() {
        return StringUtil.isEmpty(name) ? "" : String.format("%s(%s)", name, workno);
    }

    public static List<PropertyDataDto> toPersist(DataSimple data, MetadataPropertyVo propDef) {
        // 获取属性中的员工字段
        String empPrefix = propDef.getProperty();
        Map dataMap = JsonEnhanceUtil.toObject(data, Map.class);

        PropertyValue pValue = data.getProperties().get(empPrefix);

        EmpSimple empSimple = null;
        // 员工存在于原生属性中
        if (dataMap.containsKey(empPrefix)) {
            try {
                Method readMethod = BeanUtils.getPropertyDescriptor(data.getClass(), empPrefix).getReadMethod();
                empSimple = (EmpSimple) readMethod.invoke(data);
            }
            catch (Exception e) {
                log.error("获取empSimple失败", e);
            }

            Object standardPropertyValue = dataMap.get(empPrefix);
            if (empSimple==null && null != standardPropertyValue) {
                empSimple = standardPropertyValue instanceof  EmpSimple ? ((EmpSimple) standardPropertyValue)
                        :JsonEnhanceUtil.toObject(standardPropertyValue, EmpSimple.class);
            }
            return EmpSimple.toPropList(empSimple, empPrefix);
        } else if ((empSimple = handleFlatProperty(data, empPrefix)) != null) {
            return EmpSimple.toPropList(empSimple, empPrefix);
        }

        if (null != pValue) {
            if (pValue instanceof EmpSimple) {
                empSimple = ((EmpSimple) pValue);
            } else {
                empSimple = new EmpSimple();
                String dictValue = ((SimplePropertyValue) pValue).getValue();
                empSimple.setEmpId(dictValue);

                doEmpSimple(empSimple);
            }
            return EmpSimple.toPropList(empSimple, empPrefix);
        }
        return EmpSimple.toPropList(null, empPrefix);
    }

    private static EmpSimple handleFlatProperty(DataSimple data, String dictPrefix) {
        var b = data.getProperties().containsKey(String.format("%s.empId", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.name", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.workno", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.enName", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.deptDesc", dictPrefix));
        if (!b) {
            return null;
        }
        var empSimple = new EmpSimple();
        if (data.getProperties().containsKey(String.format("%s.empId", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.empId", dictPrefix));
            empSimple.setEmpId(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.name", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.name", dictPrefix));
            empSimple.setName(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.workno", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.workno", dictPrefix));
            empSimple.setWorkno(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.enName", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.enName", dictPrefix));
            empSimple.setEnName(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.deptDesc", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.deptDesc", dictPrefix));
            empSimple.setDeptDesc(propertyValue.getValue());
        }
        return empSimple;
    }

    private static List<PropertyDataDto> toPropList(EmpSimple empSimple, String empPrefix) {
        List<PropertyDataDto> result = Lists.newArrayList();
        if (null == empSimple) {
            empSimple = new EmpSimple();
        } else {
            doEmpSimple(empSimple);
        }

        PropertyDataDto dataDto = new PropertyDataDto();
        dataDto.setProperty(String.format("%s.empId", empPrefix));
        dataDto.setDataType(PropertyDataType.Emp);
        dataDto.setValue(empSimple.getEmpId());
        result.add(dataDto);

        dataDto = new PropertyDataDto();
        dataDto.setProperty(String.format("%s.name", empPrefix));
        dataDto.setDataType(PropertyDataType.Emp);
        dataDto.setValue(empSimple.getName());
        result.add(dataDto);

        dataDto = new PropertyDataDto();
        dataDto.setProperty(String.format("%s.workno", empPrefix));
        dataDto.setDataType(PropertyDataType.Emp);
        dataDto.setValue(empSimple.getWorkno());
        result.add(dataDto);

        dataDto = new PropertyDataDto();
        dataDto.setProperty(String.format("%s.enName", empPrefix));
        dataDto.setDataType(PropertyDataType.Emp);
        dataDto.setValue(empSimple.getEnName());
        result.add(dataDto);

        return result;
    }

    public static void doEmpSimple(EmpSimple empSimple) {
        String empId = empSimple.getEmpId();
        if (StringUtil.isBlank(empId)) {
            return;
        }
        if (empSimple.force) {
            return;
        }

        EmployeeDto employeeDto = QueryInfoCache.loadEmpById(String.format(EMP_BASIC_INFO_KEY, empId), empId);
        if (null == employeeDto) {
            return;
        }

        empSimple.setName(employeeDto.getName());
        empSimple.setWorkno(employeeDto.getWorkno());
        empSimple.setEnName(employeeDto.getEnName());
    }

}
