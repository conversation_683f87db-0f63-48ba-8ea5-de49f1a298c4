package com.caidaocloud.hrpaas.service.test;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.service.DataOperatorServiceFeignImpl;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHolder;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date 2021/12/31
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class MetadataSdkTest {

    @Test
    public void testDataQueryOne() {
        UserInfo simpleUserInfo = new UserInfo();
        simpleUserInfo.setTenantId("caidaotest");
        simpleUserInfo.setBelongOrgId(0).setCorpid(0).setEmpid(0).setUserid(0);
        RequestHolder.setUserInfo(simpleUserInfo);

        DataOperatorServiceFeignImpl bean = SpringUtil.getBean(DataOperatorServiceFeignImpl.class);
        DataQueryDto queryDto = new DataQueryDto();
        queryDto.setBid("1311361873942540");
        queryDto.setIdentifier("entity.hr.simpletest");
        EntityDataDto dataDto = bean.one(queryDto, 0l);
        System.out.println(FastjsonUtil.toJson(dataDto));
    }
}
