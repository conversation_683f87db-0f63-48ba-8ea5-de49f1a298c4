package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Data
@DataComponent(dataType = PropertyDataType.PID)
public class TreeParent implements ComponentPropertyValue {

    private String pid;

    private String path;

    private String namePath;

    private String pName;

    @Override
    public Map<String, Boolean> propertySuffixToWhetherLong(){
        return Maps.map(
                Sequences.sequence(
                        Pair.pair(".pid", false),
                        Pair.pair(".path", true),
                        Pair.pair(".namePath", true)
                )
        );
    }

    @Override
    public PropertyValue extractDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties){
        return toDisplay(properties);
    }

    public static TreeParent toDisplay(List<PropertyDataDto> properties){
        val pidDto = properties.stream()
                .filter(propertyDataDto -> propertyDataDto.getProperty().equals("pid.pid"))
                .findFirst();
        if(pidDto.isPresent()){
            TreeParent treeParent = new TreeParent();
            treeParent.setPid(pidDto.get().getValue());
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals("pid.path"))
                    .findFirst().ifPresent(pathDto->{
                        treeParent.setPath(pathDto.getValue());
                    });
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals("pid.namePath"))
                    .findFirst().ifPresent(namePathDto->{
                        treeParent.setNamePath(namePathDto.getValue());
                        if(StringUtils.isNotEmpty(treeParent.getNamePath())){
                            if(treeParent.getNamePath().contains("/")){
                                treeParent.setPName(treeParent.getNamePath().substring(treeParent.getNamePath().lastIndexOf("/")+1));
                            }else{
                                treeParent.setPName(treeParent.getNamePath());
                            }
                        }
                    });
            return treeParent;
        }else{
            return null;
        }
    }

    @Override
    public List<PropertyDataDto> extractPersist(DataSimple data, MetadataPropertyVo propDef){
        return toPersist(data, propDef);
    }

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {
        dataValue.fitDataValue(((TreeParent) dataVal).getPid());
        DataValueFunction dataValueDto = dataValue.dataValueBulid();
        dataValueDto.fitDataProp(String.format(UIFORM_RELATION_FORMAT_TXT, dataValue.loadDataProp()));
        String pidVal = ((TreeParent) dataVal).getNamePath();
        dataValueDto.fitDataValue(StringUtil.isNotBlank(pidVal) && pidVal.indexOf("/") > -1 ? pidVal.substring(pidVal.lastIndexOf("/") + 1) : pidVal);
        dataVals.add(dataValueDto);

        // 树的name全路径
        DataValueFunction fullPathNameDto = dataValue.dataValueBulid();
        fullPathNameDto.fitDataProp(String.format(UIFORM_RELATION_FORMAT_FULLPATH_NAME_TXT, dataValue.loadDataProp()));
        fullPathNameDto.fitDataValue(pidVal);
        dataVals.add(fullPathNameDto);

        // 树的id全路径
        DataValueFunction fullPathIdDto = dataValue.dataValueBulid();
        fullPathIdDto.fitDataProp(String.format(UIFORM_RELATION_FORMAT_FULLPATH_ID_TXT, dataValue.loadDataProp()));
        fullPathIdDto.fitDataValue(((TreeParent) dataVal).getPath());
        dataVals.add(fullPathIdDto);
    }

    @Override
    public String toText() {
        return namePath;
    }

    public static List<PropertyDataDto> toPersist(DataSimple data, MetadataPropertyVo propDef){
        List<PropertyDataDto> result = Lists.newArrayList();
        val pid = data.fetchPid();
        PropertyDataDto dataDto = new PropertyDataDto();
        dataDto.setProperty(propDef.getProperty());
        dataDto.setDataType(PropertyDataType.PID);
        dataDto.setValue(pid);
        result.add(dataDto);
        return result;
    }
}
