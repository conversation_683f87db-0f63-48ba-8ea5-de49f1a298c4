package com.caidaocloud.hrpaas.service.test;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.hrpaas.service.application.metadata.dto.MetadataSchemaInfoDto;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataSchemaDo;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataSchemaDto;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ScriptVersionTest {
    public static void main(String[] args) {
        //readJson2List();
        String str = "caidao-hr-paas-service/paas-service/target/classes/metadata/script/20210629/V20210629114122_表单引擎初始化脚本.json";

        str = str.substring(str.indexOf("script/") + 7, str.indexOf("/V"));
        System.out.println(str);
    }

    public static void readJson2List(){
        List<MetadataSchemaDto> schemaList = new ArrayList<>();

        MetadataSchemaDto metadataSchemaDto = new MetadataSchemaDto();
        metadataSchemaDto.setFolder(20210628);
        metadataSchemaDto.setSchemaModel("payroll");
        metadataSchemaDto.setSchemaName("V20210628062222_员工信息新增字段.json");
        metadataSchemaDto.setVersion(20210628062222L);

        MetadataSchemaInfoDto metadataSchemaContentDto = new MetadataSchemaInfoDto();
        metadataSchemaContentDto.setAction(MetadataSchemaInfoDto.SchemaAction.add_prop);
        //metadataSchemaContentDto.setFolder(20210628);
        //metadataSchemaContentDto.setModel("payroll");
        List<MetadataSchemaInfoDto> list = new ArrayList<>();
        list.add(metadataSchemaContentDto);
        metadataSchemaDto.setContent(list);

        System.out.println("-------------");
        schemaList.add(metadataSchemaDto);
        System.out.println(JSON.toJSONString(schemaList));

        List<MetadataSchemaDo> metadataSchemaList = schemaList.stream().map(schema -> {
            MetadataSchemaDo msd = new MetadataSchemaDo();
            BeanUtils.copyProperties(schema, msd);

            if(null != schema.getContent() && !schema.getContent().isEmpty()){
                List<MetadataSchemaInfoDto> schemaInfoDtos = schema.getContent().stream().map(info -> {
                    info.setFolder(schema.getFolder());
                    info.setScriptName(schema.getSchemaName());
                    info.setModel(schema.getSchemaModel());
                    return info;
                }).collect(Collectors.toList());
                schema.setContent(schemaInfoDtos);
            }

            msd.setSchemaInfo(JSON.toJSONString(schema.getContent()));
            return msd;
        }).collect(Collectors.toList());

        System.out.println("==============");
        System.out.println(JSON.toJSONString(metadataSchemaList));
    }

    public void scriptVersionTest(){
        String scriptName = "V20210623062222_员工信息新增字段.json";
        String scriptVersion = scriptName.substring(1, 15);
        System.out.println(scriptVersion);

        Long dbVersion = Long.parseLong(scriptVersion);
        Long version = 20210623062223L;

        System.out.println(dbVersion >= version);
    }
}
