package com.caidaocloud.hrpaas.metadata.sdk.feign;

import com.caidaocloud.hrpaas.metadata.sdk.dto.AuthResourceDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AuthRoleScopeFilterDetailDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@FeignClient(value = "caidaocloud-auth-service",
        fallback = MetadataAuthFeignFallback.class,
        configuration = {FeignConfiguration.class},
        contextId = "metadataAuthFeign", qualifier = "metadataAuthFeign")
public interface IMetadataAuthFeignClient {

    @GetMapping("/api/auth/v1/subject/role/scope/list")
    Result<List<AuthRoleScopeFilterDetailDto>> getRoleScope(@RequestParam("identifier") String identifier,
                                                            @RequestParam("subjectId") String subjectId);

    /**
     * 创建权限资源
     *
     * @param resourceList
     * @return
     */
    @PostMapping("/api/auth/v1/resources")
    Result<String> createResources(@RequestBody List<AuthResourceDto> resourceList);

    @PutMapping("/api/auth/v1/resources")
    Result<String> update(@RequestBody List<AuthResourceDto> resourceList);

    @DeleteMapping("/api/auth/v1/resources")
    Result<String> delete(@RequestBody List<AuthResourceDto> removeList);
}
