package com.caidaocloud.hrpaas.paas.common.dto;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DynamicPageDto {

    private PageResult<Map> pageData;

    private UserDynamicConfig userDynamicConfig;

    private List<MetadataPropertyDto> dynamicConfig;
}
