package com.caidaocloud.metadata.interfaces.facade;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TransactionInfoDto;
import com.caidaocloud.metadata.application.transaction.TransactionService;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @date 2022/9/29
 */
@RestController
@RequestMapping("/api/${service.name.short:hrpaas}/v1/transaction")
@Slf4j
public class TransactionController {

	@Autowired
	private TransactionService transactionService;

	@PostMapping
	public Result<TransactionInfoDto> begin() {
		return Result.ok(transactionService.begin());
	}

	@DeleteMapping("commit")
	public Result commit(String transactionId) {
		PreCheck.preCheckNotNull(transactionId, "Tx id can not be null");
		transactionService.commit(transactionId);
		return Result.ok(true);
	}

	@DeleteMapping("rollback")
	public Result rollback(String transactionId) {
		PreCheck.preCheckNotNull(transactionId, "Tx id can not be null");
		transactionService.rollback(transactionId);
		return Result.ok(true);
	}
}
