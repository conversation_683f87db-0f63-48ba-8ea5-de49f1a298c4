package com.caidaocloud.hrpaas.service.application.feildMapping.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import lombok.Data;

@Data
public class ChangeDefDto {
    /**
     * 异动类型ID
     */
    private String typeId;

    /**
     * 异动名称
     */
    private String name;

    /**
     * 申请说明
     */
    private String desc;

    /**
     * 合同信息字段
     */
    private String contractProps;

    /**
     * 任职信息字段
     */
    private String workProps;

    /**
     * 薪资字段
     */
    private String salaryProps;

    /**
     * 员工字段
     */
    private String displayWorkInfos;


    /**
     * 其他配置字段
     */
    private String otherProps;

    /**
     * 挂载表单
     */
    private String form;

    /**
     * 版本控制，
     * 正式版本 release
     * 历史版本 snapshot
     */
    private EnumSimple version;

    /**
     * 字段映射关系字段
     */
    private String mappingConfigId;
}
