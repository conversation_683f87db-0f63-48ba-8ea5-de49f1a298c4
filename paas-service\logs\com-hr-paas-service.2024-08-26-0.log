2024-08-26 16:25:18.355 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-26 16:25:18.368 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-26 16:25:20.637 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='172.16.0.138:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-26 16:25:25.164 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-26 16:25:29.872 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-26 16:25:31.931 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-26 16:25:31.935 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-26 16:25:32.069 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 113ms. Found 0 repository interfaces.
2024-08-26 16:25:34.228 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-26 16:25:34.232 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-26 16:25:36.579 [main] ERROR c.j.c.a.AutoloadCacheManageConfiguration - Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1092)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1065)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:865)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:340)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration.createRedisCacheManager(AutoloadCacheManageConfiguration.java:163)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration.autoloadCacheCacheManager(AutoloadCacheManageConfiguration.java:157)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$94624e6c.CGLIB$autoloadCacheCacheManager$0(<generated>)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$94624e6c$$FastClassBySpringCGLIB$$d30ee668.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:363)
	at com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$94624e6c.autoloadCacheCacheManager(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:620)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:605)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1239)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1166)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:855)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:758)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1239)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1166)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:855)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:758)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1239)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1166)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:855)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:758)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:109)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1064)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:487)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:391)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:109)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1064)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:487)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:391)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1288)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1127)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.aop.framework.autoproxy.BeanFactoryAdvisorRetrievalHelper.findAdvisorBeans(BeanFactoryAdvisorRetrievalHelper.java:91)
	at org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator.findCandidateAdvisors(AbstractAdvisorAutoProxyCreator.java:109)
	at org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator.findCandidateAdvisors(AnnotationAwareAspectJAutoProxyCreator.java:92)
	at org.springframework.aop.aspectj.autoproxy.AspectJAwareAdvisorAutoProxyCreator.shouldSkip(AspectJAwareAdvisorAutoProxyCreator.java:101)
	at org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator.postProcessBeforeInstantiation(AbstractAutoProxyCreator.java:253)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeforeInstantiation(AbstractAutowireCapableBeanFactory.java:1064)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:487)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.registerBeanPostProcessors(PostProcessorRegistrationDelegate.java:240)
	at org.springframework.context.support.AbstractApplicationContext.registerBeanPostProcessors(AbstractApplicationContext.java:707)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:531)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:204)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:113)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:113)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1085)
	... 154 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:6379
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:327)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:340)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:616)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:563)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:480)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:442)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused: no further information
	... 12 common frames omitted
2024-08-26 16:25:36.583 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-08-26 16:25:36.583 [main] WARN  c.c.config.ErrorLogRollingPolicy - mailService bean non-existent.
2024-08-26 16:25:36.759 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-26 16:25:36.760 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-26 16:28:43.844 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-26 16:28:43.849 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-26 16:28:45.655 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-26 16:28:45.940 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-26 16:28:47.458 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-26 16:28:49.716 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-26 16:28:49.719 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-26 16:28:49.837 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 99ms. Found 0 repository interfaces.
2024-08-26 16:28:52.063 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-26 16:28:52.067 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-26 16:28:52.606 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-26 16:28:52.991 [main] INFO  org.reflections.Reflections - Reflections took 354 ms to scan 2 urls, producing 16 keys and 353 values 
2024-08-26 16:28:53.068 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-26 16:28:56.932 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:28:56.938 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:28:56.939 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:28:57.013 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:28:57.171 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:28:57.281 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:28:57.318 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:28:57.673 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:28:57.699 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:28:57.851 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:28:58.458 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-26 16:28:58.676 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-26 16:28:58.837 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-26 16:28:58.907 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-26 16:28:58.908 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-26 16:29:00.028 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:06.063 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:29:06.064 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:29:06.079 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:29:06.080 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:29:09.396 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:09.507 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:09.538 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:10.669 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:10.718 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:10.751 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:14.568 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@76a41af0[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-26 16:29:14.570 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7a1148f2[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$2a665609#prepareRecentTask]
2024-08-26 16:29:14.627 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:14.638 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:14.654 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:14.692 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:14.717 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:14.729 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:29:14.800 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-26 16:29:14.825 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-26 16:29:14.845 [Thread-34] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-26 16:29:14.860 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-26 16:29:15.326 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-26 16:29:15.944 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-26 16:29:16.423 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-26 16:29:16.575 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-26 16:29:16.577 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-26 16:29:16.579 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-26 16:29:16.582 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-26 16:29:16.584 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-26 16:29:16.586 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-26 16:29:16.589 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-26 16:29:16.592 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-26 16:29:16.610 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-26 16:29:16.611 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-26 16:29:16.612 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-26 16:29:16.613 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-26 16:29:16.692 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-26 16:29:16.757 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-26 16:29:16.781 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-26 16:29:16.796 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-26 16:29:16.900 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-26 16:31:20.513 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-26 16:31:20.518 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-26 16:31:22.356 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-26 16:31:22.713 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-26 16:31:24.134 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-26 16:31:28.302 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-26 16:31:28.306 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-26 16:31:28.647 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 204ms. Found 0 repository interfaces.
2024-08-26 16:31:31.609 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-26 16:31:31.616 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-26 16:31:32.222 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-26 16:31:32.517 [main] INFO  org.reflections.Reflections - Reflections took 257 ms to scan 2 urls, producing 16 keys and 353 values 
2024-08-26 16:31:32.602 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-26 16:31:37.989 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:37.999 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:38.000 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:38.174 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:38.580 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:38.895 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:39.052 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:40.793 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:40.807 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:40.962 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:41.882 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-26 16:31:42.101 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-26 16:31:42.260 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-26 16:31:42.353 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-26 16:31:42.353 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-26 16:31:43.903 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:53.256 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:31:53.262 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:31:53.298 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:31:53.299 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:31:56.172 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:56.288 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:56.399 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:58.292 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:58.367 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:31:58.423 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:32:01.611 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3e0575a2[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-26 16:32:01.616 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@65589d76[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$36cb2677#prepareRecentTask]
2024-08-26 16:32:01.736 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:32:01.758 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:32:01.785 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:32:01.818 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:32:01.836 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:32:01.853 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:32:01.909 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-26 16:32:01.949 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-26 16:32:01.970 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-26 16:32:01.992 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-26 16:32:02.460 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-26 16:32:02.981 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-26 16:32:03.378 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-26 16:32:03.557 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-26 16:32:03.559 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-26 16:32:03.561 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-26 16:32:03.564 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-26 16:32:03.568 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-26 16:32:03.571 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-26 16:32:03.574 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-26 16:32:03.577 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-26 16:32:03.680 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-26 16:32:03.750 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-26 16:32:03.780 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-26 16:32:03.811 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-26 16:32:04.049 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-26 16:32:04.151 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-26 16:32:04.173 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-26 16:32:04.185 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-26 16:32:04.541 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-26 16:32:04.566 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-26 16:32:04.579 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-26 16:32:04.683 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-26 16:32:04.709 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-26 16:32:04.712 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-26 16:32:04.936 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-26 16:32:04.940 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-26 16:32:04.945 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-26 16:32:04.959 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-26 16:32:04.963 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-26 16:32:04.972 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-26 16:32:04.978 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-26 16:32:04.992 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-26 16:32:04.997 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-26 16:32:05.001 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-26 16:32:05.006 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-26 16:32:05.011 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-26 16:32:05.016 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-26 16:32:05.050 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-26 16:32:05.077 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-26 16:32:05.080 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-26 16:32:05.083 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-26 16:32:05.092 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-26 16:32:05.095 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-26 16:32:05.122 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-26 16:32:05.248 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-26 16:32:06.713 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-26 16:32:06.714 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-26 16:32:06.946 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-26 16:32:07.303 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-26 16:32:07.303 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-26 16:32:07.304 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-26 16:32:07.305 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-26 16:32:07.315 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-26 16:32:07.325 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-26 16:32:07.326 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-26 16:32:07.326 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-26 16:32:07.327 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-26 16:32:07.327 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-26 16:32:07.328 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-26 16:32:07.373 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:32:07.373 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:32:07.469 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-26 16:32:07.710 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-26 16:32:07.733 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-08-26 16:32:38.501 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-26 16:32:38.507 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-26 16:32:44.375 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-26 16:32:46.194 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-26 16:32:55.575 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-26 16:33:02.756 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-26 16:33:02.771 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-26 16:33:03.114 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 287ms. Found 0 repository interfaces.
2024-08-26 16:33:12.772 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-26 16:33:12.785 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-26 16:33:14.209 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-26 16:33:14.987 [main] INFO  org.reflections.Reflections - Reflections took 696 ms to scan 2 urls, producing 16 keys and 353 values 
2024-08-26 16:33:15.216 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-26 16:33:26.343 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:26.484 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:26.493 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:26.725 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:27.293 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:27.588 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:27.808 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:29.766 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:29.793 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:30.268 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:33.317 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-26 16:33:34.182 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-26 16:33:34.498 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-26 16:33:34.659 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-26 16:33:34.660 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-26 16:33:36.280 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:48.516 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:33:48.534 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:33:48.612 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:33:48.613 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:33:55.064 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:55.122 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:55.168 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:56.704 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:56.764 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:33:56.836 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:34:01.318 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3e0575a2[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-26 16:34:01.321 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@65589d76[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$a491dadb#prepareRecentTask]
2024-08-26 16:34:01.386 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:34:01.400 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:34:01.423 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:34:01.445 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:34:01.462 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:34:01.476 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:34:01.517 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-26 16:34:01.540 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-26 16:34:01.554 [Thread-36] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-26 16:34:01.571 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-26 16:34:02.032 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-26 16:34:02.502 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-26 16:34:02.810 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-26 16:34:02.997 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-26 16:34:03.000 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-26 16:34:03.002 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-26 16:34:03.004 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-26 16:34:03.007 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-26 16:34:03.010 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-26 16:34:03.013 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-26 16:34:03.016 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-26 16:34:03.106 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-26 16:34:03.156 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-26 16:34:03.181 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-26 16:34:03.204 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-26 16:34:03.337 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-26 16:34:03.369 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-26 16:34:03.381 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-26 16:34:03.391 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-26 16:34:03.432 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-26 16:34:03.454 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-26 16:34:03.465 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-26 16:34:03.514 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-26 16:34:03.535 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-26 16:34:03.537 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-26 16:34:03.688 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-26 16:34:03.691 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-26 16:34:03.696 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-26 16:34:03.709 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-26 16:34:03.713 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-26 16:34:03.721 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-26 16:34:03.730 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-26 16:34:03.746 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-26 16:34:03.749 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-26 16:34:03.753 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-26 16:34:03.757 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-26 16:34:03.760 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-26 16:34:03.763 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-26 16:34:03.792 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-26 16:34:03.818 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-26 16:34:03.820 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-26 16:34:03.823 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-26 16:34:03.829 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-26 16:34:03.832 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-26 16:34:03.859 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-26 16:34:03.959 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-26 16:34:04.444 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-26 16:34:04.444 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-26 16:34:04.687 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-26 16:34:51.557 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=46s734ms258µs300ns).
2024-08-26 16:34:51.684 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-26 16:34:51.685 [Thread-31] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-26 16:34:51.686 [Thread-31] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-26 16:34:51.688 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-26 16:34:51.744 [Thread-36] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-26 16:34:51.754 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-26 16:34:51.755 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-26 16:34:51.755 [Thread-38] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-26 16:34:51.756 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-26 16:34:51.758 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-26 16:34:51.759 [Thread-35] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-26 16:34:51.861 [Thread-38] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:34:51.862 [Thread-38] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:34:52.114 [Thread-38] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-26 16:34:52.297 [Thread-38] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-26 16:34:52.332 [Thread-38] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-08-26 16:35:05.512 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-26 16:35:05.519 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-26 16:35:09.267 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-26 16:35:09.635 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-26 16:35:11.087 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-26 16:35:16.603 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-26 16:35:16.613 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-26 16:35:16.751 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 114ms. Found 0 repository interfaces.
2024-08-26 16:35:19.592 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-26 16:35:19.598 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-26 16:35:20.376 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-26 16:35:20.636 [main] INFO  org.reflections.Reflections - Reflections took 222 ms to scan 2 urls, producing 16 keys and 353 values 
2024-08-26 16:35:20.723 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-26 16:35:26.924 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:26.929 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:26.930 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:26.999 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:27.158 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:27.207 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:27.241 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:27.628 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:27.649 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:27.785 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:28.397 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-26 16:35:28.583 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-26 16:35:28.743 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-26 16:35:28.804 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-26 16:35:28.805 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-26 16:35:29.244 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:32.478 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:35:32.478 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:35:32.490 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:35:32.491 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:35:34.315 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:34.364 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:34.439 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:35.637 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:35.688 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:35.730 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:38.146 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5304ac92[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-26 16:35:38.148 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@71669835[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$36cb2677#prepareRecentTask]
2024-08-26 16:35:38.204 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:38.218 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:38.240 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:38.264 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:38.280 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:38.295 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:38.341 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-26 16:35:38.375 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-26 16:35:38.391 [Thread-34] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-26 16:35:38.418 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-26 16:35:38.802 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-26 16:35:39.272 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-26 16:35:39.589 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-26 16:35:39.839 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-26 16:35:39.843 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-26 16:35:39.847 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-26 16:35:39.851 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-26 16:35:39.854 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-26 16:35:39.857 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-26 16:35:39.860 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-26 16:35:39.863 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-26 16:35:39.961 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-26 16:35:40.007 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-26 16:35:40.027 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-26 16:35:40.055 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-26 16:35:40.199 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-26 16:35:40.244 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-26 16:35:40.256 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-26 16:35:40.264 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-26 16:35:40.313 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-26 16:35:40.339 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-26 16:35:40.355 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-26 16:35:40.397 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-26 16:35:40.413 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-26 16:35:40.414 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-26 16:35:40.554 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-26 16:35:40.557 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-26 16:35:40.561 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-26 16:35:40.574 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-26 16:35:40.577 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-26 16:35:40.588 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-26 16:35:40.593 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-26 16:35:40.608 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-26 16:35:40.613 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-26 16:35:40.619 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-26 16:35:40.623 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-26 16:35:40.627 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-26 16:35:40.632 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-26 16:35:40.671 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-26 16:35:40.691 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-26 16:35:40.694 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-26 16:35:40.697 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-26 16:35:40.703 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-26 16:35:40.705 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-26 16:35:40.726 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-26 16:35:40.810 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-26 16:35:41.154 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-26 16:35:41.154 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-26 16:35:41.375 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-26 16:35:45.509 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:35:45.596 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-workflow-service-v2 instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-workflow-service-v2,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-26 16:35:45.629 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-26 16:35:46.056 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:35:46.064 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-workflow-service-v2 initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-workflow-service-v2,current list of Servers=[***************:10019],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10019;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@48c7773
2024-08-26 16:35:46.206 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:35:46.648 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:35:47.075 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-26 16:35:47.076 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-26 16:35:47.080 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-26 16:35:47.081 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-26 16:35:47.121 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-26 16:35:47.195 [Thread-34] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-26 16:35:47.230 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-26 16:35:47.264 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-26 16:35:47.276 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-26 16:35:47.277 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-26 16:35:47.280 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-26 16:35:47.288 [Thread-33] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-26 16:35:47.432 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:35:47.432 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:35:47.611 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-26 16:35:47.777 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-26 16:35:47.831 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-08-26 16:36:05.926 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-26 16:36:05.932 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-26 16:36:07.890 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-26 16:36:08.343 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-26 16:36:09.888 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-26 16:36:13.551 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-26 16:36:13.556 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-26 16:36:13.684 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 104ms. Found 0 repository interfaces.
2024-08-26 16:36:16.582 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-26 16:36:16.589 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-26 16:36:17.366 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-26 16:36:17.655 [main] INFO  org.reflections.Reflections - Reflections took 249 ms to scan 2 urls, producing 16 keys and 353 values 
2024-08-26 16:36:17.748 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-26 16:36:22.187 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:22.194 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:22.195 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:22.293 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:22.510 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:22.701 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:22.838 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:23.300 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:23.325 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:23.522 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:24.340 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-26 16:36:24.614 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-26 16:36:24.831 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-26 16:36:24.923 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-26 16:36:24.924 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-26 16:36:25.628 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:31.544 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:36:31.544 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:36:31.556 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:36:31.557 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:36:33.467 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:33.525 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:33.579 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:38.071 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:38.151 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:38.199 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:42.745 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@71669835[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-26 16:36:42.749 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@727d937a[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$548667b#prepareRecentTask]
2024-08-26 16:36:42.844 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:42.865 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:42.890 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:42.915 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:42.931 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:42.943 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:36:42.995 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-26 16:36:43.025 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-26 16:36:43.044 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-26 16:36:43.062 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-26 16:36:43.647 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-26 16:36:45.675 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-26 16:36:46.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-26 16:36:46.564 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-26 16:36:46.578 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-26 16:36:46.592 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-26 16:36:46.601 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-26 16:36:46.614 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-26 16:36:46.623 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-26 16:36:46.628 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-26 16:36:46.635 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-26 16:36:46.860 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-26 16:36:46.936 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-26 16:36:46.968 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-26 16:36:47.003 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-26 16:36:47.341 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-26 16:36:47.432 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-26 16:36:47.476 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-26 16:36:47.482 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-26 16:36:47.630 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-26 16:36:47.648 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-26 16:36:47.657 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-26 16:36:47.735 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-26 16:36:47.765 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-26 16:36:47.767 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-26 16:36:48.136 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-26 16:36:48.140 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-26 16:36:48.148 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-26 16:36:48.173 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-26 16:36:48.182 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-26 16:36:48.194 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-26 16:36:48.202 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-26 16:36:48.218 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-26 16:36:48.221 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-26 16:36:48.226 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-26 16:36:48.232 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-26 16:36:48.235 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-26 16:36:48.238 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-26 16:36:48.274 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-26 16:36:48.295 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-26 16:36:48.298 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-26 16:36:48.300 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-26 16:36:48.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-26 16:36:48.311 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-26 16:36:48.337 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-26 16:36:48.450 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-26 16:36:49.008 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-26 16:36:49.009 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-26 16:36:49.236 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-26 16:36:50.066 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:36:50.125 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-workflow-service-v2 instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-workflow-service-v2,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-26 16:36:50.138 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-26 16:36:50.316 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:36:50.320 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-workflow-service-v2 initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-workflow-service-v2,current list of Servers=[***************:10019],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10019;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@428c9250
2024-08-26 16:36:50.373 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:37:42.103 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:37:42.103 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=51s233ms573µs700ns).
2024-08-26 16:38:15.582 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-26 16:38:15.583 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-26 16:38:15.584 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-26 16:38:15.584 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-26 16:38:15.590 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-26 16:38:15.621 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-26 16:38:15.627 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-26 16:38:15.629 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-26 16:38:15.629 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-26 16:38:15.632 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-26 16:38:15.634 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-26 16:38:15.635 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-26 16:38:15.690 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:38:15.690 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:38:15.819 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-26 16:38:15.968 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-26 16:38:15.980 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-08-26 16:45:34.741 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-26 16:45:34.748 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-26 16:45:39.671 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-26 16:45:40.366 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-26 16:45:42.085 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-26 16:45:45.869 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-26 16:45:45.875 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-26 16:45:46.041 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 140ms. Found 0 repository interfaces.
2024-08-26 16:45:49.119 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-26 16:45:49.125 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-26 16:45:50.147 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-26 16:45:50.501 [main] INFO  org.reflections.Reflections - Reflections took 291 ms to scan 2 urls, producing 16 keys and 353 values 
2024-08-26 16:45:50.645 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-26 16:45:56.724 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:45:56.731 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:45:56.732 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:45:56.818 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:45:57.011 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:45:57.079 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:45:57.129 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:45:57.613 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:45:57.644 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:45:58.020 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:45:59.136 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-26 16:45:59.576 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-26 16:45:59.749 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-26 16:45:59.839 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-26 16:45:59.840 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-26 16:46:00.581 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:08.034 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:46:08.035 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:46:08.055 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:46:08.055 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:46:11.561 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:11.608 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:11.649 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:12.756 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:12.806 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:12.839 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:15.817 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7a213820[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-26 16:46:15.820 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@34b69a14[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$36cb2677#prepareRecentTask]
2024-08-26 16:46:15.886 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:15.901 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:15.922 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:15.946 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:15.961 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:15.975 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:16.013 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-26 16:46:16.037 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-26 16:46:16.053 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-26 16:46:16.068 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-26 16:46:16.459 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-26 16:46:16.991 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-26 16:46:17.339 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-26 16:46:17.543 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-26 16:46:17.545 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-26 16:46:17.547 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-26 16:46:17.550 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-26 16:46:17.554 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-26 16:46:17.557 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-26 16:46:17.559 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-26 16:46:17.562 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-26 16:46:17.664 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-26 16:46:17.728 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-26 16:46:17.759 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-26 16:46:17.782 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-26 16:46:17.945 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-26 16:46:17.995 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-26 16:46:18.011 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-26 16:46:18.022 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-26 16:46:18.065 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-26 16:46:18.089 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-26 16:46:18.104 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-26 16:46:18.149 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-26 16:46:18.171 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-26 16:46:18.174 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-26 16:46:18.367 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-26 16:46:18.371 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-26 16:46:18.375 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-26 16:46:18.398 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-26 16:46:18.403 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-26 16:46:18.416 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-26 16:46:18.424 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-26 16:46:18.446 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-26 16:46:18.451 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-26 16:46:18.455 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-26 16:46:18.463 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-26 16:46:18.468 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-26 16:46:18.472 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-26 16:46:18.512 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-26 16:46:18.536 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-26 16:46:18.538 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-26 16:46:18.541 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-26 16:46:18.553 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-26 16:46:18.557 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-26 16:46:18.591 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-26 16:46:18.702 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-26 16:46:19.224 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-26 16:46:19.225 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-26 16:46:19.490 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-26 16:46:20.529 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:46:20.590 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-workflow-service-v2 instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-workflow-service-v2,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-26 16:46:20.606 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-26 16:46:20.774 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:46:20.778 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-workflow-service-v2 initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-workflow-service-v2,current list of Servers=[***************:10019],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10019;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@7f66d549
2024-08-26 16:46:20.840 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:46:46.570 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:48:40.034 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m48s282ms34µs400ns).
2024-08-26 16:48:40.042 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-26 16:48:40.044 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-26 16:48:40.045 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-26 16:48:40.049 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-26 16:48:40.060 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-26 16:48:40.196 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-26 16:48:40.211 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-26 16:48:40.212 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-26 16:48:40.212 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-26 16:48:40.214 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-26 16:48:40.216 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-26 16:48:40.217 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-26 16:48:40.300 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:48:40.301 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:48:40.491 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-26 16:48:40.668 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-26 16:48:40.696 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-08-26 16:48:52.068 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-26 16:48:52.079 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-26 16:49:00.353 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-26 16:49:00.747 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-26 16:49:02.293 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-26 16:49:05.147 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-26 16:49:05.154 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-26 16:49:05.361 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 180ms. Found 0 repository interfaces.
2024-08-26 16:49:08.975 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-26 16:49:08.983 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-26 16:49:09.840 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-26 16:49:10.185 [main] INFO  org.reflections.Reflections - Reflections took 288 ms to scan 2 urls, producing 16 keys and 353 values 
2024-08-26 16:49:10.302 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-26 16:49:17.095 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:17.100 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:17.101 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:17.192 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:17.385 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:17.474 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:17.537 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:18.438 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:18.469 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:18.722 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:20.323 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-26 16:49:20.717 [redisson-netty-5-12] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-26 16:49:20.878 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-26 16:49:20.959 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-26 16:49:20.959 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-26 16:49:21.463 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:27.297 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:49:27.298 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:49:27.315 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:49:27.316 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:49:29.679 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:29.743 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:29.802 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:31.047 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:31.106 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:31.182 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:34.774 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7d9587b3[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-26 16:49:34.776 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5ce50c82[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$36cb2677#prepareRecentTask]
2024-08-26 16:49:34.846 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:34.862 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:34.889 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:34.920 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:34.938 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:34.950 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:34.992 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-26 16:49:35.019 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-26 16:49:35.034 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-26 16:49:35.054 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-26 16:49:35.444 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-26 16:49:35.900 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-26 16:49:36.275 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-26 16:49:36.471 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-26 16:49:36.475 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-26 16:49:36.477 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-26 16:49:36.481 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-26 16:49:36.485 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-26 16:49:36.488 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-26 16:49:36.491 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-26 16:49:36.494 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-26 16:49:36.607 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-26 16:49:36.666 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-26 16:49:36.691 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-26 16:49:36.711 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-26 16:49:36.855 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-26 16:49:36.899 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-26 16:49:36.912 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-26 16:49:36.924 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-26 16:49:36.971 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-26 16:49:37.000 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-26 16:49:37.014 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-26 16:49:37.064 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-26 16:49:37.087 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-26 16:49:37.089 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-26 16:49:37.280 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-26 16:49:37.284 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-26 16:49:37.290 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-26 16:49:37.304 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-26 16:49:37.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-26 16:49:37.316 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-26 16:49:37.324 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-26 16:49:37.343 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-26 16:49:37.347 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-26 16:49:37.352 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-26 16:49:37.356 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-26 16:49:37.359 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-26 16:49:37.364 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-26 16:49:37.402 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-26 16:49:37.426 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-26 16:49:37.428 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-26 16:49:37.430 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-26 16:49:37.442 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-26 16:49:37.448 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-26 16:49:37.485 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-26 16:49:37.645 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-26 16:49:38.035 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-26 16:49:38.036 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-26 16:49:38.227 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-26 16:49:38.959 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:49:39.031 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-workflow-service-v2 instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-workflow-service-v2,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-26 16:49:39.054 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-26 16:49:39.265 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:49:39.269 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-workflow-service-v2 initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-workflow-service-v2,current list of Servers=[***************:10019],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10019;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@2f38b35e
2024-08-26 16:49:39.327 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:49:42.970 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:49:51.420 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-business-config-center.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:49:51.454 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-business-config-center instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-business-config-center,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-26 16:49:51.463 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-26 16:49:51.844 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-business-config-center.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:49:51.851 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-business-config-center initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-business-config-center,current list of Servers=[***************:10003],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10003;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@16463f2
2024-08-26 16:49:51.952 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-26 16:50:04.397 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-business-config-center.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-26 16:50:04.590 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-26 16:50:04.590 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-26 16:50:04.590 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-26 16:50:04.591 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-26 16:50:04.596 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-26 16:50:04.608 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-26 16:50:04.616 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-26 16:50:04.616 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-26 16:50:04.617 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-26 16:50:04.618 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-26 16:50:04.620 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-26 16:50:04.621 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-26 16:50:04.673 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-26 16:50:04.674 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-26 16:50:04.800 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-26 16:50:04.954 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-26 16:50:04.968 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
