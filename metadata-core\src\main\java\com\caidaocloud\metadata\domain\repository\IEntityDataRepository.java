package com.caidaocloud.metadata.domain.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.enums.IndicateType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SpecifiedMultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.metadata.domain.entity.EntityData;
import com.caidaocloud.metadata.domain.entity.EntityRelationDef;
import com.googlecode.totallylazy.Option;

import java.util.List;
import java.util.Map;

public interface IEntityDataRepository  extends IBaseRepository<EntityData> {

    PageResult<EntityData> page(String identifier, DataFilter filter, int pageNo, int pageSize, List<String> relateList, List<String> specifyProperties, boolean group, String orderBy, long time, DataQueryDto query);

    void delete(String identifier, String id);

    long countBids(String identifier, List<String> bidList, long time);

    Option<EntityData> load(String identifier, String bid, long time, List<String> relateList, DataQueryDto query);

	void update(EntityData entityData, List<EntityRelationDef> relationDefs);

	List<EntityData> loadAll(String identifier, String bid);

    List<EntityData> range(String identifier, String bid, long startTime, long endTime);

    void batchUpdate(String identifier, DataFilter filter, EntityData entity);

    void batchDelete(String identifier, DataFilter filter);

    void batchInsert(String identifier, List<EntityData> dataList);

    long count(String identifier, DataFilter filter,List<String> relateList, boolean group, long time);

    PageResult<EntityData> max(String identifier, String filter, String maxProperty, List<String> by, int pageSize, int pageNo, long queryTime);

    List<Map<String, Object>> countByGroup(String identifier, DataFilter filter, List<String> by, long queryTime);

    PageResult<List<EntityData>> join(DataJoin dataJoin);

    String searchableEncrypt(String str);

	List<EmpSimple> loadByCondition(SpecifiedMultiDataFilter filter, long queryTime);

    List<Map<String, Object>> indicateByGroup(String identifier, DataFilter filter, List<String> by, long queryTime, IndicateType indicateType, String indicateProperty);
}
