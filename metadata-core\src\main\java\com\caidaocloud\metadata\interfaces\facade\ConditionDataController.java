package com.caidaocloud.metadata.interfaces.facade;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SpecifiedMultiDataFilter;
import com.caidaocloud.metadata.application.service.ConditionDataService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/${service.name.short:hrpaas}/v1/data/conditon")
public class ConditionDataController {
    @Autowired
    private ConditionDataService entityDataService;

    @PostMapping("load")
    public Result<List<EmpSimple>> loadByCondition(@RequestBody DataQueryDto query, @RequestParam long queryTime) {
        return Result.ok(entityDataService.loadByCondition((SpecifiedMultiDataFilter) DataFilter.fromJsonString(query.getFilter()), queryTime));
    }
}
        