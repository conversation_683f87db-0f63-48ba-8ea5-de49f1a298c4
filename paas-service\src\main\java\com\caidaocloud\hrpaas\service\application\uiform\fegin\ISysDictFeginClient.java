package com.caidaocloud.hrpaas.service.application.uiform.fegin;

import com.caidaocloud.hrpaas.service.application.uiform.dto.SysDictDataSourceDto;
import com.caidaocloud.hrpaas.service.application.uiform.dto.SysDictRequestDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "${feign.rename.caidaocloud-business-config-center:caidaocloud-business-config-center}", fallback = SysDictFeginFallBack.class, configuration = FeignConfiguration.class)
public interface ISysDictFeginClient {
    @PostMapping("api/bcc/dict/getSystemParams")
    Result<SysDictDataSourceDto> postSysDictList(@RequestBody SysDictRequestDto dto);

    @GetMapping("api/bcc/dict/common/v1/dict/getEnableDictList")
    Result<List<SysDictDataSourceDto>> getSysDictList(@RequestParam("typeCode") String typeCode, @RequestParam(name = "belongModule") String belongModule);
}
