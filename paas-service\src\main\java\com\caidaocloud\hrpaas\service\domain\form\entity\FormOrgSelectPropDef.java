package com.caidaocloud.hrpaas.service.domain.form.entity;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IDictFeignClient;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.workflow.dto.WfMetaApproverDto;
import com.caidaocloud.workflow.enums.WfApproverFetchType;
import com.caidaocloud.workflow.enums.WfValueComponentEnum;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

@Data
@Slf4j
public class FormOrgSelectPropDef extends FormSelectPropDef {


    @Override
    public List<WfMetaApproverDto> buildApproverDto(String formId, String funCode, String tenantId) {
        List<DictSimple> data = SpringUtil.getBean(IDictFeignClient.class)
                .getEnableDictList("OrganizationalRole", "M_ORG").getData();
        List<WfMetaApproverDto> list = Lists.list(orgApprover(formId, funCode, tenantId,"leader","责任人"));
        for (DictSimple dict : data) {
            list.add(orgApprover(formId, funCode, tenantId, dict.getValue(), dict.getText()));
        }
        return list;
    }

    private WfMetaApproverDto orgApprover(String formId, String funCode, String tenantId, String code, String name) {
        String approverCode = String.format("approver_%s_%s_%s", getProperty(), formId, code);
        String approverName = String.format("表单_%s_%s", getName(), name);
        var wfMetaApproverDto = new WfMetaApproverDto(approverName, approverCode,
                tenantId, "caidaocloud-hr-service",
                String.format("/api/hr/org/v1/workflow/approver/%s", code),
                WfApproverFetchType.RELATIVE_PATH,
                WfValueComponentEnum.NONE,
                Lists.list(),
                "");
        wfMetaApproverDto.setFunCode(funCode);
        return wfMetaApproverDto;
    }
}
