package com.caidaocloud.hrpaas.metadata.sdk.feign;

import com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.TxFeignConfiguration;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "caidaocloud-masterdata-service",
        fallback = MdMetadataFeignClientFallBack.class,
        configuration = {FeignConfiguration.class, TxFeignConfiguration.class},
        contextId = "mdMetadataFeign", qualifier = "mdMetadataFeign")
public interface IMdMetadataFeign {

    @GetMapping("/api/masterdata/v1/metadata")
    Result one(@RequestParam("identifier") String identifier);
}
