package com.caidaocloud.metadata.domain.factory;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.googlecode.totallylazy.Maps;

/**
 *
 * <AUTHOR>
 * @date 2024/8/14
 */
public class PropertyDefFactory {
	public static PropertyDef createPropertyDef(String property, String name, PropertyDataType dataType) {
		PropertyDef propertyDef = new PropertyDef();
		propertyDef.setProperty(property);
		propertyDef.setName(name);
		propertyDef.setI18nName(Maps.map("default", name));
		propertyDef.setRequired(false);
		propertyDef.setUnique(false);
		propertyDef.setExpEnable(false);
		propertyDef.setDataType(dataType);
		propertyDef.setDefaultValue(null);
		return propertyDef;
	}
}
