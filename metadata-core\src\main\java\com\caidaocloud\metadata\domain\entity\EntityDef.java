package com.caidaocloud.metadata.domain.entity;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.DefStatus;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.util.I18nUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.metadata.domain.dto.EntityDefSearchDto;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.caidaocloud.metadata.domain.repository.IEntityDefRepository;
import com.caidaocloud.metadata.infrastructure.repository.utils.EncryptionTool;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

@Data
@Slf4j
public class EntityDef extends BaseEntity<IEntityDefRepository> {

    private String identifier;

    private boolean builtIn = false;

    private String name;

    private Map<String, String> i18nName;

    private String owner;

    private String parent = "entity.common.BaseModel";

    private boolean tree = false;

    private String label;

    private boolean timelineEnabled = true;

    private long createTime;

    private String createBy;

    private long updateTime;

    private String updateBy;

    private DefStatus status;
    //todo not supported for v1
    private List<String> mainKey = Lists.newArrayList();

    private List<PropertyDef> standardProperties = Lists.newArrayList();

    private List<PropertyDef> customProperties = Lists.newArrayList();

    private List<EntityRelationDef> relationProperties = Lists.newArrayList();

    public static void addIndex(String identifier, List<String> properties) {
        ((IEntityDefRepository)repository(EntityDef.class)).addIndex(identifier, properties);
    }

    public void create(DefChannel defChannel){
        if(load(identifier).isDefined()){
            throw new ServerException("全限定名已存在");
        }
        this.status = DefStatus.ENABLED;
        if(!this.identifier.startsWith("entity." + owner + ".")){
            throw new ServerException("全限定名与owner不匹配");
        }
        this.builtIn = defChannel.equals(DefChannel.SCRIPT);
        this.checkBuiltInAndChannel(defChannel, true,
                !standardProperties.isEmpty(), !customProperties.isEmpty());
        val insertProps = Sequences.sequence(standardProperties).join(customProperties);
        insertProps.forEach(insert->insert.validateWhenModelInit());
        if(null == parent){
            parent = "entity.common.BaseModel";
        }
        load(parent).getOrThrow(new ServerException("父模型不存在"));
        this.checkConflict(insertProps, true, true, true);
        initCreateInfo(null, defChannel);
        tree = treeOrNot();
        this.insert();
    }

    public void update(DefChannel defChannel){
        val existDef = load(identifier).getOrThrow(new ServerException("模型不存在"));
        existDef.checkDefStatus();
        this.status = DefStatus.ENABLED;
        existDef.checkBuiltInAndChannel(defChannel, true, false,false);
        this.builtIn = existDef.builtIn;
        this.owner = existDef.owner;
        if(!customProperties.isEmpty() || !standardProperties.isEmpty()){
            throw new ServerException("属性维护请勿使用模型更改接口");
        }
        this.standardProperties = existDef.standardProperties;
        this.customProperties = existDef.customProperties;
        if(null == parent){
            parent = "entity.common.BaseModel";
        }
        if(!StringUtils.equals(this.parent, existDef.parent)){
            if(!"entity.common.BaseModel".equals(existDef.parent)){
                throw new ServerException("不允许修改父模型");
            }
            load(parent).getOrThrow(new ServerException("父模型不存在"));
        }
        initCreateInfo(existDef, defChannel);
        tree = treeOrNot();
        this.update();
    }

    private void initCreateInfo(EntityDef existDef, DefChannel channel){
        if(existDef == null){
            this.createTime = System.currentTimeMillis();
            if(channel.equals(DefChannel.SCRIPT)){
                this.createBy = null;
                this.updateBy = null;
            }else{
                val user = SecurityUserUtil.getSecurityUserInfo();
                this.createBy = String.valueOf(user.getUserId());
                this.updateBy = String.valueOf(user.getUserId());
            }
        }else{
            this.createTime = existDef.createTime;
            this.createBy = existDef.createBy;
            if(channel.equals(DefChannel.SCRIPT)){
                this.updateBy = null;
            }else{
                val user = SecurityUserUtil.getSecurityUserInfo();
                this.updateBy = String.valueOf(user.getUserId());
            }
        }
        this.updateTime = System.currentTimeMillis();

    }

    public static void insertStandardProps(String identifier, Sequence<PropertyDef> props,
                                           DefChannel defChannel){
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).insertProps(
                props, defChannel, true);
    }

    public static void updateStandardProps(String identifier, Sequence<Pair<String, PropertyDef>> props,
                                           DefChannel defChannel){
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).updateProps(
                props, defChannel, true);
    }

    public static void removeStandardProps(String identifier, Sequence<String> props,
                                     DefChannel defChannel){
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).removeProps(props,defChannel,true);
    }

    public static void recoverStandardProps(String identifier, Sequence<Pair<String, PropertyDefaultValue>> propsWithDefaultValue,
                                           DefChannel defChannel){
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).recoverProps(propsWithDefaultValue,defChannel, true);
    }

    public static void physicalDeleteStandardProps(String identifier, Sequence<String> props,
                                             DefChannel defChannel) {
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).physicalDeleteProps(props, defChannel, true);
    }

    public static void insertCustomProps(String identifier, Sequence<PropertyDef> props,
                                           DefChannel defChannel){
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).insertProps(
                props, defChannel, false);
    }

    public static void saveCustomProps(String identifier, Sequence<PropertyDef> props, DefChannel defChannel){
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).saveProps(props, defChannel, false);
    }

    public static void saveStandardProps(String identifier, Sequence<PropertyDef> props, DefChannel defChannel){
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).saveProps(props, defChannel, true);
    }

    public static void updateCustomProps(String identifier, Sequence<Pair<String, PropertyDef>> props,
                                           DefChannel defChannel){
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).updateProps(
                props, defChannel, false);
    }

    public static void removeCustomProps(String identifier, Sequence<String> props,
                                           DefChannel defChannel){
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).removeProps(props, defChannel,false);
    }

    public static void recoverCustomProps(String identifier, Sequence<Pair<String, PropertyDefaultValue>> propsWithDefaultValue,
                                            DefChannel defChannel){
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).recoverProps(propsWithDefaultValue,defChannel, false);
    }

    public static void physicalDeleteCustomProps(String identifier, Sequence<String> props,
                                                   DefChannel defChannel) {
        EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).physicalDeleteProps(props, defChannel, false);
    }

    private void insertProps(Sequence<PropertyDef> props,
                                    DefChannel defChannel, boolean isStandard) {
        this.checkBuiltInAndChannel(defChannel,false, isStandard, !isStandard);
        this.checkDefStatus();
        this.checkConflict(props, false, false, false);
        props.forEach(insert->insert.validateInsert());
        if(isStandard){
            this.standardProperties.addAll(props);
        }else{
            this.customProperties.addAll(props);
        }
        initCreateInfo(this, defChannel);
        props.stream()
                .filter(it->PropertyDataType.Data_Table.equals(it.getDataType())).forEach(propertyDef -> {
            val slaveIdentifier = identifier + "_" + propertyDef.getProperty();
            val slaveDef =new EntityDef();
            slaveDef.setIdentifier(slaveIdentifier);
            slaveDef.setName(name + "_" + propertyDef.getName());
            slaveDef.setI18nName(I18nUtil.concat(name, propertyDef.getName(),
                    i18nName, propertyDef.getI18nName(), "_" ));
            slaveDef.setTimelineEnabled(timelineEnabled);
            slaveDef.setOwner(owner);
            propertyDef.appendMasterBidSlaveProperty();
            if(defChannel.equals(DefChannel.SCRIPT)){
                slaveDef.setStandardProperties(propertyDef.getSlaveProperties());
            }else{
                slaveDef.setCustomProperties(propertyDef.getSlaveProperties());
            }
            if(slaveDef.fetchProperties().stream().filter(slaveProperty->!com.googlecode.totallylazy.Lists.list(PropertyDataType.Integer,
                    PropertyDataType.Number, PropertyDataType.String, PropertyDataType.Timestamp, PropertyDataType.Enum).contains(slaveProperty.getDataType())).findFirst().isPresent()){
                throw new ServerException("unsupported slave model property type");
            }
            val slaveDefExisted = EntityDef.load(slaveIdentifier);
            if(slaveDefExisted.isDefined()){
                slaveDef.update(defChannel);
            }else{
                slaveDef.create(defChannel);
            }
        });
        this.update();
    }

    private void saveProps(Sequence<PropertyDef> props, DefChannel defChannel, boolean isStandard) {
        Option<EntityDef> dbData = load(this.identifier);
        if(dbData.isEmpty()){
            // db 中不存在数据，则全部为新增
            insertProps(props, defChannel, isStandard);
            return;
        }

        EntityDef dbEntityDef = dbData.get();
        List<PropertyDef> dbCusProps = dbEntityDef.getCustomProperties();
        if(isStandard){
            dbCusProps = dbEntityDef.getStandardProperties();
        }
        if(null == dbCusProps || dbCusProps.isEmpty()){
            // db 中不存在数据，则全部为新增
            insertProps(props, defChannel, isStandard);
            return;
        }

        List<String> deleteList = new ArrayList<>(props.size());
        dbCusProps.forEach(dbCusProp -> {
            deleteList.add(dbCusProp.getProperty());
        });
        List<Pair<String, PropertyDef>> updateList = new ArrayList<>(props.size());
        List<PropertyDef> insertList = new ArrayList<>(props.size());
        AtomicBoolean insertFlag = new AtomicBoolean(true);
        Map<String, String> fieldMap = new HashMap<>(props.size());
        Map<String, String> fieldNameMap = new HashMap<>(props.size());
        List<PropertyDef> finalDbCusProps = dbCusProps;
        props.forEach(propertyDef -> {
            String fieldProp = propertyDef.getProperty();
            String fieldname = StringUtils.isNotEmpty(propertyDef.getName())?propertyDef.getName():propertyDef.getI18nName().get("default");
            if(StringUtil.isEmpty(fieldProp)){
                throw new ServerException("字段不能为空");
            }

            if(fieldMap.containsKey(fieldProp) || fieldNameMap.containsKey(fieldname)){
                throw new ServerException("属性重复");
            }

            fieldMap.put(fieldProp, fieldProp);
            fieldNameMap.put(propertyDef.getName(), propertyDef.getName());

            insertFlag.set(true);
            finalDbCusProps.forEach(dbCusProp -> {
                if(fieldProp.equals(dbCusProp.getProperty()) || fieldname.equals(dbCusProp.getName())){
                    // db 中存在此字段，则 update
                    updateList.add(Pair.pair(dbCusProp.getProperty(), propertyDef));
                    insertFlag.set(false);

                    if(deleteList.contains(dbCusProp.getProperty())){
                        deleteList.remove(dbCusProp.getProperty());
                    }
                }
            });

            if(insertFlag.get()){
                // 如果未匹配到，则代表是新增的字段，进行 insert
                insertList.add(propertyDef);
            }

            if(deleteList.contains(fieldProp)){
                deleteList.remove(fieldProp);
            }
        });

        if(!insertList.isEmpty()){
            this.insertProps(Sequences.sequence(insertList), defChannel, isStandard);
        }

        if(!updateList.isEmpty()){
            this.updateProps(Sequences.sequence(updateList), defChannel, isStandard);
        }

        if(!deleteList.isEmpty()){
            // 物理删除字段
            this.physicalDeleteProps(Sequences.sequence(deleteList), defChannel, isStandard);
        }
    }

    private void updateProps(Sequence<Pair<String, PropertyDef>> props, DefChannel defChannel, boolean isStandard) {
        this.checkBuiltInAndChannel(defChannel,false, isStandard, !isStandard);
        this.checkDefStatus();
        this.checkConflict(props.map(prop->prop.getValue()), isStandard, !isStandard, false);
        var conflict = props.find(update ->
            update.getValue().conflict(update.getKey(), Sequences.sequence(isStandard ? standardProperties : customProperties))
        );

        if(conflict.isDefined()){
            throw new ServerException("属性重复");
        }

        props.forEach(
            update-> {
                val toBeReplaced = Sequences.sequence(isStandard ? standardProperties : customProperties);
                if(isStandard){
                    this.standardProperties = update.getValue().replace(update.getKey(), toBeReplaced).toList();
                }else{
                    this.customProperties = update.getValue().replace(update.getKey(), toBeReplaced).toList();
                }
            }
        );

        initCreateInfo(this, defChannel);
        props.stream()
                .filter(it->PropertyDataType.Data_Table.equals(it.second().getDataType())).forEach(propertyDef -> {
            val slaveIdentifier = identifier + "_" + propertyDef.second().getProperty();
            val slaveDef =new EntityDef();
            slaveDef.setIdentifier(slaveIdentifier);

            slaveDef.setName(name + "_" + propertyDef.second().getName());
            slaveDef.setI18nName(I18nUtil.concat(name, propertyDef.second().getName(),
                    i18nName, propertyDef.second().getI18nName(), "_" ));
            slaveDef.setTimelineEnabled(timelineEnabled);
            slaveDef.setOwner(owner);
            propertyDef.second().appendMasterBidSlaveProperty();
            if(defChannel.equals(DefChannel.SCRIPT)){
                slaveDef.setStandardProperties(propertyDef.second().getSlaveProperties());
            }else{
                slaveDef.setCustomProperties(propertyDef.second().getSlaveProperties());
            }
            val slaveDefExisted = EntityDef.load(slaveIdentifier);
            if(slaveDefExisted.isDefined()){
                slaveDef.update(defChannel);
            }else{
                slaveDef.create(defChannel);
            }
        });
        this.update();
    }

    private void removeProps(Sequence<String> props, DefChannel defChannel, boolean isStandard) {
        this.checkBuiltInAndChannel(defChannel,false, isStandard, !isStandard);
        this.checkDefStatus();
        props.forEach(remove->{
            val prop = Sequences.sequence(isStandard ? standardProperties : customProperties)
                    .find(exist->remove.equals(exist.getProperty()) && DefStatus.ENABLED.equals(exist.getStatus()))
                    .getOrThrow(new ServerException("属性不存在"));
            prop.setStatus(DefStatus.DISABLED);
        });

        initCreateInfo(this, defChannel);
        this.update();
    }

    private void recoverProps(Sequence<Pair<String, PropertyDefaultValue>> propsWithDefaultValue,
                                     DefChannel defChannel, boolean isStandard) {
        this.checkBuiltInAndChannel(defChannel,false, isStandard,!isStandard);
        this.checkDefStatus();
        propsWithDefaultValue.forEach(recover->{
                    val prop = Sequences.sequence(isStandard?standardProperties:customProperties)
                            .find(toBeRecovered->recover.first().equals(toBeRecovered.getProperty()) && DefStatus.DISABLED.equals(toBeRecovered.getStatus()))
                            .getOrThrow(new ServerException("属性不存在"));
                    if(null != recover.second()){
                        prop.setDefaultValue(recover.second());
                    }
                    prop.validateRecover();
                }
        );
        initCreateInfo(this, defChannel);
        this.update();
    }

    private void physicalDeleteProps(Sequence<String> props,
                                     DefChannel defChannel, boolean isStandard) {
        this.checkBuiltInAndChannel(defChannel,false, isStandard,!isStandard);
        this.checkDefStatus();
        val originalProps = isStandard ? standardProperties : customProperties;
        val resultProps = Sequences.sequence(originalProps).filter(check ->
                props.find(delete->
                        delete.equals(check.getProperty())
                ).isEmpty()
        ).toList();
        if(originalProps.size() - resultProps.size() != props.size()){
            throw new ServerException("删除属性不存在或有重复");
        }
        if(isStandard){
            this.standardProperties = resultProps;
        }else{
            this.customProperties = resultProps;
        }
        //todo delete data
        initCreateInfo(this, defChannel);
        this.update();
    }

    public static Option<EntityDef> load(String identifier) {
        return ((IEntityDefRepository)repository(EntityDef.class)).loadByIdentifier(identifier);
    }

    public boolean treeOrNot(){
        if(this.getStandardProperties().stream().filter(propDef-> PropertyDataType.PID.equals(propDef.getDataType())).findFirst().isPresent()){
            return true;
        }
        if(this.getCustomProperties().stream().filter(propDef-> PropertyDataType.PID.equals(propDef.getDataType())).findFirst().isPresent()){
            return true;
        }
        val hierarchy = this.loadHierarchyProperties();
        if(hierarchy.first().find(propDef-> PropertyDataType.PID.equals(propDef.getDataType())).isDefined()){
            return true;
        }
        if(hierarchy.second().find(propDef-> PropertyDataType.PID.equals(propDef.getDataType())).isDefined()){
            return true;
        }
        return false;
    }

    public static boolean treeOrNot(String identifier){
        val entityDef = load(identifier).getOrThrow(new ServerException("模型不存在"));
        return entityDef.treeOrNot();
    }

    public static PageResult<EntityDef> page(String keywords, int pageNo, int pageSize){
        return ((IEntityDefRepository)repository(EntityDef.class)).page(keywords, pageNo, pageSize);
    }

    public static Sequence<EntityDef> list(EntityDefSearchDto query, int maxCount){
        return ((IEntityDefRepository)repository(EntityDef.class)).list(query, maxCount);
    }

    public static List<EntityDef> getListByIdentifier(List<String> identifierList, int maxCount){
        return ((IEntityDefRepository)repository(EntityDef.class)).getListByIdentifier(identifierList, maxCount);
    }

    public static Sequence<EntityRelationDef> loadRelationsByIdentifier(String identifier){
        return EntityRelationDef.loadByIdentifier(identifier);
    }

    public static void addRelation(EntityRelationDef relation, DefChannel defChannel){
        val entity = load(relation.getIdentifier()).getOrThrow(new ServerException("模型不存在"));
        val related = load(relation.getRelatedIdentifier()).getOrThrow(new ServerException("关联模型不存在"));
        if(DefChannel.SCRIPT.equals(defChannel) && !(entity.builtIn && related.builtIn)){
            throw new ServerException("自定义模型关联不允许通过script维护");
        }

        relation.initInsert(defChannel);
        List<EntityRelationDef> relationProperties = entity.getRelationProperties();
        relationProperties.add(relation);
        entity.checkConflict(relation);
        entity.initCreateInfo(entity, defChannel);
        entity.update();
    }

    public static void deleteRelation(String identifier, String property, DefChannel defChannel){
        val entity = load(identifier).getOrThrow(new ServerException("模型不存在"));
        val delete = loadRelationsByIdentifier(identifier).find(prop->property.equals(prop.getProperty())).getOrThrow(new ServerException("关联关系不存在"));
        delete.checkDelete(defChannel);
        List<EntityRelationDef> relationProperties = entity.getRelationProperties();
        Sequences.sequence(relationProperties).removeIf(e -> property.equals(e.getProperty()));
        entity.initCreateInfo(entity, defChannel);
        entity.setRelationProperties(relationProperties);
        entity.update();
    }

    public Pair<Sequence<PropertyDef>, Sequence<PropertyDef>> loadHierarchyProperties(){
        var parent = this.parent;
        Sequence<PropertyDef> standardProperties = Sequences.sequence();
        Sequence<PropertyDef> customProperties = Sequences.sequence();
        while(null != parent){
            EntityDef parentDef = load(parent).getOrThrow(new ServerException("父模型不存在"));
            standardProperties = standardProperties.join(parentDef.standardProperties);
            customProperties = customProperties.join(parentDef.customProperties);
            parent = parentDef.parent;
        }
        return Pair.pair(standardProperties, customProperties);
    }

    private void checkDefStatus(){
        if(DefStatus.DISABLED.equals(this.status)){
            throw new ServerException("模型已禁用");
        }
    }

    private void checkBuiltInAndChannel(DefChannel defChannel, boolean modifyBasicInfo, boolean modifyStandardProps, boolean modifyCustomProps){
        if(builtIn){
            if (DefChannel.SCRIPT.equals(defChannel)) {
                if(modifyCustomProps){
                    throw new ServerException("模型自定义属性不允许通过script维护");
                }
            }else{
                if(modifyBasicInfo){
                    throw new ServerException("标准模型只允许通过script维护");
                }
                if(modifyStandardProps){
                    //throw new ServerException("标准属性只允许通过script维护");
                }
            }
        }else{
            if (DefChannel.SCRIPT.equals(defChannel)) {
                throw new ServerException("自定义模型不允许通过script维护");
            }else{
                if(modifyStandardProps){
                    //throw new ServerException("标准属性只允许通过script维护");
                }
            }
        }
    }

    private void checkConflict(EntityRelationDef relation){
        val parentProperties = this.loadHierarchyProperties();
        var conflict = relation.conflictProperty(this.standardProperties, this.customProperties,
                parentProperties.first(),parentProperties.second());
        if(conflict){
            throw new ServerException("属性重复");
        }
        val relations = loadRelationsByIdentifier(identifier);
        conflict = relation.conflict(relations);
        if(conflict){
            throw new ServerException("关联重复");
        }
    }

    private void checkConflict(Sequence<PropertyDef> props, boolean skipStandard, boolean skipCustom, boolean skipRelation){
        val parentProperties = loadHierarchyProperties();
        val exist = Sequences.join(
                parentProperties.first(), parentProperties.second(),
                skipStandard?Sequences.empty():standardProperties,
                skipCustom?Sequences.empty():customProperties
        );
        var conflict = props.find(prop ->
                prop.conflict(exist)
        );
        if(conflict.isDefined()){
            throw new ServerException("属性重复");
        }
        if(skipRelation){
            return;
        }
        val relations = loadRelationsByIdentifier(identifier);
        conflict = props.find(prop ->
                prop.conflictRelation(relations)
        );
        if(conflict.isDefined()){
            throw new ServerException("关联重复");
        }
    }

    public void delete(DefChannel defChannel) {
        checkBuiltInAndChannel(defChannel, true, false, false);
        checkRelated();
        repository().delete(this);
    }

    private void checkRelated(){
        val relatedCount = EntityRelationDef.checkEntityDefRelatedCount(this.identifier);
        if(relatedCount > 0){
            throw new ServerException("模型被关联，不可删除");
        }
    }

    public List<PropertyDef> fetchAllProperties() {
        val hierarchy = loadHierarchyProperties();
        return Sequences.sequence(this.standardProperties)
                .join(this.customProperties)
                .join(hierarchy.first())
                .join(hierarchy.second()).toList();
    }

    public List<PropertyDef> fetchProperties() {
        return Sequences.sequence(this.standardProperties)
                .join(this.customProperties).toList();
    }

    public EntityDef appendEventOn(Map<String, String> eventOn){
        eventOn.forEach((property, event)->{
            standardProperties.stream().filter(it->it.getProperty().equals(property)).forEach(it->{
                it.setOnEvent(event);
            });
            customProperties.stream().filter(it->it.getProperty().equals(property)).forEach(it->{
                it.setOnEvent(event);
            });
        });
        return this;
    }

    public void doExpProperty(EntityData data, Map<String, Object> record, List<String> specifyExpProperty, String encryptKey){
        Map<String, Object> map = convertData(record, encryptKey);
        Sequence<PropertyDef> sequence = Sequences.sequence(fetchAllProperties());
        if (!specifyExpProperty.isEmpty()) {
            sequence = sequence.filter(it -> specifyExpProperty.contains(it.getProperty()));
        }
        sequence.filter(PropertyDef::isExpEnable).forEach(expProp -> {
            Object value = expProp.getDataType() == PropertyDataType.Number || expProp.getDataType() == PropertyDataType.Timestamp ? 0 : "";
            try {
                value = AviatorEvaluator.execute(expProp.getExpression(), map);
            }
            catch (Exception e) {
                log.error("expression execute error, expression: {}", expProp.getExpression(), e);
            }
             data.addPropertyValue(expProp.getProperty(), String.valueOf(value), expProp.getDataType());
        });
    }

    private Map<String, Object> convertData(Map<String, Object> dataMap, String encryptKey) {
        dataMap.keySet().forEach(it->{
            Object value = dataMap.get(it);
            if(value != null && EncryptionTool.isSearchableEncrypted(value.toString())){
                dataMap.put(it, EncryptionTool.searchableDecrypt(value.toString(), encryptKey));
            }
        });
        Map<String, Object> map = new HashMap<>();
        Sequences.sequence(this.fetchAllProperties()).forEach(p -> {
            if (p.getDataType().isComponent() || "DateRangePicker".equals(p.getWidgetType())) {
                return;
            }
            String sk = SnakeCaseConvertor.toSnake(p.getProperty());
            Object value = dataMap.get(sk);
            switch (p.getDataType()) {
            case Timestamp:
                map.put(p.getProperty(), value == null || StringUtil.isBlank(value.toString()) ? null : Long.valueOf(String.valueOf(value)));
                break;
            case Number:
                map.put(p.getProperty(), value == null || StringUtil.isBlank(value.toString()) ? null : Double.valueOf(String.valueOf(value)));
                break;
            case String:
                map.put(p.getProperty(), value);
            default:
            }
        });
        return map;
    }
}
