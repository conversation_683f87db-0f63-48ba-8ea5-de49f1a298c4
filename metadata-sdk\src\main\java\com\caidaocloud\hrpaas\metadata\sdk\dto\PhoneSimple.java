package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 手机号+区号组件、电话号+区号组件
 * <AUTHOR>
 */
@Data
@DataComponent(dataType = PropertyDataType.Phone)
public class PhoneSimple implements ComponentPropertyValue{
    public final static String DEF_CODE = "+86", FORMAT_CODE = "%s/%s/%s", DEF_UN = "CN";
    private String code;
    private String value;
    // 国家简称
    private String un;

    public PhoneSimple() {
    }

    public PhoneSimple(String phone) {
        String[] splits = phone.split("/");
        this.value = splits[0];
        int len = splits.length;
        this.code = len > 1 ? splits[1] : "+86";
        this.un = len > 2 ? splits[2] : "CN";
    }

    /**
     * 存储 value 转换成对象
     */
    @Override
    public PropertyValue extractDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties) {
        return PhoneSimple.toDisplay(propDef, properties);
    }

    /**
     * PhoneSimple 对象转换成要持久化存储的 value
     */
    @Override
    public List<PropertyDataDto> extractPersist(DataSimple data, MetadataPropertyVo propDef) {
        return PhoneSimple.toPersist(data, propDef);
    }

    @Override
    public Map<String, Boolean> propertySuffixToWhetherLong() {
        return Maps.map(
                Sequences.sequence(
                        Pair.pair("", false)
                )
        );
    }

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {

    }

    @Override
    public String toText() {
        return value;
    }

    public static PhoneSimple toDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> propList){
        PhoneSimple phone = new PhoneSimple();
        String fieldProp = propDef.getProperty();
        if(null == propList || propList.isEmpty() || StringUtil.isEmpty(fieldProp)){
            return phone;
        }

        for (PropertyDataDto prop : propList) {
            if(null == prop || !prop.getProperty().equals(fieldProp)){
                continue;
            }

            String propValue = prop.getValue();
            if(StringUtil.isEmpty(propValue)){
                return phone;
            }

            phone.setValue(propValue);
            PhoneSimple.doValue(phone);
            return phone;
        }

        return phone;
    }

    public static void doValue(PhoneSimple phone){
        if(null == phone || StringUtil.isEmpty(phone.getValue())){
            return;
        }

        String [] splits = phone.getValue().split("/");
        phone.setValue(splits[0]);
        int len = splits.length;
        phone.setCode(len > 1 ? splits[1] : DEF_CODE);
        phone.setUn(len > 2 ? splits[2] : DEF_UN);
    }

    public static List<PropertyDataDto> toPersist(DataSimple data, MetadataPropertyVo propDef){
        String prop = propDef.getProperty();
        Map dataMap = JsonEnhanceUtil.toObject(data, Map.class);
        PhoneSimple phone = null;
        if(dataMap.containsKey(prop)){
            Object standardPropertyValue = dataMap.get(prop);
            if(null != standardPropertyValue){
                phone = JsonEnhanceUtil.toObject(standardPropertyValue, PhoneSimple.class);
                return PhoneSimple.toPropList(phone, prop);
            }

            return PhoneSimple.toPropList(null, prop);
        }

        // 获取属性中的字典字段
        PropertyValue customPropertyValue = data.getProperties().get(prop);
        if(null != customPropertyValue){
            if(customPropertyValue instanceof PhoneSimple){
                phone = ((PhoneSimple) customPropertyValue);
            } else{
                phone = new PhoneSimple();
                String phoneValue = ((SimplePropertyValue) customPropertyValue).getValue();
                phone.setValue(phoneValue);
                PhoneSimple.doValue(phone);
            }
            return PhoneSimple.toPropList(phone, prop);
        }

        return PhoneSimple.toPropList(null, prop);
    }

    private static List<PropertyDataDto> toPropList(PhoneSimple data, String prop){
        List<PropertyDataDto> result = Lists.newArrayList();
        PropertyDataDto dataDto = new PropertyDataDto();
        dataDto.setProperty(prop);
        dataDto.setDataType(PropertyDataType.Phone);
        if(null != data){
            String pValue = null == data.getValue() ? "" : data.getValue(),
                pCode = null == data.getCode() ? DEF_CODE : data.getCode(),
                pUn = null == data.getUn() ? DEF_UN : data.getUn();
            dataDto.setValue(String.format(FORMAT_CODE, pValue, pCode, pUn));
        }
        result.add(dataDto);
        return result;
    }
}
