package com.caidaocloud.hrpaas.service.application.form.feign;

import com.caidaocloud.hrpaas.service.application.form.dto.WfTaskApproveDTO;
import com.caidaocloud.hrpaas.service.application.form.dto.WfTaskRevokeDTO;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class WfRuntimeFeignFallback implements IWfRuntimeFeign{
    @Override
    public Result approve(WfTaskApproveDTO approve) {
        return Result.fail();
    }

    @Override
    public Result revoke(WfTaskRevokeDTO approve) {
        return Result.fail();
    }
}
