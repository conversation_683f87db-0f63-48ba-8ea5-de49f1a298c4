spring:
 profiles:
  active: local

caidaocloud:
  oss:
  durable: none
  sync:
    identifier: entity.hr.Company,entity.hr.CostCenter,entity.hr.JobGradeChannel,entity.hr.JobGrade,entity.hr.JobType,entity.hr.Job,entity.hr.Post,entity.hr.Workplace,entity.hr.Org,entity.hr.OrgReportExtend,entity.hr.CustomOrgRole,entity.hr.EmpConcurrentPost,entity.hr.<PERSON>p<PERSON><PERSON>ard,entity.hr.EmpBank,entity.hr.FamilyMember,entity.hr.EmpCertificate,entity.hr.EmpWorkInfo,entity.hr.EmpPrivateInfo,entity.hr.EmpEduInfo,entity.hr.EmpWorkOverview,entity.hr.Contract

msg:
 middleware:
  type: rabbitmq

rabbitmq:
 topics:
  - topic: DATA_MODIFICATION
    exchange: caidao.hrpaas
    routingKey: caidao.hrpaas.model.data.modification
    queue: caidao.hrpaas.model.data.modification
    exchangeType: DIRECT
    tenantIsolated: false
    consumersCount: 10
  - topic: RELATION_MODIFICATION
    exchange: caidao.hrpaas
    routingKey: caidao.hrpaas.model.relation.modification
    queue: caidao.hrpaas.model.relation.modification
    exchangeType: DIRECT
    tenantIsolated: false
    consumersCount: 10
  - topic: EMP_LEADER_CHANGE
    exchange: caidao.hrpaas
    routingKey: caidao.hrpaas.emp.leader.change
    queue: caidao.hrpaas.emp.leader.change
    exchangeType: DIRECT
    tenantIsolated: false
    consumersCount: 1
i18n:
  resource:
    path: i18n/hrpaas/message
