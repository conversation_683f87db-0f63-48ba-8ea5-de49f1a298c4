package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.domain.dynamic.entity.DynamicColumnConfig;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicPropertyDto;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Data
@TableName("dynamic_column_config")
public class DynamicColumnConfigPo {
    @TableId(type = IdType.INPUT)
    private Long id;
    private String code;
    private String name;
    private String propertiesStr;

    public static DynamicColumnConfigPo fromEntity(DynamicColumnConfig dynamicColumnConfig) {
        val config = FastjsonUtil.convertObject(dynamicColumnConfig, DynamicColumnConfigPo.class);
        config.setPropertiesStr(FastjsonUtil.toJson(dynamicColumnConfig.getProperties()));
        return config;
    }

    public static List<DynamicColumnConfig> toEntityList(List<DynamicColumnConfigPo> list) {
        return list.stream().map(dynamicColumnConfig->{
            val config = FastjsonUtil.convertObject(dynamicColumnConfig, DynamicColumnConfig.class);
            if(StringUtils.isNotEmpty(dynamicColumnConfig.propertiesStr)){
                config.setProperties(FastjsonUtil.toList(dynamicColumnConfig.propertiesStr, DynamicPropertyDto.class));
            }
            return config;
        }).collect(Collectors.toList());
    }

    public DynamicColumnConfig toEntity() {
        val config = FastjsonUtil.convertObject(this, DynamicColumnConfig.class);
        if(StringUtils.isNotEmpty(propertiesStr)){
            config.setProperties(FastjsonUtil.toList(propertiesStr, DynamicPropertyDto.class));
        }
        return config;
    }
}
