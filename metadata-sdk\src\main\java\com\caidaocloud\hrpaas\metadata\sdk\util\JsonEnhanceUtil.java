package com.caidaocloud.hrpaas.metadata.sdk.util;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;

import java.util.Collection;
import java.util.List;

public class JsonEnhanceUtil {

    public static <T> T toObject(Object obj, Class<T> clazz){
        return FastjsonUtil.toObject(FastjsonUtil.toJson(obj), clazz);
    }

    public static <T,S> Sequence<T> toObjects(Sequence<S> objs, Class<T> clazz){
        return Sequences.sequence(objs.map(obj->JsonEnhanceUtil.toObject(obj, clazz)).toList());
    }

    public static <T,S> List<T> toObjects(Collection<S> objs, Class<T> clazz){
        return Sequences.sequence(objs).map(obj->JsonEnhanceUtil.toObject(obj, clazz)).toList();
    }

    public static <T> List<T> toObjects(Object obj, Class<T> clazz){
        String json = FastjsonUtil.toJson(obj);
        return FastjsonUtil.toArrayList(json, clazz);
    }

    public static <T,S> PageResult<T> toPage(PageResult<S> page, Class<T> clazz){
        return new PageResult(
                toObjects(Sequences.sequence(page.getItems()), clazz).toList(),
                page.getPageNo(), page.getPageSize(), page.getTotal());
    }

    public static <T> T toPage(Object obj, TypeReference<T> typeReference){
        String json = FastjsonUtil.toJson(obj);
        return FastjsonUtil.toObject(json, typeReference);
    }
}
