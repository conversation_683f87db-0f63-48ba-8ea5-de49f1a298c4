package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import com.caidaocloud.hrpaas.service.application.rules.IRule;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@AllArgsConstructor
public class CheckRuleDto implements IRule {
    /**
     * 校验规则类型
     */
    private String ruleType;

    /**
     * 字段ID
     */
    private String fieldProp;

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 字段值
     */
    private String fieldValue;

    /**
     * 字段校验规则
     */
    private String fieldRule;

    /**
     * 字段校验提示
     */
    private String ruleTips;

    @Override
    public String type() {
        return this.ruleType;
    }

    @Override
    public String prop() {
        return this.fieldProp;
    }

    @Override
    public String name() {
        return this.fieldName;
    }

    @Override
    public String value() {
        return this.fieldValue;
    }

    @Override
    public void value(String dataValue) {
        this.fieldValue = dataValue;
    }

    @Override
    public String rule() {
        return this.fieldRule;
    }

    @Override
    public String tips() {
        return this.ruleTips;
    }
}
