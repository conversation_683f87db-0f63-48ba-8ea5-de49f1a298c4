package com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign;

import com.caidaocloud.hrpaas.metadata.sdk.dto.TransactionInfoDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "${feign.rename.caidaocloud-hr-paas-service:caidaocloud-hr-paas-service}",
		fallback = TransactionFeignFallback.class,
		configuration = {FeignConfiguration.class},
		contextId = "transactionFeign")
public interface ITransactionFeign {

	@PostMapping("/api/hrpaas/v1/transaction")
	Result<TransactionInfoDto> begin();

	@DeleteMapping("/api/hrpaas/v1/transaction/commit")
	Result commit(@RequestParam("transactionId") String transactionId);

	@DeleteMapping("/api/hrpaas/v1/transaction/rollback")
	Result rollback(@RequestParam("transactionId") String transactionId);
}
