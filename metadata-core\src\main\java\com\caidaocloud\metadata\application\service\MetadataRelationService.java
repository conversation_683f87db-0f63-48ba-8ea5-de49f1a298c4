package com.caidaocloud.metadata.application.service;

import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.EntityRelationDef;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.googlecode.totallylazy.Sequence;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MetadataRelationService {
    @Transactional(rollbackFor=Exception.class)
    public void addRelation(EntityRelationDef relation, DefChannel defChannel){
        EntityDef.addRelation(relation, defChannel);
    }
    @Transactional(rollbackFor=Exception.class)
    public void deleteRelation(String identifier, String property, DefChannel defChannel){
        EntityDef.deleteRelation(identifier, property, defChannel);
    }

    public Sequence<EntityRelationDef> loadRelationsByIdentifier(String identifier){
        return EntityDef.loadRelationsByIdentifier(identifier);
    }
}
