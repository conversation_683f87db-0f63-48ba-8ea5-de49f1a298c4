package com.caidaocloud.metadata.application.event.publish

import com.caidaocloud.hrpaas.metadata.sdk.event.AbstractInteriorEvent
import com.caidaocloud.metadata.domain.entity.EntityDataChange
import com.caidaocloud.metadata.domain.enums.OperationType

const val DATA_MODIFICATION = "DATA_MODIFICATION"

class EntityDataOperationEvent(
    val identifier: String,
    val targetId: String,
    val data: List<EntityDataChange>,
    val operation: OperationType,
    val startTime : Long,
    val eventTime : Long) : AbstractInteriorEvent(DATA_MODIFICATION) {
}