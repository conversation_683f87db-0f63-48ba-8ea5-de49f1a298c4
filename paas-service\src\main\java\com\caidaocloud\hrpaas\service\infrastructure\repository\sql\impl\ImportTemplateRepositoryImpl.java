package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.ImportTemplateDo;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.ImportTemplateRepository;
import org.springframework.stereotype.Repository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.ImportTemplatePo;
import com.caidaocloud.hrpaas.service.interfaces.dto.template.ImportTemplateDto;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Option;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class ImportTemplateRepositoryImpl implements ImportTemplateRepository {
    @Resource
    private ISessionService sessionService;

    @Autowired
    ImportTemplateMapper mapper;

    @Override
    public String insert(ImportTemplateDo importTemplateDo) {
        if (StringUtil.isEmpty(importTemplateDo.getTenantId())) {
            importTemplateDo.setTenantId(sessionService.getTenantId());
        }
        val po = ImportTemplatePo.fromEntity(ImportTemplatePo.class, importTemplateDo);
        mapper.insert(po);
        return po.getId().toString();
    }


    @Override
    public void update(ImportTemplateDo importTemplateDo) {
        if (StringUtil.isEmpty(importTemplateDo.getTenantId())) {
            importTemplateDo.setTenantId(sessionService.getTenantId());
        }
        ImportTemplateDo menu = loadById(importTemplateDo.getTenantId(),importTemplateDo.getId());
        if (StringUtil.isEmpty(menu.getId())) {
            throw new ServerException("更新失败！");
        }
        val po = ImportTemplatePo.fromEntity(ImportTemplatePo.class, importTemplateDo);
        mapper.updateById(po);

    }

    @Override
    public Option<ImportTemplateDo> load(String tenantId, String id) {
        LambdaQueryWrapper<ImportTemplatePo> queryWrapper = new QueryWrapper<ImportTemplatePo>().lambda();
        queryWrapper.eq(ImportTemplatePo::getTenantId, tenantId).eq(ImportTemplatePo::getId,id)
                .last("limit 1");
        ImportTemplatePo po = mapper.selectOne(queryWrapper);
        if (po == null) {
            return Option.none();
        }
        return Option.some(po.toEntity(ImportTemplateDo.class));
    }

    @Override
    public void delete(String id){
        mapper.delete(new LambdaQueryWrapper<ImportTemplatePo>()
                .in(ImportTemplatePo::getTenantId,
                        0, sessionService.getUserInfo().getBelongOrgId())
                .eq(ImportTemplatePo::getId, Long.valueOf(id)));
    }

    @Override
    public ImportTemplateDo loadByCode(String tenantId, String templateCode) {
        if (StringUtil.isEmpty(tenantId)) {
            tenantId = sessionService.getTenantId();
        }
        LambdaQueryWrapper<ImportTemplatePo> queryWrapper = new QueryWrapper<ImportTemplatePo>().lambda();
        queryWrapper.eq(ImportTemplatePo::getTenantId, tenantId).eq(ImportTemplatePo::getTemplateCode,templateCode)
                .last("limit 1");
        val po = mapper.selectOne(queryWrapper);
        if(po == null){
            throw new ServerException("数据不存在！");
        }
        return po.toEntity(ImportTemplateDo.class);
    }

    @Override
    public ImportTemplateDo loadById(String tenantId,String id) {
        if (StringUtil.isEmpty(tenantId)) {
            tenantId = sessionService.getTenantId();
        }
        LambdaQueryWrapper<ImportTemplatePo> queryWrapper = new QueryWrapper<ImportTemplatePo>().lambda();
        queryWrapper.eq(ImportTemplatePo::getTenantId, tenantId).eq(ImportTemplatePo::getId,id)
                .last("limit 1");
        val po = mapper.selectOne(queryWrapper);
        if(po == null){
            throw new ServerException("数据不存在！");
        }
        return po.toEntity(ImportTemplateDo.class);
    }

    @Override
    public List<ImportTemplateDo> getList(ImportTemplateDto importTemplateDto, int pageNo, int pageSize) {
        QueryWrapper<ImportTemplatePo> queryWrapper = new QueryWrapper<>();
        String tenantId = importTemplateDto.getTenantId();
        if(StringUtil.isEmpty(tenantId)){
            tenantId = sessionService.getTenantId();
        }
        queryWrapper.eq("tenant_id", tenantId)
                .eq("deleted", 0);
        val poList = mapper.selectList(queryWrapper);
        return ImportTemplatePo.toEntityList(ImportTemplateDo.class, poList);
    }
}
