package com.caidaocloud.metadata.application.service;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropAndRelationUpdateDto;
import com.caidaocloud.hrpaas.metadata.sdk.util.I18nUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.metadata.application.dto.MetadataIndexDto;
import com.caidaocloud.metadata.application.dto.MetadataQueryModelDataDto;
import com.caidaocloud.metadata.application.event.factory.EventFactory;
import com.caidaocloud.metadata.domain.dto.EntityDefSearchDto;
import com.caidaocloud.metadata.domain.entity.EntityData;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.EntityRelationDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.LanguageUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MetadataService {
    @Resource
    private ISessionService sessionService;

    @Resource
    private MetadataSyncService metadataSyncService;

    @Transactional(rollbackFor=Exception.class)
    public void create(EntityDef def, DefChannel defChannel){
        def.create(defChannel);
        def.fetchProperties().stream()
                .filter(it->PropertyDataType.Data_Table.equals(it.getDataType())).forEach(propertyDef -> {
                    val slaveIdentifier = def.getIdentifier() + "_" + propertyDef.getProperty();
                    val slaveDef =new EntityDef();
                    slaveDef.setIdentifier(slaveIdentifier);
                    slaveDef.setName(def.getName() + "_" + propertyDef.getName());
                    slaveDef.setI18nName(I18nUtil.concat(def.getName(), propertyDef.getName(),
                    def.getI18nName(), propertyDef.getI18nName(), "_" ));
                    slaveDef.setTimelineEnabled(def.isTimelineEnabled());
                    slaveDef.setOwner(def.getOwner());
                    if(slaveDef.fetchProperties().stream().filter(slaveProperty->!Lists.list(PropertyDataType.Integer,
                            PropertyDataType.Number, PropertyDataType.String, PropertyDataType.Timestamp, PropertyDataType.Enum).contains(slaveProperty.getDataType())).findFirst().isPresent()){
                        throw new ServerException("unsupported slave model property type");
                    }
                    propertyDef.appendMasterBidSlaveProperty();
                    if(defChannel.equals(DefChannel.SCRIPT)){
                        slaveDef.setStandardProperties(propertyDef.getSlaveProperties());
                    }else{
                        slaveDef.setCustomProperties(propertyDef.getSlaveProperties());
                    }
                    slaveDef.create(defChannel);
        });
    }
    @Transactional(rollbackFor=Exception.class)
    public void update(EntityDef def, DefChannel defChannel){
        def.update(defChannel);
        def.fetchProperties().stream()
                .filter(it->PropertyDataType.Data_Table.equals(it.getDataType())).forEach(propertyDef -> {
            val slaveIdentifier = def.getIdentifier() + "_" + propertyDef.getProperty();
            val slaveDef =new EntityDef();
            slaveDef.setIdentifier(slaveIdentifier);
            slaveDef.setName(def.getName() + "_" + propertyDef.getName());
            slaveDef.setI18nName(I18nUtil.concat(def.getName(), propertyDef.getName(),
                    def.getI18nName(), propertyDef.getI18nName(), "_" ));
            slaveDef.setTimelineEnabled(def.isTimelineEnabled());
            slaveDef.setOwner(def.getOwner());
            if(slaveDef.fetchProperties().stream().filter(slaveProperty->!Lists.list(PropertyDataType.Integer,
                    PropertyDataType.Number, PropertyDataType.String, PropertyDataType.Timestamp, PropertyDataType.Enum).contains(slaveProperty.getDataType())).findFirst().isPresent()){
                throw new ServerException("unsupported slave model property type");
            }
            propertyDef.appendMasterBidSlaveProperty();
            if(defChannel.equals(DefChannel.SCRIPT)){
                slaveDef.setStandardProperties(propertyDef.getSlaveProperties());
            }else{
                slaveDef.setCustomProperties(propertyDef.getSlaveProperties());
            }
            val slaveDefExisted = EntityDef.load(slaveIdentifier);
            if(slaveDefExisted.isDefined()){
                slaveDef.update(defChannel);
            }else{
                slaveDef.create(defChannel);
            }
        });
        metadataSyncService.sync2Md(def);
    }

    public EntityDef one(String identifier){
        val def = EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在"));
        return def;
    }
    @Transactional(rollbackFor=Exception.class)
    public void delete(String identifier, DefChannel defChannel){
        val def = EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在"));
        def.delete(defChannel);
    }

    public Pair<Sequence<PropertyDef>, Sequence<PropertyDef>> loadHierarchyProperties(EntityDef def){
        return def.loadHierarchyProperties();
    }

    public PageResult<EntityDef> page(String keywords, int pageNo, int pageSize){
        return EntityDef.page(keywords, pageNo, pageSize);
    }

    public Sequence<EntityDef> search(String identifierPattern, boolean filterInheritable,boolean filterTree, int maxCount){
        EntityDefSearchDto query = new EntityDefSearchDto();
        if(StringUtils.isNotEmpty(identifierPattern)){
            query.getLikeCondition().put("identifier", identifierPattern);
            //query.put("identifier", Pattern.compile(identifierPattern.replaceAll("\\.","\\\\.")));
        }
        query.setFilterTree(filterTree);
        return EntityDef.list(query, maxCount);
    }

    public PageResult<KeyValue> queryModelData(MetadataQueryModelDataDto query, long queryTime){
        String identifier = query.getIdentifier();
        PreCheck.preCheckArgument(StringUtil.isBlank(identifier), "模型不存在");

        EntityDef entityDef = this.one(identifier);
        String lableText = entityDef.getLabel();

        List<String> relatedProperties = Lists.list();
        DataFilter dataFilter = DataFilter.eq("tenantId", sessionService.getTenantId());
        PageResult<EntityData> page = EntityData.page(identifier, dataFilter, query.getPageNo(), query.getPageSize(), relatedProperties, false, queryTime);
        List<KeyValue> items = page.getItems().stream().map(entityData -> new KeyValue(entityData.fetchPropertyStringValue(lableText), entityData.getBid())).collect(Collectors.toList());

        return new PageResult(items, page.getPageNo(), page.getPageSize(), page.getTotal());
    }

    @Transactional(rollbackFor=Exception.class)
    public void updatePropsAndRelations(MetadataPropAndRelationUpdateDto update, DefChannel defChannel){
        val props = JsonEnhanceUtil.toObjects(Sequences.sequence(update.getProps()), PropertyDef.class);
        Sequence<PropertyDef> standardProps = JsonEnhanceUtil.toObjects(Sequences.sequence(update.getStandardProps()), PropertyDef.class);
        EntityDef.saveCustomProps(update.getIdentifier(), props, defChannel);
        EntityDef.saveStandardProps(update.getIdentifier(), standardProps, defChannel);
        val existedRelations = EntityDef.loadRelationsByIdentifier(update.getIdentifier());
        update.getRelations().forEach(relation -> {
            if(!existedRelations.exists(existed->existed.getProperty().equals(relation.getProperty()))){

            }
        });
        Sequences.sequence(update.getRelations()).filter(relation->
            !existedRelations.exists(existed->existed.getProperty().equals(relation.getProperty()))
        ).forEach(relation->
            EntityDef.addRelation(JsonEnhanceUtil.toObject(relation, EntityRelationDef.class), defChannel)
        );

        existedRelations.filter(existed->
            !Sequences.sequence(update.getRelations()).exists(relation -> existed.getProperty().equals(relation.getProperty()))
        ).forEach(existed->{
            EntityDef.deleteRelation(update.getIdentifier(), existed.getProperty(), defChannel);
        });
        metadataSyncService.sync2Md(update.getIdentifier());
    }


    public PropertyDef loadPropertyDef(String identifier, String property) {
        EntityDef def = one(identifier);
        return def.fetchAllProperties().stream().filter(p -> property.equals(p.getProperty())).findAny()
                .orElseThrow(() -> new ServerException("Property does not exist"));
    }

    public List<KeyValue> loadPropertyEnumDef(String identifier, String property) {
        EntityDef def = one(identifier);
        PropertyDef propertyDef = def.fetchAllProperties().stream()
                .filter(p -> property.equals(p.getProperty())).findAny()
                .orElseThrow(() -> new ServerException("Property does not exist"));
        if (propertyDef.getDataType() != PropertyDataType.Enum) {
            throw new ServerException("Enum property does not exist");
        }
        return Sequences.sequence(propertyDef.getEnumDef()).map(p -> {
            String display = LanguageUtil.getCurrentLangVal(p.getI18nDisplay());
            return new KeyValue(display, p.getValue());
        }).toList();
    }

    public void addIndex(MetadataIndexDto index) {
        EntityDef.addIndex(index.getIdentifier(), index.getProperties());
    }
}
