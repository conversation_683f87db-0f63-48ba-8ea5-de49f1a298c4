package com.caidaocloud.hrpaas.paas.common.feign;

import java.util.List;

import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2022/12/7
 */
@Component
public class ConditionFeignFallback implements IConditionFeign{
	@Override
	public Result<List<ConditionDataVo>> getConditionDataByCode(String code, boolean showDisable) {
		return Result.fail();
	}
}
