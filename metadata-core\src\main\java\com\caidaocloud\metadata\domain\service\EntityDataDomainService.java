package com.caidaocloud.metadata.domain.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.RelationOperationType;
import com.caidaocloud.metadata.domain.entity.EntityData;
import com.caidaocloud.metadata.domain.entity.EntityDataChange;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.EntityRelation;
import com.googlecode.totallylazy.Lists;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class EntityDataDomainService {

    public List<EntityDataChange> operateRelation(String bid, EntityRelation entityRelation, RelationOperationType operationType, long startTime){
        val data = EntityData.load(entityRelation.getIdentifier(), bid, startTime).getOrThrow(new ServerException("数据不存在"));
        val targetIds = entityRelation.getTargetIds().stream().distinct().collect(Collectors.toList());
        val relationDefs = EntityDef.loadRelationsByIdentifier(entityRelation.getIdentifier());
        val relationDef = relationDefs.find(def ->
                entityRelation.getProperty().equals(def.getProperty())
        ).getOrThrow(new ServerException("关联不存在"));
        val relatedIdentifier = relationDef.getRelatedIdentifier();
        if(!RelationOperationType.DELETE.equals(operationType)
                && !RelationOperationType.DELETE_ALL.equals(operationType)){
            val count = EntityData.countByBids(relatedIdentifier, targetIds, startTime);
            if(count < targetIds.size()){
                throw new ServerException("被关联数据不存在");
            }
        }
        for(EntityRelation existRelation: data.getRelations()){
            if(StringUtils.equals(entityRelation.getProperty(),existRelation.getProperty())){
                switch (operationType){
                    case ADD :
                        targetIds.addAll(existRelation.getTargetIds());
                        existRelation.setTargetIds(
                                targetIds.stream().distinct().collect(Collectors.toList())
                        );
                        break;
                    case REPLACE_ALL:
                        existRelation.setTargetIds(targetIds);
                        break;
                    case DELETE:
                        existRelation.getTargetIds().removeAll(targetIds);
                        break;
                    case DELETE_ALL:
                        existRelation.getTargetIds().clear();
                        break;
                }
                return data.updateWithTimeline(Lists.list(relationDef));
            }
        }
        entityRelation.setTargetIdentifier(relatedIdentifier);
        data.getRelations().add(entityRelation);
        return data.updateWithTimeline(Lists.list(relationDef));
    }
}
