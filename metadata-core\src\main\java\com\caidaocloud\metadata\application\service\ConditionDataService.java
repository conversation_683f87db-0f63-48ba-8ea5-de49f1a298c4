package com.caidaocloud.metadata.application.service;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SpecifiedMultiDataFilter;
import com.caidaocloud.metadata.domain.entity.EntityData;

import org.springframework.stereotype.Service;

@Service
public class ConditionDataService {


    public List<EmpSimple> loadByCondition( SpecifiedMultiDataFilter filter,  long queryTime) {
        return EntityData.loadByCondition(filter, queryTime);
    }
}
