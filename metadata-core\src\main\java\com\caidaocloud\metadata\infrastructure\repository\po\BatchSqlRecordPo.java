package com.caidaocloud.metadata.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("paas_batch_sql_record")
public class BatchSqlRecordPo {
    @TableId
    private Long id;
    private String sqlContent;
    private long execTime;
}
