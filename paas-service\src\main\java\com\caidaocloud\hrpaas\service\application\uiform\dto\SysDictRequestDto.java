package com.caidaocloud.hrpaas.service.application.uiform.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysDictRequestDto {
    /**
     * 字典分类 code
     */
    private String typeCode;

    /**
     * true 表示返回的数据包括 domainid = 0
     */
    private boolean sysConstant = false;

    /**
     * 字典所属模块
     */
    private String belongModule;

    public static SysDictRequestDto bulid(String typeCode, boolean sysConstant){
        SysDictRequestDto requestDto = new SysDictRequestDto();
        requestDto.setSysConstant(sysConstant);
        requestDto.setTypeCode(typeCode);
        return requestDto;
    }
}
