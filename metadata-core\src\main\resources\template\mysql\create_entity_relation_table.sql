create table if not exists $tableName  (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `identifier` varchar(50) NOT NULL,
    `property` varchar(50) NOT NULL,
    `source_id` varchar(50) NOT NULL,
    `target_id` varchar(50) NOT NULL,
    `create_time` bigint NOT NULL,
    `create_by` varchar(50) default NULL,
    `data_start_time` bigint default NULL,
    `data_end_time` bigint default NULL,
    PRIMARY KEY (`id`) USING BTREE,
    index `idx_sourceId` (`source_id`),
    index `idx_targetId` (`target_id`)
)ENGINE = InnoDB COMMENT = '$table_desc';