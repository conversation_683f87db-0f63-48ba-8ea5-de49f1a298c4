package com.caidaocloud.hrpaas.service.interfaces.facade.uiform;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataValueFunction;
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.LabelData;
import com.caidaocloud.hrpaas.metadata.sdk.dto.ProvinceCity;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeData;
import com.caidaocloud.hrpaas.service.application.uiform.service.UiFormDataService;
import com.caidaocloud.hrpaas.service.application.uiform.service.UiFormService;
import com.caidaocloud.hrpaas.service.application.uiform.service.convert.FieldValueAssembleBuilder;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.*;
import com.caidaocloud.hrpaas.service.interfaces.vo.uiform.UiFormDataVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.uiform.UiFormDataWorkflowVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-07-12
 */
@RestController
@RequestMapping("/api/hrpaas/uiform/data/v1")
@Api(value = "/api/hrpaas/uiform/data/v1", description = "表单引擎数据接口", tags = "表单引擎数据接口")
public class UiFormDataController {
    @Resource
    private UiFormDataService uiFormDataService;
    @Resource
    private UiFormService uiFormService;
    @Value("${uiform.anonymous.id:}")
    private String allowedAnonymousUiformId;

    @PostMapping("/put/anonymous")
    @ApiOperation("新增/编辑表单数据")
    public UiFormDataDto putUiFormDataAnonymous(@RequestBody UiFormDataDto uiFormDataDto){
        if(StringUtil.isEmpty(allowedAnonymousUiformId) || StringUtil.isEmpty(uiFormDataDto.getFormId())){
            throw new ServerException("禁止匿名访问");
        }
        if(!Lists.newArrayList(allowedAnonymousUiformId.split(",")).contains(uiFormDataDto.getFormId())){
            throw new ServerException("禁止匿名访问");
        }
        try{
            ThreadLocalUtil.allowAnonymous();
            return putUiFormData(uiFormDataDto);
        }finally {
            ThreadLocalUtil.anonymousEnd();
        }
    }

    @PostMapping("/put")
    @ApiOperation("新增/编辑表单数据")
    public UiFormDataDto putUiFormData(@RequestBody UiFormDataDto uiFormDataDto){
        boolean checkValid = StringUtil.isEmpty(uiFormDataDto.getFormId()) || null == uiFormDataDto.getDataVals() || uiFormDataDto.getDataVals().isEmpty();
        PreCheck.preCheckArgument(checkValid, "数据填写不完整，请仔细检查");
        return uiFormDataService.putUiFormData(uiFormDataDto);
    }

    @GetMapping("/detail")
    @ApiOperation(value = "获取表单数据详情")
    public UiFormDataVo getUiFormDataDetail(@RequestParam("formId") String formId, @RequestParam("dataId") String dataId){
        if(StringUtil.isEmpty(formId) || StringUtil.isEmpty(dataId)){
            return UiFormDataVo.bulidDataValEmpty(formId, dataId);
        }

        UiFormDataQueryDto queryDto = new UiFormDataQueryDto();
        queryDto.setPageNo(1);
        queryDto.setPageSize(1);
        queryDto.setFormId(formId);
        queryDto.setDataId(dataId);
        PageResult pageResult = uiFormDataService.getUiFormDataList(queryDto, true);
        List<UiFormDataDto> dataList= null;
        if(null == pageResult || null == (dataList = pageResult.getItems()) || dataList.isEmpty()){
            return UiFormDataVo.bulidDataValEmpty(formId, dataId);
        }

        return ObjectConverter.convert(dataList.get(0), UiFormDataVo.class);
    }

    @PostMapping("/remove")
    @ApiOperation("删除表单数据")
    public UiFormDataVo removeUiFormData(@RequestBody UiFormDataBaseDto uiFormDataBaseDto){
        if(StringUtil.isEmpty(uiFormDataBaseDto.getFormId()) || StringUtil.isEmpty(uiFormDataBaseDto.getBid())){
            return UiFormDataVo.bulidDataValEmpty(uiFormDataBaseDto.getFormId(), uiFormDataBaseDto.getBid());
        }

        uiFormDataService.removeUiFormData(uiFormDataBaseDto.getFormId(), uiFormDataBaseDto.getBid());
        return UiFormDataVo.bulidDataValEmpty(uiFormDataBaseDto.getFormId(), uiFormDataBaseDto.getBid());
    }

    @PostMapping("/revoke")
    @ApiOperation("撤销表单数据流程")
    public UiFormDataVo revokeUiFormData(@RequestBody UiFormDataBaseDto uiFormDataBaseDto){
        if(StringUtil.isEmpty(uiFormDataBaseDto.getFormId()) || StringUtil.isEmpty(uiFormDataBaseDto.getBid())){
            return UiFormDataVo.bulidDataValEmpty(uiFormDataBaseDto.getFormId(), uiFormDataBaseDto.getBid());
        }

        uiFormDataService.revokeUiFormData(uiFormDataBaseDto.getFormId(), uiFormDataBaseDto.getBid());
        return UiFormDataVo.bulidDataValEmpty(uiFormDataBaseDto.getFormId(), uiFormDataBaseDto.getBid());
    }

    @PostMapping("/list")
    @ApiOperation("分页获取表单数据")
    public PageResult<Map> getUiFormDataList(@RequestBody UiFormDataQueryDto queryDto){
        if(StringUtil.isEmpty(queryDto.getFormId())){
            return new PageResult();
        }

        PageResult pageResult = uiFormDataService.getUiFormDataList(queryDto, false);
        HolderUtil.clear();
        List<UiFormDataDto> values = null;
        if(null == pageResult || null == (values = pageResult.getItems()) || values.isEmpty()){
            return pageResult;
        }

        Map<String, UiDataSourceDto> fieldDataSourceMap = uiFormService.getUiFormFieldDataSource(queryDto.getFormId());
        List<Map> resultData = values.stream().filter(item -> null != item).map(data -> {
            Map uiFormDataVoMap = new HashMap();
            List<UiFormDataValueDto> dataValueDtos = data.getDataVals();
            if(null != dataValueDtos && !dataValueDtos.isEmpty()){
                int size = data.getDataVals().size();
                DataValueFunction dataValue = null;
                UiDataSourceDto fieldDataSource = null;
                String fieldProp;
                Object fieldVal = null;
                for (int i = 0; i < size; i ++) {
                    dataValue = dataValueDtos.get(i);
                    fieldProp = dataValue.loadDataProp();
                    if(StringUtil.isEmpty(fieldProp)){
                        continue;
                    }
                    fieldVal = dataValue.loadDataValue();
                    // fieldVal = null == fieldVal ? "" : fieldVal;
                    uiFormDataVoMap.put(fieldProp, fieldVal);
                    fieldDataSource = fieldDataSourceMap.get(fieldProp);

                    if(StringUtil.isNotEmpty(fieldVal) && null != fieldDataSource && null != fieldDataSource.getType()){
                        FieldValueAssembleBuilder.getFieldValueAssembleStrategy(fieldDataSource.getType())
                                .packageFieldValue(uiFormDataVoMap, fieldProp, (String) fieldVal, fieldDataSource);
                    }
                }
            }

            uiFormDataVoMap.put("bid", data.getBid());
            uiFormDataVoMap.put("formId", data.getFormId());

            return uiFormDataVoMap;
        }).collect(Collectors.toList());
        pageResult.setItems(resultData);

        HolderUtil.clear();
        return pageResult;
    }

    @GetMapping("/workflowDetail")
    @ApiOperation("获取流程表单数据详情")
    public UiFormDataWorkflowVo getUiFormDataWorkflowDetail(@RequestParam("functionType") String functionType, @RequestParam("dataId") String dataId){
        if(StringUtil.isEmpty(functionType) || StringUtil.isEmpty(dataId)){
            UiFormDataWorkflowVo reData = UiFormDataWorkflowVo.builder().functionType(functionType).build();
            reData.setId(dataId);
            return reData;
        }

        UiFormDataDto formData = uiFormDataService.getUiFormDataWorkflowDetail(functionType, dataId);
        return ObjectConverter.convert(formData, UiFormDataWorkflowVo.class);
    }

    @GetMapping("/tree")
    public List<TreeData<LabelData>> getDataTree(@RequestParam("identifier") String identifier, @RequestParam("queryTime") long queryTime){
        return uiFormDataService.getDataTree(identifier, queryTime);
    }

    @GetMapping("/province/list")
    @ApiOperation("获取省份")
    public List<LabelData> getProvince(){
        return uiFormDataService.getProvince();
    }

    @GetMapping("/city/list")
    @ApiOperation("获取城市")
    public List<LabelData> getCity(@RequestParam("provinceId") String provinceId){
        return uiFormDataService.getCity(provinceId);
    }

    @PostMapping("/cityProvinceInfo")
    @ApiOperation("获取城市包含省份信息")
    public List<ProvinceCity> cityProvinceInfo(@RequestParam("tenantId") String tenantId){
        return uiFormDataService.getCityAll(tenantId);
    }
}
