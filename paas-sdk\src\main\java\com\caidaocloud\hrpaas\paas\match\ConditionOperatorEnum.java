package com.caidaocloud.hrpaas.paas.match;

import com.googlecode.totallylazy.Lists;

import java.util.List;

public enum ConditionOperatorEnum {
    EQ, NE, GT, GE, LT, LE, CONTAIN,
    // 不包含于
    NOT_CONTAIN, IN, IS_NULL, IS_NOT_NULL,
    // 包含子级
    CONTAIN_CHILD;

    public static List<ConditionOperatorEnum> reentryOpList(){
        return Lists.list(EQ, NE, IN);
    }

    public static List<ConditionOperatorEnum> reentryChildOpList(){
        return Lists.list(EQ, NE, IN, NOT_CONTAIN, CONTAIN_CHILD);
    }
}