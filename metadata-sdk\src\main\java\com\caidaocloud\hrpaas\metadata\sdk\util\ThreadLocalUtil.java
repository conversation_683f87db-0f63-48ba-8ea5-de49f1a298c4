package com.caidaocloud.hrpaas.metadata.sdk.util;

public class ThreadLocalUtil {
    // 线程变量
    public static final ThreadLocal<Object> LOCAL_REQUEST_DATA = new ThreadLocal<Object>();

    private static final ThreadLocal<Boolean> allowedAnonymous = new ThreadLocal();

    public static Boolean isAnonymousAllowed(){
        Boolean allowed = allowedAnonymous.get();
        if(null == allowed){
            allowed = false;
        }
        return allowed;
    }

    public static void allowAnonymous(){
        allowedAnonymous.set(true);
    }

    public static void anonymousEnd(){
        allowedAnonymous.remove();
    }
}
