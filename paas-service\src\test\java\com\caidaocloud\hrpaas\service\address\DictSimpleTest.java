package com.caidaocloud.hrpaas.service.address;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class DictSimpleTest {
    @Test
    public void testCode2Dict(){
        DictSimple dictSimple = DictSimple.code2Dict("F");
        log.info(FastjsonUtil.toJson(dictSimple));
    }

    @Before
    public void beforeTest(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setUserId(0L);
        userInfo.setTenantId("11");
        userInfo.setEmpId(0L);
        userInfo.setLang("JA");
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    @Test
    public void testLangTxt(){
        DictSimple dictSimple = DictSimple.doDictSimple("1539");
        log.info(FastjsonUtil.toJson(dictSimple));
    }
}
