package com.caidaocloud.hrpaas.service.test;

import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormDataDto;
import com.caidaocloud.util.FastjsonUtil;

public class UiFormDataValueTest {
    public static void main(String[] args) {
        String str = "{\"dataVals\":[{\"prop\":\"name\",\"propType\":\"Input\",\"value\":\"50\"},{\"prop\":\"age\",\"propType\":\"InputNumber\",\"value\":50},{\"prop\":\"status\",\"propType\":\"EnumSelect\",\"value\":\"41\"},{\"prop\":\"pid\",\"propType\":\"TreeSelect\"},{\"prop\":\"org\",\"propType\":\"TreeSelect\",\"value\":{\"pid\":\"1301440867506177\"}}],\"formId\":\"1302170727290958\"}";
        UiFormDataDto s = FastjsonUtil.toObject(str, UiFormDataDto.class);

        System.out.println(s.getFormId());
    }
}
