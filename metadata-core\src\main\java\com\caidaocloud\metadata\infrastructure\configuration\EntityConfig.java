package com.caidaocloud.metadata.infrastructure.configuration;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 * @date 2024/3/12
 */
@Configuration
public class EntityConfig {
	@Bean
	@ConditionalOnMissingBean(IEntityPropertyRule.class)
	public IEntityPropertyRule entityPropertyRule(){
		return new DefaultEntityPropertyRule();
	}
}
