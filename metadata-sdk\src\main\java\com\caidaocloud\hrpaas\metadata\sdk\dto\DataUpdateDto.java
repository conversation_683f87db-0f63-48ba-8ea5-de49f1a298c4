package com.caidaocloud.hrpaas.metadata.sdk.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class DataUpdateDto {

    private List<PropertyDataDto> updateDataList;

    public static DataUpdateDto updateData(List<PropertyDataDto> dataList){
        DataUpdateDto request = new DataUpdateDto();
        request.updateDataList = dataList;
        return request;
    }

}
