package com.caidaocloud.hrpaas.service.application.uiform.service.render;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.service.application.uiform.dto.SysDictDataSourceDto;
import com.caidaocloud.hrpaas.service.application.uiform.dto.SysDictRequestDto;
import com.caidaocloud.hrpaas.service.application.uiform.dto.UiDataSourceSysDictRenderDto;
import com.caidaocloud.hrpaas.service.application.uiform.fegin.ISysDictFeginClient;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiDataSourceRenderDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class SysDictDataSourceRender implements DataSourceRender {
    @Resource
    private ISysDictFeginClient sysDictFeginClient;

    @Override
    public PageResult<Map> doRender(String dataSourceConfig, Integer pageNo, Integer pageSize, Map<String, String[]> parameterMap) {
        log.info("system dictionary request parameters. dataSourceConfig={}, pageNo={}, pageSize={}", dataSourceConfig, pageNo, pageSize);
        UiDataSourceSysDictRenderDto dataSourceRenderDto = JSON.parseObject(dataSourceConfig, UiDataSourceSysDictRenderDto.class);
        return doRender(dataSourceRenderDto, pageNo, pageSize, parameterMap);
    }

    @Override
    public PageResult<Map> doRender(UiDataSourceRenderDto dataSourceConfig, Integer pageNo, Integer pageSize, Map<String, String[]> parameterMap) {
        if(null == dataSourceConfig){
            return PageResult.pageResultEmpty();
        }

        UiDataSourceSysDictRenderDto renderDto = JSON.parseObject(JSON.toJSONString(dataSourceConfig), UiDataSourceSysDictRenderDto.class);
        return this.defaultRender(renderDto);
    }

    private PageResult<Map> defaultRender(UiDataSourceSysDictRenderDto dataSourceRenderDto){
        SysDictRequestDto params = dataSourceRenderDto.getRequest();
        // 请求参数不能为空 Request parameter cannot be empty
        PreCheck.preCheckArgument(null == params || StringUtil.isEmpty(params.getTypeCode()), "请求参数不能为空");

        Result result = null;
        switch (dataSourceRenderDto.getRequestMethod()) {
            case POST:
                result = sysDictFeginClient.postSysDictList(params);
                return doPostRender(result);
            case GET:
                result = sysDictFeginClient.getSysDictList(params.getTypeCode(), params.getBelongModule());
                return doGetRender(result);
            default:
                throw new ServerException("暂不支持的请求!");
        }

    }

    private PageResult checkResult(Result result){
        log.info("defaultRender checkResult result={}", FastjsonUtil.toJson(result));
        if(null == result || !result.isSuccess() || null == result.getData()){
            return PageResult.pageResultEmpty();
        }

        return null;
    }

    private PageResult doPostRender(Result result){
        PageResult pageResult = checkResult(result);
        if(null != pageResult){
            return pageResult;
        }

        SysDictDataSourceDto sysDictDataSourceDto = (SysDictDataSourceDto) result.getData();
        if(null == sysDictDataSourceDto || null == sysDictDataSourceDto.getItems() || sysDictDataSourceDto.getItems().isEmpty()){
            return PageResult.pageResultEmpty();
        }

        pageResult = new PageResult(sysDictDataSourceDto.getItems(), 0, 100, sysDictDataSourceDto.getItems().size());
        return pageResult;
    }

    private PageResult doGetRender(Result result){
        PageResult pageResult = checkResult(result);
        if(null != pageResult){
            return pageResult;
        }

        List<SysDictDataSourceDto> dictList = (List<SysDictDataSourceDto>) result.getData();
        if(null == dictList || dictList.isEmpty()){
            return PageResult.pageResultEmpty();
        }

        pageResult = new PageResult(dictList, 0, 100, dictList.size());
        return pageResult;
    }

}
