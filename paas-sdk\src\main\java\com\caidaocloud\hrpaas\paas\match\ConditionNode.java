package com.caidaocloud.hrpaas.paas.match;

import com.googlecode.totallylazy.Sequences;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/25
 **/
@Data
public class ConditionNode {

    private String id;

    private ConditionNodeTypeEnum type;

    private ConditionNodeRelationEnum relation;

    private List<ConditionNode> children;

    private ConditionExp condition;

    public boolean match(Map<String, String> empInfo) {
        if(ConditionNodeTypeEnum.single.equals(type)){
            return condition.match(empInfo);
        }
        if(ConditionNodeRelationEnum.and.equals(relation)){
            return Sequences.sequence(children).forAll(it->it.match(empInfo));
        }else{
            return Sequences.sequence(children).exists(it->it.match(empInfo));
        }

    }
}
