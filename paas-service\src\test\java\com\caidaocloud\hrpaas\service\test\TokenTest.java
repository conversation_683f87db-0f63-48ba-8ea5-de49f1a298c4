package com.caidaocloud.hrpaas.service.test;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.security.dto.TokenDto;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.security.token.TokenVerify;
import com.caidaocloud.util.FastjsonUtil;

public class TokenTest {
    public static void main(String[] args) {
        String accessToken = "eyJhbGciOiJIUzUxMiJ9.eyJ0ZW5hbnRJZCI6InR4ankiLCJ0aW1lIjoxNjM2NTA4NzMxNDIyLCJ0eXBlIjoyLCJ1c2VySWQiOiIzOTc2MiJ9.nNUgjGW9waAP9ODQNxiuwKo7KzPOwtvo-0EMnUYhUvxfTrgcsIIZ8z-rN0NRcxvIAQzt1YTLRwt19pEzSvzkiA";
        TokenDto tokenDataModel = TokenVerify.getTokenDataModel(accessToken);
        System.out.println(FastjsonUtil.toJson(tokenDataModel));

        System.out.println("--------------1--------------");
        accessToken = "eyJhbGciOiJIUzUxMiJ9.eyJ0ZW5hbnRJZCI6IjU2NTk0IiwidGltZSI6MTYzNzA2MDc3MzA4OCwidHlwZSI6MiwidXNlcklkIjoiMCJ9.qmlE6c9WKpY3yuZwHOCpPfg9oLHXYeCxRom9KfABYegIreangBQCFn5_uOBGQzi02S61VtnJxnvXRao2Z9HUUQ";
        tokenDataModel = TokenVerify.getTokenDataModel(accessToken);
        System.out.println(FastjsonUtil.toJson(tokenDataModel));
        System.out.println("--------------1--------------");

        System.out.println("--------------2--------------");
        accessToken = "eyJhbGciOiJIUzUxMiJ9.eyJ0ZW5hbnRJZCI6InR4ankiLCJ0aW1lIjoxNjM2NTA5MzA4NjMzLCJ0eXBlIjoyLCJ1c2VySWQiOiI0NjQ1OSJ9.Vxu4mYBoUHz6GMHgp_O6I85Qyhp0WH4VDoLyZLZbCa8mP9VjAOzeEhUvWPiFiC4kKczCFhHeWXvCt5k2BAwuQQ";
        tokenDataModel = TokenVerify.getTokenDataModel(accessToken);
        System.out.println(FastjsonUtil.toJson(tokenDataModel));
        System.out.println("--------------2--------------");

        System.out.println(JSON.toJSONString(tokenDataModel));
    }
}
