package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataSchemaDo;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.IMetadataSchemaRepository;
import org.springframework.stereotype.Repository;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.MetadataSchemaPo;
import com.caidaocloud.hrpaas.service.infrastructure.util.SqlUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Option;
import lombok.val;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Repository
public class MetadataSchemaRepositoryImpl implements IMetadataSchemaRepository {
    @Autowired
    private MetadataSchemaMapper mapper;

    private static final int BATCH_SIZE = 1000;

    @Override
    public String insert(MetadataSchemaDo metadataSchemaDo) {
        val po = MetadataSchemaPo.fromEntity(MetadataSchemaPo.class, metadataSchemaDo);
        mapper.insert(po);
        return po.getId().toString();
    }

    @Override
    public void update(MetadataSchemaDo metadataSchemaDo) {
        val po =  MetadataSchemaPo.fromEntity(MetadataSchemaPo.class, metadataSchemaDo);
        mapper.updateById(po);
    }

    @Override
    public Option<MetadataSchemaDo> load(String id) {
        val po = mapper.selectById(id);
        if (po == null) {
            return Option.none();
        }
        return Option.some(po.toEntity(MetadataSchemaDo.class));
    }

    @Override
    public void batchSave(List<MetadataSchemaDo> schemaDoList) {
        List<MetadataSchemaPo> poList = ObjectConverter.convertList(schemaDoList, MetadataSchemaPo.class);
        String sqlStatement = SqlHelper.getSqlStatement(MetadataSchemaMapper.class, SqlMethod.INSERT_ONE);
        SqlSession sqlSession = SqlUtil.getBatchSqlSession(MetadataSchemaPo.class);
        int i = 1;
        for (MetadataSchemaPo po : poList) {
            sqlSession.insert(sqlStatement, po);
            if ((i % BATCH_SIZE == 0) || i == poList.size()) {
                sqlSession.flushStatements();
            }
            i++;
        }
    }

    @Override
    public Option<List<MetadataSchemaDo>> findAllSchema() {
        LambdaQueryWrapper<MetadataSchemaPo> queryWrapper = new QueryWrapper<MetadataSchemaPo>().lambda();
        queryWrapper.orderByAsc(MetadataSchemaPo::getVersion);
        val poList = mapper.selectList(queryWrapper);
        return Option.some(MetadataSchemaPo.toEntityList(MetadataSchemaDo.class, poList));
    }

    @Override
    public List<MetadataSchemaDo> findSchemaByIds(String [] scriptIds) {
        LambdaQueryWrapper<MetadataSchemaPo> queryWrapper = new QueryWrapper<MetadataSchemaPo>().lambda();
        queryWrapper.in(BasePo::getId, scriptIds)
                .orderByAsc(MetadataSchemaPo::getVersion);
        val poList = mapper.selectList(queryWrapper);
        return MetadataSchemaPo.toEntityList(MetadataSchemaDo.class, poList);
    }

    @Override
    public List<MetadataSchemaDo> findSchemaByNameOrVersion(String name, Long version) {
        LambdaQueryWrapper<MetadataSchemaPo> queryWrapper = new QueryWrapper<MetadataSchemaPo>().lambda();
        queryWrapper.eq(MetadataSchemaPo::getSchemaName, name)
                .or().eq(MetadataSchemaPo::getVersion, version)
                .orderByAsc(MetadataSchemaPo::getVersion);
        val poList = mapper.selectList(queryWrapper);
        return MetadataSchemaPo.toEntityList(MetadataSchemaDo.class, poList);
    }

    @Override
    public List<MetadataSchemaDo> findSchemaGtVersion(Long version) {
        LambdaQueryWrapper<MetadataSchemaPo> queryWrapper = new QueryWrapper<MetadataSchemaPo>().lambda();
        queryWrapper.gt(MetadataSchemaPo::getVersion, version)
                .orderByAsc(MetadataSchemaPo::getVersion);
        val poList = mapper.selectList(queryWrapper);
        return MetadataSchemaPo.toEntityList(MetadataSchemaDo.class, poList);
    }


}
