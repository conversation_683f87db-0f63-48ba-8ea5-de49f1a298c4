package com.caidaocloud.metadata.infrastructure.repository.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.hrpaas.metadata.sdk.dto.ComponentPropertyValue;
import com.caidaocloud.metadata.domain.dto.EntityDefSearchDto;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.domain.repository.IEntityDefRepository;
import com.caidaocloud.metadata.infrastructure.configuration.IEntityPropertyRule;
import com.caidaocloud.metadata.infrastructure.repository.impl.util.TenantMapperUtil;
import com.caidaocloud.metadata.infrastructure.repository.po.EntityDefPo;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.metadata.infrastructure.repository.po.PropertyEventPo;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.RestUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.*;
import com.jarvis.cache.annotation.Cache;
import com.jarvis.cache.annotation.CacheDelete;
import com.jarvis.cache.annotation.CacheDeleteKey;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * @Author: Jarvis
 * @Desc:
 * @Date: 6/11/2021 3:57 PM
 */
@Repository
@Slf4j
public class EntityDefRepositoryImpl implements IEntityDefRepository {

    @Autowired
    private EntityDefMapper mapper;

    @Autowired
    private PropertyEventMapper propertyEventMapper;

    @Resource
    private IEntityPropertyRule entityPropertyRule;

    @NacosValue("${dbType:mysql}")
    private String dbType;

    @NacosValue("${anonymous.tenant:}")
    private String anonymousTenant;

    private Map<String, String> escapeChar = Maps.map(
            "mysql", "`",
            "postgresql", "\""
    );
    private Map.Entry<String, String> propertyToField;

    private String colEscapeChar(){
        return escapeChar.get(dbType);
    }

    private String getTenantId() {
        String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        if(StringUtil.isEmpty(tenantId)){
            if(ThreadLocalUtil.isAnonymousAllowed()){
                tenantId = anonymousTenant;
            }
        }
        return tenantId;
    }

    @Override
    public String insert(EntityDef entityDef) {
        val identifier = entityDef.getIdentifier();
        List<PropertyDef> properties = entityDef.fetchProperties();
        propertyEventMapper.delete(new LambdaQueryWrapper<PropertyEventPo>().eq(PropertyEventPo::getIdentifier, identifier));
        for(PropertyDef propertyDef : properties){
            val onEvent = propertyDef.getOnEvent();
            propertyDef.setOnEvent(null);
            val property = propertyDef.getProperty();
            if(StringUtils.isNotEmpty(onEvent)){
                propertyEventMapper.insert(new PropertyEventPo(identifier, property, onEvent));
            }
        }
        val po = EntityDefPo.fromEntity(EntityDefPo.class, entityDef);
        Map<String, String> propertyToFieldMapping = Maps.map();
        List<PropertyDef> dbProperties = propertiesMapFields(properties, false, propertyToFieldMapping);
        if(!propertyToFieldMapping.isEmpty()){
            po.setPropertyToFieldMapping(FastjsonUtil.toJson(propertyToFieldMapping));
            createTable(entityDef, dbProperties);
            createRefTable(entityDef);
        }
        this.mapper.insert(po);
        SpringUtil.getBean(EntityDefRepositoryImpl.class)
                .clearDefPoCache(entityDef.getIdentifier(), getTenantId());
        return String.valueOf(po.getId());
    }

    @Override
    public void update(EntityDef entityDef) {
        Wrapper<EntityDefPo> wrapper = new LambdaUpdateWrapper<EntityDefPo>()
                .eq(EntityDefPo::getIdentifier, entityDef.getIdentifier());
        val exist = this.mapper.selectOne(wrapper);
        val existEntity = exist.toEntity(Maps.map());
        if(null == exist){
            throw new ServerException("模型不存在");
        }
        val identifier = entityDef.getIdentifier();
        List<PropertyDef> properties = entityDef.fetchProperties();
        propertyEventMapper.delete(new LambdaQueryWrapper<PropertyEventPo>().eq(PropertyEventPo::getIdentifier, identifier));
        for(PropertyDef propertyDef : properties){
            val onEvent = propertyDef.getOnEvent();
            //propertyDef.setOnEvent(null);
            val property = propertyDef.getProperty();
            if(StringUtils.isNotEmpty(onEvent)){
                propertyEventMapper.insert(new PropertyEventPo(identifier, property, onEvent));
            }
        }
        val defCopy = FastjsonUtil.convertObject(entityDef, EntityDef.class);
        defCopy.fetchProperties().forEach(it->it.setOnEvent(null));
        val po = EntityDefPo.fromEntity(EntityDefPo.class, defCopy);
        if(StringUtils.isEmpty(exist.getPropertyToFieldMapping())){
            Map<String, String> propertyToFieldMapping = Maps.map();
            List<PropertyDef> propertyDefList = propertiesMapFields(properties, false, propertyToFieldMapping);
            if(!propertyToFieldMapping.isEmpty()){
                po.setPropertyToFieldMapping(FastjsonUtil.toJson(propertyToFieldMapping));
                createTable(entityDef,propertyDefList);
            }
        }else{
            Map<String, String> propertyToFieldMapping = FastjsonUtil.toObject(exist.getPropertyToFieldMapping(), Map.class);
            List<PropertyDef> dbExpList = Sequences.sequence(existEntity.fetchProperties())
                    .filter(p -> p.isExpEnable()).toList();
            List<PropertyDef> newStandard = propertiesMapFields(entityDef.getCustomProperties(), true, propertyToFieldMapping, dbExpList);
            newStandard.addAll(propertiesMapFields(entityDef.getStandardProperties(), false, propertyToFieldMapping, dbExpList));
            po.setPropertyToFieldMapping(FastjsonUtil.toJson(propertyToFieldMapping));
            // 自定义字段数量固定，标准字段可通过脚本添加
            addTableColumn(newStandard, exist.getIdentifier());
        }
        createRefTable(entityDef);
        this.mapper.update(po, wrapper);
        SpringUtil.getBean(EntityDefRepositoryImpl.class)
                .clearDefPoCache(entityDef.getIdentifier(), getTenantId());
    }

    private List<PropertyDef> propertiesMapFields(List<PropertyDef> properties, Boolean useBuiltInFields, Map<String, String> propertyToFieldMapping) {
        return propertiesMapFields(properties, useBuiltInFields, propertyToFieldMapping, new ArrayList<>());
    }


    @Override
    public Option<EntityDef> loadByIdentifier(String identifier) {
        if("entity.common.BaseModel".equals(identifier)){
            try {
                return Option.some(FastjsonUtil.toObject(
                        IOUtils.toString(new ClassPathResource("template/metadata/BaseModelDef.json").getInputStream(), "UTF-8"),
                        EntityDef.class
                ));
            } catch (Exception e) {
                throw new ServerException("BaseModel not defined",e);
            }
        }
        val result = SpringUtil.getBean(EntityDefRepositoryImpl.class)
                .getDefPo(identifier, getTenantId());
        if(result == null){
            return Option.none();
        }
        val events = Maps.map(propertyEventMapper.selectList(new LambdaQueryWrapper<PropertyEventPo>()
                .eq(PropertyEventPo::getIdentifier, identifier))
                .stream().map(it-> Pair.pair(it.getProperty(), it.getEvent())).collect(Collectors.toList()));
        return Option.option(result.toEntity(events));
    }

    @Override
    public PageResult<EntityDef> page(String keywords, int pageNo, int pageSize) {
        LambdaQueryWrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>();
        if(StringUtils.isNotEmpty(keywords)){
            wrapper.and(it->
                    it.like(EntityDefPo::getIdentifier, keywords).or().like(EntityDefPo::getName, keywords));
        }
        val page = TenantMapperUtil.selectTenantPage(mapper, new Page<>(pageNo, pageSize), wrapper);

        return new PageResult(page.getRecords().stream().map(it->{
//            val events = Maps.map(propertyEventMapper.selectList(new LambdaQueryWrapper<PropertyEventPo>()
//                    .eq(PropertyEventPo::getIdentifier, it.getIdentifier()))
//                    .stream().map(eventDef-> Pair.pair(eventDef.getProperty(), eventDef.getEvent())).collect(Collectors.toList()));
            val result = it.toEntity(Maps.map());
            return result;
        }).collect(Collectors.toList()), pageNo, pageSize, (int)page.getTotal());
    }

    @Override
    public Sequence<EntityDef> list(EntityDefSearchDto query, int maxCount) {
        QueryWrapper<EntityDefPo> wrapper = new QueryWrapper<>();
        query.getEqCondition().entrySet().forEach(it -> wrapper.eq(it.getKey(), it.getValue()));
        if (query.isFilterTree()) {
            wrapper.eq("tree", 1);
        }
        query.getLikeCondition().entrySet().forEach(it -> wrapper.like(it.getKey(), it.getValue()));
        List<EntityDef> list = mapper.selectPage(new Page<EntityDefPo>(1, maxCount).setSearchCount(false), wrapper)
                .getRecords().stream().map(it -> {
//                    val events = Maps.map(propertyEventMapper.selectList(new LambdaQueryWrapper<PropertyEventPo>()
//                            .eq(PropertyEventPo::getIdentifier, it.getIdentifier()))
//                            .stream().map(eventDef-> Pair.pair(eventDef.getProperty(), eventDef.getEvent())).collect(Collectors.toList()));
                    val result = it.toEntity(Maps.map());
                    return result;
                }).collect(Collectors.toList());
        return Sequences.sequence(list);
    }

    @Override
    public List<EntityDef> getListByIdentifier(List<String> identifierList, int maxCount) {
        if(identifierList.size() > maxCount){
            identifierList = identifierList.subList(0, maxCount);
        }
        Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                .in(EntityDefPo::getIdentifier, identifierList);
        val result = this.mapper.selectList(wrapper);
        return result.stream().map(it->{
//            val events = Maps.map(propertyEventMapper.selectList(new LambdaQueryWrapper<PropertyEventPo>()
//                    .eq(PropertyEventPo::getIdentifier, it.getIdentifier()))
//                    .stream().map(eventDef-> Pair.pair(eventDef.getProperty(), eventDef.getEvent())).collect(Collectors.toList()));
            val entity = it.toEntity(Maps.map());
            return entity;
        }).collect(Collectors.toList());
    }

    @Override
    public void delete(EntityDef entityDef) {
        Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                .eq(EntityDefPo::getIdentifier, entityDef.getIdentifier());
        this.mapper.delete(wrapper);
        SpringUtil.getBean(EntityDefRepositoryImpl.class)
                .clearDefPoCache(entityDef.getIdentifier(), getTenantId());
    }

    @SneakyThrows
    private void createRefTable(EntityDef entityDef){
        String sqlTemplate = IOUtils
                .toString(new ClassPathResource("template/" + dbType + "/create_entity_relation_table.sql")
                        .getInputStream(), "UTF-8");
        val identifier = entityDef.getIdentifier();
        val tableName = SnakeCaseConvertor.toSnake(entityDef.getIdentifier()) + "_" + getTenantId();
        entityDef.getRelationProperties().forEach(relation ->{
            val sql = sqlTemplate
                    .replaceAll("\\$tableName", SnakeCaseConvertor.toSnake(identifier+".relation."+relation.getProperty() + "_" + getTenantId()))
                    .replaceAll("\\$table_desc", entityDef.getName() + "-" + relation.getPropertyName())
                    .replaceAll("\\$source_index_name", "idx_source_" + tableName + "_" + SnakeCaseConvertor.toSnake(relation.getProperty()))
                    .replaceAll("\\$target_index_name", "idx_target_" + tableName + "_" + SnakeCaseConvertor.toSnake(relation.getProperty()));
                    try {
                        mapper.exec(sql);
                    } catch (Exception e) {
                        log.info("关联关系表创建异常", e);
                    }
                }
        );
    }

    private void addTableColumn(List<PropertyDef> entityDef, String identifier){
        List<String> varcharFields = Lists.list();
        List<String> encryptVarcharFields = Lists.list();
        List<String> textFields = Lists.list();
        Sequences.sequence(entityDef).filter(it->!PropertyDataType.Data_Table.equals(it.getDataType()))
                .forEach(property->{
                    if(property.getDataType().isComponent()){
                        ComponentPropertyValue.componentHolder.get(property.getDataType())
                                .propertySuffixToWhetherLong().forEach((suffix, isLong) ->{
                            if(property.isEncrypted()){
                                encryptVarcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty() + suffix));
                            }else if(isLong){
                                textFields.add(SnakeCaseConvertor.toSnake(property.getProperty() + suffix));
                            }else{
                                varcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty() + suffix));
                            }
                        });
                    }else{
                        boolean isLong = false;
                        if(property.isEncrypted()){
                            encryptVarcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty()));
                            if(null != property.getRules()){
                                property.getRules().stream().filter(it->
                                        "maxLength".equals(it.getType())
                                                && StringUtils.isNotEmpty(it.getValue()) && Long.valueOf(it.getValue()) > 30)
                                        .findFirst().ifPresent(it->{
                                    throw new ServerException("加密字段长度不能超过30");
                                });

                            }
                        }else{
                            if(null != property.getRules()){
                                isLong = property.getRules().stream().filter(it->
                                        "maxLength".equals(it.getType())
                                                && StringUtils.isNotEmpty(it.getValue()) && Long.valueOf(it.getValue()) > 200).findFirst().isPresent();

                            }
                            if(isLong){
                                textFields.add(SnakeCaseConvertor.toSnake(property.getProperty()));
                            }else{
                                varcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty()));
                            }
                        }
                    }
                });
        varcharFields.forEach(it->{
            mapper.exec("alter table " +
                    SnakeCaseConvertor.toSnake(identifier) + "_" + getTenantId() +
                    " add column " + colEscapeChar()+ it + colEscapeChar() + " varchar(200) default null;");
        });
        textFields.forEach(it->{
            mapper.exec("alter table " +
                    SnakeCaseConvertor.toSnake(identifier) + "_" + getTenantId() +
                    " add column " + colEscapeChar()+ it + colEscapeChar() + " text default null;");
        });
        encryptVarcharFields.forEach(it->{
            mapper.exec("alter table " +
                    SnakeCaseConvertor.toSnake(identifier) + "_" + getTenantId() +
                    " add column " + colEscapeChar()+ it + colEscapeChar() + " varchar(600) default null;");
        });
    }

    @SneakyThrows
    private void createTable(EntityDef entityDef){
        createTable(entityDef, entityDef.fetchProperties());
    }

    @SneakyThrows
    private void createTable(EntityDef entityDef,List<PropertyDef> propertyDefs){
        String sqlTemplate = IOUtils
                .toString(new ClassPathResource("template/" + dbType + "/create_entity_table.sql")
                        .getInputStream(), "UTF-8");
        List<String> varcharFields = Lists.list();
        List<String> encryptVarcharFields = Lists.list();
        List<String> textFields = Lists.list();
        Sequences.sequence(propertyDefs).filter(it-> !PropertyDataType.Data_Table.equals(it.getDataType()))
                .forEach(property->{
                    if(property.getDataType().isComponent()){
                        ComponentPropertyValue.componentHolder.get(property.getDataType())
                                .propertySuffixToWhetherLong().forEach((suffix, isLong) ->{
                                    if(property.isEncrypted()){
                                        encryptVarcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty() + suffix));
                                    }else if(isLong){
                                        textFields.add(SnakeCaseConvertor.toSnake(property.getProperty() + suffix));
                                    }else{
                                        varcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty() + suffix));
                                    }
                                });
                    }else{
                        boolean isLong = false;
                        if(property.isEncrypted()){
                            encryptVarcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty()));
                            if(null != property.getRules()){
                                property.getRules().stream().filter(it->
                                                "maxLength".equals(it.getType())
                                                        && StringUtils.isNotEmpty(it.getValue()) && Long.valueOf(it.getValue()) > 30)
                                        .findFirst().ifPresent(it->{
                                            throw new ServerException("加密字段长度不能超过30");
                                        });

                            }
                        }else{
                            if(null != property.getRules()){
                                isLong = property.getRules().stream().filter(it->
                                        "maxLength".equals(it.getType())
                                                && StringUtils.isNotEmpty(it.getValue()) && Long.valueOf(it.getValue()) > 200).findFirst().isPresent();

                            }
                            if(isLong){
                                textFields.add(SnakeCaseConvertor.toSnake(property.getProperty()));
                            }else{
                                varcharFields.add(SnakeCaseConvertor.toSnake(property.getProperty()));
                            }
                        }
                    }
                });
        varcharFields.addAll(SnakeCaseConvertor.preBuiltSnakeCase);
        textFields.addAll(SnakeCaseConvertor.preBuiltLargeSnakeCase);
        encryptVarcharFields.addAll(SnakeCaseConvertor.preBuiltEncryptSnakeCase);
        sqlTemplate = replacePlaceHolder(varcharFields, sqlTemplate, "varcharLoopBy");
        sqlTemplate = replacePlaceHolder(encryptVarcharFields, sqlTemplate, "encryptLoopBy");
        sqlTemplate = replacePlaceHolder(textFields, sqlTemplate, "textLoopBy");
        val tableName = SnakeCaseConvertor.toSnake(entityDef.getIdentifier()) + "_" + getTenantId();
        val sql = sqlTemplate.replaceAll("\\$tableName", tableName)
                .replaceAll("\\$table_desc", entityDef.getName())
                .replaceAll("\\$index_name", "i_b_" + tableName.replace("entity_", ""))
                .replaceAll("\\$index_tenant", "i_t_" + tableName.replace("entity_", ""))
                .replaceAll("\\$index_deleted", "i_d_" + tableName.replace("entity_", ""))
                .replaceAll("\\$index_start_time", "i_s_" + tableName.replace("entity_", ""))
                .replaceAll("\\$index_end_time", "i_e" + tableName.replace("entity_", ""));
        mapper.exec(sql);
    }

    private String replacePlaceHolder(List<String> fields, String sqlTemplate, String pattern){
        Pattern loopPattern = Pattern.compile("##" + pattern + ":.*:.*##");
        Matcher match = loopPattern.matcher(sqlTemplate);
        StringBuffer sb = new StringBuffer();
        while(match.find()){
            val loop = match.group(0).replace("##"+pattern+":", "").replace("##", "");
            val loopBy = loop.substring(0, loop.indexOf(":"));
            val loopStatement = loop.substring(loop.indexOf(":") +1);
            val replace = StringUtils.join(
                    fields.stream().map(it->
                            loopStatement.replaceAll("\\$column_name", it)
                    ).collect(Collectors.toList()),
                    loopBy
            );
            match.appendReplacement(sb, replace);
        }
        match.appendTail(sb);
        return sb.toString();
    }

    @SneakyThrows
    private List<PropertyDef> propertiesMapFields(List<PropertyDef> properties, Boolean useBuiltInFields, Map<String, String> propertyToFieldMapping, List<PropertyDef> dbExpList){
        Set<PropertyDef> newPropertySet = new HashSet<>();
        for(PropertyDef propertyDef : properties){
            val property = propertyDef.getProperty();
            val dataType = propertyDef.getDataType();

            boolean isBuiltInField= entityPropertyRule.isBuiltInField(dbExpList, propertyDef, useBuiltInFields);
            if (propertyDef.isExpEnable()) {
                continue;
            }
            if(dataType.isComponent()){
                if(!ComponentPropertyValue.componentHolder.containsKey(dataType)){
                    Class<? extends ComponentPropertyValue> componentClass =new Reflections("com.caidaocloud.hrpaas.metadata.sdk.dto").getSubTypesOf(ComponentPropertyValue.class).stream().filter(
                            it->dataType.equals(it.getAnnotation(DataComponent.class).dataType())
                    ).findFirst().get();
                    ComponentPropertyValue.componentHolder.put(dataType, componentClass.newInstance());
                }
                Map<String, Boolean> suffixToLongMap = ComponentPropertyValue.componentHolder.get(dataType).propertySuffixToWhetherLong();
                for(Map.Entry<String, Boolean> suffixToLong : suffixToLongMap.entrySet()){
                    if(!propertyToFieldMapping.containsKey(property + suffixToLong.getKey())){
                        if (!isBuiltInField){
                            newPropertySet.add(propertyDef);
                        }
                        propertyMapField(propertyToFieldMapping, property + suffixToLong.getKey(), isBuiltInField, suffixToLong.getValue(),propertyDef.isEncrypted());
                    }
                }
            }else{
                if(!propertyToFieldMapping.containsKey(property)){
                    boolean isLong = false;
                    if(null != propertyDef.getRules()){
                        isLong = propertyDef.getRules().stream().filter(it->
                                "maxLength".equals(it.getType())
                                        && StringUtils.isNotEmpty(it.getValue()) && Long.valueOf(it.getValue()) > 200).findFirst().isPresent();

                    }
                    if (!isBuiltInField){
                        newPropertySet.add(propertyDef);
                    }
                    propertyMapField(propertyToFieldMapping, property, isBuiltInField, isLong, propertyDef.isEncrypted());
                }
            }

        }
        return Sequences.sequence(newPropertySet).toList();
    }

    private void propertyMapField(Map<String, String> propertyToFieldMapping, String property, Boolean useBuiltInFields, Boolean isLong, boolean encrypted){
        if(propertyToFieldMapping.containsKey(property)){
            return;
        }
        if(useBuiltInFields){
            List<String> preBuilt = encrypted?SnakeCaseConvertor.preBuiltEncryptSnakeCase:
                    (isLong ? SnakeCaseConvertor.preBuiltLargeSnakeCase : SnakeCaseConvertor.preBuiltSnakeCase);
            for(String preBuiltSnakeCase : preBuilt){
                if(!propertyToFieldMapping.containsValue(preBuiltSnakeCase)){
                    propertyToFieldMapping.put(property, preBuiltSnakeCase);
                    break;
                }
            }
            if(!propertyToFieldMapping.containsKey(property)){
                throw new ServerException("属性数量过多");
            }
        }else{
            val field = SnakeCaseConvertor.toSnake(property);
            propertyToFieldMapping.put(property, field);
        }
    }

    @Override
    public String fetchColumnName(String identifier, String property){
        Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                .eq(EntityDefPo::getIdentifier, identifier);
        val result = this.mapper.selectOne(wrapper);
        if(null == result || StringUtils.isEmpty(result.getPropertyToFieldMapping())){
            return property;
        }
        Map<String, String> propertyToFieldMapping = FastjsonUtil.toObject(result.getPropertyToFieldMapping(), Map.class);
        val field = propertyToFieldMapping.get(property);
        if(field == null){
            return property;
        }else{
            return field;
        }
    }

    @Override
    public void addIndex(String identifier, List<String> properties) {
        val propertyToField = (Map<String, String>)FastjsonUtil.toObject(getDefPo(identifier, SecurityUserUtil.getSecurityUserInfo().getTenantId())
                .getPropertyToFieldMapping(), Map.class);
        val fields = properties.stream().map(it->
            propertyToField.getOrDefault(it, it)
        ).collect(Collectors.toList());
        val table = SnakeCaseConvertor.toSnake(identifier + "_" + getTenantId());
        if(fields.isEmpty()){
            return;
        }
        mapper.exec("create index idx_" +SnowUtil.nextId()+ " on "+table+
                " (" + colEscapeChar() +
                StringUtils.join(fields,
                        colEscapeChar()+"," +colEscapeChar()
                ) +colEscapeChar()+");");
    }

    @Cache(key = "'hr_paas_def_' + #args[0] + '_' + #args[1]",expire = 3600)
    public EntityDefPo getDefPo(String identifier, String tenantId){
        boolean addTenant = false;
        if(StringUtils.isEmpty(SecurityUserUtil.getSecurityUserInfo().getTenantId())){
            SecurityUserInfo user = new SecurityUserInfo();
            user.setTenantId(tenantId);
            addTenant = true;
            SecurityUserUtil.setSecurityUserInfo(user);
        }
        try{
            Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                    .eq(EntityDefPo::getIdentifier, identifier);
            val result = this.mapper.selectOne(wrapper);
            return result;
        }finally {
            if(addTenant){
                SecurityUserUtil.removeSecurityUserInfo();
            }
        }
    }

    @CacheDelete(
            @CacheDeleteKey(value = "'hr_paas_def_' + #args[0] + '_' + #args[1]")
    )
    public void clearDefPoCache(String identifier, String tenantId){
        return;
    }

    /**
     * 更新模型属性mapping
     */
    public void removeDictTextMapping(String identifier){
        Wrapper<EntityDefPo> wrapper = new LambdaUpdateWrapper<EntityDefPo>()
                .eq(EntityDefPo::getIdentifier, identifier);
        val exist = this.mapper.selectOne(wrapper);

        // 移除dict.text字段mapping
        log.info("before remove dict text >>>>>>>>>>>>> {}", exist.getPropertyToFieldMapping());
        Map<String,String> propertiesToFieldMap = FastjsonUtil.toObject(exist.getPropertyToFieldMapping(), Map.class);
        Map<String, String> newMapping = new HashMap<>();
        Iterator<Map.Entry<String, String>> iterator = propertiesToFieldMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            if (entry.getKey().endsWith("dict.text")) {
                newMapping.put(entry.getKey() + ".deprecated", entry.getValue());
            }else {
                newMapping.put(entry.getKey(), entry.getValue());
            }
        }
        exist.setPropertyToFieldMapping(FastjsonUtil.toJson(newMapping));
        log.debug("after remove dict text >>>>>>>>>>>>> {}", exist.getPropertyToFieldMapping());

        this.mapper.update(exist, wrapper);
        clearDefPoCache(identifier, getTenantId());
    }
}
