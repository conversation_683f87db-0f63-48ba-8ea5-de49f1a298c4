package com.caidaocloud.hrpaas.service.interfaces.facade.dynamic;

import com.caidaocloud.hrpaas.paas.common.dto.UserDynamicConfig;
import com.caidaocloud.hrpaas.service.application.dynamic.service.DynamicColumnService;
import com.caidaocloud.hrpaas.service.domain.dynamic.entity.DynamicColumnConfig;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicColumnConfigDto;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicPropertyDto;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/hrpaas/v1")
@Api(value = "/api/hrpaas/v1")
@Slf4j
public class DynamicColumnController {

    @Autowired
    private DynamicColumnService dynamicColumnService;
    @PostMapping("/dynamicTable/set")
    public Result<Boolean> dynamicTableSet(@RequestBody DynamicColumnConfigDto config) {
        dynamicColumnService.save(config);
        return Result.ok();
    }

    @GetMapping("/dynamicTable/set/list")
    public Result<List<DynamicColumnConfig>> dynamicTableSet() {
        return Result.ok(dynamicColumnService.list());
    }

    @GetMapping("/dynamicTable/set/load")
    public Result<DynamicColumnConfig> dynamicTableSet(@RequestParam String code) {
        return Result.ok(dynamicColumnService.loadByCode(code.toUpperCase()));
    }

    @PostMapping("/dynamicTable/set/user")
    public Result<Boolean> userDynamicTableSet(@RequestBody UserDynamicConfig config) {
        dynamicColumnService.saveUser(config, String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        return Result.ok();
    }

    @GetMapping("/dynamicTable/set/user/load")
    public Result<UserDynamicConfig> userDynamicTableLoad(@RequestParam String code) {
        return Result.ok(dynamicColumnService.loadUser(code.toUpperCase(), String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId())));
    }

    @GetMapping("/dynamicTable/set/load/onlyProperties")
    public Result<List<DynamicPropertyDto>> dynamicTableSetOnlyProperties(@RequestParam String code) {
        return Result.ok(dynamicColumnService.loadByCode(code.toUpperCase()).getProperties());
    }

    @PostMapping("/dynamicTable/set/load")
    public Result<List<DynamicColumnConfig>> dynamicTablesSet(@RequestBody List<String> codes) {
        return Result.ok(dynamicColumnService.loadByCode(codes));
    }
}
