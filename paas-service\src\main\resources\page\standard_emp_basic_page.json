{"key": "page.detail.basicEmpInfo", "type": "EmpInfo", "label": "员工信息详情", "script": "/**\\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系才到平台的技术支持获得服务（可能收费）。\\n* 我们可以用 JS 面板来开发一些定制度高功能。\\n* 你可以点击面板上方的 「使用帮助」了解。\\n*/\\n\\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\\nexport function didMount() {\\n\\tconsole.log(`「页面 JS」：当前页面地址 ${location.href}`);\\n // 更多相关 API 请参考：https://aliwork.com/developer/API\\n}", "childList": [{"property": "workno", "name": "员工工号", "identifier": "entity.hr.EmpWorkInfo"}, {"property": "name", "name": "员工姓名", "identifier": "entity.hr.EmpWorkInfo"}, {"property": "empStatus", "name": "员工状态", "identifier": "entity.hr.EmpWorkInfo"}, {"property": "organizeTxt", "name": "所属组织名称", "identifier": "entity.hr.EmpWorkInfo"}, {"property": "postTxt", "name": "岗位名称", "identifier": "entity.hr.EmpWorkInfo"}, {"property": "companyEmail", "name": "员工公司邮箱", "identifier": "entity.hr.EmpWorkInfo"}, {"property": "sex", "name": "员工性别", "identifier": "entity.hr.EmpPrivateInfo"}, {"property": "phone", "name": "手机号", "identifier": "entity.hr.EmpPrivateInfo"}, {"property": "organizeTxt", "name": "任职组织名称", "identifier": "entity.hr.Contract"}]}