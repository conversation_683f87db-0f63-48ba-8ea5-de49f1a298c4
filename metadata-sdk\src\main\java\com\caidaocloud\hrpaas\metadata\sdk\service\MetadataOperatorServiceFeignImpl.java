package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.hrpaas.metadata.sdk.feign.IMdMetadataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.query.MdQueryTL;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMetadataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.ResultUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class MetadataOperatorServiceFeignImpl implements MetadataOperatorService {

    @Autowired
    private IMetadataFeign metadataFeign;

    @Autowired
    private IMdMetadataFeign mdMetadataFeign;

    @Override
    public MetadataVo load(String identifier) {
        Result result = MdQueryTL.get()? mdMetadataFeign.one(identifier): metadataFeign.one(identifier);
        if(result.isSuccess()){
            return JsonEnhanceUtil.toObject(result.getData(), MetadataVo.class);
        }
        ResultUtil.checkMetadataResult(result);
        return null;
    }

}
