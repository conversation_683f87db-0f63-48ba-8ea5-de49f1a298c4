package com.caidaocloud.metadata.infrastructure.configuration;

import com.caidaocloud.metadata.application.filter.TokenHandlerInterceptorAdapter;
import com.caidaocloud.metadata.infrastructure.repository.transaction.TransactionInterceptorAdapter;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Resource
    private TokenHandlerInterceptorAdapter tokenHandlerInterceptorAdapter;

    @Resource
    private TransactionInterceptorAdapter transactionInterceptorAdapter;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tokenHandlerInterceptorAdapter);
        registry.addInterceptor(transactionInterceptorAdapter);
    }

}