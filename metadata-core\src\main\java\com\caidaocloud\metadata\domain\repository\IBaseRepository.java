package com.caidaocloud.metadata.domain.repository;

import com.caidaocloud.metadata.domain.dto.BaseSearchDto;
import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.googlecode.totallylazy.Option;

import javax.annotation.PostConstruct;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

public interface IBaseRepository<T extends BaseEntity> {

    @PostConstruct
    default void registerDomainRepository(){
        Type[] types = this.getClass().getGenericInterfaces();
        ParameterizedType parameterizedType = (ParameterizedType) ((Class) types[0]).getGenericInterfaces()[0];
        Class<T> domainClass = ((Class<T>)(parameterizedType)
                .getActualTypeArguments()[0]);
        RepositoryHolder.registerRepository(domainClass, this);
    }

    String insert(T entity);

    void update(T entity);

    default Option<T> load(String id){
        throw new UnsupportedOperationException("not supported");
    }

    // default List<T> getListByPage(Map<String, Object> query, int pageNo, int pageSize){
    //     throw new UnsupportedOperationException("not supported");
    // }

    default List<T> getListByPage(BaseSearchDto query, int pageNo, int pageSize){
        throw new UnsupportedOperationException("not supported");
    }

    default void remove(String id){
        throw new UnsupportedOperationException("not supported");
    }
}
