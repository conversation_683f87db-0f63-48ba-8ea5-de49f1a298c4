package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceAuthMode;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceRequestMethod;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceRequestType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class UiDataSourceRenderDto implements Serializable {
    private final static long serialVersionUID = 1L;

    /**
     * 请求 url
     */
    @ApiModelProperty("请求url")
    private String url;

    /**
     * 请求方法，当前仅支持简单 get/post
     */
    @ApiModelProperty("请求方法")
    private DataSourceRequestMethod requestMethod;

    /**
     * 请求参数
     */
    @ApiModelProperty("请求参数")
    private Map<String, Object> requestParams;

    /**
     * 请求方式
     */
    @ApiModelProperty("请求方式")
    private DataSourceAuthMode requestWay;

    /**
     * 请求参数格式
     * 如 JSON 等格式
     */
    @ApiModelProperty("请求格式：JSON")
    private DataSourceRequestType contentType;
}
