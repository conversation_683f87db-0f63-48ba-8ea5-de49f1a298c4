package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AllAuthScopeRestrictionHandler implements ScopeRestrictionHandler {

    @Override
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        return Lists.list(SecurityUserUtil.getSecurityUserInfo().getTenantId());
    }
}
