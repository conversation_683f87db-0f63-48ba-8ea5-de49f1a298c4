package com.caidaocloud.metadata.infrastructure.repository.impl.util;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.MultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SimpleDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.infrastructure.repository.impl.EntityDefMapper;
import com.caidaocloud.metadata.infrastructure.repository.impl.EntityDefRepositoryImpl;
import com.caidaocloud.metadata.infrastructure.repository.po.EntityDefPo;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Queues;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.stream.Collectors;

@Data
public class QuerySqlGenInfo {

    private String tenantId;

    private String colEscape;

    private String dbType;

    private List<String> specifyExpProperty = new ArrayList<>();

    private boolean expression = false;


    public QuerySqlGenInfo(String tenantId, String colEscape, String dbType){
        this.tenantId = tenantId;
        this.colEscape = colEscape;
        this.dbType = dbType;
    }

    private Map<String, EntityDef> entityDefMap = new HashMap<>();

    private Map<String, EntityDefPo> entityDefPoMap = new HashMap<>();

    private Map<String, PathPrefixAndProperties> relatedPropertiesMap = new HashMap<>();

    private boolean queryOneTable = true;

    private boolean byFilter = false;

    private boolean group = false;

    private String countSql;

    private String selectSql;

    private String bidSql;

    private Map<String, PropertyDataType> relationDataTypes = Maps.map();

    private EntityDefRepositoryImpl defRep(){
        return SpringUtil.getBean(EntityDefRepositoryImpl.class);
    }

    private EntityDefMapper defMapper(){
        return SpringUtil.getBean(EntityDefMapper.class);
    }

    private String limit(int pageNo, int pageSize){
        if(pageSize == -1){
            return " ";
        }
        if("postgresql".equals(dbType)){
            return new StringBuilder(" limit ").append(pageSize).append(" offset ").append(pageSize*(pageNo-1)).append(" ").toString();
        }else{
            return new StringBuilder(" limit ").append(pageSize*(pageNo-1)).append(", ").append(pageSize).append(" ").toString();
        }

    }

    public EntityDef fetchDef(String identifier){
        if(!entityDefMap.containsKey(identifier)){
            val def = fetchDefPo(identifier).toEntity(EntityDef.class);
            entityDefMap.put(identifier, def);
            return def;
        }
        return entityDefMap.get(identifier);
    }

    public EntityDefPo fetchDefPo(String identifier){
        if(!entityDefPoMap.containsKey(identifier)){
            val def = defRep().getDefPo(identifier, tenantId);
            entityDefPoMap.put(identifier, def);
            return def;
        }
        return entityDefPoMap.get(identifier);
    }

    public Object[] generateSqlAndReturnJdbcParams(String identifier, String bid, long time, DataFilter filter, List<String> specifyProperties, int pageSize, int pageNo, String orderBy) {
        for(PathPrefixAndProperties properties : relatedPropertiesMap.values()){
            properties.generateSql(identifier, "", time);
        }
        String orderByColumn = "update_time";
        String selectOrderByColumn = "update_time";
        if(StringUtils.isNotEmpty(orderBy)){
            orderByColumn= SnakeCaseConvertor.toSnake(
                    SqlGeneratorUtil.fetchColumnName(fetchDefPo(identifier), orderBy.replaceAll("\\$",".")));
            selectOrderByColumn = selectOrderByColumn +","+orderByColumn;
        }

        Queue<String> readyShortNames = Queues.newLinkedBlockingQueue(
                Lists.list("m","n","o"));
        val tableName = SnakeCaseConvertor.toSnake(identifier) +
                "_" + tenantId;
        val selectTableShortName = readyShortNames.poll();
        List<String> selectTableShortNames = Lists.list(selectTableShortName);
        StringBuilder joinTableBuilder = new StringBuilder();
        for(PathPrefixAndProperties properties : relatedPropertiesMap.values()){
            properties.appendSql(selectTableShortNames, joinTableBuilder,
                    readyShortNames, selectTableShortName, time);
        }
        val selectFields = StringUtils.join(
                Sequences.sequence(selectTableShortNames).map(it->it+".*").toList(),
                ","
        );
        if(byFilter){
            Pair<String, Object[]> whereCause = SqlGeneratorUtil.toWhereCauses(identifier, filter);
            String where = whereCause.first();
            Object[] params = whereCause.second();
            String returnFields = "*";
            if(!specifyProperties.isEmpty()){
                specifyProperties = SqlGeneratorUtil.filterExpProperty(fetchDef(identifier),specifyProperties);
                returnFields = StringUtils.join(specifyProperties.stream().map(specifyProperty->
                        SnakeCaseConvertor.toSnake(SqlGeneratorUtil.fetchColumnName(fetchDefPo(identifier), specifyProperty))
                ).collect(Collectors.toList()), ",");
            }

            if(queryOneTable){
                StringBuilder countBuilder = new StringBuilder("select count(1) ");
                StringBuilder selectBuilder = new StringBuilder("select " + returnFields + " ");
                Lists.list(countBuilder, selectBuilder).forEach(sql->{
                    sql.append(" from ")
                            .append(tableName).append(appendWhereDataTime(time))
                            .append(" and ").append(where);
                });
                String ascOrDesc = orderByColumn.contains(" asc") ? " ": " desc ";
                selectBuilder.append(" order by " + orderByColumn + ascOrDesc).append(limit(pageNo, pageSize));
                selectSql = selectBuilder.toString();
                countSql = countBuilder.toString();
            }else if(!group){
                StringBuilder countBuilder = new StringBuilder("select count(1) from (select ");
                StringBuilder selectBuilder = new StringBuilder("select " + returnFields + " from (select ");
                Lists.list(countBuilder, selectBuilder).forEach(sql->{
                    sql.append(selectFields);
                    sql.append(" from (").append("select * from ")
                            .append(tableName).append(appendWhereDataTime(time)).append(") ")
                            .append(selectTableShortName);
                    sql.append(joinTableBuilder);
                    sql.append(") result");
                    sql.append(" where ").append(where);
                });
                String ascOrDesc = orderByColumn.contains(" asc") ? " ": " desc ";
                selectBuilder.append(" order by "+orderByColumn+ascOrDesc).append(limit(pageNo, pageSize));
                selectSql = selectBuilder.toString();
                countSql = countBuilder.toString();
            }else{
                StringBuilder countBuilder = new StringBuilder("select count(1) from (    select bid from (select ");
                StringBuilder bidBuilder = new StringBuilder("select bid from (  select bid,"+selectOrderByColumn+" from (select ");
                StringBuilder selectBuilder = new StringBuilder("select " + returnFields + " from (select ");
                Lists.list(countBuilder, bidBuilder, selectBuilder).forEach(sql->{
                    sql.append(selectFields);
                    sql.append(" from (").append("select * from ")
                            .append(tableName).append(appendWhereDataTime(time)).append(") ")
                            .append(selectTableShortName);
                    sql.append(joinTableBuilder);
                    sql.append(") result");
                    sql.append(" where ").append(where);
                });
                countBuilder.append(" group by bid) countTable");
                String ascOrDesc = orderByColumn.contains(" asc") ? " ": " desc ";
                bidBuilder.append("  group by bid,update_time ) grouped order by "+orderByColumn+ascOrDesc).append(limit(pageNo, pageSize));
                selectBuilder.append(" and bid in (##bidList##) order by "+orderByColumn+ascOrDesc);
                selectSql = selectBuilder.toString();
                bidSql = bidBuilder.toString();
                countSql = countBuilder.toString();
            }
            return params;
        }else{
            StringBuilder sql = new StringBuilder("select ");
            sql.append(selectFields);
            sql.append(" from (").append("select * from ")
                    .append(tableName).append(appendWhereDataTime(time)).append(" and bid = ?) ")
                    .append(selectTableShortName);
            sql.append(joinTableBuilder);
            selectSql = sql.toString();
            return new Object[]{bid};
        }
    }

    private String appendWhereDataTime(long time){
        if(time == -1l){
            return " where data_start_time >= 0";
        }
        return new StringBuilder().append(" where data_start_time <= ")
                .append(time).append(" and data_end_time >= ").append(time).toString();
    }

    public void addFilter(DataFilter filter) {
        byFilter = true;
        if(filter instanceof SimpleDataFilter){
            if(((SimpleDataFilter) filter).getProperty().contains(".") ){
                SqlGeneratorUtil.addProperties(Lists.list(((SimpleDataFilter) filter).getProperty()), ".");
            }
        }else if(filter instanceof MultiDataFilter){
            ((MultiDataFilter) filter).getFilters().forEach(it->addFilter(it));
        }
    }


    public void putRelationDataType(String relation, PropertyDataType dataType) {
        relationDataTypes.put(relation, dataType);
    }

    public void addSpecifyExpProperty(String property) {
        this.specifyExpProperty.add(property);
    }
}
