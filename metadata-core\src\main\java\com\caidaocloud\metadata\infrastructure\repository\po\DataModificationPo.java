package com.caidaocloud.metadata.infrastructure.repository.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.metadata.domain.entity.DataModification;
import com.caidaocloud.metadata.domain.enums.OperationTarget;
import com.caidaocloud.metadata.domain.enums.OperationType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;

@Data
@Accessors(chain = true)
@TableName("paas_data_modification")
public class DataModificationPo extends BasePo<DataModificationPo, DataModification> {

    private String identifier;
    
    private String targetId;
    
    private OperationTarget operationTarget;

    @TableField(jdbcType = JdbcType.OTHER)
    private String data;
    
    private long createTime;
    
    private String createBy;
    
    private OperationType operation;
    
    private long startTime;
}
