package com.caidaocloud.hrpaas.metadata.sdk.vo;

import com.caidaocloud.hrpaas.metadata.sdk.enums.EntityRelationType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MetadataRelationVo {

    @ApiModelProperty("模型全限定名")
    private String identifier;

    @ApiModelProperty("是否内置")
    private boolean builtIn;

    @ApiModelProperty("关联模型全限定名")
    private String relatedIdentifier;

    @ApiModelProperty("属性")
    private String property;

    @ApiModelProperty("属性名")
    private String propertyName;

    @ApiModelProperty("关联类型")
    private EntityRelationType relationType;

    @ApiModelProperty("是否多选")
    private boolean multiple;

}
