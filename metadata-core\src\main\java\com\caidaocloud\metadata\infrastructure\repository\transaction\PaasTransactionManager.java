package com.caidaocloud.metadata.infrastructure.repository.transaction;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import com.caidaocloud.hrpaas.metadata.sdk.transaction.exception.PaasTransactionException;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.metadata.infrastructure.repository.enums.TransactionThreadStatus;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sets;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * paas事务管理器，处理业务端的事务需求
 * <AUTHOR> Zhou
 * @date 2022/9/29
 */
@Slf4j
public class PaasTransactionManager {

	private ConcurrentMap<String, List<String>> transactionLocks = new ConcurrentHashMap<>();

	public void addLock(String transactionId, String lock){
		val locks = transactionLocks.get(transactionId);
		if(null != locks){
			locks.add(lock);
		}
	}

	@Autowired
	private Locker locker;

	@Autowired
	private StringRedisTemplate redisTemplate;

	@Value("${caidaocloud.paas.transaction.expire.time:20}")
	private int expireTime;

	private final ThreadPoolExecutor threadPoolExecutor;

	private final ExecutorService expiredCheckExecutor;

	private PlatformTransactionManager transactionManager;

	private final Map<String, TransactionRunner> runnerHolder = new ConcurrentHashMap<>();

	public PaasTransactionManager(ThreadPoolExecutor threadPoolExecutor, PlatformTransactionManager platformTransactionManager, int expireInterval) {
		this.threadPoolExecutor = threadPoolExecutor;
		this.transactionManager = platformTransactionManager;

		ThreadFactory expireCheckerFactory = new ThreadFactoryBuilder().setNameFormat("Transaction-Expire-Checker-%d").build();
		ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(1, expireCheckerFactory);
		this.expiredCheckExecutor = scheduledExecutorService;
		scheduledExecutorService.scheduleWithFixedDelay(new ExpiredTransactionChecker(), 0, expireInterval, TimeUnit.SECONDS);
	}

	/**
	 * 开启事务
	 * @param transactionId
	 */
	public void doBegin(String transactionId) {
		TransactionRunner runner = new TransactionRunner(transactionId, transactionManager, expireTime);
		threadPoolExecutor.execute(runner);
		runnerHolder.put(transactionId, runner);
		transactionLocks.put(transactionId, Lists.list());
	}

	/**
	 * 获取事务
	 * @param transactionId
	 * @return
	 */
	public TransactionRunner getRunner(String transactionId) {
		TransactionRunner runner = runnerHolder.get(transactionId);
		if (runner == null) {
			throw new PaasTransactionException(String.format("Could not find transaction,tx id=%s", transactionId));
		}
		return runner;
	}

	/**
	 * 事务提交
	 * @param transactionId
	 */
	public void doCommit(String transactionId) {
		TransactionRunner runner = getRunner(transactionId);
		runner.commit();
	}

	/**
	 * 事务回滚
	 * @param transactionId
	 */
	public void doRollback(String transactionId) {
		TransactionRunner runner = getRunner(transactionId);
		runner.rollback();
	}

	/**
	 * 事务超时检测
	 */
	public class ExpiredTransactionChecker implements Runnable {
		@Override
		public void run() {
			long currentTimeMillis = System.currentTimeMillis();
			for (String key : runnerHolder.keySet()) {
				TransactionRunner runner = runnerHolder.get(key);
				if (runner.isRunning() && runner.isExpired(currentTimeMillis)) {
					log.info("Rolling back expired transaction,tx id={}", key);
					runner.rollback();
				}
			}
		}
	}


	public class TransactionRunner implements Runnable {
		// 需要处理的任务
		private BlockingQueue<FutureTask> taskQueue = new LinkedBlockingQueue<>(2);
		// 事务状态
		private AtomicInteger txStatus = new AtomicInteger(TransactionThreadStatus.INIT.val);
		// 事务id
		private final String transactionId;
		// 事务开始时间，
		private long startTime;
		// 事务超时时间
		private long expireTime;
		// 事务执行线程
		private Thread thread;
		private final TransactionTemplate transactionTemplate;
		private SecurityUserInfo userInfo;
		private final CountDownLatch countDownLatch = new CountDownLatch(1);


		public TransactionRunner(String transactionId, PlatformTransactionManager transactionManager) {
			this(transactionId, transactionManager, 20, TimeUnit.SECONDS);
		}

		public TransactionRunner(String transactionId, PlatformTransactionManager transactionManager, long time) {
			this(transactionId, transactionManager, time, TimeUnit.SECONDS);
		}

		public TransactionRunner(String transactionId, PlatformTransactionManager transactionManager, long time, TimeUnit timeUnit) {
			this.transactionId = transactionId;
			this.transactionTemplate = new TransactionTemplate(transactionManager);
			this.startTime = System.currentTimeMillis();
			this.expireTime = this.startTime + timeUnit.toMillis(time);
			this.userInfo = SecurityUserUtil.getSecurityUserInfo();
		}

		@Override
		public void run() {
			try {
				SecurityUserUtil.setSecurityUserInfo(userInfo);
				// 记录当前执行线程
				thread = Thread.currentThread();
				txStatus.set(TransactionThreadStatus.RUNNING.val);
				// 手动开启事务
				transactionTemplate.execute(new TransactionCallbackWithoutResult() {
					@SneakyThrows
					@Override
					protected void doInTransactionWithoutResult(TransactionStatus status) {
						FutureTask task;
						while ((task = getTask()) != null) {
							task.run();
						}
						if (txStatus.get() == TransactionThreadStatus.ROLLING_BACK.val) {
							status.setRollbackOnly();
						}
					}
				});
			}
			finally {
				cleanUp();
			}
		}

		private void cleanUp() {
			// 设置状态为 terminated（暂无实际作用）
			txStatus.set(TransactionThreadStatus.TERMINATED.val);
			val timeRange = redisTemplate.opsForValue().get("transactionTimeRange" + transactionId);
			// 从map中移除
			runnerHolder.remove(transactionId);
			releaseTransactionLock(timeRange, transactionId);
			countDownLatch.countDown();
			SecurityUserUtil.removeSecurityUserInfo();
		}


		private FutureTask getTask() {
			// 当任务队列为空，事务状态不为running时，返回null，结束事务
			while (!taskQueue.isEmpty() || txStatus.get() == TransactionThreadStatus.RUNNING.val) {
				try {
					FutureTask task = taskQueue.take();
					return task;
				}
				catch (InterruptedException e) {
					log.debug("Thread interrupted");
				}
			}
			return null;
		}

		public void offerTask(FutureTask task) {
			// 事务状态不为running时，不能继续提交task
			int status = txStatus.get();
			if (status > TransactionThreadStatus.RUNNING.val) {
				throw new PaasTransactionException(String.format("Transaction is not running,could not offer task,tx id=%s,status=%d", transactionId, status));
			}
			boolean flag = this.taskQueue.offer(task);
			if (!flag) {
				throw new PaasTransactionException(String.format("Failed to offer task,tx id=%s,queue size=%d", transactionId, taskQueue.size()));
			}
		}

		@SneakyThrows
		public void commit() {
			boolean flag = txStatus.compareAndSet(TransactionThreadStatus.RUNNING.val, TransactionThreadStatus.COMMITTING.val);
			if (!flag) {
				throw new PaasTransactionException("Failed to set transaction status to commit");
			}
			thread.interrupt();
			countDownLatch.await();
		}

		public void rollback() {
			boolean flag = txStatus.compareAndSet(TransactionThreadStatus.RUNNING.val, TransactionThreadStatus.ROLLING_BACK.val);
			if (!flag) {
				throw new PaasTransactionException("Failed to set transaction status to rollback");
			}
			// 回滚时，清空任务队列
			taskQueue.clear();
			thread.interrupt();
		}

		public boolean isRunning() {
			return TransactionThreadStatus.RUNNING.val == txStatus.get();
		}

		public boolean isExpired(long currentTimeMillis) {
			return currentTimeMillis > expireTime;
		}
	}

	private void releaseTransactionLock(String timeRange, String transactionId){
		val timeRangeNew = StringUtils.isNotEmpty(timeRange) ? StringUtils.substringBefore(timeRange, ",")  + ","
					+ System.currentTimeMillis() : "";
		val locks = transactionLocks.get(transactionId);
		if(null != locks){
			locks.forEach(lock->{
				try{
					log.info("unlock key : " + lock);
					locker.getLock(lock).unlock();
					if(StringUtils.isNotEmpty(timeRangeNew)){
						log.info("set lock time key : " + "transactionLockTimeRange" + lock +
								"-" + transactionId+", value:" + timeRangeNew);
						redisTemplate.opsForValue().set("transactionLockTimeRange" + lock +
										"-" + transactionId,
								timeRangeNew, 30, TimeUnit.SECONDS);
					}
				}catch (Exception e){

				}
			});
			locks.clear();
			transactionLocks.remove(transactionId);
		}
	}
}
