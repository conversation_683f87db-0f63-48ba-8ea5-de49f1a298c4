package com.caidaocloud.hrpaas.service.application.feildMapping.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMetadataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.service.application.feildMapping.dto.ChangeDefDto;
import com.caidaocloud.hrpaas.service.application.feildMapping.feign.HrFeignClient;
import com.caidaocloud.hrpaas.service.application.form.dto.FormPropertyMapping;
import com.caidaocloud.hrpaas.service.application.form.dto.MappingConfigDto;
import com.caidaocloud.hrpaas.service.domain.feildMapping.entity.MappingTypeEnum;
import com.caidaocloud.hrpaas.service.domain.feildMapping.entity.PreMappingConfig;
import com.caidaocloud.hrpaas.service.interfaces.vo.feildMapping.MappingMetadataSelectVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.feildMapping.MappingMetadataVo;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.metadata.application.service.MetadataService;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MappingConfigService {
    @Resource
    private IMetadataFeign metadataFeign;
    @Resource
    private HrFeignClient hrFeignClient;
    @Resource
    private MetadataService metadataService;


    public List<PreMappingConfig> listByType(MappingTypeEnum type) {
        return PreMappingConfig.listByCategory(type.name());
    }

    @Transactional
    public void deleteConfig(String id) {
        //删除校验
        deleteCheckMappingConfig(id);
        PreMappingConfig config = PreMappingConfig.load(id);
        PreCheck.preCheckNotNull(config, ErrorMessage.fromCode("caidao.exception.error_90006"));
        config.delete();
    }

    private void deleteCheckMappingConfig(String id) {
        Result<List<ChangeDefDto>> result = hrFeignClient.getByMappingConfigId(id);
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(result.getData()), "该映射关系已被引用，无法删除！");
    }



    @Transactional
    public void saveConfig(MappingConfigDto dto) {
        //模型字段映射名称校验
        checkMappingConfigName(dto);
        // 处理模型高级组件
        checkDataType(dto);

        PreMappingConfig config = ObjectConverter.convert(dto, PreMappingConfig.class);
        config.save();
    }

    private void checkMappingConfigName(MappingConfigDto dto) {
        if (StringUtils.isNotEmpty(dto.getName())) {
            PreCheck.preCheckArgument(dto.getName().length() > 20, "名称长度超过20个字符！");
            PreMappingConfig config = PreMappingConfig.loadByName(dto.getName());
            PreCheck.preCheckArgument(config != null, "名称已存在！");
        }
    }

    /**
     * 模型字段映射名称校验
     * @param dto
     */
    private void editCheckMappingConfig(MappingConfigDto dto) {
        if (StringUtils.isNotEmpty(dto.getName())) {
            PreCheck.preCheckArgument(dto.getName().length() > 20, "名称长度超过20个字符！");
            Result<List<ChangeDefDto>> result = hrFeignClient.getByMappingConfigId(dto.getId());
            if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
                PreCheck.preCheckArgument(!result.getData().get(0).getName().equals(dto.getName()), "该映射关系已被引用，无法修改名称！");
            }
        }
    }


    @Transactional
    public void editConfig(MappingConfigDto dto) {
        //模型字段映射名称校验
        editCheckMappingConfig(dto);
        // 处理模型高级组件
        checkDataType(dto);

        PreMappingConfig dbData = PreMappingConfig.load(dto.getId());
        PreCheck.preCheckNotNull(dbData, ErrorMessage.fromCode("caidao.exception.error_90006"));
        BeanUtil.copyWithNoValue(dto, dbData);
        dbData.update();
    }

    public PreMappingConfig loadConfig(String id) {
        return PreMappingConfig.load(id);
    }

    private void checkDataType(MappingConfigDto dto) {
        Map<String, MetadataPropertyVo> sourcePropertyMap = fetchAllProperties(dto.getSource());
        Map<String, MetadataPropertyVo> targetPropertyMap = fetchAllProperties(dto.getTarget());

        for (FormPropertyMapping mapping : dto.getMapping()) {
            MetadataPropertyVo sp = sourcePropertyMap.get(mapping.getSource());
            MetadataPropertyVo tp = targetPropertyMap.get(mapping.getTarget());
            PreCheck.preCheckArgument(sp.getDataType() != tp.getDataType(), String.format(MessageHandler.getMessage("caidao.exception.mapping.data_type.match", WebUtil.getRequest()), sp.getName(), tp.getName()));
            mapping.setDataType(sp.getDataType());
        }
    }

    private Map<String, MetadataPropertyVo> fetchAllProperties(String source2) {
        MetadataVo source = JsonEnhanceUtil.toObject(metadataFeign.one(source2)
                .getData(), MetadataVo.class);
        return source.fetchAllProperties().stream()
                .collect(Collectors.toMap(MetadataPropertyVo::getProperty, obj -> obj));
    }

    public MappingMetadataSelectVo loadMappingMetadataList() {
        MappingMetadataSelectVo vo = new MappingMetadataSelectVo();
        List<MappingMetadataVo> sourceMetaList = new ArrayList<>();
        List<MappingMetadataVo> targetMetaList = new ArrayList<>();
        PageResult<EntityDef> result = metadataService.page(null, 1, 1000);
        if (result != null && result.getItems() != null) {
            for (EntityDef def : result.getItems()) {
                MappingMetadataVo mappingMetadataVo = new MappingMetadataVo();
                mappingMetadataVo.setIdentifier(def.getIdentifier());
                mappingMetadataVo.setName(def.getName());
                targetMetaList.add(mappingMetadataVo);

                mappingMetadataVo = new MappingMetadataVo();
                mappingMetadataVo.setIdentifier(def.getIdentifier());
                mappingMetadataVo.setName(def.getName());
                sourceMetaList.add(mappingMetadataVo);
            }
        }
        vo.setSource(sourceMetaList);
        vo.setTarget(targetMetaList);
        return vo;
    }

    public List<PreMappingConfig> listByIds(String ids) {
        String[] split = ids.split(",");
        return PreMappingConfig.listByids(Arrays.asList(split));
    }

    public List<PreMappingConfig> listByModelMapping(String id) {
//        Result<List<FormDefDto>> result = tenantFeignClient.defList(false);
//        if (result.isSuccess() && result.getData() != null) {
//            List<String> ids = result.getData().stream().map(FormDefDto::getId).collect(Collectors.toList());
        return PreMappingConfig.listFormByCategory(MappingTypeEnum.MODEL_MAPPING.name()).stream().filter(st -> {
            String[] split = st.getSource().split("\\.");
            if (split.length > 2) {
                return id.equals(split[2]);
            }
            return false;
        }).collect(Collectors.toList());
//        }
    }
}
