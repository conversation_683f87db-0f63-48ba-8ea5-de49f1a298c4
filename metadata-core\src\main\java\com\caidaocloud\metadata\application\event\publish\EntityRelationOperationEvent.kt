package com.caidaocloud.metadata.application.event.publish

import com.caidaocloud.hrpaas.metadata.sdk.enums.RelationOperationType
import com.caidaocloud.hrpaas.metadata.sdk.event.AbstractInteriorEvent
import com.caidaocloud.metadata.domain.entity.EntityDataChange

const val RELATION_MODIFICATION = "RELATION_MODIFICATION"

data class EntityRelationOperationEvent(
    val identifier : String,
    val targetId: String,
    val data: List<EntityDataChange>,
    val operation: RelationOperationType,
    val startTime : Long,
    val eventTime : Long
) : AbstractInteriorEvent(RELATION_MODIFICATION)