package com.caidaocloud.hrpaas.service.form;

import java.util.Optional;

import com.caidaocloud.hrpaas.paas.common.event.FormPublishEvent;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.hrpaas.service.application.form.dto.FormDefDto;
import com.caidaocloud.hrpaas.service.application.form.service.FormDefService;
import com.caidaocloud.hrpaas.service.domain.condition.repository.IConditionDefRepository;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormData;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDataStatus;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormTarget;
import com.caidaocloud.hrpaas.service.domain.form.repository.IFormDefRepository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl.FormDefRepositoryImpl;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Option;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = HrPaasApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class FormDefServiceTest {

    @SpyBean
    private FormDefRepositoryImpl formDefRepository;


    @InjectMocks
    private FormDefService formDefService;


   @Before
    public void mockUser() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
       SecurityUserUtil.setSecurityUserInfo(userInfo);
        // Mock the user details however they are needed for the test
    }

    @Test
    public void event(){
        // new FormPublishEvent("2002872552970240",null, SecurityUserUtil.getSecurityUserInfo()
        //         .getTenantId()).publish();
        Option<FormDef> formDefs = FormDef.loadById("2002903484119040");
        SpringUtil.getBean(FormDefService.class).registerWorkflow(formDefs.get(), "11");
    }

    @Test
    public void addDef_whenCodeDoesNotExist_shouldSaveFormDef() {

        // Arrange
        FormDefDto formDefDto = new FormDefDto();
        formDefDto.setName("code");
        formDefDto.setTarget(FormTarget.EMP);
        formDefDto.setWorkflowNodeAvailable(true);
        formDefDto.setWorkflowAvailable(true);
        formDefDto.setCode("uniqueCode");

        // when(SecurityUserUtil.getSecurityUserInfo()).thenReturn(mockUser());
        when(formDefRepository.loadByCode(formDefDto.getCode())).thenReturn(Option.none());

        // Act
        String formId = formDefService.addDef(formDefDto);

        // Assert
        // You should verify that the formDef is saved, and other interactions occurred as expected
        FormDef formDef = formDefService.one(formId);
        assertNotNull(formDef);
        assertEquals(formDefDto.getCode(), formDef.getCode());
        formDefService.deleteDef(formId);
    }

    @Test
public     void addDef_whenCodeAlreadyExists_shouldThrowException() {
        // Arrange
        FormDefDto formDefDto = new FormDefDto();
        formDefDto.setCode("existingCode");

        // Mockito.d(Option.some(formDefDto.toEntity())).when(formDefRepository).loadByCode(Mockito.anyString());
        when(formDefRepository.loadByCode(Mockito.anyString())).then(invocation -> {
            return Option.some(formDefDto.toEntity(null));
        });
        try {
            formDefService.addDef(formDefDto);
        }
        catch (Exception e) {
            assertEquals("form def code already exist",e.getMessage());

        }
    }

    // @Test
    // public void formDataApproved(){
    //     FormData.updateApproveStatusAndReturnTaskId("2147133609482240", "2147135206512640", FormDataStatus.APPROVED);
    // }

        @Test
    public void cd(){
            IConditionDefRepository repository = SpringUtil.getBean(IConditionDefRepository.class);
            repository.loadByCode("123");
        }
}

