package com.caidaocloud.hrpaas.service.test;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormFieldDto;
import com.caidaocloud.hrpaas.service.interfaces.vo.uiform.UiFormVo;
import com.caidaocloud.util.FastjsonUtil;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class TypeReferenceTest {
    public static void main(String[] args) {
        UiFormVo testVo = new UiFormVo();
        testVo.setBid("id");
        testVo.setConfig("config");

        String jsonStr = FastjsonUtil.toJson(testVo);
        System.out.println(jsonStr);

        UiFormVo newUiFormVo = FastjsonUtil.toObject(jsonStr, UiFormVo.class);

        System.out.println("newUiFormVo=" + FastjsonUtil.toJson(newUiFormVo));

        List<UiFormFieldDto> fieldConfig = new ArrayList<>();
        UiFormFieldDto uffd = new UiFormFieldDto();
        uffd.setDataSourceId("setDataSourceId");
        uffd.setDataSourceId("typeRef");
        fieldConfig.add(uffd);

        String fieldConfigStr = FastjsonUtil.toJson(fieldConfig);
        System.out.println("fieldConfig = " + fieldConfigStr);

        List<UiFormFieldDto> newUiFormVoObj = null;
        // List<UiFormFieldDto> newUiFormVoObj = FastjsonUtil.toObject(fieldConfigStr, createListTr(UiFormFieldDto.class));
        // System.out.println("fieldConfigStr newUiFormVoObj = " + FastjsonUtil.toJson(newUiFormVoObj));

        newUiFormVoObj = FastjsonUtil.toObject(fieldConfigStr, createListTr(UiFormFieldDto.class));
        System.out.println("createListTr newUiFormVoObj = " + FastjsonUtil.toJson(newUiFormVoObj));

        newUiFormVoObj = FastjsonUtil.toObject(fieldConfigStr, typeRef(UiFormFieldDto.class));
        System.out.println("typeRef newUiFormVoObj = " + FastjsonUtil.toJson(newUiFormVoObj));

        UiFormFieldDto jsonObj = FastjsonUtil.toObject(FastjsonUtil.toJson(uffd), new TypeReference<UiFormFieldDto>(){});
        System.out.println("TypeReference jsonObj = " + FastjsonUtil.toJson(jsonObj));

        jsonObj = FastjsonUtil.toObject(FastjsonUtil.toJson(uffd), objTypeRef(UiFormFieldDto.class));
        System.out.println("objTypeRef jsonObj = " + FastjsonUtil.toJson(jsonObj));
    }

    public static <T> TypeReference<T> objTypeRef(Class<T> t){
        return new TypeReference<T>(){};
    }

    public static <T> TypeReference<List<T>> typeRef(Type... actualTypeArguments){
        return new TypeReference<List<T>>(actualTypeArguments){};
    }

    public static <T> TypeReference<List<T>> createListTr(Class<T> t){
        return new TypeReference<List<T>>(){};
    }

    public static <T> TypeReference<T> createTypeReference(Class<T> t){
        return new TypeReference<T>(){};
    }

}
