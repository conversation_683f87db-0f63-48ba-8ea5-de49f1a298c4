package com.caidaocloud.hrpaas.service.application.uiform.event.publish;

import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class UiFormDataPushEvent {
    private final static String EXCHANGE = "hrpass.uiform.data.change.exchange";
    private final static String ROUTING_KEY = "routingKey.hrpass.uiform.data";
    private final static String QUEUE_NAME = "hrpass.uiform.data.change.queue";

    @Resource
    private MqMessageProducer<UiFormDataChangeMsg> producer;

    @Bean("hrpassUiformWorkflowQueue")
    private Queue queue() {
        // autoDelete 默认为 false
        return new Queue(QUEUE_NAME, true);
    }

    @Bean("hrpassUiformWorkflowExchange")
    private DirectExchange exchange() {
        return new DirectExchange(EXCHANGE);
    }

    @Bean
    private Binding binding(@Qualifier("hrpassUiformWorkflowQueue") Queue queue,
        @Qualifier("hrpassUiformWorkflowExchange") DirectExchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTING_KEY);
    }

    public void publish(String msg) {
        UiFormDataChangeMsg message = new UiFormDataChangeMsg();
        message.setBody(msg);
        message.setExchange(EXCHANGE);
        message.setRoutingKey(ROUTING_KEY);
        producer.publish(message);
    }
}
