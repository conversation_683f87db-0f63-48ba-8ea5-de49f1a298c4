package com.caidaocloud.hrpaas.service.interfaces.vo.form;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormData;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDataStatus;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Maps;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class FormDataMapVo {

    private String id;
    private String formId;
    //private String formName;
    private long createTime;
    private String createBy;
    private long updateTime;
    private String updateBy;
    private Map<String, Object> propertiesMap = Maps.map();

    private String targetId;
    private FormDataStatus status;
    private String sysProcessCode;

    public static FormDataMapVo fromFormData(FormData formData){
        val result = FastjsonUtil.convertObject(formData, FormDataMapVo.class);
        formData.getProperties().forEach(it->{
            String key = it.getProperty();
            Object value = it.getValue();
            if(StringUtils.isNotEmpty(it.getSuffix())){
                key = key + "&" + it.getSuffix();
            }
            if(it.getDataType().isComponent()){
                value = FastjsonUtil.toObject((String)value, Map.class);
                if(it.getDataType().equals(PropertyDataType.Enum)){
                    value = ((Map)value).get("value");
                }
            }
            if(it.getDataType().equals(PropertyDataType.Data_Table)){
                value = FastjsonUtil.toList((String)value, Map.class);
                ((List<Map>)value).forEach(data->data.remove("masterBid"));
            }
            if(it.getDataType().equals(PropertyDataType.Enum_Array)){
                value = FastjsonUtil.toList((String)value, String.class);
                if(((List<?>) value).isEmpty()){
                    value = null;
                }
            }
            if(it.getDataType().equals(PropertyDataType.Boolean)){
                if(String.valueOf(value).equals("true")){
                    value = true;
                }else if(String.valueOf(value).equals("false")){
                    value = false;
                }else{
                    value = null;
                }
            }
            result.getPropertiesMap().put(key, value);
        });
        return result;
    }
}
