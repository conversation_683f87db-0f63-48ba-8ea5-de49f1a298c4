package com.caidaocloud.hrpaas.service.test;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.hrpaas.service.application.uiform.service.convert.GeneralFieldValueAssembleStrategy;
import com.caidaocloud.hrpaas.service.application.uiform.service.hook.HookTemplate;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceRequestMethod;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiDataSourceDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiGeneralDataSourceDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-09-08
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class RestTemplateTest {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private GeneralFieldValueAssembleStrategy generalFieldValueAssembleStrategy;
    @Resource
    private HookTemplate hookTemplate;

    @Test
    public void restTemplateTest(){
        log.info(restTemplate.toString());

        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("test", "test");
        String strData = restTemplate.getForObject("http://115.29.176.239:3000/mock/55/api/getOperations", String.class, requestParams);

        log.info("strData={}", strData);
    }

    @Test
    public void generalFieldValueAssembleStrategyTest(){
        Map fieldMap = new HashMap(){
            {
                put("empid", 8888);
                put("empname", "涛哥");
                put("age", 88);
                put("sex", 1);
            }
        };

        log.info("fieldMap={}", JSON.toJSONString(fieldMap));

        String fieldKey = "sex", fieldVal = "1";
        UiDataSourceDto fieldDataSource = new UiDataSourceDto();

        UiGeneralDataSourceDto generalDataSource = new UiGeneralDataSourceDto();
        generalDataSource.setUrl("http://115.29.176.239:3000/mock/55/api/form/select");
        generalDataSource.setRequestMethod(DataSourceRequestMethod.GET);
        generalDataSource.setRequestScope(true);

        Map params = new HashMap();
        params.put("page", "test");
        params.put("name", "caidao");

        generalDataSource.setRequestParams(params);

        String strConfig = JSON.toJSONString(generalDataSource);
        log.info("strConfig={}", strConfig);
        fieldDataSource.setConfig(generalDataSource);

        generalFieldValueAssembleStrategy.packageFieldValue(fieldMap, fieldKey, fieldVal, fieldDataSource);

        log.info("fieldMap={}", JSON.toJSONString(fieldMap));

        fieldMap = new HashMap(){
            {
                put("empid", 6666);
                put("empname", "hello");
                put("age", 66);
                put("sex", 2);
            }
        };

        fieldVal = "2";
        generalFieldValueAssembleStrategy.packageFieldValue(fieldMap, fieldKey, fieldVal, fieldDataSource);

        log.info("fieldMap={}", JSON.toJSONString(fieldMap));
    }

    @Test
    public void hookTemplateTest(){

        String postHook = "defaultCallbackHookService.defaultSelectConvert(sourceMap, convertMap)";

        Map sourceMap = new HashMap();
        sourceMap.put("empid", 1008);
        sourceMap.put("name", "一号人员");
        sourceMap.put("workno", "A8888");
        sourceMap.put("sex", 30);
        sourceMap.put("dept", "技术部");

        Map convertMap = new HashMap();
        convertMap.put("a", "name$workno$dept$sex");
        convertMap.put("b", "empid");

        Map<String, Object> binding = new HashMap<>();
        binding.put("sourceMap", sourceMap);
        binding.put("convertMap", convertMap);

        Map targetMap = (Map) hookTemplate.executeObject(postHook, binding);
        log.info("targetMap={}", JSON.toJSONString(targetMap));
        log.info("--------------------------2");

        log.info("binding={}", JSON.toJSONString(binding));
        postHook = "defaultCallbackHookService.putTxtValSelectConvert(sourceMap, convertMap)";
        Map newMap = (Map) hookTemplate.executeObject(postHook, binding);
        log.info("newMap={}", JSON.toJSONString(newMap));
        log.info("--------------------------3");
    }

    @Test
    public void generalFieldValueAssembleStrategyHookTest(){
        Map fieldMap = new HashMap(){
            {
                put("workno", "A8888");
                put("empid", 8888);
                put("empname", "涛哥");
                put("age", 88);
                put("sex", 1);
            }
        };

        log.info("fieldMap={}", JSON.toJSONString(fieldMap));

        String fieldKey = "sex", fieldVal = "1";
        UiDataSourceDto fieldDataSource = new UiDataSourceDto();

        UiGeneralDataSourceDto generalDataSource = new UiGeneralDataSourceDto();
        generalDataSource.setUrl("http://115.29.176.239:3000/mock/55/api/form/sex_select");
        generalDataSource.setRequestMethod(DataSourceRequestMethod.GET);
        generalDataSource.setRequestScope(true);
        generalDataSource.setPostHook("defaultCallbackHookService.txtValSelectHook(sourceList, convertMap)");

        Map dataExchange = new HashMap();
        dataExchange.put("text", "name$aliasVal$noid");
        dataExchange.put("value", "noid");
        generalDataSource.setPostOption(dataExchange);

        Map params = new HashMap();
        params.put("pageNo", "1");
        params.put("pageSize", "10");

        generalDataSource.setRequestParams(params);

        String strConfig = JSON.toJSONString(generalDataSource);
        log.info("strConfig={}", strConfig);
        fieldDataSource.setConfig(generalDataSource);

        generalFieldValueAssembleStrategy.packageFieldValue(fieldMap, fieldKey, fieldVal, fieldDataSource);
        log.info("--------------------------------------");
        log.info("newFieldMap={}", JSON.toJSONString(fieldMap));
    }
}
