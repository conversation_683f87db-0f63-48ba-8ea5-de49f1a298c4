package com.caidaocloud.hrpaas.service.application.form.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.paas.common.event.FormPublishEvent;
import com.caidaocloud.hrpaas.service.application.form.dto.FormDefDto;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormFilter;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDefStatus;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormTarget;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormWidgetType;
import com.caidaocloud.hrpaas.service.domain.form.factory.FormFactory;
import com.caidaocloud.hrpaas.service.interfaces.dto.form.FormWfApproverDto;
import com.caidaocloud.hrpaas.service.interfaces.vo.form.FormPropDefVo;
import com.caidaocloud.metadata.application.service.MetadataPropertyService;
import com.caidaocloud.metadata.application.service.MetadataService;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.metadata.interfaces.facade.SqlRepositoryToolController;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.workflow.dto.*;
import com.caidaocloud.workflow.enums.*;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FormDefService {

    @Autowired
    private MetadataService metadataService;
    @Resource
    private IWfRegisterFeign iwfRegisterFeign;
    @Resource
    private MetadataPropertyService metadataPropertyService;
    @Resource
    private SqlRepositoryToolController sqlRepositoryToolController;

    public static final String WORKFLOW_CODE_PREFIX = "FORM-FUNC-";

    public PageResult<FormDef> defPage(int pageNo, int pageSize) {
        return FormDef.page(pageNo, pageSize);
    }

    public PageResult<FormDef> defPage(int pageNo, int pageSize, String keywords) {
        return FormDef.pageByKeyWorks(pageNo, pageSize, keywords);
    }

    public List<FormDef> defList() {
        return FormDef.list().stream().filter(it -> it.getStatus().equals(FormDefStatus.PUBLISHED)).collect(Collectors.toList());
    }

    public void saveMappingId(FormDefDto dto) {
        Option<FormDef> existedOp = FormDef.loadById(dto.getId());
        val existed = existedOp.getOrThrow(new ServerException("form def not exist"));
        existed.setMappingId(dto.getMappingId());
        existed.update();
    }

    @Transactional
    public String addDef(FormDefDto formDef) {
        if (StringUtils.isNotBlank(formDef.getCode()) && FormDef.loadByCode(formDef.getCode()).isDefined()) {
            throw new ServerException("form def code already exist");
        }

        val entity = FormFactory.createFormDef(formDef);
        entity.setStatus(FormDefStatus.DRAFT);
        return entity.insert();
    }

    @Transactional
    public void alterDef(FormDefDto formDef) {
        val user = SecurityUserUtil.getSecurityUserInfo();
        Option<FormDef> existedOp = FormDef.loadById(formDef.getId());
        val existed = existedOp.getOrThrow(new ServerException("form def not exist"));
        if(FormDefStatus.DISABLED.equals(existed.getStatus())){
            throw new ServerException("form def disabled");
        }
        if (!ObjectUtil.nullSafeEquals(formDef.getCode(), existed.getCode())) {
            throw new ServerException("form def code cannot be changed");
        }
        val entity = FormFactory.createFormDef(formDef, existed);
        if (formDef.isOnlyBasic()) {
            entity.setHeaders(existed.getHeaders());
            entity.setProperties(existed.getProperties());
        }
        if(FormDefStatus.DRAFT.equals(existed.getStatus())){
            entity.setStatus(FormDefStatus.DRAFT);
        }else if(FormDefStatus.PUBLISHED.equals(existed.getStatus())){
            entity.setStatus(FormDefStatus.PUBLISHED);
            if(!formDef.isOnlyBasic()){
                val changed = existed.getProperties().stream().anyMatch(existedProperty->
                        existedProperty.notCoveredBy(entity.getProperties())
                );
                if(changed){
                    throw new ServerException("已发布属性不允许变更");
                }
            }
        }
        entity.update();
        if(!formDef.isOnlyBasic() && FormDefStatus.PUBLISHED.equals(existed.getStatus())){
            List<FormPropDef> alterProps = Sequences.sequence(entity.getProperties())
                    .filter(property -> !existed.getProperties().stream().anyMatch(existProperty ->
                            existProperty.getProperty().equals(property.getProperty()))).toList();
            initEntityDef(entity,alterProps, true);

            val seniorEmpPickers = entity.getProperties().stream().filter(it->it.getWidgetType()
                    .equals(FormWidgetType.SeniorEmpPicker)).collect(Collectors.toList());
            val seniorEmpProperties = seniorEmpPickers.stream().map(it->it.toEntityProperties()).flatMap(it->it.stream()).collect(Collectors.toList());
            val existedEntityProperties = metadataService.one("entity.form."+formDef.getId()).fetchProperties();
            val newSeniorEmpProperties = Sequences.sequence(seniorEmpProperties)
                    .filter(property -> !existedEntityProperties.stream().anyMatch(existProperty ->
                            existProperty.getProperty().equals(property.getProperty())));
            metadataPropertyService.insertStandardProps("entity.form."+formDef.getId(),
                    newSeniorEmpProperties, DefChannel.API);
            new FormPublishEvent(existed.getId(), existed.getCode(), SecurityUserUtil.getSecurityUserInfo()
                    .getTenantId()).publish();
            if (existed.isWorkflowAvailable()) {
                // 如果开启了工作流，表单发布后注册表单到工作流
                //formRegWorkflow(existed, Lists.list(), SecurityUserUtil.getSecurityUserInfo().getTenantId());
                registerWorkflow(entity, SecurityUserUtil.getSecurityUserInfo().getTenantId());
            }
        }
    }

    @Transactional
    public void deleteDef(String id) {
        Option<FormDef> existedOp = FormDef.loadById(id);
        val existed = existedOp.getOrThrow(new ServerException("form def not exist"));
        if (!FormDefStatus.DRAFT.equals(existed.getStatus())) {
            throw new ServerException("form def published");
        }
        FormDef.deleteById(id);
    }

    @Transactional
    public void pubDef(String id, List<String> workflowSeqProperties) {
        Option<FormDef> existedOp = FormDef.loadById(id);
        val existed = existedOp.getOrThrow(new ServerException("form def not exist"));
        if (!FormDefStatus.DRAFT.equals(existed.getStatus())) {
            throw new ServerException("form def published");
        }
        existed.publish();
        initEntityDef(existed, false);
        new FormPublishEvent(existed.getId(), existed.getCode(), SecurityUserUtil.getSecurityUserInfo()
                .getTenantId()).publish();
        if (existed.isWorkflowAvailable()) {
            // 如果开启了工作流，表单发布后注册表单到工作流
            registerWorkflow(existed, SecurityUserUtil.getSecurityUserInfo().getTenantId());
            //formRegWorkflow(existed, workflowSeqProperties, SecurityUserUtil.getSecurityUserInfo().getTenantId());
        }
    }
    private void initEntityDef(FormDef formDef,List<FormPropDef> formProperties, boolean update) {
        EntityDef entityDef = new EntityDef();
        entityDef.setIdentifier("entity.form." + formDef.getId());
        entityDef.setName("表单" + formDef.getName() + "模型");
        entityDef.setTimelineEnabled(false);
        entityDef.setOwner("form");
        List<PropertyDef> properties = Lists.list();
        entityDef.setCustomProperties(properties);
        formProperties.stream().filter(it->
                !FormWidgetType.withNoPersist(it.getWidgetType()) && !FormWidgetType.Operation.equals(it.getWidgetType())).forEach(it -> {
            val entityProperties = it.toEntityProperties();
            properties.addAll(entityProperties);
        });
        if(update){
            metadataPropertyService.insertStandardProps(entityDef.getIdentifier(),
                    Sequences.sequence(entityDef.getCustomProperties()), DefChannel.API);
        }else{

            if (!FormTarget.NONE.equals(formDef.getTarget())) {
                PropertyDef targetId = new PropertyDef();
                targetId.setProperty("formTargetId");
                targetId.setDataType(PropertyDataType.String);
                targetId.setName("表单对象");
                properties.add(targetId);
            }
            if (formDef.isWorkflowAvailable()) {
                PropertyDef sysProcessCode = new PropertyDef();
                sysProcessCode.setProperty("sysProcessCode");
                sysProcessCode.setDataType(PropertyDataType.String);
                sysProcessCode.setName("审批编码");
                properties.add(sysProcessCode);
            }
            PropertyDef workflowTaskId = new PropertyDef();
            workflowTaskId.setProperty("workflowTaskId");
            workflowTaskId.setDataType(PropertyDataType.String);
            workflowTaskId.setName("流程节点任务ID");
            properties.add(workflowTaskId);
            PropertyDef status = new PropertyDef();
            status.setProperty("status");
            status.setDataType(PropertyDataType.String);
            status.setName("表单状态");
            properties.add(status);
            metadataService.create(entityDef, DefChannel.API);
        }
    }

    private void initEntityDef(FormDef formDef, boolean update) {
        initEntityDef(formDef, formDef.getProperties(), update);
    }

    @Transactional
    public void disableDef(String id) {
        Option<FormDef> existedOp = FormDef.loadById(id);
        val existed = existedOp.getOrThrow(new ServerException("form def not exist"));
        if (!FormDefStatus.PUBLISHED.equals(existed.getStatus())) {
            throw new ServerException("form def not published");
        }
        existed.disable();
    }

    public FormDef one(String id) {
        Option<FormDef> existedOp = FormDef.loadById(id);
        return existedOp.getOrThrow(new ServerException("form def not exist"));
    }


    public FormDef oneByName(String name) {
        Option<FormDef> existedOp = FormDef.loadByName(name);
        return existedOp.getOrThrow(new ServerException("form def not exist"));
    }

    public void initStandardForm(String tenantId) {
        try {
            HolderUtil.setTenantId(tenantId);
            List<FormDefDto> standardForms = loadStandardForm();
            // TODO: 2022/8/3 标准表单重复创建
            standardForms.forEach(this::addDef);
        } finally {
            HolderUtil.clear();
        }
    }

    @SneakyThrows
    private List<FormDefDto> loadStandardForm() {
        String formJson = IOUtils.toString(new ClassPathResource("form/standard_form.json").getInputStream(), "UTF-8");
        return FastjsonUtil.toArrayList(formJson, FormDefDto.class);
    }

    /**
     * 查看属性
     *
     * @param id
     * @param
     */
    public List<FormPropDefVo> loadApproverPropDef(String id) {
        List<FormPropDefVo> empProp = getPropDefpByDataType(id, PropertyDataType.Emp);
        List<FormPropDefVo> orgProp = getPropDefpByWidgetType(id, FormWidgetType.OrgSelect);
        empProp.addAll(orgProp);
        return empProp;
    }

    private List<FormPropDefVo> getPropDefpByWidgetType(String id, FormWidgetType dataType) {
        if (StringUtils.isBlank(id) || dataType == null) {
            return Lists.list();
        }
        var formDefOption = FormDef.loadById(id);
        if (formDefOption.isEmpty()) {
            return Lists.list();
        }
        var formDef = formDefOption.get();
        if (CollectionUtils.isEmpty(formDef.getProperties())) {
            return Lists.list();
        }
        var resultList = formDef.getProperties()
                .stream()
                .filter(e -> dataType.equals(e.getWidgetType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultList)) {
            return Lists.list();
        }
        return FastjsonUtil.convertList(resultList, FormPropDefVo.class);

    }

    /**
     * 查看属性
     *
     * @param id
     * @param dataType
     */
    public List<FormPropDefVo> getPropDefpByDataType(String id, PropertyDataType dataType) {
        if (StringUtils.isBlank(id) || dataType == null) {
            return Lists.list();
        }
        var formDefOption = FormDef.loadById(id);
        if (formDefOption.isEmpty()) {
            return Lists.list();
        }
        var formDef = formDefOption.get();
        if (CollectionUtils.isEmpty(formDef.getProperties())) {
            return Lists.list();
        }
        var resultList = formDef.getProperties()
                .stream()
                .filter(e -> dataType.equals(e.getDataType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultList)) {
            return Lists.list();
        }
        return FastjsonUtil.convertList(resultList, FormPropDefVo.class);
    }

    @Transactional
    public void updateEmpPropAsApprover(FormWfApproverDto formWfApproverDto) {
        if (formWfApproverDto == null || StringUtils.isBlank(formWfApproverDto.getId())) {
            log.warn("parameters is illegal, parameters={}", FastjsonUtil.toJson(formWfApproverDto));
            return;
        }
        if(formWfApproverDto.getProperties() == null) {
            formWfApproverDto.setProperties(Lists.list());
        }
        var formDefOption = FormDef.loadById(formWfApproverDto.getId());
        if (formDefOption.isEmpty()) {
            log.info("not found form definition, parameters={}", FastjsonUtil.toJson(formWfApproverDto));
            return;
        }
        if (CollectionUtils.isEmpty(formDefOption.get().getProperties())) {
            return;
        }
        formDefOption.get().updateApproverProp(formWfApproverDto.getProperties().stream().collect(Collectors.toSet()));
        if (formDefOption.get().getStatus()==FormDefStatus.PUBLISHED)
            registerApprover(formDefOption.get(), SecurityUserUtil.getSecurityUserInfo()
                    .getTenantId(), formDefOption.get().getWfCode());
    }

    /**
     * 表单注册工作流
     */
//    @Deprecated
//    private void formRegWorkflow(FormDef formDef, List<String> workflowSeqProperties, String tenantId) {
//        if (formDef == null) {
//            log.warn("formDef is null, tenantId={}", tenantId);
//            return;
//        }
//
//        Set<String> propertySet = new HashSet<String>();
//        if (!CollectionUtils.isEmpty(workflowSeqProperties)) {
//            propertySet = workflowSeqProperties.stream()
//                    .filter(e -> StringUtils.isNotBlank(e)).collect(Collectors.toSet());
//        }
//        List<WfMetaFunFormFieldDto> fields = Lists.list();
//        List<FormPropDef> seqPropertyList = Lists.list();
//        List<FormPropDef> approverPropertyList = Lists.list();
//
//        if (!CollectionUtils.isEmpty(formDef.getProperties())) {
//            Set<String> finalPropertySet = propertySet;
//            formDef.getProperties().forEach(e -> {
//                if (finalPropertySet.contains(e.getProperty())) {
//                    seqPropertyList.add(e);
//                }
//                if (BooleanUtils.toBooleanDefaultIfNull(e.getApprover(), false)) {
//                    approverPropertyList.add(e);
//                }
//                fields.add(new WfMetaFunFormFieldDto(e.getProperty(), e.getName()));
//            });
//        }
//        var funCode = String.format("form_func_%s", formDef.getId());
//        var dto = new WfMetaFunDto(formDef.getName(), funCode,
//                WfFunctionPageJumpType.RELATIVE_PATH, tenantId, "caidaocloud-hr-paas-service",
//                "", "", "", fields);
//        iwfRegisterFeign.registerFunction(dto);
//        var callback = new WfMetaCallbackDto(
//                String.format("%s回调", formDef.getName()), String.format("form_callback_%s", formDef.getId()),
//                Lists.list(funCode), tenantId,
//                "/api/hrpaas/v1/form/data/workflow/callback", "caidaocloud-hr-paas-service", "", WfCallbackTypeEnum.RELATIVE_PATH,
//                WfCallbackTimeTypeEnum.NOW
//        );
//        iwfRegisterFeign.registerCallback(callback);
//        callback = new WfMetaCallbackDto(
//                String.format("%s驳回回调", formDef.getName()), String.format("form_reject_callback_%s", formDef.getId()),
//                Lists.list(funCode), tenantId,
//                "/api/hrpaas/v1/form/data/workflow/callback/reject", "caidaocloud-hr-paas-service", "", WfCallbackTypeEnum.RELATIVE_PATH,
//                WfCallbackTimeTypeEnum.NOW
//        );
//        iwfRegisterFeign.registerCallback(callback);
//        registrySeq(seqPropertyList, formDef.getId(), funCode, tenantId);
//        registryApprover(approverPropertyList, formDef.getId(), funCode, tenantId);
//    }

    public void registerWorkflow(FormDef formDef, String tenantId) {
        val funCode = formDef.getWfCode();
        List<WfMetaFunFormFieldDto> formFields = Lists.list();
        List<WfMetaSeqConditionDto> seqList = Lists.list();
        List<WfMetaNoticeVarDto> noticeVarList = Lists.list();
        val seqCallAddress = "/api/hrpaas/v1/form/workflow/seq/"+formDef.getId();
        if (null != formDef && !CollectionUtils.isEmpty(formDef.getProperties())) {
            formDef.getProperties().forEach(propDef -> {
                boolean isSeq = false;
                List<WfComponentValueDto> componentValueList = Lists.list();
                var seqCode = String.format(funCode.replace("-", "_") + "_" + propDef.getProperty());
                boolean isNoticeVar = true;
                val noticeVarBuilder =
                        WfMetaNoticeVarDto.builder().code(propDef.getProperty())
                                .name(propDef.fetchName()).funCode(funCode)
                                .url("/api/hrpaas/v1/form/workflow/notice/var")
                                .serviceName("caidaocloud-hr-paas-service");
                try {
                    noticeVarBuilder.type(propDef.getDataType().toString());
                } catch (Exception e) {
                    isNoticeVar = false;
                }
                if (PropertyDataType.Enum.equals(propDef.getDataType())) {
                    if (!CollectionUtils.isEmpty(propDef.getEnumDef())) {
                        isSeq = true;
                        propDef.getEnumDef().forEach(it -> {
                            componentValueList.add(new WfComponentValueDto(it.getDisplay(), it.getValue()));
                            noticeVarBuilder.enums(it.getDisplay(), it.getValue());
                        });
                    }

                } else if (PropertyDataType.Boolean.equals(propDef.getDataType())) {
                    isSeq = true;
                    componentValueList.add(new WfComponentValueDto("开", "true"));
                    componentValueList.add(new WfComponentValueDto("关", "false"));
                    noticeVarBuilder.enums("开", "true");
                    noticeVarBuilder.enums("关", "false");
                } else if (PropertyDataType.Timestamp.equals(propDef.getDataType()) && FormWidgetType.DatePicker.equals(propDef.getWidgetType())) {
                    if (StringUtils.isNotEmpty(propDef.getStyleExtras())) {
                        val style = FastjsonUtil.toObject(propDef.getStyleExtras(), Map.class);
                        if (null != style.get("format") && !style.get("format").toString().isEmpty()) {
                            noticeVarBuilder.dateFormat(style.get("format").toString());
                        }
                    }
                }
                if (isSeq) {
                    val seq = new WfMetaSeqConditionDto(propDef.fetchName(),
                            seqCode,
                            Lists.list(funCode),
                            "caidaocloud-hr-paas-service",
                            seqCallAddress,
                            WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
                            SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                            Lists.list(WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN),
                            WfValueComponentEnum.ENUM,
                            componentValueList,
                            "", false);
                    seqList.add(seq);
                }
                formFields.add(new WfMetaFunFormFieldDto(propDef.getProperty(), propDef.fetchName()));
                if (isNoticeVar) {
                    noticeVarList.add(noticeVarBuilder.build());
                }
            });
        }
        var dto = new WfMetaFunDto(formDef.getName(), funCode,
                WfFunctionPageJumpType.RELATIVE_PATH, SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                "caidaocloud-hr-paas-service",
                "", "", "", formFields);
        iwfRegisterFeign.registerFunction(dto);
        iwfRegisterFeign.registerSeqCondition(seqList);
        try {
            iwfRegisterFeign.registerNoticeVar(noticeVarList);
        } catch (Exception e) {
            log.error("流程消息变量注册异常", e);
        }
        registerCallback(formDef.getId(), formDef.getName(), funCode, tenantId);
        registerApprover(formDef, tenantId, funCode);
    }

    private void registerApprover(FormDef formDef, String tenantId, String funCode) {
        List<FormPropDef> approverPropertyList = Lists.list();
        if (!CollectionUtils.isEmpty(formDef.getProperties())) {
            formDef.getProperties().forEach(e -> {
                if (BooleanUtils.toBooleanDefaultIfNull(e.getApprover(), false)) {
                    approverPropertyList.add(e);
                }
            });
        }
        if (CollectionUtils.isEmpty(approverPropertyList)) {
            return;
        }
        for (FormPropDef formPropDef : approverPropertyList) {
            // String approverCode = String.format("approver_%s_%s", formPropDef.getProperty(), formDef.getId());
            // var wfMetaApproverDto = new WfMetaApproverDto(formPropDef.getName(), approverCode,
            //         tenantId, "caidaocloud-hr-paas-service",
            //         String.format("/api/hrpaas/v1/form/workflow/approver/%s", formDef.getId()),
            //         WfApproverFetchType.RELATIVE_PATH,
            //         WfValueComponentEnum.NONE,
            //         Lists.list(),
            //         "");
            // wfMetaApproverDto.setFunCode(funCode);
           List< WfMetaApproverDto> wfMetaApproverDto = formPropDef.buildApproverDto(formDef.getId(), funCode, tenantId);
            try {
                for (WfMetaApproverDto dto : wfMetaApproverDto) {
                    iwfRegisterFeign.registerApprover(dto);
                }
            } catch (Exception e) {
                log.error(String.format("registryApprover occur error, approverPropertyList=%s formDefId=%s funCode=%s tenantId=%s",
                        FastjsonUtil.toJson(approverPropertyList), formDef.getId(), funCode, tenantId), e);
            }
        }
    }

    /**
     * 离职回调注册
     *
     * @param funCode 工作流fun code
     */
    private void registerCallback(String formId, String funName, String funCode, String tenantId) {
        var callback = new WfMetaCallbackDto(
                String.format("%s回调", funName), String.format("form_callback_%s", formId),
                Lists.list(funCode), tenantId,
                "/api/hrpaas/v1/form/data/workflow/callback", "caidaocloud-hr-paas-service", "", WfCallbackTypeEnum.RELATIVE_PATH,
                WfCallbackTimeTypeEnum.NOW
        );
        iwfRegisterFeign.registerCallback(callback);
        callback = new WfMetaCallbackDto(
                String.format("%s驳回回调", funName), String.format("form_reject_callback_%s", formId),
                Lists.list(funCode), tenantId,
                "/api/hrpaas/v1/form/data/workflow/callback/reject", "caidaocloud-hr-paas-service", "", WfCallbackTypeEnum.RELATIVE_PATH,
                WfCallbackTimeTypeEnum.NOW
        );
        iwfRegisterFeign.registerCallback(callback);
    }

    public FormDef oneByCode(String code, FormDefStatus status) {
        Option<FormDef> existedOp = FormDef.loadByCode(code);
        if (existedOp.isEmpty()) {
            return null;
        }
        if (status != null && !existedOp.get().getStatus().equals(status)) {
            return null;
        }
        return existedOp.get();
    }

    public void addUserFilter(Map config) {
        String key = (String)config.get("key");
        val userId = SecurityUserUtil.getSecurityUserInfo().getUserId().toString();
        val filter = new FormFilter();
        filter.setKey(key);
        filter.setUserId(userId);
        filter.setConfig(FastjsonUtil.toJson(config));
        filter.addOrUpdate();
    }

    public Map fetchUserFilter(String key) {
        val userId = SecurityUserUtil.getSecurityUserInfo().getUserId().toString();
        val filter = FormFilter.load(key, userId);
        if(filter == null){
            return Maps.map("key",key, "data", Lists.list());
        }else{
            return FastjsonUtil.convertObject(filter.getConfig(), Map.class);
        }
    }

    public void addSysProcessCodeColumn(){
        FormDef.list().stream().filter(it->FormDefStatus.PUBLISHED.equals(it.getStatus())).forEach(formDef -> {
            val formDefId = formDef.getId();
            val identifier = "entity.form."+formDefId;
            val metadata = metadataService.one(identifier);
            val processCodeExist = metadata.fetchProperties().stream().filter(it->"sysProcessCode".equals(it.getProperty()))
                    .findFirst().isPresent();
            if(!processCodeExist && formDef.isWorkflowAvailable()){
                PropertyDef sysProcessCode = new PropertyDef();
                sysProcessCode.setProperty("sysProcessCode");
                sysProcessCode.setDataType(PropertyDataType.String);
                sysProcessCode.setName("审批编码");
                sqlRepositoryToolController.addProperty(identifier, Lists.list(sysProcessCode));
            }
        });
    }

}