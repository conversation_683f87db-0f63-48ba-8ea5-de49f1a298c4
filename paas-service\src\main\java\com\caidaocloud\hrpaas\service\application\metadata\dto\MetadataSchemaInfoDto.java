package com.caidaocloud.hrpaas.service.application.metadata.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataRelationDto;
import com.caidaocloud.metadata.application.dto.PropertyDefDto;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import lombok.Data;

import java.util.List;

@Data
public class MetadataSchemaInfoDto {
    private Integer folder;

    private String scriptName;

    private String model;

    private SchemaAction action;

    private EntityDef entityDef;

    private List<PropertyDefDto> propertyDef;

    private String identifier;

    /**
     * 关联模型
     */
    private List<MetadataRelationDto> relation;

    private String relationProp;

    public enum SchemaAction {
        create, update, add_prop, change_prop, add_relation, delete_relation
    }
}
