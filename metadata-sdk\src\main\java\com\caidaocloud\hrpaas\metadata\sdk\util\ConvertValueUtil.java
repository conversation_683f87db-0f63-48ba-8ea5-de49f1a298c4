package com.caidaocloud.hrpaas.metadata.sdk.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SysParamDictDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeParent;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMetadataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import static com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType.Phone;

@Slf4j
@Component
public class ConvertValueUtil {
    @Autowired
    private CacheService cacheService;
    @Autowired
    private IMetadataFeign metadataFeign;
    private final String PAAS_PROVINCE_IDETIFIER = "entity.hrpaas.Province";
    private final String PAAS_CITY_IDETIFIER = "entity.hrpaas.City";

    /**
     * 将value转换成文本
     *
     * @param value
     * @param formDefMetadata
     * @return
     */
    public Object convertValueToText(Object value, MetadataPropertyVo formDefMetadata) {
        switch (formDefMetadata.getDataType()) {
            case Dict:
                return handleDict(formDefMetadata.getDatasource(), value);
            case Enum:
                return handleEnum(formDefMetadata, value);
            case PROVINCE_CITY:
                return handleProvinceCity(value);
            case Timestamp:
                return handleTimestamp(value);
            case Boolean:
                return handleBoolean(value);
            case Address:
                return handleAddress(value);
        case Attachment:
            return handleAttachment(value);
        default:
                return value;
        }
    }


    private Object handleAttachment(Object value) {
        if (value == null) {
            return "";
        }
        if (value instanceof Map) {
            Map map = (Map) value;
            return map.get("names");
        }
        return value;
    }

    private Object handleDict(String dataSource, Object value) {
        if (log.isDebugEnabled()) {
            log.debug("prepare to run handleDict method, dataSource={} value={}", dataSource, value);
        }
        dataSource = StringUtils.defaultString(dataSource, "");
        if (dataSource.contains("/")) {
            dataSource = dataSource.substring(dataSource.indexOf("typeCode=") + "typeCode=".length());
        }
        var result = FastjsonUtil.toObject(cacheService.getValue(String.format("DICT_%s", value)), SysParamDictDto.class);
        if (result==null) {
            return result.getDictChnName();
        } else {
            log.info("not found text of value, value={}", value);
        }
        return null;
    }

    private Object handleEnum(MetadataPropertyVo formDefMetadata, Object value) {
        List<PropertyEnumDefDto> enumDef = formDefMetadata.getEnumDef();
        for (PropertyEnumDefDto propertyEnumDefDto : enumDef) {
            if (propertyEnumDefDto.getValue().equals(value)) {
                return propertyEnumDefDto.getDisplay();
            }
        }
        return "";
    }

    private Object forEarchMetaPropertyList(List<MetadataPropertyVo> list, String property, Object value) {
        if (!CollectionUtils.isEmpty(list)) {
            var optional = list.stream()
                    .filter(e -> e.getDataType() == PropertyDataType.Enum && e.getProperty().equals(property))
                    .map(e -> e.getEnumDef()).findFirst();
            List<PropertyEnumDefDto> propertyEnumDefList = null;
            if (optional.isPresent() && !CollectionUtils.isEmpty(propertyEnumDefList = optional.get())) {
                for (PropertyEnumDefDto propertyEnumDefDto : propertyEnumDefList) {
                    if (value != null && propertyEnumDefDto.getValue().equalsIgnoreCase(value.toString())) {
                        return propertyEnumDefDto.getDisplay();
                    }
                }
            }
        }
        return null;
    }

    private Object handleProvinceCity(Object value) {
        if (value == null) {
            log.info("value is empty");
            return value;
        }
        if (value instanceof Map) {
            Map map = (Map) value;
            var provinceName = map.computeIfPresent("provinceId", (k, v) -> {
                return queryProvinceAndCity(PAAS_PROVINCE_IDETIFIER, v.toString());
            });
            var cityName = map.computeIfPresent("cityId", (k, v) -> {
                return queryProvinceAndCity(PAAS_CITY_IDETIFIER, v.toString());
            });
            if (!Objects.isNull(provinceName) && !Objects.isNull(cityName)) {
                return String.format("%s/%s", provinceName, cityName);
            } else if (!Objects.isNull(provinceName)) {
                return provinceName;
            } else if (!Objects.isNull(cityName)) {
                return cityName;
            }
            log.info("not found name of province and city, value={}", FastjsonUtil.toJson(value));
        }
        return value;
    }

    private String queryProvinceAndCity(String identifer, String bid) {
        var provinceAndCityDto = DataQuery.identifier(identifer)
                .decrypt()
                .specifyLanguage()
                .queryInvisible()
                .oneOrNull(bid, DataSimple.class);
        if (provinceAndCityDto == null) {
            return null;
        }
        return ((SimplePropertyValue) provinceAndCityDto.getProperties().get("name")).getValue();
    }

    private String handleTimestamp(Object value) {
        if (value == null || StringUtils.isBlank(value.toString()) || "null".equals(value.toString())) {
            return null;
        }
        return DateUtil.formatDate(Long.parseLong(value.toString()));
    }

    private String handleAddress(Object value) {
        if (value == null) {
            return "";
        }
        Address address = null;
        if (value instanceof String) {
            address = new Address();
            var split = String.valueOf(value).split("/");
            if (split.length == 4) {
                address.setProvince(StringUtils.isBlank(split[0]) ? null : Long.valueOf(split[0]));
                address.setCity(StringUtils.isBlank(split[1]) || "null".equals(split[1]) ? null : Long.valueOf(split[1]));
                address.setArea(StringUtils.isBlank(split[2]) || "null".equals(split[2]) ? null : Long.valueOf(split[2]));
                address.setAddress(split[3]);
            }
        } else if (value instanceof Map) {
            Map map = (Map) value;
            if (map.containsKey("addressTxt")) {
                return String.valueOf(map.get("addressTxt"));
            }
            address = FastjsonUtil.convertObject(value, Address.class);
        } else {
            address = FastjsonUtil.convertObject(value, Address.class);
        }
        address.doValue();
        try {
            QueryInfoCache.init();
            Address.doAddress(address);
        } finally {
            QueryInfoCache.clear();
        }

        ArrayList<String> textList = Lists.newArrayList(address.getProvinceTxt(), address.getCityTxt(), address.getAreaTxt());
        String text = textList.stream().filter(txt -> StringUtils.isNotEmpty(txt)).collect(Collectors.joining("/"));
        return text;
    }

    private String handleBoolean(Object value) {
        if (value == null) {
            return "否";
        }
        Boolean b = Boolean.valueOf(value.toString());
        if (b) {
            return "是";
        } else {
            return "否";
        }
    }

    public  PropertyValue convertData2PropertyValue(Object value, MetadataPropertyVo metadataProperty){
        final PropertyDataType dataType = metadataProperty.getDataType();
        switch (dataType) {
        case Enum:
            if (value instanceof Map){
                return FastjsonUtil.toObject(FastjsonUtil.toJson(value), EnumSimple.class);
            }
            EnumSimple enumSimple = new EnumSimple();
            if (null == value) {
                return enumSimple;
            }
            enumSimple.setValue(Objects.toString(value));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(metadataProperty.getEnumDef())) {
                PropertyEnumDefDto defDto = metadataProperty.getEnumDef().stream().filter(it -> it.getValue().equals(enumSimple.getValue())).findFirst().orElse(null);
                enumSimple.setText(defDto != null ? defDto.getDisplay() : enumSimple.getValue());
            }
            return enumSimple;
        case Attachment:
            return null == value ? new Attachment() : FastjsonUtil.toObject(FastjsonUtil.toJson(value), Attachment.class);
        case Emp:
            return null == value ? new EmpSimple() : FastjsonUtil.toObject(FastjsonUtil.toJson(value), EmpSimple.class);
        case Dict:
            if (value instanceof Map) {
                return FastjsonUtil.toObject(FastjsonUtil.toJson(value), DictSimple.class);
            }
            return null == value ? new DictSimple() : DictSimple.doDictSimple(Objects.toString(value, null));
        case Job_Grade_Range:
            return null == value ? new JobGradeRange() : FastjsonUtil.toObject(FastjsonUtil.toJson(value), JobGradeRange.class);
        case Address:
            return null == value ? new Address() : FastjsonUtil.toObject(FastjsonUtil.toJson(value), Address.class);
		case Phone:
			return null == value ? new PhoneSimple() : FastjsonUtil.toObject(FastjsonUtil.toJson(value), PhoneSimple.class);
		case PID:
			return null == value ? new TreeParent() : FastjsonUtil.toObject(FastjsonUtil.toJson(value), TreeParent.class);
        default:
			if (dataType.isComponent()){
				throw new ServerException("Data type not support");
			}

            if (value instanceof Map) {
                return FastjsonUtil.toObject(FastjsonUtil.toJson(value), SimplePropertyValue.class);
            }
            if (dataType.isArray()) {
                SimplePropertyValue propertyValue = new SimplePropertyValue();
                propertyValue.setArrayValues(FastjsonUtil.convertObject(value, List.class));
                return propertyValue;
            }
            return new SimplePropertyValue(Objects.toString(null == value ? "" : value));
        }

    }
}