package com.caidaocloud.hrpaas.service.interfaces.facade.file;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.service.application.metadata.enums.OcrTypes;
import com.caidaocloud.hrpaas.service.application.metadata.service.FileService;
import com.caidaocloud.hrpaas.service.application.metadata.service.OcrService;
import com.caidaocloud.hrpaas.service.interfaces.dto.file.AttachmentInfoDto;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date  2021/12/2
 **/
@RestController
@RequestMapping("/api/hrpaas/v1/file")
public class FileController {

    @Autowired
    private FileService fileService;
    
    @Autowired
    private OcrService ocrService;

    /**
     * 上传文件
     * <AUTHOR>
     * @date  2021/12/2
     * @param multipartFile
     * @return
     */
    @PostMapping("/upload")
    public AttachmentInfoDto uploadFile(@RequestParam("file") MultipartFile multipartFile){
        return fileService.upload(multipartFile);
    }

    /**
     * 上传文件（指定桶名）
     *
     * @param bucketName    桶名
     * @param multipartFile 文件
     * @return
     */
    @PostMapping("/uploadWithBucketName")
    public AttachmentInfoDto uploadFileWithBucketName(@RequestParam(value = "bucketName", required = false) String bucketName,
                                                      @RequestParam("file") MultipartFile multipartFile) {
        return fileService.upload(multipartFile, bucketName);
    }

    @PostMapping("batchUpload")
    public List<AttachmentInfoDto> batchUploadFile(@RequestParam("file") List<MultipartFile> fileList) {
        if (fileList.size() > 100) {
            throw new ServerException("Batch upload cannot exceed 100 files");
        }
        return fileService.batchUpload(fileList);
    }

    @PostMapping("/download")
    public void downloadFile(@RequestParam("path") String path,
                             @RequestParam("fileName") String fileName,
                             HttpServletResponse response) throws Exception {
        if(StringUtils.equals(path, fileName)){
            Workbook workbook = ExcelExportUtil.exportExcel(Lists.newArrayList(), ExcelType.XSSF);
            val os = response.getOutputStream();
            try{
                workbook.write(os);
            }finally {
                os.close();
                workbook.close();
            }
            return;
        }
        fileService.download(path, fileName, response);
    }

    @GetMapping("/preview")
    public void previewFile(@RequestParam("path") String path,
                             @RequestParam("fileName") String fileName,
                             HttpServletResponse response) throws UnsupportedEncodingException {
        fileService.download(path, fileName, response);
    }

    @PostMapping("/ocr")
    public Result uploadOcr(@RequestParam(value = "ocrType", required = true) OcrTypes ocrType,
                            @RequestParam("file") MultipartFile multipartFile) {
        return ocrService.uploadOcr(multipartFile, ocrType);
    }
    @PostMapping("/ocrByUrl")
    public Result readOcrByUrl(@RequestParam(value = "ocrType", required = true) OcrTypes ocrType,
                            @RequestParam("fileUrl") String fileUrl)throws Exception {
        return ocrService.readOcrByFileUrl(fileUrl, ocrType);
    }
}
