package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropStyle;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDefStatus;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormTarget;
import com.caidaocloud.hrpaas.service.domain.form.factory.FormFactory;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.metadata.infrastructure.configuration.handler.BooleanTypeHandler;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.WebUtil;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;
import java.util.Map;
import java.util.function.BiFunction;


@Data
@Accessors(chain = true)
@TableName("paas_form_def")
public class FormDefPo extends BasePo<FormDefPo, FormDef> {

    private String name;

    private String code;

    private String codeAsId;

    private FormTarget target;

    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean workflowNodeAvailable;
    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean workflowAvailable;

    private String description;

    private FormDefStatus status;

    @DisplayAsArray
    private String headers;

    @DisplayAsArray
    private String properties;
    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean standard = false;
    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean implantable = false;

    private long createTime;

    private String createBy;

    private long updateTime;

    private String updateBy;

    //字段映射对应id
    private String mappingId;

//    public FormDef toEntity(Map<String, String> eventOn) {
//        val result = super.toEntity(FormDef.class);
//        result.appendEventOn(eventOn);
//        return result;
//    }

    public void appendPropertyDefInfo(FormPropDef propertyDef){
        val styleExtras = propertyDef.getStyleExtras();
        if(StringUtils.isNotEmpty(styleExtras)){
            val style = FastjsonUtil.toObject(styleExtras, FormPropStyle.class);
            if(StringUtils.isNotEmpty(style.getPlaceholder()) || (null != style.getI18nPlaceholder() && !style.getI18nPlaceholder().isEmpty())){
                fillLang(style.getPlaceholder(), style.getI18nPlaceholder(), (str, i18n)->{
                    style.setPlaceholder(str);
                    style.setI18nPlaceholder(i18n);
                    return style;
                });
            }
            propertyDef.setStyleExtras(FastjsonUtil.toJson(style));
        }
        fillLang(propertyDef.getName(), propertyDef.getI18nName(), (str, i18n)->{
            propertyDef.setName(str);
            propertyDef.setI18nName(i18n);
            return propertyDef;
        });
        fillLang(propertyDef.getDesc(), propertyDef.getI18nDesc(), (str, i18n)->{
            propertyDef.setDesc(str);
            propertyDef.setI18nDesc(i18n);
            return propertyDef;
        });
        if(null != propertyDef.getEnumDef()){
            propertyDef.getEnumDef().forEach(enumDef->{
                fillLang(enumDef.getDisplay(),enumDef.getI18nDisplay(), (str, i18n)->{
                    enumDef.setDisplay(str);
                    enumDef.setI18nDisplay(i18n);
                    return enumDef;
                });
            });
        }
        if(null != propertyDef.getSlaveProperties()){
            propertyDef.getSlaveProperties().forEach(it->
                appendPropertyDefInfo(it)
            );
        }
    }


    public FormDef toEntity(Map<String, String> eventOn) {
        val result = super.toEntity(FormDef.class);
        result.setProperties(Sequences.sequence(result.getProperties()).map(FormFactory::convertPropertyDef)
                .toList());
        result.appendEventOn(eventOn);
        fillLang(result.getName(), Maps.map(), (str, i18n)->{
            result.setName(str);
            result.setI18nName(i18n);
            return result;
        });
        result.getProperties().forEach(propertyDef ->
            appendPropertyDefInfo(propertyDef)
        );
        return result;
    }

    private void fillLang(String str, Map<String, String> i18nStr, BiFunction<String, Map<String,String>, Object> func){
        if(null == i18nStr || i18nStr.isEmpty()){
            i18nStr = Maps.map("default", str);
            try{
                val json = FastjsonUtil.toObject(str, Map.class);
                if(null != json && !json.isEmpty()){
                    i18nStr = json;
                }
            }catch(Exception e){
            }
        }
        func.apply(lang(i18nStr), i18nStr);
    }

    public String lang(Map<String, String> i18n){
        Locale locale = MessageHandler.getLocaleByRequest(WebUtil.getRequest());
        String currentLang = locale.getLanguage();
        String langDetail = currentLang;
        val country = locale.getCountry();
        if(StringUtils.isNotEmpty(country)){
            langDetail = currentLang + "-" + country;
        }
        if(i18n.containsKey(currentLang)){
            return i18n.get(currentLang);
        }else{
            if(i18n.containsKey(langDetail)){
                return i18n.get(langDetail);
            }else{
                return i18n.get("default");
            }
        }
    }

    public static FormDefPo fromEntity(Class<FormDefPo> clazz, FormDef entity){
        val result = JsonEnhanceUtil.toObject(entity, clazz);
        if(null != entity.getI18nName() && !entity.getI18nName().isEmpty()){
            result.setName(FastjsonUtil.toJson(entity.getI18nName()));
        }
        return result;
    }

}
