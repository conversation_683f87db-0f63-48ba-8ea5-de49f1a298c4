package com.caidaocloud.hrpaas.service.interfaces.facade.uiform;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil;
import com.caidaocloud.hrpaas.service.application.uiform.dto.UiFormBasicDataDto;
import com.caidaocloud.hrpaas.service.application.uiform.dto.UiFormDto;
import com.caidaocloud.hrpaas.service.application.uiform.service.UiFormService;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageTemplate;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormBasicsDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormBodyDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormFieldDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormSearchDto;
import com.caidaocloud.hrpaas.service.interfaces.vo.uiform.UiFormVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-06-16
 */
@Slf4j
@RestController
@RequestMapping("/api/hrpaas/uiform/v1")
@Api(value = "/api/hrpaas/uiform/v1", description = "表单引擎接口", tags = "表单引擎接口")
public class UiFormController {
    @Autowired
    private UiFormService uiFormService;

    @Value("${uiform.anonymous.id:}")
    private String allowedAnonymousUiformId;

    @PostMapping("/basics")
    @ApiOperation("新增/编辑表单基本信息")
    public Result<UiFormVo> saveUiFormBasics(@RequestBody UiFormBasicsDto uiFormBasicsDto){
        log.info("basicsDto = {}", FastjsonUtil.toJson(uiFormBasicsDto));
        UiFormDto uiFormDto = uiFormService.saveUiFormBasics(uiFormBasicsDto);
        return Result.ok(ObjectConverter.convert(uiFormDto, UiFormVo.class));
    }

    @PostMapping("/body")
    @ApiOperation("新增/编辑表单配置信息")
    public Result<UiFormVo> saveUiForm(@RequestBody @Valid UiFormBodyDto uiFormBodyDto){
        log.info("bodyDto = {}", FastjsonUtil.toJson(uiFormBodyDto));
        UiFormDto uiFormDto = uiFormService.saveUiFormBody(uiFormBodyDto);
        UiFormVo uiFormVo = ObjectConverter.convert(uiFormDto, UiFormVo.class);
        uiFormVo.setFieldConfig(uiFormBodyDto.getFieldConfig());
        return Result.ok(uiFormVo);
    }

    @PostMapping("/down")
    @ApiOperation("删除/禁用/移除表单")
    public List<String> deleteUiForm(@RequestBody String [] bids){
        log.info("remove uiform by formId=[{}] ", FastjsonUtil.toJson(bids));
        uiFormService.deleteUiForm(bids);
        return Arrays.asList(bids);
    }

    @PostMapping("/config/list")
    @ApiOperation("表单列表")
    public PageResult<UiFormVo> formList(@RequestBody UiFormSearchDto uiFormSearchDto){
        PageResult<UiFormBasicDataDto> pageData = uiFormService.formList(uiFormSearchDto);
        List<UiFormBasicDataDto> list = pageData.getItems();
        List<UiFormVo> items = new ArrayList<>();
        if(null != list && !list.isEmpty()){
            items = list.stream().map(uiFormDto ->{
                UiFormVo uiFormVo = ObjectConverter.convert(uiFormDto, UiFormVo.class);
                if(null != uiFormDto.getType().getValue()){
                    uiFormVo.setType(PageType.valueOf(uiFormDto.getType().getValue()));
                }

                if(null != uiFormDto.getPageTemplate().getValue()){
                    uiFormVo.setPageTemplate(PageTemplate.valueOf(uiFormDto.getPageTemplate().getValue()));
                }
                uiFormVo.setFieldConfig(FastjsonUtil.toObject(uiFormDto.getFieldConfig(), new TypeReference<List<UiFormFieldDto>>(){}));
                return uiFormVo;
            }).collect(Collectors.toList());
        }

        PageResult pageResult = new PageResult(items, pageData.getPageNo(), pageData.getPageSize(), pageData.getTotal());
        return pageResult;
    }

    @GetMapping("/detail/anonymous")
    @ApiOperation("表单详情信息/匿名")
    public Result<UiFormVo> detailAnonymous(String bid){
        if(StringUtil.isEmpty(allowedAnonymousUiformId)){
            throw new ServerException("禁止匿名访问");
        }
        if(!Lists.newArrayList(allowedAnonymousUiformId.split(",")).contains(bid)){
            throw new ServerException("禁止匿名访问");
        }
        try{
            ThreadLocalUtil.allowAnonymous();
            return detail(bid);
        }finally {
            ThreadLocalUtil.anonymousEnd();
        }
    }

    @GetMapping("/detail")
    @ApiOperation("表单详情信息")
    public Result<UiFormVo> detail(String bid){
        if(StringUtil.isBlank(bid)){
            return Result.fail(bid);
        }

        UiFormDto uiFormDto = uiFormService.getUiFormDetail(bid);
        UiFormVo uiFormVo = ObjectConverter.convert(uiFormDto, UiFormVo.class);

        if(null != uiFormDto){
            uiFormVo.setJoinModelName(uiFormService.getEntityDefName(uiFormDto.getJoinModel()));
        }

        uiFormVo.setFieldConfig(FastjsonUtil.toObject(uiFormDto.getFieldConfig(), new TypeReference<List<UiFormFieldDto>>(){}));
        return Result.ok(uiFormVo);
    }

    @GetMapping("/updateFormFunctionType")
    @ApiOperation("修改表单绑定流程类型")
    public Result updateFormFunctionType(@RequestParam("formId") String formId, @RequestParam("functionType") String functionType){
        UiFormDto uiFormDto = uiFormService.getUiFormDetail(formId);
        uiFormDto.getProperties().add("functionType", functionType);
        uiFormDto = uiFormService.saveUiForm(uiFormDto);
        UiFormVo uiFormVo = ObjectConverter.convert(uiFormDto, UiFormVo.class);
        return Result.ok(uiFormVo);
    }

}
