package com.caidaocloud.metadata.domain.service

import com.caidaocloud.excption.ServerException
import com.caidaocloud.hrpaas.core.metadata.interfaces.dto.EntityEventDefAddDto
import com.caidaocloud.hrpaas.core.metadata.interfaces.dto.EntityEventDefUpdateDto
import com.caidaocloud.hrpaas.metadata.sdk.enums.RelationOperationType
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil
import com.caidaocloud.metadata.domain.entity.EntityEvent
import com.caidaocloud.metadata.domain.entity.EntityEventDef
import com.caidaocloud.metadata.domain.entity.TrackedPropertiesGroup
import com.caidaocloud.metadata.domain.entity.EntityDataChange
import com.caidaocloud.metadata.domain.enums.DefChannel
import com.caidaocloud.metadata.domain.enums.OperationType
import org.springframework.stereotype.Service

@Service
class EntityEventDefDomainService {

    fun open(id : String){
        EntityEventDef.open(id)
    }

    fun close(id : String){
        EntityEventDef.close(id)
    }

    fun updateConsumerList(id : String, consumerList : List<String>){
        EntityEventDef.loadById(id)?.apply {
            this.updateConsumerList(consumerList)
        }?:throw ServerException("事件不存在")
    }

    fun addConsumerList(id : String, consumerList : List<String>){
        EntityEventDef.loadById(id)?.apply {
            this.consumerList = listOf(this.consumerList, consumerList).flatten()
            this.updateConsumerList(consumerList)
        }?:throw ServerException("事件不存在")
    }

    fun removeConsumerList(id : String, consumerList : List<String>){
        EntityEventDef.loadById(id)?.apply {
            this.consumerList = this.consumerList.filter {
                it !in consumerList
            }
            this.updateConsumerList(consumerList)
        }?:throw ServerException("事件不存在")
    }

    fun update(id : String, channel: DefChannel, def: EntityEventDefUpdateDto){
        EntityEventDef.initExist(id, channel, def.name,def.trackAll,
            def.trackedProperties.map { JsonEnhanceUtil.toObject(it, TrackedPropertiesGroup::class.java) },
            def.notifyProperties).update()
    }

    fun create(channel: DefChannel, def: EntityEventDefAddDto) : String{
        return EntityEventDef.initNew(def.identifier, def.modelRef, channel, def.name,
            def.trackAll,
            def.trackedProperties.map { JsonEnhanceUtil.toObject(it, TrackedPropertiesGroup::class.java) },
            def.notifyProperties).create()
    }

    fun loadByModelRef(modelRef : String) : List<EntityEventDef>{
        return EntityEventDef.loadByModelRef(modelRef)
    }

//    fun checkDataEventMatch(preData: EntityData, postData : EntityData, eventDef: EntityEventDef) : Boolean{
//        if(eventDef.trackAll){
//            return true
//        }
//        return listOf(eventDef.standardTrackedProperties,
//            eventDef.customTrackedProperties).flatten().any { propertyGroup ->
//            propertyGroup.properties.all { property ->
//                var preValue = preData.fetchPropertyValue(property)
//                var postValue = postData.fetchPropertyValue(property)
//                if(preValue is List<*> && postValue is List<*>){
//                    preValue.any { it !in postValue } ||
//                            postValue.any{ it !in preValue }
//                }else{
//                    preValue != postValue
//                }
//            }
//        }
//    }

    fun initDataEvent(bid : String, operation : OperationType, change:List<EntityDataChange>, eventDef: EntityEventDef) : EntityEvent {
        //val properties = listOf(eventDef.customNotifyProperties, eventDef.standardNotifyProperties).flatten()
        //val preDataMap = preData?.let { JsonEnhanceUtil.toObject(it, Map::class.java) }
        //val postDataMap = postData?.let { JsonEnhanceUtil.toObject(it, Map::class.java) }
        val event = EntityEvent()
//        val eventContentPre = mutableMapOf<String, Any?>()
//        val eventContentPost = mutableMapOf<String, Any?>()
        event.data = change
        event.dataOperationType = operation
        event.isRelationOperation = false
        event.eventIdentifier = eventDef.identifier
        event.dataId = bid
        return event
    }

    fun initDataEvent(bid : String, operation : OperationType, change:List<EntityDataChange>, eventIdentifier:String) : EntityEvent {
        //val properties = listOf(eventDef.customNotifyProperties, eventDef.standardNotifyProperties).flatten()
        //val preDataMap = preData?.let { JsonEnhanceUtil.toObject(it, Map::class.java) }
        //val postDataMap = postData?.let { JsonEnhanceUtil.toObject(it, Map::class.java) }
        val event = EntityEvent()
//        val eventContentPre = mutableMapOf<String, Any?>()
//        val eventContentPost = mutableMapOf<String, Any?>()
        event.data = change
        event.dataOperationType = operation
        event.isRelationOperation = false
        event.eventIdentifier = eventIdentifier
        event.dataId = bid
        return event
    }

    fun initRelationEvent(sourceId:String, change:List<EntityDataChange>, eventDef: EntityEventDef, operationType : RelationOperationType): EntityEvent {
        val event = EntityEvent()
        event.data = change
        event.relationOperationType = operationType
        event.isRelationOperation = true
        event.eventIdentifier = eventDef.identifier
        event.dataId = sourceId
        return event
    }
}