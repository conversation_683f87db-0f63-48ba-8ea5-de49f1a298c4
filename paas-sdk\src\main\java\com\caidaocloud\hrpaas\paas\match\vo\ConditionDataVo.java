package com.caidaocloud.hrpaas.paas.match.vo;

import com.caidaocloud.hrpaas.paas.match.ConditionComponentEnum;
import com.caidaocloud.hrpaas.paas.match.ConditionOperator;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

/**
 *
 * <AUTHOR>
 * @date 2022/11/2
 */
@ApiModel("匹配条件字段详情vo")
@Data
public class ConditionDataVo {
	@JsonInclude(NON_NULL)
	@ApiModelProperty("模型identifier")
	private String identifier;
	@JsonInclude(NON_NULL)
	@ApiModelProperty("模型名称")
	private String identifierName;
	@ApiModelProperty("字段名")
	private String property;
	@ApiModelProperty("显示名称")
	private String name;
	@ApiModelProperty("可选操作符")
	private List<ConditionOperator> operators;
	@ApiModelProperty("字段code")
	private String code;
	@ApiModelProperty("字段类型")
	private ConditionComponentEnum component;
	@ApiModelProperty("字典类型dataSource参数")
	private Map<String, Object> dataSourceParams;
	@ApiModelProperty("枚举类型类型componentValueEnum参数")
	private List<Map<String, Object>> componentValueEnum;

	public String getQueryProperty(){
		switch (component) {
		case DICT_SELECTOR:
			return property + ".dict.value";
		default:
			return property;
		}
	}
}
