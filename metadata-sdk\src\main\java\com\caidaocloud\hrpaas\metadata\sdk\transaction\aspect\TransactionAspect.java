package com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect;

import com.caidaocloud.hrpaas.metadata.sdk.dto.TransactionInfoDto;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.exception.PaasTransactionException;
import com.caidaocloud.web.Result;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.util.CollectionUtils;

import java.util.Iterator;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/10/9
 */
@Aspect
@Slf4j
@Order(0)
public class TransactionAspect {
    @Autowired
    private ITransactionFeign transactionFeign;


    public static final ThreadLocal<TransactionHolder> TRANSACTION_HOLDER = new ThreadLocal<>();

    public static final ThreadLocal<Set<TransactionAfterCommit>> SYNCRONIZATIONS = new ThreadLocal<>();

    @Pointcut("@annotation(com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional)")
    public void transactionPointCut() {
    }

    /**
     * paas事务拦截
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("transactionPointCut()")
    public Object doTransaction(ProceedingJoinPoint joinPoint) throws Throwable {
        // 当前线程已经拥有事务，直接执行业务逻辑
        TransactionHolder txHolder = getTransactionHolder();
        if (txHolder.isTransactionActivating()) {
            if (log.isDebugEnabled()) {
                log.debug("Found existed paas transaction,tx id={}", txHolder.getTransactionId());
            }
            return joinPoint.proceed();
        }

        // 开启事务
        doBegin(txHolder);
        try {
            // 执行业务逻辑
            Object retVal = joinPoint.proceed();
            // 事务提交
            doCommit(txHolder);
            return retVal;
        } catch (Exception e) {
            // 事务回滚
            log.error("Rolling back paas transaction by exception,tx id={}", txHolder.getTransactionId(), e);
            doRollback(txHolder);
            throw e;
        } finally {
            // 清理事务缓存
            cleanUpTransactionHolder();
        }
    }

    private void cleanUpTransactionHolder() {
        TRANSACTION_HOLDER.remove();
        if (SYNCRONIZATIONS.get() != null) {
            SYNCRONIZATIONS.remove();
        }
    }


    public static TransactionHolder getTransactionHolder() {
        return TRANSACTION_HOLDER.get() == null ? new TransactionHolder() : TRANSACTION_HOLDER.get();
    }

    public void doBegin(TransactionHolder txHolder) {
        if (log.isDebugEnabled()) {
            log.debug("Acquiring paas transaction");
        }
        Result<TransactionInfoDto> result;
        try {
            result = transactionFeign.begin();
            if (!result.isSuccess()) {
                throw new PaasTransactionException("Failed to acquire paas transaction");
            }
        } catch (Exception e) {
            throw new PaasTransactionException("Could not acquire paas transaction", e);
        }
        TransactionInfoDto transactionInfo = result.getData();
        BeanUtils.copyProperties(transactionInfo, txHolder);
        TRANSACTION_HOLDER.set(txHolder);
    }

    private void doCommit(TransactionHolder txHolder) {
        if (log.isDebugEnabled()) {
            log.debug("Committing paas transaction,tx id={}", txHolder.getTransactionId());
        }
        try {
            Result result = transactionFeign.commit(txHolder.getTransactionId());
            if (!result.isSuccess()) {
                throw new PaasTransactionException("Failed to commit paas transaction");
            }
            Set<TransactionAfterCommit> transactionSynchronizations = SYNCRONIZATIONS.get();
            if (!CollectionUtils.isEmpty(transactionSynchronizations)) {
                Set<Integer> isHandledSet = Sets.newHashSet();
                var firstSize = transactionSynchronizations.size();
                afterHandle(transactionSynchronizations, isHandledSet);
                var secondSize = transactionSynchronizations.size();
                var dobuleCheck = !(firstSize == secondSize);
                if (dobuleCheck) {
                    afterHandle(transactionSynchronizations, isHandledSet);
                }
            }
        } catch (Exception e) {
            throw new PaasTransactionException("Could not commit paas transaction", e);
        }
    }

    private void afterHandle(Set<TransactionAfterCommit> transactionSynchronizations, Set<Integer> isHandledSet) {
        Iterator<TransactionAfterCommit> iterator = transactionSynchronizations.iterator();
        while (iterator.hasNext()) {
            TransactionAfterCommit transactionSynchronization = iterator.next();
            if (!isHandledSet.isEmpty() && isHandledSet.contains(transactionSynchronization.hashCode())) {
                continue;
            }
            isHandledSet.add(transactionSynchronization.hashCode());
            transactionSynchronization.handle();
        }
    }

    private void doRollback(TransactionHolder txHolder) {
        if (log.isDebugEnabled()) {
            log.debug("Rolling back paas transaction,tx id={}", txHolder.getTransactionId());
        }
        try {
            Result result = transactionFeign.rollback(txHolder.getTransactionId());
            if (!result.isSuccess()) {
                throw new PaasTransactionException("Failed to roll back paas transaction");
            }
        } catch (Exception e) {
            throw new PaasTransactionException("Could not roll back paas transaction", e);
        }
    }

    /**
     * 注册事务提交后置处理
     *
     * @param synchronization
     */
    public static synchronized void registerSynchronization(TransactionAfterCommit synchronization) {
        if (synchronization == null) {
            return;
        }
        Set<TransactionAfterCommit> transactionSynchronizations = SYNCRONIZATIONS.get();
        if (transactionSynchronizations == null) {
            transactionSynchronizations = Sets.newHashSet();
        }
        transactionSynchronizations.add(synchronization);
        SYNCRONIZATIONS.set(transactionSynchronizations);
    }
}
