package com.caidaocloud.hrpaas.service.address;

import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.util.FastjsonUtil;

public class PhoneSimpleTest {
    public static void main(String[] args) {
        String propValue = "15121095842+52";
        PhoneSimple phone = new PhoneSimple();
        String [] splits = propValue.split("\\+");
        phone.setValue(splits[0]);
        phone.setCode(splits.length < 2 ? PhoneSimple.DEF_CODE : String.format(PhoneSimple.FORMAT_CODE, splits[1]));
        System.out.println(FastjsonUtil.toJson(phone));
    }
}
