package com.caidaocloud.metadata.infrastructure.repository.transaction;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

import com.caidaocloud.hrpaas.metadata.sdk.transaction.exception.PaasTransactionException;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @date 2022/10/14
 */
@Slf4j
public class TransactionRejectHandler implements RejectedExecutionHandler {

	@Override
	public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
		log.warn("The number of running transaction reaches the limit,{}", executor.toString());
		throw new PaasTransactionException("The number of running transaction reaches the limit");
	}
}
