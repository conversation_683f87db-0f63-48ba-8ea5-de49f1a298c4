package com.caidaocloud.hrpaas.service.application.uiform.service.hook;

import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 默认提供的 3 种简单后置处理器
 * <AUTHOR>
 * @date 2021-09-09
 */
@Service
public class DefaultCallbackHookService implements BaseHookBindable{

    /**
     * 默认的 text/value 转换器，抛弃其他多余字段，只留下 text/value
     * @param sourceMap
     * @param convertMap
     * @return
     */
    public Map defaultSelectConvert(Map sourceMap, Map convertMap){
        Set keySet = null;
        if(null == sourceMap || null == convertMap || null == (keySet = convertMap.keySet())){
            return sourceMap;
        }

        String targetTextKey = null, targetValueKey = null, targetText = null, targetValue = null;
        for (Object textKey : keySet) {
            if(null == targetTextKey){
                targetTextKey = (String) textKey;
                targetText = (String) convertMap.get(textKey);
                continue;
            }

            if(null == targetValueKey){
                targetValueKey = (String) textKey;
                targetValue = (String) convertMap.get(textKey);
                continue;
            }
        }

        targetText = !Objects.equals(targetTextKey, targetText) ? multiFieldConvert(sourceMap, targetText) : (String) sourceMap.getOrDefault(targetTextKey, "");
        targetValue = !Objects.equals(targetValueKey, targetValue) ? multiFieldConvert(sourceMap, targetValue) : (String) sourceMap.getOrDefault(targetValueKey, "");

        Map targetMap = new HashMap(10);
        targetMap.put(HookConstant.CONVERT_SELECT_FEILD_VALUE, targetValue);
        targetMap.put(HookConstant.CONVERT_SELECT_FEILD_TEXT, targetText);
        return targetMap;
    }

    /**
     * 多字段分隔转换展示 text 处理器
     * @param sourceMap
     * @param keys
     * @return
     */
    private String multiFieldConvert(Map sourceMap, String keys){
        String [] keyArr = keys.split("\\$");
        return Arrays.stream(keyArr).map(itemKey -> sourceMap.getOrDefault(itemKey, "").toString()).collect(Collectors.joining("-"));
    }

    /**
     * 单个：下拉框 text/value 后置处理器
     * @param sourceMap
     * @param convertMap
     * @return
     */
    public Map putTxtValSelectConvert(Map sourceMap, Map convertMap){
        Set keySet = null;
        if(null == sourceMap || null == convertMap || null == (keySet = convertMap.keySet())){
            return sourceMap;
        }

        String targetTxtKey = null, targetValKey = null, targetTxt = null, targetVal = null;
        for (Object txtKey : keySet) {
            if(null == txtKey){
                continue;
            }

            if(null == targetTxtKey){
                targetTxtKey = (String) txtKey;
                targetTxt = (String) convertMap.get(txtKey);
                continue;
            }

            if(null == targetValKey){
                targetValKey = (String) txtKey;
                targetVal = (String) convertMap.get(txtKey);
                continue;
            }
        }

        targetTxt = !Objects.equals(targetTxtKey, targetTxt) ? multiFieldConvert(sourceMap, targetTxt) : (String) sourceMap.getOrDefault(targetTxtKey, "");
        targetVal = !Objects.equals(targetValKey, targetVal) ? multiFieldConvert(sourceMap, targetVal) : (String) sourceMap.getOrDefault(targetValKey, "");

        sourceMap.put(HookConstant.CONVERT_SELECT_FEILD_VALUE, targetVal);
        sourceMap.put(HookConstant.CONVERT_SELECT_FEILD_TEXT, targetTxt);
        return sourceMap;
    }

    /**
     * 批量：下拉框 text/value 后置处理器
     * @param sourceList
     * @param convertMap
     * @return
     */
    public List<Map> txtValSelectHook(List<Map> sourceList, Map convertMap){
        if(null == sourceList || sourceList.isEmpty()){
            return sourceList;
        }

        List<Map> resultList = new ArrayList<>(sourceList.size());
        sourceList.forEach(map -> {
            resultList.add(this.putTxtValSelectConvert(map, convertMap));
        });
        return resultList;
    }
}
