package com.caidaocloud.metadata.application.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("元数据关联模型数据实体")
public class MetadataQueryModelDataDto extends BasePage {
    /**
     * 模型名称，如：entity.hr.CostCenter 等
     */
    @ApiModelProperty("模型名称")
    private String identifier;

    @ApiModelProperty("数据过滤条件")
    private String filter;
}
