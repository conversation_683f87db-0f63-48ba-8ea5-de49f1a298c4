package com.caidaocloud.metadata.infrastructure.repository.impl.util;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.MultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SimpleDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.infrastructure.repository.po.EntityDefPo;
import com.caidaocloud.metadata.infrastructure.repository.utils.EncryptionTool;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class SqlGeneratorUtil {

    private static ThreadLocal<QuerySqlGenInfo> querySqlGenInfo = new ThreadLocal<>();

    public static void initQuerySqlGenInfo(String tenantId, String colEscape, String dbType, DataQueryDto query){
        val info = new QuerySqlGenInfo(tenantId, colEscape, dbType);
        if (query != null) {
            info.setExpression(query.isExpression());
        }
        querySqlGenInfo.set(info);
    }

    public static void addSpecifyExpProperty(String property){
        querySqlGenInfo.get().addSpecifyExpProperty(property);
    }

    public static void clearQuerySqlGenInfo(){
        querySqlGenInfo.remove();
    }

    public static QuerySqlGenInfo getQuerySqlGenInfo(){
        return querySqlGenInfo.get();
    }


    private static String encrypt(String s){
        val tenantId = getTenantId();
        String tenantSecret = SpringUtil.getContext().getEnvironment().getProperty("property.encrypt.key." + tenantId, "");
        if(StringUtils.isEmpty(tenantSecret)){
            val encryptKey = SpringUtil.getContext().getEnvironment().getProperty("property.encrypt.key.common", "jlt4fdgak56jsdladsjdgladsjgldsjl");
            if(StringUtils.isEmpty(encryptKey)){
                throw new ServerException("密钥为空");
            }
            tenantSecret = encryptKey;
        }
        return EncryptionTool.encrypt(s, tenantSecret);
    }


    public static void addProperties(List<String> relateList, String splitter) {
        if(!relateList.isEmpty()){
            getQuerySqlGenInfo().setQueryOneTable(false);
        }
        for(String relateProperty : relateList){
            if(!relateProperty.contains(splitter)){
                throw new ServerException("关联属性格式错误");
            }
            addProperty(querySqlGenInfo.get().getRelatedPropertiesMap(), relateProperty, splitter);
        }
    }

    private static void addProperty(Map<String, PathPrefixAndProperties> relatedProperties, String propertyPath, String splitter){
        val pathPrefix = propertyPath.substring(0, propertyPath.indexOf(splitter)).replaceAll("\\$", ".");
        val pathSuffix = propertyPath.substring(propertyPath.indexOf(splitter) + 1);
        if(!relatedProperties.containsKey(pathPrefix)){
            relatedProperties.put(pathPrefix, new PathPrefixAndProperties(pathPrefix));
        }
        val properties = relatedProperties.get(pathPrefix);
        if(pathSuffix.contains(splitter)){
            addProperty(properties.getSubProperties(), pathSuffix, splitter);
        }else{
            val relatedProperty = new PathPrefixAndProperties.RelatedProperty();
            relatedProperty.setProperty(pathSuffix.replaceAll("\\$", "."));
            properties.getProperties().add(relatedProperty);
        }
    }

    public static String getSelectSql(){
        return getQuerySqlGenInfo().getSelectSql();
    }

    public static String getBidSql(){
        return getQuerySqlGenInfo().getBidSql();
    }

    public static String getCountSql(){
        return getQuerySqlGenInfo().getCountSql();
    }

    public static Object[] generateSqlAndReturnJdbcParams(String identifier, String bid, long time, DataFilter filter, int pageSize, int pageNo) {
        return getQuerySqlGenInfo().generateSqlAndReturnJdbcParams(identifier, bid, time, filter, Lists.list(), pageSize, pageNo, null);
    }

    public static Object[] generateSqlAndReturnJdbcParams(String identifier, String bid, long time, DataFilter filter, List<String> specifyProperties, int pageSize, int pageNo, String orderBy) {
        return getQuerySqlGenInfo().generateSqlAndReturnJdbcParams(identifier, bid, time, filter, specifyProperties, pageSize, pageNo, orderBy);
    }

    public static EntityDef fetchDef(String identifier){
        return getQuerySqlGenInfo().fetchDef(identifier);
    }

    public static EntityDefPo fetchDefPo(String identifier){
        return getQuerySqlGenInfo().fetchDefPo(identifier);
    }

    public static String getTenantId(){
        return getQuerySqlGenInfo().getTenantId();
    }

    public static String getColEscape(){
        return getQuerySqlGenInfo().getColEscape();
    }

    public static String fetchColumnName(EntityDefPo entityDef, String property){
        if(StringUtils.isEmpty(entityDef.getPropertyToFieldMapping())){
            return property;
        }
        Map<String, String> propertyToFieldMapping = FastjsonUtil.toObject(entityDef.getPropertyToFieldMapping(), Map.class);
        for(String key : propertyToFieldMapping.keySet()){
            if(SnakeCaseConvertor.toSnake(property).equals(SnakeCaseConvertor.toSnake(key))){
                return propertyToFieldMapping.get(key);
            }
        }
        return property;
    }

    public static void addFilter(DataFilter filter) {
        getQuerySqlGenInfo().addFilter(filter);
    }

    public static Pair<String,Object[]> toWhereCauses(String identifier, DataFilter dataFilter){
        return toWhereCauses(identifier, dataFilter, "");
    }

    public static Pair<String,Object[]> toWhereCauses(String identifier, DataFilter dataFilter, String prefix){
        if(dataFilter instanceof SimpleDataFilter){
            return toWhereCause(identifier, (SimpleDataFilter) dataFilter, prefix);
        }else if (dataFilter instanceof MultiDataFilter){
            val whereCauseList = ((MultiDataFilter) dataFilter).getFilters().stream()
                    .map(subFilter-> toWhereCauses(identifier, subFilter, prefix)).collect(Collectors.toList());
            List<String> whereCauseSqlList = Lists.list();
            List<Object> whereCauseParamList = Lists.list();
            whereCauseList.forEach(whereCause->{
                whereCauseSqlList.add(whereCause.first());
                whereCauseParamList.addAll(Lists.list(whereCause.second()));
            });
            val whereCauseSql = "(" + StringUtils.join(
                    whereCauseSqlList,
                    " "+((MultiDataFilter) dataFilter).getOperator().toString()+" "
            )+ ")";
            return Pair.pair(whereCauseSql, whereCauseParamList.toArray());
        }else{
            return Pair.pair("", new Object[]{});
        }
    }

    private static PropertyDef getPropertyDefByPath(String identifier, String path){
        while(path.contains(".")){
            val relationProperty = path.substring(0, path.indexOf("."));
            path = path.substring(path.indexOf(".") + 1);
            EntityDef def = fetchDef(identifier);
            identifier = def.getRelationProperties().stream()
                    .filter(it->StringUtils.equals(it.getProperty(),
                            relationProperty)).findFirst().get().getRelatedIdentifier();
        }
        if(path.contains("$")){
            path = path.substring(0, path.indexOf("$"));
        }
        val property = path;
        EntityDef def = fetchDef(identifier);
        try {
            return def.fetchAllProperties().stream().filter(it->StringUtils.equals(it.getProperty(), property)).findFirst().get();
        }
        catch (Exception e) {
            throw new ServerException("Getting property occurs error,property: " + property + ",identifier: " + identifier + ".error msg: " + e.getMessage());
        }
    }

    private static String castColumnNameToNumber(String column){
        val dbType = getQuerySqlGenInfo().getDbType();
        if("mysql".equals(dbType)){
            return "cast(" + column + " as Decimal(50,2))";
        }else if("postgresql".equals(dbType)){
            return "cast(" + column + " as Decimal(50,2))";
        }else{
            throw new ServerException("unsupported db type");
        }
    }

    private static Pair<String,Object[]> toWhereCause(String identifier, SimpleDataFilter dataFilter, String prefix){
        val property = dataFilter.getProperty();
        String propertySuffix = property;
        if(property.contains(".")){
            propertySuffix = property.substring(property.lastIndexOf(".")+1);
        }
        val entityDef = fetchDefPo(identifier);
        val column = SnakeCaseConvertor.toSnake(
                fetchColumnName(entityDef, property.replaceAll("\\$","."))
        );
        val toLong = Lists.list("create_time","update_time",
                "data_start_time","data_end_time").contains(SnakeCaseConvertor.toSnake(propertySuffix));
        val toBoolean = Lists.list("deleted")
                .contains(SnakeCaseConvertor.toSnake(propertySuffix));
        String escapeColumn = prefix + getColEscape() + column + getColEscape();
        val isEncrypted = getPropertyDefByPath(identifier, dataFilter.getProperty()).isEncrypted();
        switch (dataFilter.getOperator()){
            case LT: {
                if(isEncrypted){
                    throw new ServerException("unsupported operator for encrypted property");
                }
                String value = dataFilter.getValue();
                if(!toLong){
                    escapeColumn = castColumnNameToNumber(escapeColumn);
                }
                return Pair.pair(escapeColumn + "<?", new Object[]{convertValue(value, true, toBoolean, true)});
            }
            case GT: {
                if(isEncrypted){
                    throw new ServerException("unsupported operator for encrypted property");
                }
                String value = dataFilter.getValue();
                if(!toLong){
                    escapeColumn = castColumnNameToNumber(escapeColumn);
                }
                return Pair.pair(escapeColumn + ">?", new Object[]{convertValue(value, true, toBoolean, true)});
            }
            case LE: {
                if(isEncrypted){
                    throw new ServerException("unsupported operator for encrypted property");
                }
                String value = dataFilter.getValue();
                if(!toLong){
                    escapeColumn = castColumnNameToNumber(escapeColumn);
                }
                return Pair.pair(escapeColumn + "<=?", new Object[]{convertValue(value, true, toBoolean, true)});
            }
            case GE: {
                if(isEncrypted){
                    throw new ServerException("unsupported operator for encrypted property");
                }
                String value = dataFilter.getValue();
                if(!toLong){
                    escapeColumn = castColumnNameToNumber(escapeColumn);
                }
                return Pair.pair(escapeColumn + ">=?", new Object[]{convertValue(value, true, toBoolean, true)});
            }
            case EQ: {
                if(dataFilter.getValue() == null){
                    return Pair.pair(escapeColumn + " is null ", new Object[]{});
                }
                if(!isEncrypted){
                    String value = dataFilter.getValue();
                    return Pair.pair(escapeColumn + "=?", new Object[]{convertValue(value, toLong, toBoolean, false)});
                }else{
                    if(dataFilter.getValue() == null){
                        throw new ServerException("错误的比较值");
                    }
                    String value = encrypt(dataFilter.getValue());
                    return Pair.pair(escapeColumn + " like ?", new Object[]{value + "%"});
                }
            }
            case NE: {
                if(dataFilter.getValue() == null){
                    return Pair.pair(escapeColumn + " is not null ", new Object[]{});
                }
                if(!isEncrypted){
                    String value = dataFilter.getValue();
                    return Pair.pair("(" + escapeColumn + "!=? or "+escapeColumn+" is null)", new Object[]{convertValue(value, toLong, toBoolean, false)});
                }else{
                    String value = encrypt(dataFilter.getValue());
                    return Pair.pair("(" + escapeColumn + " not like ? or " + escapeColumn + " is null)", new Object[]{value + "%"});
                }
            }
            case REGEX : {
                if(dataFilter.getValue() == null){
                    throw new ServerException("错误的比较值");
                }
                String value = dataFilter.getValue();
                if(isEncrypted){
                    value = encrypt(value);
                }
                return Pair.pair(escapeColumn + " like ?", new Object[]{"%" + value + "%"});
            }
            case NOT_LIKE : {
                if(dataFilter.getValue() == null){
                    throw new ServerException("错误的比较值");
                }
                String value = dataFilter.getValue();
                if(isEncrypted){
                    value = encrypt(value);
                }
                return Pair.pair(escapeColumn + " not like ?", new Object[]{"%" + value + "%"});
            }
            case IN : {
                if(isEncrypted){
                    val params = dataFilter.getValues().stream().map(it->{
                        if(it==null){
                            throw new ServerException("错误的比较值");
                        }
                        return encrypt(it) + "%";
                    }).collect(Collectors.toList()).toArray();
                    String escapeColumnFinal = escapeColumn;
                    val compare = "(" + StringUtils.join(dataFilter.getValues().stream()
                            .map(it->escapeColumnFinal +" like ? ").collect(Collectors.toList()), " or ") + ")";
                    return Pair.pair(compare, params);
                }else{
                    val values = dataFilter.getValues().stream()
                            .map(it->"?").collect(Collectors.toList());
                    val params = dataFilter.getValues().stream().map(it->
                            convertValue(it, toLong, toBoolean,false)).collect(Collectors.toList()).toArray();
                    return Pair.pair(escapeColumn + " in ("+StringUtils.join(values,",")+")", params);
                }
            }
            case NOT_IN : {
                if(isEncrypted){
                    val params = dataFilter.getValues().stream().map(it->{
                        if(it==null){
                            throw new ServerException("错误的比较值");
                        }
                        return encrypt(it) + "%";
                    }).collect(Collectors.toList()).toArray();
                    String escapeColumnFinal = escapeColumn;
                    val compare = "(" + StringUtils.join(dataFilter.getValues().stream()
                            .map(it->escapeColumnFinal +" not like ? ").collect(Collectors.toList()), " and ") + ")";
                    return Pair.pair(compare, params);
                }else{
                    val values = dataFilter.getValues().stream()
                            .map(it->"?").collect(Collectors.toList());
                    val params = dataFilter.getValues().stream().map(it->
                            convertValue(it, toLong, toBoolean,false)).collect(Collectors.toList()).toArray();
                    return Pair.pair(escapeColumn + " not in ("+StringUtils.join(values,",")+")", params);
                }
            }
            default: throw new ServerException("不支持的过滤条件");
        }
    }

    private static Object convertValue(String value, boolean toLong, boolean toBoolean, boolean toDouble){
        if(value == null){
            throw new ServerException("错误的比较值");
        }
        if(toDouble){
            return Double.valueOf(value);
        }
        if(toLong){
            return Long.valueOf(value);
        }
        if(toBoolean){
            return Boolean.valueOf(value);
        }
        return value;
    }

    public static Map<String, PropertyDataType> getRelationDataTypes() {
        return getQuerySqlGenInfo().getRelationDataTypes();
    }

    public static void putRelationDataType(String relation, PropertyDataType dataType){
        getQuerySqlGenInfo().putRelationDataType(relation, dataType);
    }

    public static void setGroup(boolean group) {
        getQuerySqlGenInfo().setGroup(group);
    }

    public static List<String> filterExpProperty(EntityDef entityDef, List<String> specifyProperty) {
        Set<String> expSet = Sequences.sequence(entityDef.fetchProperties()).filter(PropertyDef::isExpEnable)
                .map(PropertyDef::getProperty)
                .toSet();
        List<String> ret = new ArrayList<>();
        for (String property : specifyProperty) {
            if (expSet.contains(property)) {
                addSpecifyExpProperty(property);
            }
            else {
                ret.add(property);
            }
        }
        return ret;
    }

    public static List<String> getSpecifyExpProperty() {
        return querySqlGenInfo.get().getSpecifyExpProperty();
    }

    public static boolean isExpression() {
        QuerySqlGenInfo info = getQuerySqlGenInfo();
        return info != null && info.isExpression();
    }
}
