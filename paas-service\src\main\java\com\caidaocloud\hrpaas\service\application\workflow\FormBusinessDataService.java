package com.caidaocloud.hrpaas.service.application.workflow;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.service.application.form.service.FormDataService;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormData;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropertyData;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.service.IBusinessDetailService;
import com.caidaocloud.workflow.service.IBusinessServiceLoader;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 表单流程摘要实现
 *
 * <AUTHOR>
 * @date 2024/4/29
 **/
@Slf4j
@Service
public class FormBusinessDataService extends IBusinessDetailService implements IBusinessServiceLoader {
    @Autowired
    private FormDataService formDataService;

    @Override
    public IBusinessDetailService getService(String funCode) {
        return SpringUtil.getBean(FormBusinessDataService.class);
    }

    @Override
    protected Map<String, Object> getBusinessDetail(String businessKey) {
        if (StringUtils.isBlank(businessKey)) {
            return Maps.newHashMap();
        }
        var split = businessKey.split("_");
        if (split.length != 2) {
            return Maps.newHashMap();
        }
        var funcCode = split[1];
        var split1 = funcCode.split("-");
        Map<String, Object> detail = Maps.newHashMap();
        try {
            String formId = split1[split1.length - 1], id = split[0];
            val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("form not exit"));
            val formData = FormData.loadById(formId, id, formDef, false);
            if (formData == null || CollectionUtils.isEmpty(formData.getProperties())) {
                return detail;
            }
            var formPropDefMap = formDef.getProperties().stream().collect(Collectors.toMap(e -> e.getProperty(), Function.identity()));
            for (FormPropertyData property : formData.getProperties()) {
                detail.put(property.getProperty(), property.getValue() != null ? property.convertData(formPropDefMap) : "");
            }
        } catch (Exception e) {
            log.error(String.format("getBusinessDetail occur error, businessKey=%s", businessKey), e);
        }
        return detail;
    }

    @Override
    public Map<String, Object> getDetail(String businessKey) {
        return getBusinessDetail(businessKey);
    }

    @Override
    protected void convertData(Map<String, Object> detailMap, Map<String, WfMetaFunFormFieldDto> funField) {
    }

    @Override
    protected List<String> belongFunCode() {
        return null;
    }
}