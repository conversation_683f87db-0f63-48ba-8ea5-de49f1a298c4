package com.caidaocloud.hrpaas.service.domain.metadata.repository;

import com.caidaocloud.hrpaas.service.domain.metadata.entity.ImportTemplateDo;
import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import com.caidaocloud.hrpaas.service.interfaces.dto.template.ImportTemplateDto;
import com.googlecode.totallylazy.Option;

import java.util.List;

public interface ImportTemplateRepository extends IBaseRepository<ImportTemplateDo> {
    ImportTemplateDo loadById(String tenantId, String id);

    Option<ImportTemplateDo> load(String tenantId, String id);

    List<ImportTemplateDo> getList(ImportTemplateDto importTemplateDto, int pageNo, int pageSize);

    void delete(String id);

    ImportTemplateDo loadByCode(String tenantId, String templateCode);
}
