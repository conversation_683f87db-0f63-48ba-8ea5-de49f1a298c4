package com.caidaocloud.metadata.application.event

import com.caidaocloud.hrpaas.metadata.sdk.enums.RelationOperationType
import com.caidaocloud.metadata.application.dto.EntityDataChangeDto
import com.caidaocloud.metadata.domain.enums.OperationType
import com.caidaocloud.msg.message.AbstractBasicMessage

class EntityEventMsg: AbstractBasicMessage(){
    var data : List<EntityDataChangeDto> = listOf()
    var dataOperationType : OperationType? = null
    var relationOperationType : RelationOperationType? = null
    var isRelationOperation : Boolean = false
    var eventIdentifier : String = ""
    var dataId : String? = null
}