package com.caidaocloud.hrpaas.metadata.sdk.expression;

import javax.annotation.PostConstruct;

import com.caidaocloud.hrpaas.metadata.sdk.expression.function.ContractInfo;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.spring.SpringContextFunctionLoader;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 * @date 2024/3/6
 */
@Configuration
@ComponentScan({"com.caidaocloud.hrpaas.metadata.sdk.expression.function"})
public class ExpressionConfiguration implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @PostConstruct
    public void init(){
        SpringContextFunctionLoader loader = new SpringContextFunctionLoader(applicationContext);
        AviatorEvaluator.addFunctionLoader(loader);
    }
	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}

}
