package com.caidaocloud.metadata.infrastructure.repository.transaction;

import javax.annotation.PostConstruct;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration;
import com.alibaba.cloud.nacos.registry.NacosServiceRegistry;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.discovery.event.InstanceRegisteredEvent;
import org.springframework.cloud.commons.util.InetUtils;
import org.springframework.context.ApplicationListener;

/**
 *
 * <AUTHOR>
 * @date 2022/10/18
 */

@Data
@Slf4j
public class ServiceProperties implements ApplicationListener<InstanceRegisteredEvent> {
	String service;
	String ip;
	int port;

	@Autowired
	private InetUtils inetUtils;

	/**
	 * 监听nacos注册完成的事件
	 * @param event the event to respond to
	 */
	@Override
	public void onApplicationEvent(InstanceRegisteredEvent event) {
		if (!(event.getSource() instanceof NacosAutoServiceRegistration)) {
			return;
		}
		NacosDiscoveryProperties properties = ((NacosDiscoveryProperties) event.getConfig());
		if (log.isDebugEnabled()) {
			log.debug("nacos registry info,{}",properties);
		}
		this.ip = properties.getIp();
		this.port = properties.getPort();
		this.service = properties.getService();
	}
}
