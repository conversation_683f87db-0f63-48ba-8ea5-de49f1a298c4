package com.caidaocloud.hrpaas.service.application.form.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = "${feign.rename.caidaocloud-transfer-service:caidaocloud-transfer-service}", fallback = TransferFeignFallback.class, configuration = FeignConfiguration.class, contextId = "transferFeign")
public interface ITransferFeign {

    @GetMapping("/api/change/job/v1/current")
    Result getCurrentData(@RequestParam String empId);

}
