package com.caidaocloud.hrpaas.metadata.sdk.util;

import lombok.val;
import org.apache.commons.lang3.StringUtils;

public class AuthScopeUtil {

    private static ThreadLocal<Boolean> authScopeRevert = new ThreadLocal<>();

    private static ThreadLocal<String> authScopeIdentifier = new ThreadLocal<>();

    public static void revert(){
        authScopeRevert.set(true);
    }

    public static boolean checkRevert(){
        val revert = authScopeRevert.get();
        return revert == null ? false : revert;
    }

    public static void clearRevert(){
        authScopeRevert.remove();
    }

    public static void setAuthScopeIdentifier(String identifier){
        authScopeIdentifier.set(identifier);
    }

    public static String convertAuthScopeIdentifier(String realIdentifier){
        val convert = authScopeIdentifier.get();
        return StringUtils.isEmpty(convert) ? realIdentifier : convert;
    }

    public static void clearAuthScopeIdentifier(){
        authScopeIdentifier.remove();
    }

}
