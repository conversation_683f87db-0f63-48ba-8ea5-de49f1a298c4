package com.caidaocloud.hrpaas.service.domain.form.entity;

import java.util.HashMap;
import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;

@Data
@Slf4j
public class FormSelectPropDef extends FormPropDef {

    public List<PropertyDef> toEntityProperties() {
        PropertyDef property = initProperty();
        PropertyDef txtProp = convert2TxtProp(property);
        return Lists.list(property,txtProp);
    }

    private PropertyDef convert2TxtProp(PropertyDef property) {
        PropertyDef def = new PropertyDef();
        def.setProperty(txtPropKey(property.getProperty()));
        def.setDataType(PropertyDataType.String);
        def.setI18nName(new HashMap<>());
        def.getI18nName().put("default", property.getI18nName().get("default") + "txt");
        def.setName(def.getI18nName().get("default"));
        def.setRequired(false);
        def.setUnique(false);
        def.setExpEnable(false);
        def.setDefaultValue(null);
        return def;
    }

    @NotNull
    private String txtPropKey(String property) {
        return property + "_txt";
    }

    public String txtPropKey(){
        return txtPropKey(getProperty());
    }

    public FormPropertyData loadTxtData(FormData.FormDataEntity input) {
        String txtPropKey = txtPropKey(getProperty());
        PropertyValue value = input.getProperties().get(txtPropKey);
        val formPropertyData = new FormPropertyData();
        formPropertyData.setProperty(txtPropKey);
        formPropertyData.setDataType(PropertyDataType.String);
        formPropertyData.setValue(value == null ? null : value.toText());
        formPropertyData.setSuffix("");
        return formPropertyData;
    }
}
