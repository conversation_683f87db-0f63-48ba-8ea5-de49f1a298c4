package com.caidaocloud.hrpaas.service.application.page.feign;

import com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.TxFeignConfiguration;
import com.caidaocloud.hrpaas.service.application.page.dto.ImportFunctionDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "${feign.rename.caidaocloud-import-export-service:caidaocloud-import-export-service}",
        fallback = ImportExportFeignImpl.class,
        configuration = {FeignConfiguration.class, TxFeignConfiguration.class},
        contextId = "importFeign", qualifier = "importFeign")
public interface ImportExportFeign {

    @PostMapping("/api/import/v1/config/function")
    Result add(@RequestBody ImportFunctionDto importFunction);

}
