package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.jarvis.cache.annotation.Cache;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class SelectedLeaderScopeRestrictionHandler implements ScopeRestrictionHandler {

    @Override
    @Cache(key = "'13_scope_restriction_' + #args[0] + '_' + #args[1]",expire = 5 *60)
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        Boolean isFilter = AuthScopeFilterUtil.get();
        val tlUser = SecurityUserUtil.getThreadLocalSecurityUserInfo();
        try{
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            if(StringUtils.isEmpty(simpleValues)){
                simpleValues = String.valueOf(SecurityUserUtil.getSecurityUserInfo().getEmpId());
            }
            AuthScopeFilterUtil.put(false);
            val orgs = DataQuery.identifier("entity.hr.Org").limit(2000, 1)
                    .filterProperties(DataFilter
                                    .in("leaderEmp$empId", Lists.list(simpleValues.split(","))),
                            Lists.list("bid"), System.currentTimeMillis()).getItems().stream().map(it->it.get("bid")).collect(Collectors.toList());
            return orgs;
        }finally {
            if(null == tlUser){
                SecurityUserUtil.removeSecurityUserInfo();
            }else{
                SecurityUserUtil.setSecurityUserInfo(tlUser);
            }
            AuthScopeFilterUtil.put(isFilter);
        }

    }

}
