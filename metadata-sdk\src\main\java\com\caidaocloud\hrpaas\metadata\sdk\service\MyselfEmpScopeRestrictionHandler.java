package com.caidaocloud.hrpaas.metadata.sdk.service;

import java.util.List;

import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.jarvis.cache.annotation.Cache;
import lombok.val;

import org.springframework.stereotype.Component;

@Component
public class MyselfEmpScopeRestrictionHandler implements ScopeRestrictionHandler {

    @Override
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        val empId = SecurityUserUtil.getSecurityUserInfo().getEmpId();
        if(empId == null){
            return Lists.list();
        }
        return Lists.list(String.valueOf(empId));
    }

}
