package com.caidaocloud.hrpaas.service.domain.feildMapping.repository;

import com.caidaocloud.hrpaas.service.domain.feildMapping.entity.PreMappingConfig;

import java.util.Collection;
import java.util.List;

public interface PreMappingConfigRepository {
    List<PreMappingConfig> list(List<String> sourceList);

    void deleteByIds(Collection<String> ids);

    String insert(PreMappingConfig entity);

    void delete(PreMappingConfig preMappingConfig);

    void update(PreMappingConfig preMappingConfig);

    PreMappingConfig selectById(String id);

    PreMappingConfig loadByName(String name);

    List<PreMappingConfig> listByCategory(String category);

    List<PreMappingConfig> listByIds(List<String> idList);

    List<PreMappingConfig> listFormByCategory(String category);
}
