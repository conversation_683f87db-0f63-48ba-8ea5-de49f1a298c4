package com.caidaocloud.hrpaas.service.application.page.constant;

public enum Menu {
    POST_MANAGEMENT("岗位管理", "navBar.menu.postManagement", "postManage"),
    CONTRACT_MANAGEMENT("合同管理", "navBar.menu.contractManagement", "contractManage"),
    EMPLOYEE("人事", "navBar.menu.employee", "EMPLOYEE"),
    HOME("首页", "ROOT", ""),
    DOCUMENT_SIGNING("文件签署", "navBar.menu.documentSigning", "documentSignMenu"),
    ORGANIZATION("组织", "navBar.menu.organization", "ORGANIZATION"),
    PAYROLL("薪酬", "navBar.menu.payroll", "PAYROLL"),
    BONUS_MANAGE("奖金管理", "navBar.menu.prizeManage", "BONUS_MANAGE"),
    BONUS_BUSINESS_DATA("奖金业务数据", "navBar.menu.businessData", "BONUS_BUSINESS_DATA");


    private final String name;
    private final String pageId;
    private final String code;

    Menu(String name, String navBarPath, String code) {
        this.name = name;
        this.pageId = navBarPath;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getPageId() {
        return pageId;
    }

    public String getCode() {
        return code;
    }

    public static String getCodeByNavBarPath(String navBarPath) {
        for (Menu menu : values()) {
            if (menu.getPageId().equals(navBarPath)) {
                return menu.getCode();
            }
        }
        return null; // or throw an exception if a matching pageId is required
    }
}
