package com.caidaocloud.metadata.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.metadata.domain.entity.EntityData;
import lombok.Data;

@Data
@TableName("paas_entity_data")
public class EntityDataPo extends BasePo<EntityDataPo, EntityData> {

    private String identifier;

    private String bid;

    private String tenantId;

    private long createTime;

    private String createBy;

    private long updateTime;

    private String updateBy;

    private long dataStartTime;

    private long dataEndTime;

    private boolean deleted;

}
