package com.caidaocloud.hrpaas.service.test;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.hrpaas.service.application.uiform.service.UiFormDataSourceService;
import com.caidaocloud.hrpaas.service.application.uiform.service.UiFormService;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.DataSourceDo;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceRequestMethod;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceType;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiDataSourceDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiGeneralDataSourceDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class UiDataSourceTest {
    @Resource
    private UiFormDataSourceService dataSourceService;
    @Resource
    private UiFormService uiFormService;

    @Test
    public void testTrue(){
        HolderUtil.setTenantId("caidaotest");
        uiFormService.updateUiFormShowNav("1281032843843586", 0L, System.currentTimeMillis());
    }

    @Test
    public void addDataSource(){
        UiDataSourceDto uiDataSourceDto = getSchemaType();

        UserInfo userInfo = new UserInfo();
        userInfo.setUserid(0);
        userInfo.setBelongOrgId(0);

        WebUtil.getRequest().setAttribute("_caidaocloud_userinfo_request_threadlocal_", userInfo);

        DataSourceDo dataSourceDo = ObjectConverter.convert(uiDataSourceDto, DataSourceDo.class);

        PreCheck.preCheckArgument(dataSourceDo.checkDuplicateName(), "数据源名字不能重复!");

        dataSourceDo.setConfig(FastjsonUtil.toJson(uiDataSourceDto.getConfig()));
        dataSourceDo.save();
        uiDataSourceDto.setId(dataSourceDo.getId());

        log.info("addDataSource DataSourceDto={}", FastjsonUtil.toJson(uiDataSourceDto));
    }

    /**
     * 调薪原因
     */
    private UiDataSourceDto getSalaryReason(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_DICT);
        uiDataSourceDto.setSourceName("调薪原因");
        uiDataSourceDto.setDescription("薪资管理，调薪原因字典下拉框");

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/getSystemParams");
        mongoMap.put("requestMethod", "POST");

        params.put("typeCode", "SalaryReason");
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);

        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/bcc/dict/getSystemParams", DataSourceRequestMethod.POST, params));
        return uiDataSourceDto;
    }

    /**
     * 专业职级
     */
    private UiDataSourceDto getJobGrade(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_DICT);
        uiDataSourceDto.setSourceName("专业职级");
        uiDataSourceDto.setDescription("薪资管理->薪资方案——>使用范围->专业职级字典下拉框");

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/getSystemParams");
        mongoMap.put("requestMethod", "POST");

        params.put("typeCode", "JobGrade");
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/bcc/dict/getSystemParams", DataSourceRequestMethod.POST, params));
        return uiDataSourceDto;
    }

    /**
     * 员工类型
     */
    private UiDataSourceDto getEmployType(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_DICT);
        uiDataSourceDto.setSourceName("员工类型");
        uiDataSourceDto.setDescription("薪资管理->薪资方案——>使用范围->员工类型字典下拉框");

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/getSystemParams");
        mongoMap.put("requestMethod", "POST");

        params.put("typeCode", "EmployType");
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/bcc/dict/getSystemParams", DataSourceRequestMethod.POST, params));
        return uiDataSourceDto;
    }

    /**
     * 员工状态
     */
    private UiDataSourceDto getEmployStatus(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_DICT);
        uiDataSourceDto.setSourceName("员工状态");
        uiDataSourceDto.setDescription("薪资管理->薪资方案——>使用范围->员工状态字典下拉框");

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/getSystemParams");
        mongoMap.put("requestMethod", "POST");

        params.put("typeCode", "EmployStatus");
        params.put("sysConstant", true);
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/bcc/dict/getSystemParams", DataSourceRequestMethod.POST, params));
        return uiDataSourceDto;
    }

    /**
     * 工作地
     */
    private UiDataSourceDto getWorkplace(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_DICT);
        uiDataSourceDto.setSourceName("工作地");
        uiDataSourceDto.setDescription("薪资管理->薪资方案——>使用范围->工作地字典下拉框");

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/getSystemParams");
        mongoMap.put("requestMethod", "POST");

        params.put("typeCode", "工作地");
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/bcc/dict/getSystemParams", DataSourceRequestMethod.POST, params));
        return uiDataSourceDto;
    }

    /**
     * 婚姻状况
     */
    private UiDataSourceDto getMaritalStatus(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_DICT);
        uiDataSourceDto.setSourceName("婚姻状况");
        uiDataSourceDto.setDescription("薪资管理->薪资方案——>使用范围->婚姻状况字典下拉框");

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/getSystemParams");
        mongoMap.put("requestMethod", "POST");

        params.put("typeCode", "婚姻状况");
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/bcc/dict/getSystemParams", DataSourceRequestMethod.POST, params));
        return uiDataSourceDto;
    }

    /**
     * 任职组织
     */
    private UiDataSourceDto getEmploymentOrganization(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_STANDARD_COMPONENT);
        uiDataSourceDto.setSourceName("任职组织");
        uiDataSourceDto.setDescription("薪资管理->薪资方案——>使用范围->任职组织选择框");

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/masterData/org/v1/getSysCorpOrgTreeByTid");
        mongoMap.put("requestMethod", "GET");

        params.put("code", "org");
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/masterData/org/v1/getSysCorpOrgTreeByTid", DataSourceRequestMethod.GET, params));
        return uiDataSourceDto;
    }

    /**
     * 合同公司
     */
    private UiDataSourceDto getContractCompany(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_STANDARD_COMPONENT);
        uiDataSourceDto.setSourceName("合同公司");
        uiDataSourceDto.setDescription("薪资管理->薪资方案——>使用范围->合同公司选择框");

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/payroll/empgroup/v1/getContractCompany");
        mongoMap.put("requestMethod", "GET");

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/payroll/empgroup/v1/getContractCompany", DataSourceRequestMethod.GET));
        return uiDataSourceDto;
    }

    /**
     * 社保缴纳地
     */
    private UiDataSourceDto getSocialCityTree(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_STANDARD_COMPONENT);
        uiDataSourceDto.setSourceName("社保缴纳地");
        uiDataSourceDto.setDescription("薪资管理->薪资方案——>使用范围->社保缴纳地选择框");

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/payroll/fundpolicy/v1/cityTree");
        mongoMap.put("requestMethod", "GET");

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/payroll/fundpolicy/v1/cityTree", DataSourceRequestMethod.GET));
        return uiDataSourceDto;
    }

    /**
     * 性别
     */
    private UiDataSourceDto getSex(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_DICT);
        uiDataSourceDto.setSourceName("性别");
        uiDataSourceDto.setDescription("薪资管理->薪资方案——>使用范围->性别字典下拉框");

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/getSystemParams");
        mongoMap.put("requestMethod", "POST");

        params.put("typeCode", "性别");
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/bcc/dict/getSystemParams", DataSourceRequestMethod.GET, params));
        return uiDataSourceDto;
    }

    /**
     * 选人组件
     */
    private UiDataSourceDto getSelectionComponent(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_STANDARD_COMPONENT);
        uiDataSourceDto.setSourceName("选人组件");
        uiDataSourceDto.setDescription("薪资管理->薪资方案——>使用范围->选人组件选择框");

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/masterData/org/v1/getSysCorpOrgTreeByTid");
        mongoMap.put("requestMethod", "GET");

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/masterData/org/v1/getSysCorpOrgTreeByTid", DataSourceRequestMethod.GET));
        return uiDataSourceDto;
    }

    private UiGeneralDataSourceDto bulidGeneralDataSource(String url, DataSourceRequestMethod requestMethod){
        return bulidGeneralDataSource(url, requestMethod, null);
    }

    private UiGeneralDataSourceDto bulidGeneralDataSource(String url, DataSourceRequestMethod requestMethod, Map requestParams){
        UiGeneralDataSourceDto dataSource = new UiGeneralDataSourceDto();
        dataSource.setUrl(url);
        dataSource.setRequestMethod(requestMethod);
        dataSource.setRequestParams(requestParams);

        log.info("bulidGeneralDataSource={}", FastjsonUtil.toJson(dataSource));
        return dataSource;
    }

    /**
     * 企业类型
     */
    private UiDataSourceDto getEnterpriseType(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_DICT);
        uiDataSourceDto.setSourceName("企业类型");
        uiDataSourceDto.setDescription("组织->合同公司——>企业类型->企业类型字典下拉框");

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/common/v1/dict/getEnableDictList");
        mongoMap.put("requestMethod", "GET");

        params.put("typeCode", "enterpriseType");
        params.put("belongModule", "M_ORG");
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/bcc/dict/common/v1/dict/getEnableDictList", DataSourceRequestMethod.GET, params));
        return uiDataSourceDto;
    }

    /**
     * 企业类型
     */
    private UiDataSourceDto getOrgType(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_DICT);
        uiDataSourceDto.setSourceName("组织类型");
        uiDataSourceDto.setDescription("组织->组织架构——>组织类型->组织类型字典下拉框");

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/common/v1/dict/getEnableDictList");
        mongoMap.put("requestMethod", "GET");

        params.put("typeCode", "OrgType");
        params.put("belongModule", "M_ORG");
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/bcc/dict/common/v1/dict/getEnableDictList", DataSourceRequestMethod.GET, params));
        return uiDataSourceDto;
    }

    /**
     * 组织架构
     */
    private UiDataSourceDto getSchemaType(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_DICT);
        uiDataSourceDto.setSourceName("架构类型");
        uiDataSourceDto.setDescription("组织->组织架构——>架构类型->架构类型字典下拉框");
        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/common/v1/dict/getEnableDictList");
        mongoMap.put("requestMethod", "GET");
        params.put("typeCode", "ArchitectureType");
        params.put("belongModule", "M_ORG");
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);
        log.info("mongoJson={}", mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/bcc/dict/common/v1/dict/getEnableDictList", DataSourceRequestMethod.GET, params));
        return uiDataSourceDto;
    }
}
