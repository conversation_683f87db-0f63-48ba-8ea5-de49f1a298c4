package com.caidaocloud.hrpaas.service.application.uiform.service;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.enums.DefStatus;
import com.caidaocloud.hrpaas.service.application.uiform.enums.SchemaPropertyDataTypeRelEnum;
import com.caidaocloud.hrpaas.service.application.uiform.enums.SchemaPropertyTypeEnum;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.service.application.uiform.dto.UiFormBasicDataDto;
import com.caidaocloud.hrpaas.service.application.uiform.dto.UiFormDto;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageTemplate;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.*;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-06-30
 */
@Slf4j
@Service
public class UiFormService {
    private final static String UIFORM_IDENTIFIER = "entity.hrpaas.UiForm", UIFORM_OWNER = "hrpaas";
    @Resource
    private ISessionService sessionService;
    @Resource
    private UiFormDataSourceService dataSourceService;

    /**
     * 保存表单基本信息
     * @param uiFormBasicsDto
     * @return
     */
    @Transactional
    public UiFormDto saveUiFormBasics(UiFormBasicsDto uiFormBasicsDto){
        UserInfo userInfo = sessionService.getUserInfo();
        PreCheck.preCheckArgument(userInfo == null, "用户登录过期！");
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        if(StringUtil.isBlank(uiFormBasicsDto.getBid())){
            UiFormDto uiFormDto = ObjectConverter.convert(uiFormBasicsDto, UiFormDto.class);
            uiFormDto.setTenantId(sessionService.getTenantId());
            uiFormDto.setIdentifier(UIFORM_IDENTIFIER);
            uiFormDto.setCreateBy(userId);
            uiFormDto.setCreateTime(System.currentTimeMillis());
            uiFormDto.setUpdateBy(userId);
            uiFormDto.setUpdateTime(uiFormDto.getCreateTime());
            uiFormDto.setBuiltIn(Boolean.FALSE);
            uiFormDto.doParseSchema();
            return this.saveUiForm(uiFormDto);
        }

        UiFormDto dbData = getUiFormDetail(uiFormBasicsDto.getBid());
        boolean formExist = null == dbData || StringUtil.isEmpty(dbData.getTenantId()) || !Objects.equals(dbData.getTenantId(), userInfo.getTenantId());
        PreCheck.preCheckArgument(formExist, "表单数据不存在！");
        BeanUtil.copyProperties(uiFormBasicsDto, dbData,StringUtil.isNotEmpty(uiFormBasicsDto.getSchema()) ? new String[]{"id"} : new String[]{"id", "schema"});
        dbData.setUpdateBy(userId);
        dbData.setUpdateTime(System.currentTimeMillis());
        dbData.doParseSchema();
        return this.saveUiForm(dbData);
    }

    /**
     * 保存表单配置内容
     * @param uiFormBodyDto
     * @return
     */
    @Transactional
    public UiFormDto saveUiFormBody(UiFormBodyDto uiFormBodyDto){
        UserInfo userInfo = sessionService.getUserInfo();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        String tenantId = sessionService.getTenantId();

        UiFormDto dbData = getUiFormDetail(uiFormBodyDto.getBid());
        PreCheck.preCheckArgument(null == dbData || (null != tenantId && !tenantId.equals(dbData.getTenantId())), "编辑的表单已不存在!");

        dbData.setSchema(uiFormBodyDto.getSchema());
        dbData.setUpdateBy(userId);
        dbData.setUpdateTime(System.currentTimeMillis());
        dbData.doParseSchema();

        UiFormBasicDataDto dataSimple = ObjectConverter.convert(dbData, UiFormBasicDataDto.class);
        dataSimple.setIdentifier(UIFORM_IDENTIFIER);
        enumFieldConvert(dataSimple, dbData);
        DataUpdate.identifier(UIFORM_IDENTIFIER).update(dataSimple);

        uiFormBodyDto.setFieldConfig(FastjsonUtil.toObject(dbData.getFieldConfig(), new TypeReference<List<UiFormFieldDto>>(){}));
        doUiFormDataEntity(dbData.getName(), uiFormBodyDto);

        return dbData;
    }

    /**
     * 处理UiForm的模型
     * @param uiFormDataName
     * @param uiFormBodyDto
     */
    public void doUiFormDataEntity(String uiFormDataName, UiFormBodyDto uiFormBodyDto){
        if(null == uiFormBodyDto || null == uiFormBodyDto.getFieldConfig() || uiFormBodyDto.getFieldConfig().isEmpty()){
            return;
        }

        UiFormDto uiFormDto = this.getUiFormDetail(uiFormBodyDto.getBid());
        if(null != uiFormDto && StringUtil.isNotEmpty(uiFormDto.getJoinModel())){
            // 正向关联了模型，则不用重新新增模型
            return;
        }

        String identifier = UIFORM_IDENTIFIER + "." + uiFormBodyDto.getBid();
        Option<EntityDef> option = EntityDef.load(identifier);
        if(option.isEmpty()){
            // 模型不存在，则插入
            EntityDef entityDef = new EntityDef();
            entityDef.setIdentifier(identifier);
            entityDef.setStatus(DefStatus.ENABLED);
            entityDef.setName(uiFormDataName);
            entityDef.setTimelineEnabled(false);
            entityDef.setOwner(UIFORM_OWNER);

            List<PropertyDef> customProperties = uiFormBodyDto.getFieldConfig().stream()
                    .map(this::convertUiformField).collect(Collectors.toList());

            entityDef.setCustomProperties(customProperties);
            entityDef.create(DefChannel.INTERNAL);
            return;
        }

        // 模型存在，进行修改
        EntityDef entityDef = option.get();
        entityDef.setName(uiFormDataName);
        entityDef.setStatus(DefStatus.ENABLED);

        entityDef.getCustomProperties().clear();
        entityDef.update(DefChannel.INTERNAL);

        doCustomProps(entityDef, uiFormBodyDto.getFieldConfig(), identifier);
    }

    private void doCustomProps(EntityDef entityDef, List<UiFormFieldDto> fieldConfig, String identifier){
        if(null == fieldConfig || fieldConfig.isEmpty()){
            List<PropertyDef> deleteList = entityDef.getCustomProperties();
            if(null == deleteList || deleteList.isEmpty()){
                return;
            }

            List<String> props = deleteList.stream().map(PropertyDef::getProperty).collect(Collectors.toList());
            entityDef.getCustomProperties().clear();
            EntityDef.physicalDeleteCustomProps(identifier, Sequences.sequence(props), DefChannel.INTERNAL);
            return;
        }

        List<PropertyDef> props = fieldConfig.stream().map(this::convertUiformField).collect(Collectors.toList());
        List<PropertyDef> dbCustomProperties = entityDef.getCustomProperties();
        if(null == dbCustomProperties || dbCustomProperties.isEmpty()){
            EntityDef.insertCustomProps(identifier, Sequences.sequence(props), DefChannel.INTERNAL);
            return;
        }

        List<String> oldProps = new ArrayList<>();
        List<String> newProps = new ArrayList<>();
        dbCustomProperties.forEach(prop -> oldProps.add(prop.getProperty()));
        props.forEach(prop -> newProps.add(prop.getProperty()));

        List<String> collectUpdate = newProps.stream().filter(oldProps::contains).collect(Collectors.toList());
        List<Pair<String, PropertyDef>> fieldUpdate = props.stream()
                .filter(prop -> collectUpdate.contains(prop.getProperty()))
                .map(prop -> Pair.pair(prop.getProperty(), prop)).collect(Collectors.toList());
        EntityDef.updateCustomProps(identifier, Sequences.sequence(fieldUpdate), DefChannel.INTERNAL);

        List<String> collectDelete = oldProps.stream().filter(prop -> !newProps.contains(prop)).collect(Collectors.toList());
        /*List<String> propDelete = dbCustomProperties.stream()
                .filter(prop -> collectDelete.contains(prop.getProperty()))
                .map(prop -> prop.getProperty()).collect(Collectors.toList());*/
        EntityDef.physicalDeleteCustomProps(identifier, Sequences.sequence(collectDelete), DefChannel.INTERNAL);

        List<String> collectAdd = newProps.stream().filter(prop -> !oldProps.contains(prop)).collect(Collectors.toList());
        List<PropertyDef> propAdd = props.stream().filter(prop -> collectAdd.contains(prop.getProperty())).collect(Collectors.toList());
        EntityDef.insertCustomProps(identifier, Sequences.sequence(propAdd), DefChannel.INTERNAL);
    }

    /**
     * 转换UiForm中的模型字段
     * @param fieldDto
     * @return
     */
    private PropertyDef convertUiformField(UiFormFieldDto fieldDto){
        PropertyDef propertyDef = new PropertyDef();
        propertyDef.setDataType(SchemaPropertyDataTypeRelEnum.getPropertyDataTypeBySchemaFieldDataType(fieldDto.getDataType()));
        propertyDef.setWidgetType(SchemaPropertyTypeEnum.getWidgetType(fieldDto.getType()));
        propertyDef.setProperty(fieldDto.getProp());
        propertyDef.setName(fieldDto.getLabel());
        return propertyDef;
    }

    /**
     * 保存表单
     * @param uiFormDto 表单dto
     * @return
     */
    public UiFormDto saveUiForm(UiFormDto uiFormDto){
        DataQuery dataQuery = DataQuery.identifier(UIFORM_IDENTIFIER);
        List<UiFormDto> result = null;
        UiFormBasicDataDto dataSimple = null;
        String tenantId = sessionService.getTenantId();
        uiFormDto.setTenantId(tenantId);

        if(StringUtil.isBlank(uiFormDto.getId())){
            DataFilter dataFilter = DataFilter.eq("name", uiFormDto.getName()).andEq("tenantId", tenantId);

            checkDuplicateFormName(dataQuery, dataFilter);

            dataSimple = ObjectConverter.convert(uiFormDto, UiFormBasicDataDto.class);
            dataSimple.setIdentifier(UIFORM_IDENTIFIER);
            enumFieldConvert(dataSimple, uiFormDto);
            uiFormDto.setId(DataInsert.identifier(UIFORM_IDENTIFIER).insert(dataSimple));

            //saveUiFormMenu(uiFormDto);
            return uiFormDto;
        }

        DataFilter dataFilter = DataFilter.eq("name", uiFormDto.getName())
                .andEq("tenantId", tenantId)
                .and(DataFilter.ne("id", uiFormDto.getId()));

        checkDuplicateFormName(dataQuery, dataFilter);

        dataSimple = ObjectConverter.convert(uiFormDto, UiFormBasicDataDto.class);
        dataSimple.setIdentifier(UIFORM_IDENTIFIER);
        enumFieldConvert(dataSimple, uiFormDto);
        DataUpdate.identifier(UIFORM_IDENTIFIER).update(dataSimple);
        //saveUiFormMenu(uiFormDto);
        return uiFormDto;
    }

    /**
     * 表单名称不能重复校验
     * @param dataQuery
     * @param dataFilter
     */
    private void checkDuplicateFormName(DataQuery dataQuery, DataFilter dataFilter){
        List result = dataQuery.decrypt().specifyLanguage().queryInvisible().filter(dataFilter, UiFormBasicDataDto.class).getItems();
        PreCheck.preCheckArgument(null != result && !result.isEmpty(), "表单名称不能重复!");
    }

    private void enumFieldConvert(UiFormDto uiFormDto, UiFormBasicDataDto data){
        if(null != data.getType().getValue()){
            uiFormDto.setType(PageType.valueOf(data.getType().getValue()));
        }

        if(null != data.getPageTemplate().getValue()){
            uiFormDto.setPageTemplate(PageTemplate.valueOf(data.getPageTemplate().getValue()));
        }
    }

    private void enumFieldConvert(UiFormBasicDataDto data, UiFormDto uiFormDto){
        if(null != uiFormDto.getType()){
            EnumSimple simple = new EnumSimple();
            simple.setValue(uiFormDto.getType().toString());
            data.setType(simple);
        }

        if(null != uiFormDto.getPageTemplate()){
            EnumSimple simple = new EnumSimple();
            simple.setValue(uiFormDto.getPageTemplate().toString());
            data.setPageTemplate(simple);
        }
    }

//    private void saveUiFormMenu(UiFormDto uiFormDto){
//        String tenantId = sessionService.getUserInfo().getBelongOrgId().toString();
//        // 如果开启了
//        if(BooleanUtils.isTrue(uiFormDto.getShowNav())){
//            menuService.saveUiFormMenu(uiFormDto.getId(),
//                    StringUtil.isBlank(uiFormDto.getMenuName()) ? uiFormDto.getName() : uiFormDto.getMenuName(),
//                    tenantId);
//            return;
//        }
//
//        // 如果未开启，则相当于删除、隐藏
//        menuService.deleteUiFormMenu(uiFormDto.getId(), tenantId);
//    }

    /**
     * 列表查询
     * @return
     */
    public PageResult formList(UiFormSearchDto uiFormSearchDto){
        String tenantId = sessionService.getTenantId();
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId);
        PageResult result = DataQuery.identifier(UIFORM_IDENTIFIER).decrypt().specifyLanguage().queryInvisible()
                .limit(uiFormSearchDto.getPageSize(), uiFormSearchDto.getPageNo()).filter(dataFilter, UiFormBasicDataDto.class);
        return result;
    }

    /**
     * 表单详情
     * @param formId 表单ID
     * @return
     */
    @Transactional
    public UiFormDto getUiFormDetail(String formId){
        UiFormBasicDataDto data = DataQuery.identifier(UIFORM_IDENTIFIER).decrypt().specifyLanguage().queryInvisible().oneOrNull(formId, UiFormBasicDataDto.class);
        UiFormDto formData = ObjectConverter.convert(data, UiFormDto.class);
        if(null != data.getType() && null != data.getType().getValue()){
            formData.setType(PageType.valueOf(data.getType().getValue()));
        }

        if(null != data.getPageTemplate() && null != data.getPageTemplate().getValue()){
            formData.setPageTemplate(PageTemplate.valueOf(data.getPageTemplate().getValue()));
        }
        return formData;
    }

    /**
     * 获取模型名称
     * @param identifier 模型ID
     */
    public String getEntityDefName(String identifier){
        Option<EntityDef> option = StringUtil.isEmpty(identifier) ? Option.none() : EntityDef.load(identifier);
        return option.isEmpty() ? "" : option.get().getName();
    }

    /**
     * 表单详情
     * @param functionType 表单functionType
     * @return
     */
    public UiFormDto getUiFormDetailByFunType(String functionType){
        String tenantId = sessionService.getTenantId();
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId)
                .andEq("functionType", functionType);

        PageResult result = DataQuery.identifier(UIFORM_IDENTIFIER).decrypt().specifyLanguage().queryInvisible()
                .limit(1, 0).filter(dataFilter, UiFormDto.class);

        List<UiFormDto> list = null;
        if(null == result || null == (list = result.getItems()) || list.isEmpty()){
            return null;
        }

        UiFormDto data = list.get(0);
        return data;
    }

    /**
     * 物理删除表单
     * @param formIds 表单ID
     */
    @Transactional
    public void deleteUiForm(String [] formIds){
        if(null == formIds || formIds.length <= 0){
            return;
        }

        UserInfo userInfo = sessionService.getUserInfo();
        String tenantId = userInfo.getBelongOrgId().toString();
        Long userId = userInfo.getUserId();

        for (String formId : formIds) {
            if(StringUtil.isEmpty(formId)){
                continue;
            }

            DataDelete.identifier(UIFORM_IDENTIFIER).delete(formId);
            // 删除表单后，删除对应菜单
            //menuService.deleteMenuByFormId(formId, tenantId, userId);
        }
    }

    public Map<String, UiDataSourceDto> getUiFormFieldDataSource(String formId){
        UiFormDto uiFormDto = this.getUiFormDetail(formId);
        Map<String, UiDataSourceDto> fieldDataSourceMap = new HashMap<>(30);
        if(null == uiFormDto || StringUtil.isEmpty(uiFormDto.getFieldConfig())){
            return fieldDataSourceMap;
        }

        List<UiFormFieldDto> fieldDtoList = FastjsonUtil.toArrayList(uiFormDto.getFieldConfig(), UiFormFieldDto.class);
        if(null == fieldDtoList){
            return fieldDataSourceMap;
        }

        List<String> dataSourceIds = fieldDtoList.stream().map(formField -> formField.getDataSourceId()).collect(Collectors.toList());
        List<UiDataSourceDto> configList = dataSourceService.getDataSourceList(dataSourceIds);
        Map<String, UiDataSourceDto> dataSourceDtoMap = configList.stream().collect(Collectors.toMap(UiDataSourceDto::getId, t -> t));

        fieldDtoList.forEach(formField -> {
            String fieldDataSource = formField.getDataSourceId();
            if(StringUtil.isNotEmpty(fieldDataSource)){
                fieldDataSourceMap.put(formField.getProp(), dataSourceDtoMap.get(fieldDataSource));
            }
        });
        return fieldDataSourceMap;
    }

    public void updateUiFormShowNav(String formId, Long userId, Long updateTime){
        UiFormDto uiFormDto = this.getUiFormDetail(formId);
        if(null == uiFormDto || StringUtil.isEmpty(uiFormDto.getName())){
            // 所属表单不存在，跳过更新
            return;
        }

        uiFormDto.setShowNav(false);
        uiFormDto.setUpdateBy(String.valueOf(userId));
        uiFormDto.setUpdateTime(updateTime);
        DataUpdate.identifier(UIFORM_IDENTIFIER).update(uiFormDto);
    }

}
