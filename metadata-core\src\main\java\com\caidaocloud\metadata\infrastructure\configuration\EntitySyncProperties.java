package com.caidaocloud.metadata.infrastructure.configuration;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/3/12
 */

@ConfigurationProperties(prefix = "caidaocloud.sync")
@Component
@Data
public class EntitySyncProperties {
	private Set<String> identifier = new HashSet<>();
}
