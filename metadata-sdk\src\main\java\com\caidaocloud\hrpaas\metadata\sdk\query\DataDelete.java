package com.caidaocloud.hrpaas.metadata.sdk.query;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.hrpaas.metadata.sdk.service.DataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.val;

@Data
public class DataDelete {

    private String identifier;

    private DataFilter filter;

    private boolean isMasterDataQuery = false;

    private DataDelete(){

    }

    public DataDelete setMasterDataQuery(boolean masterDataQuery) {
        isMasterDataQuery = masterDataQuery;
        return this;
    }

    protected DataDelete(String identifier){
        this.identifier = identifier;
    }

    public static DataDelete identifier(String identifier){
        DataDelete request = new DataDelete();
        request.identifier = identifier;
        return request;
    }



    public void batchDelete(DataFilter filter){
        SpringUtil.getBean(DataOperatorService.class).batchDelete(this.identifier, filter);
    }

    public void delete(String bid){
        try {
            MdQueryTL.set(isMasterDataQuery);
            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val metadata = metadataService.load(identifier);
            if(metadata.isTimelineEnabled()){
                delete(bid, DateUtil.getMidnightTimestamp());
            }else{
                delete(bid, 0);
            }
        } finally {
            MdQueryTL.remove();
        }

    }

    public void softDelete(String bid){
        try {
            MdQueryTL.set(isMasterDataQuery);
            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val metadata = metadataService.load(identifier);
            if(metadata.isTimelineEnabled()){
                softDelete(bid, DateUtil.getMidnightTimestamp());
            }else{
                softDelete(bid, 0);
            }
        } finally {
            MdQueryTL.remove();
        }
    }

    public void delete(String bid, long startTime){
        try {
            MdQueryTL.set(isMasterDataQuery);
            SpringUtil.getBean(DataOperatorService.class).delete(this.identifier, bid, startTime);
        } finally {
            MdQueryTL.remove();
        }
    }

    public void softDelete(String bid, long startTime){
        try {
            MdQueryTL.set(isMasterDataQuery);
            SpringUtil.getBean(DataOperatorService.class).softDelete(this.identifier, bid, startTime);
        } finally {
            MdQueryTL.remove();
        }
    }


}
