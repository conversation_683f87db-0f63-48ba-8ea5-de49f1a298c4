package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.metadata.infrastructure.repository.impl.EntityDefRepositoryImpl;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHolder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 *
 * <AUTHOR>
 * @date 2023/2/28
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class EntityDefRepositoryImplTests {


	@Before
	public void bfeach(){
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("33_copy2");
		userInfo.setEmpId(0L);
		userInfo.setUserId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);
		UserInfo user = new UserInfo();
		user.setTenantId("33_copy2");
		user.setStaffId(0L);
		user.setUserid(0);
		user.setEmpid(0);
		RequestHolder.setUserInfo(user);
	}


	@Test
	public void refreshFieldMapping() {
		SpringUtil.getBean(EntityDefRepositoryImpl.class).removeDictTextMapping("entity.hr.EmpWorkInfo");
	}
}