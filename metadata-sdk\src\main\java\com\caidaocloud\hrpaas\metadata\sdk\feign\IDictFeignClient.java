package com.caidaocloud.hrpaas.metadata.sdk.feign;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SysParamDictDto;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.TxFeignConfiguration;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "caidaocloud-business-config-center",
        fallback = DictFeignClientFallBack.class,
        configuration = {FeignConfiguration.class, TxFeignConfiguration.class},
        contextId = "metadataDictFeign", qualifier = "metadataDictFeign")
public interface IDictFeignClient {

    @GetMapping("/api/bcc/dict/common/v1/detail")
    Result<SysParamDictDto> one(@RequestParam("dictCode") String dictCode);

    @GetMapping("/api/bcc/dict/common/v1/info")
    Result<SysParamDictDto> getDictByTypeAndCode(@RequestParam("dictType") String dictType,
        @RequestParam("dictCode") String dictCode);

    @GetMapping("/api/bcc/dict/common/v1/dict/detail")
    Result<SysParamDictDto> getDict(@RequestParam("id") Long id);

    @GetMapping("/api/bcc/dict/common/v1/dict/getEnableDictList")
    Result<List<DictSimple>> getEnableDictList(@RequestParam("typeCode") String typeCode,
            @RequestParam(name = "belongModule", required = false) String belongModule);

}
