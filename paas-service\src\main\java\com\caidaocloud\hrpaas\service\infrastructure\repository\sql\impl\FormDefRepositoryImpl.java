package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDefStatus;
import com.caidaocloud.hrpaas.service.domain.form.repository.IFormDefRepository;
import org.springframework.stereotype.Repository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.FormDefPo;
import com.caidaocloud.metadata.infrastructure.repository.po.PropertyEventPo;
import com.caidaocloud.metadata.infrastructure.repository.impl.PropertyEventMapper;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.*;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

@Repository
public class FormDefRepositoryImpl implements IFormDefRepository {

    @Autowired
    private FormDefMapper formDefMapper;

    @Autowired
    private PropertyEventMapper propertyEventMapper;

    @Override
    public void publish(FormDef entity) {
        entity.setStatus(FormDefStatus.PUBLISHED);
        val copy = FastjsonUtil.convertObject(entity, FormDef.class);
        copy.getProperties().forEach(it->it.setOnEvent(null));
        formDefMapper.updateById(FormDefPo.fromEntity(FormDefPo.class, copy));
    }

    @Override
    public void disable(FormDef entity) {
        entity.setStatus(FormDefStatus.DISABLED);
        entity.getProperties().forEach(it->it.setOnEvent(null));
        formDefMapper.updateById(FormDefPo.fromEntity(FormDefPo.class, entity));
    }

    @Override
    public PageResult<FormDef> page(int pageNo, int pageSize) {
        val total = formDefMapper.selectCount(new QueryWrapper<>());
        val page = new Page<FormDefPo>(pageNo, pageSize);
        page.setSearchCount(false);
        val result = formDefMapper.selectPage(page, new QueryWrapper<FormDefPo>()
                .orderByDesc(Lists.list("standard", "create_time")));
        result.setTotal(total);
        return new PageResult(result.getRecords().stream().map(it->it.toEntity(Maps.map())).collect(Collectors.toList()), pageNo, pageSize, (int)result.getTotal());
    }

    @Override
    public PageResult<FormDef> pageByKeyWorks(int pageNo, int pageSize, String keyWords) {
        QueryWrapper<FormDefPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("name", keyWords);
        val total = formDefMapper.selectCount(queryWrapper);
        val page = new Page<FormDefPo>(pageNo, pageSize);
        page.setSearchCount(false);

        queryWrapper.orderByDesc(Lists.list("standard", "create_time"));
        val result = formDefMapper.selectPage(page, queryWrapper);
        result.setTotal(total);
        return new PageResult(result.getRecords().stream().map(it->it.toEntity(Maps.map())).collect(Collectors.toList()), pageNo, pageSize, (int)result.getTotal());

    }

    @Override
    public List<FormDef> list() {
        val list = formDefMapper.selectList(new QueryWrapper<FormDefPo>().orderByDesc(Lists.list("standard", "create_time")));
        return list.stream().map(it->it.toEntity(Maps.map())).collect(Collectors.toList());
    }

    @Override
    public String insert(FormDef entity) {
        val identifier = "entity.form." + (StringUtils.isEmpty(entity.getCodeAsId())?entity.getId():entity.getCodeAsId());
        propertyEventMapper.delete(new LambdaQueryWrapper<PropertyEventPo>().eq(PropertyEventPo::getIdentifier, identifier));
        for(FormPropDef propertyDef : entity.getProperties()){
            val onEvent = propertyDef.getOnEvent();
            propertyDef.setOnEvent(null);
            val property = propertyDef.getProperty();
            if(StringUtils.isNotEmpty(onEvent)){
                propertyEventMapper.insert(new PropertyEventPo(identifier, property, onEvent));
            }
        }
        val po = FormDefPo.fromEntity(FormDefPo.class, entity);
        formDefMapper.insert(po);
        return String.valueOf(po.getId());
    }

    @Override
    public void update(FormDef entity) {
        val identifier = "entity.form." + (StringUtils.isEmpty(entity.getCodeAsId())?entity.getId():entity.getCodeAsId());
        propertyEventMapper.delete(new LambdaQueryWrapper<PropertyEventPo>().eq(PropertyEventPo::getIdentifier, identifier));
        for(FormPropDef propertyDef : entity.getProperties()){
            val onEvent = propertyDef.getOnEvent();
            propertyDef.setOnEvent(null);
            val property = propertyDef.getProperty();
            if(StringUtils.isNotEmpty(onEvent)){
                propertyEventMapper.insert(new PropertyEventPo(identifier, property, onEvent));
            }
        }
        formDefMapper.updateById(FormDefPo.fromEntity(FormDefPo.class, entity));
    }

    public Option<FormDef> load(String id){
        return Option.option(formDefMapper.selectById(Long.valueOf(id))).map(def->{
            val events = Maps.map(propertyEventMapper.selectList(new LambdaQueryWrapper<PropertyEventPo>()
                    .eq(PropertyEventPo::getIdentifier, "entity.form." + (StringUtils.isEmpty(def.getCodeAsId())?def.getId():def.getCodeAsId())))
                    .stream().map(it-> Pair.pair(it.getProperty(), it.getEvent())).collect(Collectors.toList()));
            return def.toEntity(events);
        });
    }

    public void remove(String id){
        formDefMapper.deleteById(Long.valueOf(id));
    }

    @Override
    public Option<FormDef> loadByName(String name) {
        return Option.option(formDefMapper.selectOne(new LambdaQueryWrapper<FormDefPo>().eq(FormDefPo::getName, name))).map(def->{
            val events = Maps.map(propertyEventMapper.selectList(new LambdaQueryWrapper<PropertyEventPo>()
                    .eq(PropertyEventPo::getIdentifier, "entity.form." + (StringUtils.isEmpty(def.getCodeAsId())?def.getId():def.getCodeAsId())))
                    .stream().map(it-> Pair.pair(it.getProperty(), it.getEvent())).collect(Collectors.toList()));
            return def.toEntity(events);
        });
    }

    @Override
    public Option<FormDef> loadByCode(String code) {
        return Option.option(formDefMapper.selectOne(new LambdaQueryWrapper<FormDefPo>().eq(FormDefPo::getCode, code))).map(def->{
            val events = Maps.map(propertyEventMapper.selectList(new LambdaQueryWrapper<PropertyEventPo>()
                            .eq(PropertyEventPo::getIdentifier, "entity.form." + (StringUtils.isEmpty(def.getCodeAsId())?def.getId():def.getCodeAsId())))
                    .stream().map(it-> Pair.pair(it.getProperty(), it.getEvent())).collect(Collectors.toList()));
            return def.toEntity(events);
        });
    }
}
