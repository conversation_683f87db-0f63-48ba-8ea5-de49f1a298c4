package com.caidaocloud.hrpaas.paas.match;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.masterdata.enums.IdentifyRelationCodeEnum;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.val;

import java.util.List;
import java.util.Map;

/**
 * 匹配工具
 *
 * <AUTHOR>
 * @date 2022/3/25
 **/
@Data
public class ConditionTree {

    private String id;

    private ConditionNodeTypeEnum type;

    private ConditionNodeRelationEnum relation;

    private List<ConditionNode> children;

    private Map<String, String> identifierEmpIdKey;

    public ConditionTree() {
        identifierEmpIdKey = Maps.map();
    }

    public ConditionTree(Map<String, String> identifierEmpIdKey) {
        this.identifierEmpIdKey = identifierEmpIdKey;
    }

    public boolean match(String empId, List<ConditionNameMapping> mappings){
        return match(empId, mappings, false);
    }

    public boolean match(String empId, List<ConditionNameMapping> mappings,boolean md){
        Map<String, Map<String, String>> nameMappings = Maps.map();
        Map<String, String> empInfo = Maps.map();
        for(ConditionNameMapping mapping : mappings){
            val identifier = mapping.getMappingIdentifier();
            val property = mapping.getMappingProperty();
            val name = mapping.getName();
            val nameMapping = nameMappings.computeIfAbsent(identifier, (it)->Maps.map());
            nameMapping.put(property, name);
        }
        nameMappings.forEach((identifier,nameMapping)->{
            DataQuery query = DataQuery.identifier(identifier);
            if (md && IdentifyRelationCodeEnum.getEnum(identifier).isPresent()) {
                query.setMasterDataQuery(true);
            }
            val items = query.exp().limit(1, 1)
                    .filterProperties(DataFilter.eq(identifierEmpIdKey.getOrDefault(identifier,
                            "empId"), empId),
                            Lists.list(nameMapping.keySet()), System.currentTimeMillis()).getItems();
            if(!items.isEmpty()){
                items.get(0).forEach((property, value)
                        -> empInfo.put(nameMapping.get(property),value));
            }
        });
        return match(empInfo);
    }

    public boolean match(Map<String, String> empInfo) {
        if(ConditionNodeRelationEnum.and.equals(relation)){
            return Sequences.sequence(children).forAll(it->it.match(empInfo));
        }else{
            return Sequences.sequence(children).exists(it->it.match(empInfo));
        }
    }
}
