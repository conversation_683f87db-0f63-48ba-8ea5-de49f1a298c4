package com.caidaocloud.hrpaas.metadata.sdk.enums;

import com.caidaocloud.hrpaas.metadata.sdk.service.*;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.Getter;
import lombok.val;

import java.util.List;

@Getter
public enum AuthRoleScopeRestriction {

    SELECTED_COMPANY(CommonScopeRestrictionHandler.class),//公司ID
    MY_ORG_AND_BELONGINGS(MyOrgAndBelongingsScopeRestrictionHandler.class),//组织ID
    MY_ORG(MyOrgScopeRestrictionHandler.class),//组织ID
    SELECTED_ORG(CommonScopeRestrictionHandler.class),//组织ID
    SELECTED_ORG_AND_BELONGINGS(SelectedOrgAndBelongingsScopeRestrictionHandler.class),//组织ID
    MY_CONCURRENT_ORG(MyConcurrentOrgScopeRestrictionHandler.class),//组织ID
    MY_CONCURRENT_ORG_AND_BELONGINGS(MyConcurrentOrgAndBelongingsScopeRestrictionHandler.class),//组织ID
    SELECTED_HRBP(SelectedHrbpScopeRestrictionHandler.class),//组织ID
    SELECTED_LEADER(SelectedLeaderScopeRestrictionHandler.class),//组织ID
    DIRECT_SUBORDINATE(DirectSubordinateScopeRestrictionHandler.class),//员工ID
    ALL_SUBORDINATE(AllSubordinateScopeRestrictionHandler.class),//员工ID
    SELECTED_HRBP_WITH_CONCURRENT(SelectedHrbpWithConcurrentScopeRestrictionHandler.class),//员工ID
    SELECTED_LEADER_WITH_CONCURRENT(SelectedLeaderWithConcurrentScopeRestrictionHandler.class),//员工ID
    DIRECT_SUBORDINATE_WITH_CONCURRENT(DirectSubordinateWithConcurrentScopeRestrictionHandler.class),//员工ID
    //ALL_SUBORDINATE_WITH_CONCURRENT(null),
    CREATED_BY_MYSELF(MyselfScopeRestrictionHandler.class),//用户ID
    SELECTED_EMP(CommonScopeRestrictionHandler.class),//员工ID
    SELECTED_WORKPLACE(CommonScopeRestrictionHandler.class),//工作地ID
    SELECTED_EMP_TYPE(CommonScopeRestrictionHandler.class),//字典ID
    SELECTED_EMP_STATUS(CommonScopeRestrictionHandler.class),//员工状态
    SPECIFIED_ORG_CODE_PREFIX(SpecifiedOrgCodePrefixScopeRestrictionHandler.class),
    SELECTED_CONTRACT_TYPE(CommonScopeRestrictionHandler.class),
    //附件管理数据范围
    SELECT_ARCHIVE_FILE(DefaultScopeRestrictionHandler.class),
    SELECTED_PAYROLL_PLAN(CommonScopeRestrictionHandler.class),
    SELECTED_BONUS_PLAN(CommonScopeRestrictionHandler.class),
    NO_AUTH(NoAuthScopeRestrictionHandler.class),
    ALL(AllAuthScopeRestrictionHandler.class),

    //查看指定岗位
    SELECTED_POST(SelectedPostScopeRestrictionHandler.class),
    //查看指定基准岗位
    SELECTED_BENCH_POST(SelectedBenchPostScopeRestrictionHandler.class),
    //查看指定成本中心
    SELECTED_COST_CENTER(SelectedCostCenterScopeRestrictionHandler.class),
    MYSELF(MyselfEmpScopeRestrictionHandler.class),// 当前员工,
    EMP_WORK_INFO(SelectedEmpWorkInfoScopeRestrictionHandler.class),//查看指定员工任职信息字段,
    PAY_SOB(SelectedPaySobInfoScopeRestrictionHandler.class)//查看指定账套
    ;

    private Class<? extends ScopeRestrictionHandler> handler;

    AuthRoleScopeRestriction(Class<? extends ScopeRestrictionHandler> handler) {
        this.handler = handler;
    }

    public List<String> toValues(String simpleValues) {
        val userId = SecurityUserUtil.getSecurityUserInfo().getUserId();
        if (null == userId) {
            return Lists.list();
        }
        return SpringUtil.getBean(handler).toValues(userId.toString(), simpleValues, SecurityUserUtil.getSecurityUserInfo());
    }
}
