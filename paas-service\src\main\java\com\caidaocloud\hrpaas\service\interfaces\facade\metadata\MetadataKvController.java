package com.caidaocloud.hrpaas.service.interfaces.facade.metadata;

import com.caidaocloud.hrpaas.paas.common.dto.KvDto;
import com.caidaocloud.hrpaas.service.application.metadata.service.MetadataKvService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/hrpaas/v1/kv")
@Api(value = "KV配置类数据操作", tags = "KV配置类数据操作接口")
public class MetadataKvController {
    @Resource
    private MetadataKvService metadataKvService;

    @GetMapping("/detail")
    @ApiOperation("查询KV数据")
    public Result<String> getDetail(@RequestParam(value = "key") String key){
        return Result.ok(metadataKvService.getDetail(key));
    }

    @PostMapping("/save")
    @ApiOperation("保存KV数据")
    public Result<KvDto> save(@RequestBody KvDto kv){
        metadataKvService.save(kv);
        return Result.ok(kv);
    }

    @PostMapping("/update")
    @ApiOperation("刷新缓存")
    public Result<KvDto> update(@RequestBody KvDto kv){
        metadataKvService.update(kv);
        return Result.ok(kv);
    }
}
