[{"key": "page.detail.employee", "type": "EmpInfo", "label": "员工信息详情", "childList": [{"label": "任职信息", "key": "entity.hr.EmpWorkInfo", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "TreeSelect_84E8SNSCHnT9rzrjAXqf4d", "type": "TreeSelect", "label": "组织", "props": {"label": "所属组织", "prop": "organize", "placeholder": "请输入所属组织", "dataType": "string", "required": true, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "Input_qBR8MvAcuFHyjv7GcQTsXp", "type": "Input", "label": "单行文本", "props": {"label": "员工工号", "prop": "workno", "placeholder": "请输入员工工号", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "TreeSelect_cUq4qS3bm5HSevaCENS8Pr", "type": "TreeSelect", "label": "岗位", "props": {"label": "岗位", "prop": "post", "placeholder": "请输入岗位", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_dwrKXSWNo2AcjdJYFdDgSv", "type": "Input", "label": "单行文本", "props": {"label": "关联的职务", "prop": "job", "placeholder": "请输入关联的职务", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EmployeePicker_gZbhq3F4SJbzCnF75mA5vn", "type": "EmployeePicker", "label": "选人组件", "props": {"label": "直接上级", "prop": "leadEmpId", "placeholder": "请输入直接上级", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_rHEwMtxAVognVGh2nY2yn5", "type": "DatePicker", "label": "日期", "props": {"label": "入职日期", "prop": "hireDate", "placeholder": "请选择入职日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "Select_gWaSeExMpYJReV6QyxfFAw", "type": "Select", "label": "下拉单选", "props": {"label": "试用期期限", "prop": "probation", "placeholder": "请输入试用期期限", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "无试用期", "value": "0"}, {"label": "一个月", "value": "1"}, {"label": "两个月", "value": "2"}, {"label": "三个月", "value": "3"}, {"label": "六个月", "value": "6"}], "value": "0", "rules": [], "show": true}}, {"key": "DatePicker_3XcrWEXnHcpPPm9XFEFWhk", "type": "DatePicker", "label": "日期", "props": {"label": "转正日期", "prop": "confirmationDate", "placeholder": "请选择转正日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_4SWorCpTQbGmGgM57aS39h", "type": "Select", "label": "下拉单选", "props": {"label": "转正状态", "prop": "confirmation<PERSON>tatus", "placeholder": "请输入转正状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "已转正", "value": "0"}, {"label": "试用期", "value": "1"}], "value": "0", "rules": [], "show": true}}, {"key": "Select_kkbmLWmJpigjwxs8BAeznN", "type": "Select", "label": "下拉单选", "props": {"label": "员工状态", "prop": "empStatus", "placeholder": "请输入员工状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "在职", "value": "0"}, {"label": "离职", "value": "1"}, {"label": "试用期", "value": "2"}], "value": "0", "rules": [], "show": true}}, {"key": "EnumSelect_4hiZhD5ZyhTgXToBd9Fxxc", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "用工类型", "prop": "empType", "placeholder": "请输入用工类型", "dataType": "Dict", "required": true, "width": "50%", "standard": true, "options": {"dataSourceId": "EmployType"}, "rules": [], "show": true}}, {"key": "DatePicker_voPQJ4YgjMrcC9QuQBMejv", "type": "DatePicker", "label": "日期", "props": {"label": "离职日期", "prop": "leaveDate", "placeholder": "请选择离职日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_7HUoW3koBNRDu1nhKdrPfs", "type": "Select", "label": "下拉单选", "props": {"label": "工时制", "prop": "workHour", "placeholder": "请输入工时制", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "标准工时制", "value": "0"}, {"label": "综合工时制", "value": "1"}, {"label": "不定时工时制", "value": "2"}], "value": "0", "rules": [], "show": true}}, {"key": "InputNumber_h6rmxDmEw3QoY6LAPDgRW5", "type": "InputNumber", "label": "数值", "props": {"label": "司龄", "prop": "divisionAge", "placeholder": "请输入司龄", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_kh5VeVAgn3NXuDTXrJ3wj5", "type": "InputNumber", "label": "数值", "props": {"label": "司龄调整", "prop": "divisionAgeAdjust", "placeholder": "请输入司龄调整", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_gpgw3f5HSygt6b9nGVj12c", "type": "Input", "label": "单行文本", "props": {"label": "员工公司邮箱", "prop": "companyEmail", "placeholder": "请输入员工公司邮箱", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "undefined_9MP1asZqW8dpJXBE4Babtc", "props": {"label": "工作地", "prop": "workplace", "placeholder": "请输入工作地", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_iUWL4H2QoPBYZipt2UTZYE", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "入司途径", "prop": "joinCompanyWay", "placeholder": "请输入入司途径", "dataType": "Dict", "required": true, "width": "50%", "standard": true, "options": {"dataSourceId": "EntryRoute"}, "rules": [], "show": true}}, {"key": "undefined_wJv1cscG75PGRbGiSJf2pe", "props": {"label": "合同公司", "prop": "company", "placeholder": "请输入合同公司", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_8NsqEE6cAhp5kqvk3r1Ymt", "type": "DatePicker", "label": "日期", "props": {"label": "预计毕业日期", "prop": "expectGraduateDate", "placeholder": "请选择预计毕业日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_qtzhZ9RVw49fjsFMtBYG7M", "type": "Input", "label": "单行文本", "props": {"label": "成本中心", "prop": "costCenters", "placeholder": "请输入成本中心", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "个人信息", "key": "entity.hr.EmpPrivateInfo", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "Input_15c36Fr7Po6nLC1dhoQvLd", "type": "Input", "label": "单行文本", "props": {"label": "员工姓名", "prop": "name", "placeholder": "请输入员工姓名", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "Input_jnobnhVW45Cdbqu2KVL4Jj", "type": "Input", "label": "单行文本", "props": {"label": "员工英文名", "prop": "enName", "placeholder": "请输入员工英文名", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_3ft8XDRAcFQfmpTvuqKfqe", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "员工性别", "prop": "sex", "placeholder": "请输入员工性别", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Gender"}, "rules": [], "show": true}}, {"key": "EnumSelect_mbTiaFDu7Vk8yRV6TfGdwi", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "国籍", "prop": "nationality", "placeholder": "请输入国籍", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Nationality"}, "rules": [], "show": true}}, {"key": "EnumSelect_tewv52StT8e8HmeE9yyjKp", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "民族", "prop": "nation", "placeholder": "请输入民族", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Nation"}, "rules": [], "show": true}}, {"key": "Cascader_uVBL7VRF8SUuux8vwbMRXF", "type": "<PERSON>r", "label": "省、市级联选择", "props": {"label": "籍贯", "prop": "nativePlace", "placeholder": "请输入籍贯", "dataType": "PROVINCE_CITY", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "1325499191547905"}, "rules": [], "show": true}}, {"key": "EnumSelect_k1YWJJ8afV9QwyXZpuY2hg", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "户口类型", "prop": "familyType", "placeholder": "请输入户口类型", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "FamilyType"}, "rules": [], "show": true}}, {"key": "Input_pcqfPXwAR9puYYqanBGuy5", "type": "Input", "label": "单行文本", "props": {"label": "户籍地址", "prop": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "请输入户籍地址", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_on134e1NdmPHxKsj1ynwc7", "type": "DatePicker", "label": "日期", "props": {"label": "出生日期", "prop": "birthDate", "placeholder": "请选择出生日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_wdrwT3PT32VVh5NViKTUZF", "type": "InputNumber", "label": "数值", "props": {"label": "年龄", "prop": "divisionAge", "placeholder": "请输入年龄", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_itqZAEoNRwU6Q2urUucvj7", "type": "Select", "label": "下拉单选", "props": {"label": "婚姻状态", "prop": "maritalStatus", "placeholder": "请输入婚姻状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "未婚", "value": "0"}, {"label": "已婚", "value": "1"}, {"label": "丧偶", "value": "2"}], "rules": [], "show": true}}, {"key": "Select_vmEXZSR3PnGg6RMKS8bVyx", "type": "Select", "label": "下拉单选", "props": {"label": "生育状态", "prop": "fertilityStatus", "placeholder": "请输入生育状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "未育", "value": "0"}, {"label": "一胎", "value": "1"}, {"label": "二胎", "value": "2"}, {"label": "多胎", "value": "3"}], "rules": [], "show": true}}, {"key": "EnumSelect_42Lu9mrW7wshJdPqa1aML1", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "政治面貌", "prop": "politicalOutlook", "placeholder": "请输入政治面貌", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "PoliticalP<PERSON>y"}, "rules": [], "show": true}}, {"key": "Switch_6wKAokuiy4HTBCA2wXYyQ3", "type": "Switch", "label": "开关", "props": {"label": "是否残疾", "prop": "disability", "placeholder": "请输入是否残疾", "dataType": "boolean", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_kVEpGbb76UviEVu39gqF4H", "type": "Input", "label": "单行文本", "props": {"label": "监护人姓名", "prop": "guardianName", "placeholder": "请输入监护人姓名", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_8a9ztm7z1CxtkXvvpD1FAU", "type": "Input", "label": "单行文本", "props": {"label": "监护人手机号", "prop": "guardianPhone", "placeholder": "请输入监护人手机号", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_7hWtwJ2bcP6WDq6N8DZctw", "type": "Input", "label": "单行文本", "props": {"label": "监护人邮箱", "prop": "guardianEmail", "placeholder": "请输入监护人邮箱", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_6mef35WHJnFNjNBUs2vNMY", "type": "Input", "label": "单行文本", "props": {"label": "手机号", "prop": "phone", "placeholder": "请输入手机号", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_jwiHVbBEDvkrH1DL6idF9X", "type": "Input", "label": "单行文本", "props": {"label": "员工个人邮箱", "prop": "email", "placeholder": "请输入员工个人邮箱", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_dYWwhetFPB2ePZAQSaowWZ", "type": "Input", "label": "单行文本", "props": {"label": "通讯地址", "prop": "postalAddress", "placeholder": "请输入通讯地址", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_rJyMfQRQvWFPdhEh4LymF5", "type": "Select", "label": "下拉单选", "props": {"label": "证件类型", "prop": "cardType", "placeholder": "请输入证件类型", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "身份证", "value": "0"}, {"label": "护照", "value": "1"}, {"label": "台胞证", "value": "2"}, {"label": "港澳永久身份证", "value": "3"}, {"label": "港澳非永久身份证", "value": "4"}, {"label": "驾驶证", "value": "5"}, {"label": "台湾身份证", "value": "6"}, {"label": "其他", "value": "7"}], "value": "0", "rules": [], "show": true}}, {"key": "Input_rMsrRYJihu9QvKm5YyEAM9", "type": "Input", "label": "单行文本", "props": {"label": "证件号", "prop": "cardNo", "placeholder": "请输入证件号", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_2BedAhtJYjxHckPhU5fX5t", "type": "DatePicker", "label": "日期", "props": {"label": "证件有效日期", "prop": "cardEffectiveDate", "placeholder": "请选择证件有效日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "非行政组织", "key": "entity.hr.EmpOtherOrg", "type": "EmpTab", "props": {"show": true, "disabled": true}, "childList": null}, {"label": "合同信息", "key": "entity.hr.Contract", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "Input_5QJFBrSR5LaAfcmFAypGqJ", "type": "Input", "label": "单行文本", "props": {"label": "合同编号", "prop": "contractNo", "placeholder": "请输入合同编号", "dataType": "string", "required": true, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_74gYzUkBXkiXceLaGbPqkF", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "合同类型", "prop": "contractSettingType", "placeholder": "请输入合同类型", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "ContractType"}, "rules": [], "show": true}}, {"key": "undefined_7NkQARmYg49XiXs1EzyB8Q", "props": {"label": "合同公司", "prop": "company", "placeholder": "请输入合同公司", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_5fPoTNRjQBXT12ansFcTKv", "type": "DatePicker", "label": "日期", "props": {"label": "合同开始日期", "prop": "startDate", "placeholder": "请选择合同开始日期", "dataType": "Timestamp", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_mrMMJfdHYGwzTdzAbuYr16", "type": "DatePicker", "label": "日期", "props": {"label": "合同结束日期", "prop": "endDate", "placeholder": "请选择合同结束日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_2GVE2DqBUyeJ3TvRCnBp9S", "type": "InputNumber", "label": "数值", "props": {"label": "合同期（月）", "prop": "contractPeriod", "placeholder": "请输入合同期（月）", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_qtg7AwinSuFKCudp2iFDK3", "type": "Select", "label": "下拉单选", "props": {"label": "签订类型", "prop": "signType", "placeholder": "请输入签订类型", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "新签", "value": "2"}, {"label": "续签", "value": "0"}, {"label": "改签", "value": "1"}], "value": "0", "rules": [], "show": true}}, {"key": "Select_2WEL4BFMy51ij6NYHgTfn1", "type": "Select", "label": "下拉单选", "props": {"label": "状态", "prop": "contractStatus", "placeholder": "请输入状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "未生效", "value": "0"}, {"label": "生效中", "value": "1"}, {"label": "已失效", "value": "2"}, {"label": "已解除", "value": "3"}, {"label": "已终止", "value": "4"}], "value": "0", "rules": [], "show": true}}, {"key": "Select_43jJ9hoT3wY53rE34inUjU", "type": "Select", "label": "下拉单选", "props": {"label": "合同期限类型", "prop": "periodType", "placeholder": "请输入合同期限类型", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "固定期限", "value": "0"}, {"label": "无固定期限", "value": "1"}], "value": "0", "rules": [], "show": true}}, {"key": "InputNumber_vTdBQ5shCM8TvGoogdpb1S", "type": "InputNumber", "label": "数值", "props": {"label": "合同签订次数", "prop": "signTime", "placeholder": "请输入合同签订次数", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_4upbmBA4v8Ux9ST8hBvGH2", "type": "InputNumber", "label": "数值", "props": {"label": "试用期（月）", "prop": "probation<PERSON><PERSON>od", "placeholder": "请输入试用期（月）", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "工作经历", "key": "entity.hr.EmpWorkOverview", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "DatePicker_wNunFuqqiJeYw11BrNT6Xg", "type": "DatePicker", "label": "日期", "props": {"label": "首次工作日期", "prop": "firstWorkDate", "placeholder": "请选择首次工作日期", "dataType": "datetime", "required": true, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "Input_7eU9nSqBHAVqyaYpm2YxmN", "type": "Input", "label": "单行文本", "props": {"label": "工龄", "prop": "workAge", "placeholder": "请输入工龄", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_6e6NptL8mSNG2fxFR6qU2K", "type": "InputNumber", "label": "数值", "props": {"label": "工龄调整", "prop": "workAgeAdjust", "placeholder": "请输入工龄调整", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "教育经历", "key": "entity.hr.EmpEduInfo", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "EnumSelect_7YSHCjnomZkNHWkbu87Hs5", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "学位", "prop": "degree", "placeholder": "请输入学位", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "EduDegree"}, "rules": [], "show": true}}, {"key": "EnumSelect_6sE82ghD1USrXju2k1LvyN", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "最高学历", "prop": "background", "placeholder": "请输入最高学历", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "EduExp"}, "rules": [], "show": true}}, {"key": "Input_wAE5o5MhfKnU9s5nUXbiNC", "type": "Input", "label": "单行文本", "props": {"label": "毕业学校", "prop": "school", "placeholder": "请输入毕业学校", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_hMddsfQLBxuPVqMDaxce8V", "type": "Input", "label": "单行文本", "props": {"label": "专业", "prop": "major", "placeholder": "请输入专业", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "家庭信息", "key": "entity.hr.FamilyInfo", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "Input_jMTqNdCfHVZ1iJiN6qcXkz", "type": "Input", "label": "单行文本", "props": {"label": "家庭地址", "prop": "family<PERSON><PERSON><PERSON>", "placeholder": "请输入家庭地址", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "奖惩信息", "key": "entity.hr.EmpReward", "type": "EmpTab", "props": {"show": true, "disabled": true}, "childList": []}, {"label": "附件档案", "key": "entity.hr.EmpFileAttachment", "type": "EmpTab", "props": {"show": true, "disabled": true}, "childList": []}, {"label": "薪资信息", "key": "entity.hr.EmpSalaryChange", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "Select_w3DRVPjdaaXoYXwejSMfso", "type": "Select", "label": "下拉单选", "props": {"label": "薪资类型", "prop": "salaryType", "placeholder": "请输入薪资类型", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "时薪", "value": "0"}, {"label": "月薪", "value": "1"}, {"label": "年薪", "value": "2"}], "value": "0", "rules": [], "show": true}}, {"key": "Input_wyuAZmN33GB697WDpmc2ii", "type": "Input", "label": "单行文本", "props": {"label": "薪资", "prop": "salary", "placeholder": "请输入薪资", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_w7jA55reZi6vaS4GtuqsNi", "type": "DatePicker", "label": "日期", "props": {"label": "生效日期", "prop": "effectiveDate", "placeholder": "请选择生效日期", "dataType": "datetime", "required": true, "width": "50%", "standard": true, "rules": [], "show": true}}]}], "script": "/**\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系才到平台的技术支持获得服务（可能收费）。\n* 我们可以用 JS 面板来开发一些定制度高功能。\n* 你可以点击面板上方的 「使用帮助」了解。\n*/\n\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\nexport function didMount() {\n\tconsole.log(`「页面 JS」：当前页面地址 ${location.href}`);\n // 更多相关 API 请参考：https://aliwork.com/developer/API\n}"}, {"childList": [{"childList": [{"key": "Input_jvbFZxM2cD76WNJuadRpfZ", "type": "Input", "label": "单行文本", "props": {"label": "员工工号", "prop": "workno", "placeholder": "请输入员工工号", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true"}], "show": true}}, {"key": "Input_9e9L1MnxP6WaLcPGXPQDAu", "type": "Input", "label": "单行文本", "props": {"label": "员工英文名", "prop": "enName", "placeholder": "请输入员工英文名", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "false"}], "show": true}}, {"key": "DatePicker_7CubNP3eEQ5RQ9S99oYkH4", "type": "DatePicker", "label": "日期", "props": {"label": "入职日期", "prop": "hireDate", "placeholder": "请选择入职日期", "dataType": "Timestamp", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "TreeSelect_wnH4iQ9aHeUCZCcRpKsNmx", "type": "TreeSelect", "label": "组织", "props": {"label": "所属组织", "prop": "organize", "placeholder": "请输入所属组织", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "TreeSelect_rzDGThZdoYoVU7WuxnWudU", "type": "TreeSelect", "label": "岗位", "props": {"label": "岗位", "prop": "post", "placeholder": "请输入岗位", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_eiWVTGXk9xsxjqpMpm4MvV", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "用工类型", "prop": "empType", "placeholder": "请输入用工类型", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "EmployType"}, "rules": [], "show": true}}, {"key": "undefined_jp9EDv9fsMzcKgBZZZqV3F", "props": {"label": "合同公司", "prop": "company", "placeholder": "请输入合同公司", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_t9c7MvFx3f1PqsGuZnAzfd", "type": "DatePicker", "label": "日期", "props": {"label": "预计毕业日期", "prop": "expectGraduateDate", "placeholder": "请选择预计毕业日期", "dataType": "Timestamp", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_qnmfpLGC2cT7JMYBgMVDSD", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "合同类型", "prop": "contractType", "placeholder": "请输入合同类型", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "ContractType"}, "rules": [], "show": true}}, {"key": "Input_sVNg3j6znzBR3QmZKmL7mf", "type": "Input", "label": "单行文本", "props": {"label": "岗位名称", "prop": "postTxt", "placeholder": "请输入岗位名称", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_mdhc9a2217ARz1wWKgaAzc", "type": "DatePicker", "label": "日期", "props": {"label": "转正日期", "prop": "confirmationDate", "placeholder": "请选择转正日期", "dataType": "Timestamp", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_wE5kYQptTzL7GgES4eokXj", "type": "Input", "label": "单行文本", "props": {"label": "员工公司邮箱", "prop": "companyEmail", "placeholder": "请输入员工公司邮箱", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_txMEgUSZamE18TLcFnqz88", "type": "Input", "label": "单行文本", "props": {"label": "工作地名称", "prop": "workplaceTxt", "placeholder": "请输入工作地名称", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_pnJQZ3vu3kmPuXMgTdczhD", "type": "Select", "label": "下拉单选", "props": {"label": "员工状态", "prop": "empStatus", "placeholder": "请输入员工状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "在职", "value": "0"}, {"label": "离职", "value": "1"}, {"label": "试用期", "value": "2"}], "value": "0", "rules": [], "show": true}}, {"key": "EmployeePicker_jd7ShyTRpvCVGW9wwFxsDA", "type": "EmployeePicker", "label": "选人组件", "props": {"label": "直接上级", "prop": "leadEmpId", "placeholder": "请输入直接上级", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_vizHWK8xJWWA8U3iK7RbT2", "type": "Input", "label": "单行文本", "props": {"label": "成本中心", "prop": "costCenters", "placeholder": "请输入成本中心", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}], "label": "候选人任职信息", "type": "EmpTab", "key": "entity.onboarding.EmpWorkInfo", "props": {"show": true, "disabled": false}}, {"childList": [{"key": "Input_asTVJVXpPFoodGpcg5nVhy", "type": "Input", "label": "单行文本", "props": {"label": "员工姓名", "prop": "name", "placeholder": "请输入员工姓名", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true"}], "show": true}}, {"key": "Input_hNj7knGuucWpScAtM7Tjq8", "type": "Input", "label": "单行文本", "props": {"label": "证件号", "prop": "cardNo", "placeholder": "请输入证件号", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true"}], "show": true}}, {"key": "Input_9ZGvgVGPXXW6XaGyGTMu5N", "type": "Input", "label": "单行文本", "props": {"label": "手机号", "prop": "phone", "placeholder": "请输入手机号", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true"}], "show": true}}, {"key": "EnumSelect_qyCAJDQrPtwxEhSbtXGv4s", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "员工性别", "prop": "sex", "placeholder": "请输入员工性别", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Gender"}, "rules": [], "show": true}}, {"key": "EnumSelect_wLvtL7TwTGrYGeTmBhNL1F", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "国籍", "prop": "nationality", "placeholder": "请输入国籍", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Nationality"}, "rules": [], "show": true}}, {"key": "DatePicker_4pNhfksxFxfogtMhPHuVeq", "type": "DatePicker", "label": "日期", "props": {"label": "出生日期", "prop": "birthDate", "placeholder": "请选择出生日期", "dataType": "Timestamp", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_gvSU2PKkuupjKUzfUCfkFA", "type": "Input", "label": "单行文本", "props": {"label": "员工个人邮箱", "prop": "email", "placeholder": "请输入员工个人邮箱", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Switch_opceWiFKjeRy3JXXWvUuSA", "type": "Switch", "label": "开关", "props": {"label": "是否残疾", "prop": "disability", "placeholder": "请输入是否残疾", "dataType": "Boolean", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_m9zjh2CZo13Bqj3ZG9rUeG", "type": "Input", "label": "单行文本", "props": {"label": "监护人姓名", "prop": "guardianName", "placeholder": "请输入监护人姓名", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_3wNbwf3rojtJQHbq96nrVR", "type": "Input", "label": "单行文本", "props": {"label": "监护人手机号", "prop": "guardianPhone", "placeholder": "请输入监护人手机号", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_qWao45jmqFzmYEDfREZZMU", "type": "InputNumber", "label": "数值", "props": {"label": "年龄", "prop": "divisionAge", "placeholder": "请输入年龄", "dataType": "Number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_eCoP1c5YF8Cs6gFbNL6Dap", "type": "Select", "label": "下拉单选", "props": {"label": "婚姻状态", "prop": "maritalStatus", "placeholder": "请输入婚姻状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "未婚", "value": "0"}, {"label": "已婚", "value": "1"}, {"label": "丧偶", "value": "2"}], "rules": [], "show": true}}, {"key": "Select_iWUh3pLAuHwGDsL6Ye4qdU", "type": "Select", "label": "下拉单选", "props": {"label": "生育状态", "prop": "fertilityStatus", "placeholder": "请输入生育状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "未育", "value": "0"}, {"label": "一胎", "value": "1"}, {"label": "二胎", "value": "2"}, {"label": "多胎", "value": "3"}], "rules": [], "show": true}}, {"key": "Input_ry69vCipz81tpf6DczSTnY", "type": "Input", "label": "单行文本", "props": {"label": "员工英文名", "prop": "enName", "placeholder": "请输入员工英文名", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_oqX6asA2cGxsuwapkg7B9M", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "民族", "prop": "nation", "placeholder": "请输入民族", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Nation"}, "rules": [], "show": true}}, {"key": "Input_r9nmp7udqEyhDPfxQHMytk", "type": "Input", "label": "单行文本", "props": {"label": "籍贯", "prop": "nativePlace", "placeholder": "请输入籍贯", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_qCWmQNL73nAyYHhqjeiK87", "type": "Input", "label": "单行文本", "props": {"label": "户籍地址", "prop": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "请输入户籍地址", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_feVpGaDsQc4uQm845atBtk", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "政治面貌", "prop": "politicalOutlook", "placeholder": "请输入政治面貌", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "PoliticalP<PERSON>y"}, "rules": [], "show": true}}, {"key": "Switch_5giwwCLVozjCms6LVWfFAv", "type": "Switch", "label": "开关", "props": {"label": "是否满18周岁", "prop": "adult", "placeholder": "请输入是否满18周岁", "dataType": "Boolean", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}], "label": "候选人个人信息", "type": "EmpTab", "key": "entity.onboarding.EmpPrivateInfo", "props": {"show": true, "disabled": false}}], "label": "候选人员工信息", "type": "EmpInfo", "key": "page.detail.onboardingEmpInfo", "script": "/**\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系才到平台的技术支持获得服务（可能收费）。\n* 我们可以用 JS 面板来开发一些定制度高功能。\n* 你可以点击面板上方的 「使用帮助」了解。\n*/\n\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\nexport function didMount() {\n\tconsole.log(`「页面 JS」：当前页面地址 ${location.href}`);\n // 更多相关 API 请参考：https://aliwork.com/developer/API\n}"}, {"key": "page.detail.employeeSelf", "type": "EmpInfo", "label": "员工个人信息", "childList": [{"label": "任职信息", "key": "entity.hr.EmpWorkInfo", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "TreeSelect_84E8SNSCHnT9rzrjAXqf4d", "type": "TreeSelect", "label": "组织", "props": {"label": "所属组织", "prop": "organize", "placeholder": "请输入所属组织", "dataType": "string", "required": true, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "Input_qBR8MvAcuFHyjv7GcQTsXp", "type": "Input", "label": "单行文本", "props": {"label": "员工工号", "prop": "workno", "placeholder": "请输入员工工号", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "TreeSelect_cUq4qS3bm5HSevaCENS8Pr", "type": "TreeSelect", "label": "岗位", "props": {"label": "岗位", "prop": "post", "placeholder": "请输入岗位", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_dwrKXSWNo2AcjdJYFdDgSv", "type": "Input", "label": "单行文本", "props": {"label": "关联的职务", "prop": "job", "placeholder": "请输入关联的职务", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EmployeePicker_gZbhq3F4SJbzCnF75mA5vn", "type": "EmployeePicker", "label": "选人组件", "props": {"label": "直接上级", "prop": "leadEmpId", "placeholder": "请输入直接上级", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_rHEwMtxAVognVGh2nY2yn5", "type": "DatePicker", "label": "日期", "props": {"label": "入职日期", "prop": "hireDate", "placeholder": "请选择入职日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "Select_gWaSeExMpYJReV6QyxfFAw", "type": "Select", "label": "下拉单选", "props": {"label": "试用期期限", "prop": "probation", "placeholder": "请输入试用期期限", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "无试用期", "value": "0"}, {"label": "一个月", "value": "1"}, {"label": "两个月", "value": "2"}, {"label": "三个月", "value": "3"}, {"label": "六个月", "value": "6"}], "value": "0", "rules": [], "show": true}}, {"key": "DatePicker_3XcrWEXnHcpPPm9XFEFWhk", "type": "DatePicker", "label": "日期", "props": {"label": "转正日期", "prop": "confirmationDate", "placeholder": "请选择转正日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_4SWorCpTQbGmGgM57aS39h", "type": "Select", "label": "下拉单选", "props": {"label": "转正状态", "prop": "confirmation<PERSON>tatus", "placeholder": "请输入转正状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "已转正", "value": "0"}, {"label": "试用期", "value": "1"}], "value": "0", "rules": [], "show": true}}, {"key": "Select_kkbmLWmJpigjwxs8BAeznN", "type": "Select", "label": "下拉单选", "props": {"label": "员工状态", "prop": "empStatus", "placeholder": "请输入员工状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "在职", "value": "0"}, {"label": "离职", "value": "1"}, {"label": "试用期", "value": "2"}], "value": "0", "rules": [], "show": true}}, {"key": "EnumSelect_4hiZhD5ZyhTgXToBd9Fxxc", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "用工类型", "prop": "empType", "placeholder": "请输入用工类型", "dataType": "Dict", "required": true, "width": "50%", "standard": true, "options": {"dataSourceId": "EmployType"}, "rules": [], "show": true}}, {"key": "DatePicker_voPQJ4YgjMrcC9QuQBMejv", "type": "DatePicker", "label": "日期", "props": {"label": "离职日期", "prop": "leaveDate", "placeholder": "请选择离职日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_7HUoW3koBNRDu1nhKdrPfs", "type": "Select", "label": "下拉单选", "props": {"label": "工时制", "prop": "workHour", "placeholder": "请输入工时制", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "标准工时制", "value": "0"}, {"label": "综合工时制", "value": "1"}, {"label": "不定时工时制", "value": "2"}], "value": "0", "rules": [], "show": true}}, {"key": "InputNumber_h6rmxDmEw3QoY6LAPDgRW5", "type": "InputNumber", "label": "数值", "props": {"label": "司龄", "prop": "divisionAge", "placeholder": "请输入司龄", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_kh5VeVAgn3NXuDTXrJ3wj5", "type": "InputNumber", "label": "数值", "props": {"label": "司龄调整", "prop": "divisionAgeAdjust", "placeholder": "请输入司龄调整", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_gpgw3f5HSygt6b9nGVj12c", "type": "Input", "label": "单行文本", "props": {"label": "员工公司邮箱", "prop": "companyEmail", "placeholder": "请输入员工公司邮箱", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "undefined_9MP1asZqW8dpJXBE4Babtc", "props": {"label": "工作地", "prop": "workplace", "placeholder": "请输入工作地", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_iUWL4H2QoPBYZipt2UTZYE", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "入司途径", "prop": "joinCompanyWay", "placeholder": "请输入入司途径", "dataType": "Dict", "required": true, "width": "50%", "standard": true, "options": {"dataSourceId": "EntryRoute"}, "rules": [], "show": true}}, {"key": "undefined_wJv1cscG75PGRbGiSJf2pe", "props": {"label": "合同公司", "prop": "company", "placeholder": "请输入合同公司", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_8NsqEE6cAhp5kqvk3r1Ymt", "type": "DatePicker", "label": "日期", "props": {"label": "预计毕业日期", "prop": "expectGraduateDate", "placeholder": "请选择预计毕业日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_qtzhZ9RVw49fjsFMtBYG7M", "type": "Input", "label": "单行文本", "props": {"label": "成本中心", "prop": "costCenters", "placeholder": "请输入成本中心", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "个人信息", "key": "entity.hr.EmpPrivateInfo", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "Input_15c36Fr7Po6nLC1dhoQvLd", "type": "Input", "label": "单行文本", "props": {"label": "员工姓名", "prop": "name", "placeholder": "请输入员工姓名", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "Input_jnobnhVW45Cdbqu2KVL4Jj", "type": "Input", "label": "单行文本", "props": {"label": "员工英文名", "prop": "enName", "placeholder": "请输入员工英文名", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_3ft8XDRAcFQfmpTvuqKfqe", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "员工性别", "prop": "sex", "placeholder": "请输入员工性别", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Gender"}, "rules": [], "show": true}}, {"key": "EnumSelect_mbTiaFDu7Vk8yRV6TfGdwi", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "国籍", "prop": "nationality", "placeholder": "请输入国籍", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Nationality"}, "rules": [], "show": true}}, {"key": "EnumSelect_tewv52StT8e8HmeE9yyjKp", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "民族", "prop": "nation", "placeholder": "请输入民族", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "Nation"}, "rules": [], "show": true}}, {"key": "Cascader_uVBL7VRF8SUuux8vwbMRXF", "type": "<PERSON>r", "label": "省、市级联选择", "props": {"label": "籍贯", "prop": "nativePlace", "placeholder": "请输入籍贯", "dataType": "PROVINCE_CITY", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "1325499191547905"}, "rules": [], "show": true}}, {"key": "EnumSelect_k1YWJJ8afV9QwyXZpuY2hg", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "户口类型", "prop": "familyType", "placeholder": "请输入户口类型", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "FamilyType"}, "rules": [], "show": true}}, {"key": "Input_pcqfPXwAR9puYYqanBGuy5", "type": "Input", "label": "单行文本", "props": {"label": "户籍地址", "prop": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "请输入户籍地址", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_on134e1NdmPHxKsj1ynwc7", "type": "DatePicker", "label": "日期", "props": {"label": "出生日期", "prop": "birthDate", "placeholder": "请选择出生日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_wdrwT3PT32VVh5NViKTUZF", "type": "InputNumber", "label": "数值", "props": {"label": "年龄", "prop": "divisionAge", "placeholder": "请输入年龄", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_itqZAEoNRwU6Q2urUucvj7", "type": "Select", "label": "下拉单选", "props": {"label": "婚姻状态", "prop": "maritalStatus", "placeholder": "请输入婚姻状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "未婚", "value": "0"}, {"label": "已婚", "value": "1"}, {"label": "丧偶", "value": "2"}], "rules": [], "show": true}}, {"key": "Select_vmEXZSR3PnGg6RMKS8bVyx", "type": "Select", "label": "下拉单选", "props": {"label": "生育状态", "prop": "fertilityStatus", "placeholder": "请输入生育状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "未育", "value": "0"}, {"label": "一胎", "value": "1"}, {"label": "二胎", "value": "2"}, {"label": "多胎", "value": "3"}], "rules": [], "show": true}}, {"key": "EnumSelect_42Lu9mrW7wshJdPqa1aML1", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "政治面貌", "prop": "politicalOutlook", "placeholder": "请输入政治面貌", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "PoliticalP<PERSON>y"}, "rules": [], "show": true}}, {"key": "Switch_6wKAokuiy4HTBCA2wXYyQ3", "type": "Switch", "label": "开关", "props": {"label": "是否残疾", "prop": "disability", "placeholder": "请输入是否残疾", "dataType": "boolean", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_kVEpGbb76UviEVu39gqF4H", "type": "Input", "label": "单行文本", "props": {"label": "监护人姓名", "prop": "guardianName", "placeholder": "请输入监护人姓名", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_8a9ztm7z1CxtkXvvpD1FAU", "type": "Input", "label": "单行文本", "props": {"label": "监护人手机号", "prop": "guardianPhone", "placeholder": "请输入监护人手机号", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_7hWtwJ2bcP6WDq6N8DZctw", "type": "Input", "label": "单行文本", "props": {"label": "监护人邮箱", "prop": "guardianEmail", "placeholder": "请输入监护人邮箱", "dataType": "String", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_6mef35WHJnFNjNBUs2vNMY", "type": "Input", "label": "单行文本", "props": {"label": "手机号", "prop": "phone", "placeholder": "请输入手机号", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_jwiHVbBEDvkrH1DL6idF9X", "type": "Input", "label": "单行文本", "props": {"label": "员工个人邮箱", "prop": "email", "placeholder": "请输入员工个人邮箱", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_dYWwhetFPB2ePZAQSaowWZ", "type": "Input", "label": "单行文本", "props": {"label": "通讯地址", "prop": "postalAddress", "placeholder": "请输入通讯地址", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_rJyMfQRQvWFPdhEh4LymF5", "type": "Select", "label": "下拉单选", "props": {"label": "证件类型", "prop": "cardType", "placeholder": "请输入证件类型", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "身份证", "value": "0"}, {"label": "护照", "value": "1"}, {"label": "台胞证", "value": "2"}, {"label": "港澳永久身份证", "value": "3"}, {"label": "港澳非永久身份证", "value": "4"}, {"label": "驾驶证", "value": "5"}, {"label": "台湾身份证", "value": "6"}, {"label": "其他", "value": "7"}], "value": "0", "rules": [], "show": true}}, {"key": "Input_rMsrRYJihu9QvKm5YyEAM9", "type": "Input", "label": "单行文本", "props": {"label": "证件号", "prop": "cardNo", "placeholder": "请输入证件号", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_2BedAhtJYjxHckPhU5fX5t", "type": "DatePicker", "label": "日期", "props": {"label": "证件有效日期", "prop": "cardEffectiveDate", "placeholder": "请选择证件有效日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "非行政组织", "key": "entity.hr.EmpOtherOrg", "type": "EmpTab", "props": {"show": true, "disabled": true}, "childList": null}, {"label": "合同信息", "key": "entity.hr.Contract", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "Input_5QJFBrSR5LaAfcmFAypGqJ", "type": "Input", "label": "单行文本", "props": {"label": "合同编号", "prop": "contractNo", "placeholder": "请输入合同编号", "dataType": "string", "required": true, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "EnumSelect_74gYzUkBXkiXceLaGbPqkF", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "合同类型", "prop": "contractSettingType", "placeholder": "请输入合同类型", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "ContractType"}, "rules": [], "show": true}}, {"key": "undefined_7NkQARmYg49XiXs1EzyB8Q", "props": {"label": "合同公司", "prop": "company", "placeholder": "请输入合同公司", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_5fPoTNRjQBXT12ansFcTKv", "type": "DatePicker", "label": "日期", "props": {"label": "合同开始日期", "prop": "startDate", "placeholder": "请选择合同开始日期", "dataType": "Timestamp", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_mrMMJfdHYGwzTdzAbuYr16", "type": "DatePicker", "label": "日期", "props": {"label": "合同结束日期", "prop": "endDate", "placeholder": "请选择合同结束日期", "dataType": "datetime", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_2GVE2DqBUyeJ3TvRCnBp9S", "type": "InputNumber", "label": "数值", "props": {"label": "合同期（月）", "prop": "contractPeriod", "placeholder": "请输入合同期（月）", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Select_qtg7AwinSuFKCudp2iFDK3", "type": "Select", "label": "下拉单选", "props": {"label": "签订类型", "prop": "signType", "placeholder": "请输入签订类型", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "新签", "value": "2"}, {"label": "续签", "value": "0"}, {"label": "改签", "value": "1"}], "value": "0", "rules": [], "show": true}}, {"key": "Select_2WEL4BFMy51ij6NYHgTfn1", "type": "Select", "label": "下拉单选", "props": {"label": "状态", "prop": "contractStatus", "placeholder": "请输入状态", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "未生效", "value": "0"}, {"label": "生效中", "value": "1"}, {"label": "已失效", "value": "2"}, {"label": "已解除", "value": "3"}, {"label": "已终止", "value": "4"}], "value": "0", "rules": [], "show": true}}, {"key": "Select_43jJ9hoT3wY53rE34inUjU", "type": "Select", "label": "下拉单选", "props": {"label": "合同期限类型", "prop": "periodType", "placeholder": "请输入合同期限类型", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "固定期限", "value": "0"}, {"label": "无固定期限", "value": "1"}], "value": "0", "rules": [], "show": true}}, {"key": "InputNumber_vTdBQ5shCM8TvGoogdpb1S", "type": "InputNumber", "label": "数值", "props": {"label": "合同签订次数", "prop": "signTime", "placeholder": "请输入合同签订次数", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_4upbmBA4v8Ux9ST8hBvGH2", "type": "InputNumber", "label": "数值", "props": {"label": "试用期（月）", "prop": "probation<PERSON><PERSON>od", "placeholder": "请输入试用期（月）", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "工作经历", "key": "entity.hr.EmpWorkOverview", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "DatePicker_wNunFuqqiJeYw11BrNT6Xg", "type": "DatePicker", "label": "日期", "props": {"label": "首次工作日期", "prop": "firstWorkDate", "placeholder": "请选择首次工作日期", "dataType": "datetime", "required": true, "width": "50%", "standard": true, "rules": [{"type": "required", "value": "true", "message": null}], "show": true}}, {"key": "Input_7eU9nSqBHAVqyaYpm2YxmN", "type": "Input", "label": "单行文本", "props": {"label": "工龄", "prop": "workAge", "placeholder": "请输入工龄", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "InputNumber_6e6NptL8mSNG2fxFR6qU2K", "type": "InputNumber", "label": "数值", "props": {"label": "工龄调整", "prop": "workAgeAdjust", "placeholder": "请输入工龄调整", "dataType": "number", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "教育经历", "key": "entity.hr.EmpEduInfo", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "EnumSelect_7YSHCjnomZkNHWkbu87Hs5", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "学位", "prop": "degree", "placeholder": "请输入学位", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "EduDegree"}, "rules": [], "show": true}}, {"key": "EnumSelect_6sE82ghD1USrXju2k1LvyN", "type": "EnumSelect", "label": "字典选择器", "props": {"label": "最高学历", "prop": "background", "placeholder": "请输入最高学历", "dataType": "Dict", "required": false, "width": "50%", "standard": true, "options": {"dataSourceId": "EduExp"}, "rules": [], "show": true}}, {"key": "Input_wAE5o5MhfKnU9s5nUXbiNC", "type": "Input", "label": "单行文本", "props": {"label": "毕业学校", "prop": "school", "placeholder": "请输入毕业学校", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "Input_hMddsfQLBxuPVqMDaxce8V", "type": "Input", "label": "单行文本", "props": {"label": "专业", "prop": "major", "placeholder": "请输入专业", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "家庭信息", "key": "entity.hr.FamilyInfo", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "Input_jMTqNdCfHVZ1iJiN6qcXkz", "type": "Input", "label": "单行文本", "props": {"label": "家庭地址", "prop": "family<PERSON><PERSON><PERSON>", "placeholder": "请输入家庭地址", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}]}, {"label": "奖惩信息", "key": "entity.hr.EmpReward", "type": "EmpTab", "props": {"show": true, "disabled": true}, "childList": []}, {"label": "附件档案", "key": "entity.hr.EmpFileAttachment", "type": "EmpTab", "props": {"show": true, "disabled": true}, "childList": []}, {"label": "薪资信息", "key": "entity.hr.EmpSalaryChange", "type": "EmpTab", "props": {"show": true, "disabled": false}, "childList": [{"key": "Select_w3DRVPjdaaXoYXwejSMfso", "type": "Select", "label": "下拉单选", "props": {"label": "薪资类型", "prop": "salaryType", "placeholder": "请输入薪资类型", "dataType": "Enum", "required": false, "width": "50%", "standard": true, "options": [{"label": "时薪", "value": "0"}, {"label": "月薪", "value": "1"}, {"label": "年薪", "value": "2"}], "value": "0", "rules": [], "show": true}}, {"key": "Input_wyuAZmN33GB697WDpmc2ii", "type": "Input", "label": "单行文本", "props": {"label": "薪资", "prop": "salary", "placeholder": "请输入薪资", "dataType": "string", "required": false, "width": "50%", "standard": true, "rules": [], "show": true}}, {"key": "DatePicker_w7jA55reZi6vaS4GtuqsNi", "type": "DatePicker", "label": "日期", "props": {"label": "生效日期", "prop": "effectiveDate", "placeholder": "请选择生效日期", "dataType": "datetime", "required": true, "width": "50%", "standard": true, "rules": [], "show": true}}]}], "script": "/**\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系才到平台的技术支持获得服务（可能收费）。\n* 我们可以用 JS 面板来开发一些定制度高功能。\n* 你可以点击面板上方的 「使用帮助」了解。\n*/\n\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\nexport function didMount() {\n\tconsole.log(`「页面 JS」：当前页面地址 ${location.href}`);\n // 更多相关 API 请参考：https://aliwork.com/developer/API\n}"}]