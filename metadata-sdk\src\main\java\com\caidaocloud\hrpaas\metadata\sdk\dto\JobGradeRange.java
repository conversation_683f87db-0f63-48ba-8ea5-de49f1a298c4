package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Data
@DataComponent(dataType = PropertyDataType.Job_Grade_Range)
public class JobGradeRange implements ComponentPropertyValue {

    private Boolean isRange;

    private String channel;

    private String startGrade;

    private String endGrade;

    private String startLevel;

    private String endLevel;

    private String channelName;

    private String startGradeName;

    private String endGradeName;

    @Override
    public Map<String, Boolean> propertySuffixToWhetherLong() {
        return Maps.map(
                Sequences.sequence(
                        Pair.pair(".isRange", false),
                        Pair.pair(".channel", false),
                        Pair.pair(".startGrade", false),
                        Pair.pair(".endGrade", false),
                        Pair.pair(".startLevel", false),
                        Pair.pair(".endLevel", false)
                )
        );
    }

    @Override
    public PropertyValue extractDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties) {
        return toDisplay(propDef, properties);
    }

    public static JobGradeRange toDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties) {
        val range = properties.stream()
                .filter(propertyDataDto -> propertyDataDto.getProperty().startsWith(propDef.getProperty() + "."))
                .findFirst();
        if (range.isPresent()) {
            JobGradeRange jobGradeRange = new JobGradeRange();
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals(propDef.getProperty() + ".isRange"))
                    .findFirst().ifPresent(isRange -> {
                        jobGradeRange.setIsRange(Boolean.valueOf(isRange.getValue()));
                    });
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals(propDef.getProperty() + ".channel"))
                    .findFirst().ifPresent(channel -> {
                        jobGradeRange.setChannel(channel.getValue());
                        if (null != channel.getValue()) {
                            jobGradeRange.setChannelName(
                                    QueryInfoCache.getJobGradeChannel(channel.getValue()).getLabel()
                            );
                        }
                    });
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals(propDef.getProperty() + ".startGrade"))
                    .findFirst().ifPresent(startGrade -> {
                        jobGradeRange.setStartGrade(startGrade.getValue());
                        if (null != startGrade.getValue()) {
                            jobGradeRange.setStartGradeName(
                                    QueryInfoCache.getJobGrade(startGrade.getValue()).getLabel()
                            );
                        }
                    });
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals(propDef.getProperty() + ".endGrade"))
                    .findFirst().ifPresent(endGrade -> {
                        jobGradeRange.setEndGrade(endGrade.getValue());
                        if (null != endGrade.getValue()) {
                            jobGradeRange.setEndGradeName(
                                    QueryInfoCache.getJobGrade(endGrade.getValue()).getLabel()
                            );
                        }
                    });
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals(propDef.getProperty() + ".startLevel"))
                    .findFirst().ifPresent(startLevel -> {
                        jobGradeRange.setStartLevel(startLevel.getValue());
                    });
            properties.stream()
                    .filter(propertyDataDto -> propertyDataDto.getProperty().equals(propDef.getProperty() + ".endLevel"))
                    .findFirst().ifPresent(endLevel -> {
                        jobGradeRange.setEndLevel(endLevel.getValue());
                    });
            return jobGradeRange;
        } else {
            return null;
        }
    }

    @Override
    public List<PropertyDataDto> extractPersist(DataSimple data, MetadataPropertyVo propDef) {
        return toPersist(data, propDef);
    }

    public static List<PropertyDataDto> toPersist(DataSimple data, MetadataPropertyVo propDef) {
        List<PropertyDataDto> result = Lists.newArrayList();
        val dataMap = JsonEnhanceUtil.toObject(data, Map.class);
        val customPropertyValue = data.getProperties().get(propDef.getProperty());
        JobGradeRange jobGradeRange = null;
        if (dataMap.containsKey(propDef.getProperty())) {
            val standardPropertyValue = dataMap.get(propDef.getProperty());
            if (null != standardPropertyValue) {
                jobGradeRange = JsonEnhanceUtil.toObject(standardPropertyValue, JobGradeRange.class);
            }
        } else if (null != customPropertyValue) {
            if (customPropertyValue instanceof JobGradeRange) {
                jobGradeRange = (JobGradeRange) customPropertyValue;
            } else {
                jobGradeRange = FastjsonUtil.toObject(
                        ((SimplePropertyValue) customPropertyValue).getValue(), JobGradeRange.class);
            }
        } else {
            jobGradeRange = handleFlatProperty(data, propDef.getProperty());
        }
        if (null == jobGradeRange) {
            jobGradeRange = new JobGradeRange();
        }
        PropertyDataDto isRange = new PropertyDataDto();
        isRange.setProperty(String.format("%s.isRange", propDef.getProperty()));
        isRange.setDataType(PropertyDataType.Job_Grade_Range);
        if (null != jobGradeRange.getIsRange()) {
            isRange.setValue(jobGradeRange.getIsRange().toString());
        } else {
            isRange.setValue(null);
        }
        result.add(isRange);

        PropertyDataDto channel = new PropertyDataDto();
        channel.setProperty(String.format("%s.channel", propDef.getProperty()));
        channel.setDataType(PropertyDataType.Job_Grade_Range);
        channel.setValue(jobGradeRange.getChannel());
        result.add(channel);

        PropertyDataDto startGrade = new PropertyDataDto();
        startGrade.setProperty(String.format("%s.startGrade", propDef.getProperty()));
        startGrade.setDataType(PropertyDataType.Job_Grade_Range);
        startGrade.setValue(jobGradeRange.getStartGrade());
        result.add(startGrade);

        PropertyDataDto endGrade = new PropertyDataDto();
        endGrade.setProperty(String.format("%s.endGrade", propDef.getProperty()));
        endGrade.setDataType(PropertyDataType.Job_Grade_Range);
        endGrade.setValue(jobGradeRange.getEndGrade());
        result.add(endGrade);

        PropertyDataDto startLevel = new PropertyDataDto();
        startLevel.setProperty(String.format("%s.startLevel", propDef.getProperty()));
        startLevel.setDataType(PropertyDataType.Job_Grade_Range);
        startLevel.setValue(jobGradeRange.getStartLevel());
        result.add(startLevel);

        PropertyDataDto endLevel = new PropertyDataDto();
        endLevel.setProperty(String.format("%s.endLevel", propDef.getProperty()));
        endLevel.setDataType(PropertyDataType.Job_Grade_Range);
        endLevel.setValue(jobGradeRange.getEndLevel());
        result.add(endLevel);
        return result;
    }

    private static JobGradeRange handleFlatProperty(DataSimple data, String dictPrefix) {
        var b = data.getProperties().containsKey(String.format("%s.isRange", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.channel", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.startGrade", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.endGrade", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.startLevel", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.endLevel", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.channelName", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.startGradeName", dictPrefix)) ||
                data.getProperties().containsKey(String.format("%s.endGradeName", dictPrefix));
        if (!b) {
            return null;
        }
        var jobGradeRange = new JobGradeRange();
        if (data.getProperties().containsKey(String.format("%s.isRange", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.isRange", dictPrefix));
            jobGradeRange.setIsRange(StringUtils.isNotBlank(propertyValue.getValue()) ? Boolean.valueOf(propertyValue.getValue()) : false);
        }
        if (data.getProperties().containsKey(String.format("%s.channel", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.channel", dictPrefix));
            jobGradeRange.setChannel(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.startGrade", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.startGrade", dictPrefix));
            jobGradeRange.setStartGrade(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.endGrade", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.endGrade", dictPrefix));
            jobGradeRange.setEndGrade(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.startLevel", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.startLevel", dictPrefix));
            jobGradeRange.setStartLevel(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.endLevel", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.endLevel", dictPrefix));
            jobGradeRange.setEndLevel(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.channelName", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.channelName", dictPrefix));
            jobGradeRange.setChannelName(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.startGradeName", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.startGradeName", dictPrefix));
            jobGradeRange.setStartGradeName(propertyValue.getValue());
        }
        if (data.getProperties().containsKey(String.format("%s.endGradeName", dictPrefix))) {
            var propertyValue = (SimplePropertyValue) data.getProperties().get(String.format("%s.endGradeName", dictPrefix));
            jobGradeRange.setEndGradeName(propertyValue.getValue());
        }
        return jobGradeRange;
    }

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {
        JobGradeRange dataJobGradeRange = (JobGradeRange) dataVal;
        if (null == dataJobGradeRange) {
            return;
        }

        dataValue.fitDataValue(dataJobGradeRange);

        // 列表显示内容
        DataValueFunction rankTxt = dataValue.dataValueBulid();
        rankTxt.fitDataProp(String.format(UIFORM_RELATION_FORMAT_TXT, dataValue.loadDataProp()));
        rankTxt.fitDataValue(dataJobGradeRange.getChannel());

        if (StringUtil.isEmpty(dataJobGradeRange.getStartGradeName())) {
            rankTxt.fitDataValue(dataJobGradeRange.getStartGradeName());
        } else if (dataJobGradeRange.isRange) {
            rankTxt.fitDataValue(dataJobGradeRange.getStartGradeName() + " ~ " + dataJobGradeRange.getEndGradeName());
        } else {
            rankTxt.fitDataValue(dataJobGradeRange.getStartGradeName());
        }

        dataVals.add(rankTxt);
    }

    @Override
    public String toText() {
        return startGradeName;
    }
}
