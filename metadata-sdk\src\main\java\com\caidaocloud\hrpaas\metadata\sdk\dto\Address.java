package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 省市区地址组件，统称为地址组件
 * https://www.mca.gov.cn/article/sj/xzqh/2020/20201201.html
 */
@Data
@DataComponent(dataType = PropertyDataType.Address)
public class Address implements ComponentPropertyValue {
    public final static String ADDRESS_TEXT_FORMAT = "%s%s%s%s", ADDRESS_VALUE_FORMAT = "%s/%s/%s/%s";
    // 省
    private Long province;
    private String provinceTxt;
    // 市
    private Long city;
    private String cityTxt;
    // 区 或 县
    private Long area;
    private String areaTxt;
    // 地址
    private String address;
    // 实际存储的 value
    private String value;
    // 地址组件回显的文本
    private String text;
    // 区或县对应的行政编码
    private String code;

    /**
     * 存储 value 转换成对象
     */
    @Override
    public PropertyValue extractDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties) {
        return Address.toDisplay(propDef, properties);
    }

    /**
     * Address 对象转换成要持久化存储的 value
     */
    @Override
    public List<PropertyDataDto> extractPersist(DataSimple data, MetadataPropertyVo propDef) {
        return Address.toPersist(data, propDef);
    }

    @Override
    public Map<String, Boolean> propertySuffixToWhetherLong() {
        return Maps.map(
                Sequences.sequence(
                        Pair.pair("", false)
                )
        );
    }

    @Override
    public void convertDataValue(DataValueFunction<Object> dataValue, PropertyValue dataVal, List<DataValueFunction<Object>> dataVals) {

    }

    @Override
    public String toText() {
        return doText();
    }

    public static Address toDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> propList) {
        Address address = new Address();
        String fieldProp = propDef.getProperty();
        if (null == propList || propList.isEmpty() || StringUtil.isEmpty(fieldProp)) {
            return address;
        }

        for (PropertyDataDto prop : propList) {
            if (null == prop || !prop.getProperty().equals(fieldProp)) {
                continue;
            }

            String propValue = prop.getValue();
            if (StringUtil.isEmpty(propValue)) {
                return address;
            }

            address.setValue(propValue);
            doAddress(address);
            return address;
        }

        return address;
    }

    public static Address txtToAddress(String addressStr) {
        Address addr = new Address();
        addr.setText(addressStr);
        return Address.txtToAddress(addr);
    }

    public static Address txtToAddress(Address address) {
        String txt = address.getText();
        if (StringUtil.isEmpty(txt)) {
            return address;
        }

        String[] splitTxt = txt.split("/");
        int len = splitTxt.length;
        String cacheKey = "txt_long_address_%s";
        Map<String, Long> cacheMap = txtToLong(splitTxt, cacheKey);
        String addrFormat = "";
        if (len > 0) {
            addrFormat += splitTxt[0];
            address.setProvince(cacheMap.get(String.format(cacheKey, addrFormat)));
            address.setProvinceTxt(splitTxt[0]);
        }

        if (len > 1 && !"".equals(splitTxt[1])) {
            addrFormat += splitTxt[1];
            address.setCity(cacheMap.get(String.format(cacheKey, addrFormat)));
            address.setCityTxt(splitTxt[1]);
        }

        if (len > 2 && !"".equals(splitTxt[2])) {
            addrFormat += splitTxt[2];
            address.setArea(cacheMap.get(String.format(cacheKey, addrFormat)));
            address.setAreaTxt(splitTxt[2]);
        }

        address.doText();
        return address;
    }

    public static Map<String, Long> txtToLong(String[] splitTxt, String cacheKey) {
        if (null == splitTxt || splitTxt.length < 1) {
            return new HashMap<>();
        }

        List<String> collect = Arrays.stream(splitTxt).filter(txt -> StringUtil.isNotEmpty(txt)).collect(Collectors.toList());
        if (null == collect || collect.isEmpty()) {
            return new HashMap<>();
        }

        return QueryInfoCache.getAddressCode(collect, cacheKey);
    }

    public static void doAddress(Address address) {
        if (null == address || StringUtil.isEmpty(address.getValue())) {
            return;
        }
        String[] splitValue = address.getValue().split("/");
        if (null == splitValue || splitValue.length == 0) {
            return;
        }
        for (int i = 0; i < splitValue.length; i++) {
            if (i == 0) {
                address.setProvince(convertLong(splitValue[0]));
                address.setProvinceTxt(QueryInfoCache.getAddress(address.getProvince()));
            } else if (i == 1) {
                address.setCity(convertLong(splitValue[1]));
                address.setCityTxt(QueryInfoCache.getAddress(address.getCity()));
            } else if (i == 2) {
                address.setArea(convertLong(splitValue[2]));
                address.setAreaTxt(QueryInfoCache.getAddress(address.getArea()));
            }
        }
        if (splitValue.length >= 3) {
            address.doText();
        }
    }

    private static Long convertLong(String str) {
        if ("null".equals(str) || null == str || "".equals(str)) {
            return null;
        }

        return Long.valueOf(str);
    }

    public String doText() {
        this.setText(String.format(ADDRESS_TEXT_FORMAT, this.getProvinceTxt(),
                this.getCityTxt(), this.getAreaTxt(), this.getAddress()).replaceAll("null", ""));
        return this.text;
    }

    public String doValue() {
        if (null == this.getProvince() && null == this.getCity() && null == this.getArea()) {
            return null;
        }

        this.setValue(String.format(ADDRESS_VALUE_FORMAT, this.getProvince(),
                this.getCity(), this.getArea(), this.getAddress()));
        return this.value;
    }

    public static List<PropertyDataDto> toPersist(DataSimple data, MetadataPropertyVo propDef) {
        String prop = propDef.getProperty();
        Map dataMap = JsonEnhanceUtil.toObject(data, Map.class);
        Address address = null;
        if (dataMap.containsKey(prop)) {
            Object standardPropertyValue = dataMap.get(prop);
            if (null != standardPropertyValue) {
                address = JsonEnhanceUtil.toObject(standardPropertyValue, Address.class);
                return Address.toPropList(address, prop);
            }

            return Address.toPropList(null, prop);
        }

        // 获取属性中的字典字段
        PropertyValue customPropertyValue = data.getProperties().get(prop);
        if (null != customPropertyValue) {
            if (customPropertyValue instanceof Address) {
                address = ((Address) customPropertyValue);
            } else {
                address = new Address();
                String addressValue = ((SimplePropertyValue) customPropertyValue).getValue();
                address.setValue(addressValue);

                doAddress(address);
            }

            return Address.toPropList(address, prop);
        }

        return Address.toPropList(null, prop);
    }

    private static List<PropertyDataDto> toPropList(Address data, String prop) {
        List<PropertyDataDto> result = Lists.newArrayList();

        PropertyDataDto dataDto = new PropertyDataDto();
        dataDto.setProperty(prop);
        dataDto.setDataType(PropertyDataType.Address);
        if (null != data) {
            dataDto.setValue(data.doValue());
        }
        result.add(dataDto);
        return result;
    }
}
