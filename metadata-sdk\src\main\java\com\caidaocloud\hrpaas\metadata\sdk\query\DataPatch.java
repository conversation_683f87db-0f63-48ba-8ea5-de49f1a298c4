package com.caidaocloud.hrpaas.metadata.sdk.query;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.googlecode.totallylazy.Lists;

import java.util.List;

public class DataPatch {

    private String identifier;

    private DataFilter filter;

    private List<String> languages = Lists.list();

    private DataPatch(){

    }

    public static DataPatch identifier(String identifier){
        DataPatch request = new DataPatch();
        request.identifier = identifier;
        return request;
    }

    public DataPatch filter(DataFilter filter){
        this.filter = filter;
        return this;
    }

    public DataPatch specifyLanguage(String... languages){
        this.languages = Lists.list(languages);
        return this;
    }

//    public ExecuteResult modifyProperties(Map<String, Object> properties){
//        return null;
//    }


}
