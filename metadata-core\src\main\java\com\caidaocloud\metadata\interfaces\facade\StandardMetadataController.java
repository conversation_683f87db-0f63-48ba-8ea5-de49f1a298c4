package com.caidaocloud.metadata.interfaces.facade;


import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataRelationDto;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.metadata.application.service.MetadataPropertyService;
import com.caidaocloud.metadata.application.service.MetadataRelationService;
import com.caidaocloud.metadata.application.service.MetadataService;
import com.caidaocloud.metadata.domain.entity.EntityRelationDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.caidaocloud.metadata.interfaces.convertor.MetadataConvertor;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Sequences;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/${service.name.short:hrpaas}/v1/metadata/standard")
public class StandardMetadataController {

    @Autowired
    private MetadataService metadataService;

    @Autowired
    private MetadataPropertyService metadataPropertyService;

    @Autowired
    private MetadataRelationService metadataRelationService;

    @PostMapping("property")
    Result addProperty(@RequestParam("identifier") String identifier, @RequestBody List<MetadataPropertyDto> properties){
        metadataPropertyService.insertStandardProps(identifier, Sequences.sequence(FastjsonUtil.convertList(properties, PropertyDef.class)), DefChannel.SCRIPT);
        return Result.ok();
    }

    @PostMapping
    Result initEntity(@RequestBody MetadataDto metadata){
        metadataService.create(MetadataConvertor.standardFromDto(metadata), DefChannel.SCRIPT);
        return Result.ok();
    }

    @PostMapping("relation")
    Result addRelation(@RequestBody MetadataRelationDto relation){
        metadataRelationService
                .addRelation(JsonEnhanceUtil.toObject(relation, EntityRelationDef.class), DefChannel.SCRIPT);
        return Result.ok();
    }
}
