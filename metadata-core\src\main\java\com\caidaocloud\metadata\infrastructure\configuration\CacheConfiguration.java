package com.caidaocloud.metadata.infrastructure.configuration;

import com.jarvis.cache.serializer.FastjsonSerializer;
import com.jarvis.cache.serializer.ISerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 * @date  2022/1/17
 **/
@Configuration
public class CacheConfiguration {

    /**
     * 缓存序列化
     * <AUTHOR>
     * @date  2022/1/17
     * @return com.jarvis.cache.serializer.ISerializer
     **/
    @Bean
    public ISerializer<Object> serialize() {
        Object res = new FastjsonSerializer();
        return (ISerializer)res;
    }

}
