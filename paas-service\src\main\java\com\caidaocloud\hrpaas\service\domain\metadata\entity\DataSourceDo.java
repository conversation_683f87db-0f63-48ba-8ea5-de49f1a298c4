package com.caidaocloud.hrpaas.service.domain.metadata.entity;

import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.IDataSourceRepository;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceType;
import com.googlecode.totallylazy.Option;
import lombok.Data;

import java.util.List;

@Data
public class DataSourceDo extends BaseEntity<IDataSourceRepository> {
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 数据源名字
     */
    private String sourceName;

    /**
     * 数据源名字
     */
    private String description;

    /**
     * 数据源类型;0: 本地 key、value数组类型;1: 通过 api 获取远程接口数据的模式
     */
    private DataSourceType type;

    /**
     * 数据源配置
     */
    private String config;

    public static List<DataSourceDo> getListByPage(int pageNo, int pageSize){
        return ((IDataSourceRepository)repository(DataSourceDo.class)).getListByPage(pageNo, pageSize);
    }

    public static List<DataSourceDo> getAllList(){
        return ((IDataSourceRepository) repository(DataSourceDo.class)).getAllList();
    }

    public static List<DataSourceDo> getListByType(DataSourceType type, int pageNo, int pageSize){
        return ((IDataSourceRepository) repository(DataSourceDo.class)).getListByType(type, pageNo, pageSize);
    }

    public void save(){
        this.insert();
    }

    public static Option<DataSourceDo> loadById(String id){
        return ((IDataSourceRepository) repository(DataSourceDo.class)).loadById(id);
    }

    public static List<DataSourceDo> loadListById(List<String> ids){
        return ((IDataSourceRepository) repository(DataSourceDo.class)).loadListById(ids);
    }

    public boolean checkDuplicateName(){
        return null != ((IDataSourceRepository) repository(DataSourceDo.class)).checkDuplicateName(this.getId(), this.getSourceName());
    }

    public static void removeById(String id){
        repository(DataSourceDo.class).remove(id);
    }
}
