package com.caidaocloud.metadata.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.IndicateType;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.enums.RelationOperationType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MaxQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.RelationOperationDto;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.metadata.application.event.publish.EntityDataOperationEvent;
import com.caidaocloud.metadata.application.event.publish.EntityRelationOperationEvent;
import com.caidaocloud.metadata.domain.entity.Constant;
import com.caidaocloud.metadata.domain.service.EntityDataDomainService;
import com.caidaocloud.metadata.domain.enums.OperationType;
import com.caidaocloud.metadata.domain.entity.*;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Lists;
import com.jarvis.cache.annotation.Cache;
import com.jarvis.cache.annotation.CacheDelete;
import com.jarvis.cache.annotation.CacheDeleteKey;
import lombok.SneakyThrows;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class EntityDataService {
    private final static String ROOT_NODE_PID = "-1";

    @Autowired
    private EntityDataDomainService entityDataDomainService;

    /**
     * 唯一值字段校验
     *
     * @param data
     * @param entityDef
     */
    private void checkUniquePropertyValue(EntityData data, EntityDef entityDef, long time) {
        List<PropertyDef> properties = entityDef.fetchAllProperties();
        List<PropertyDef> uniqueProperties = properties.stream().filter(PropertyDef::isUnique).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uniqueProperties)) {
            return;
        }

        Map<String, String[]> uniquePropertyValueMap = new HashMap<>();
        DataFilter uniqueFieldFilter = null;
        for (PropertyDef def : uniqueProperties) {
            String property = def.getProperty();
            String name = def.getName();
            Object propertyValue = data.fetchPropertyValue(property);
            if (propertyValue == null || !(propertyValue instanceof String)) {
                continue;
            }
            uniquePropertyValueMap.put(property, new String[]{(String) propertyValue, name});
            if (uniqueFieldFilter == null) {
                uniqueFieldFilter = DataFilter.eq(property, (String) propertyValue);
            } else {
                uniqueFieldFilter = uniqueFieldFilter.orEq(property, (String) propertyValue);
            }
        }

        if (null == uniqueFieldFilter) {
            return;
        }

        DataFilter dataFilter = DataFilter.ne("deleted", "true");
        if (data.getBid() != null) {
            dataFilter = dataFilter.andNe("bid", data.getBid());
        }
        dataFilter = dataFilter.and(uniqueFieldFilter);

        List<EntityData> repeatDataList = page(data.getIdentifier(), dataFilter, 1, 1, Lists.newArrayList(), false, time).getItems();
        if (repeatDataList.isEmpty()) {
            return;
        }

        repeatDataList = repeatDataList.stream().filter(o -> !CollectionUtils.isEmpty(o.getProperties())).collect(Collectors.toList());
        for (EntityData entityData : repeatDataList) {
            List<PropertyData> propertyDataList = entityData.getProperties().stream().filter(o -> uniquePropertyValueMap.containsKey(o.getProperty())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(propertyDataList)) {
                continue;
            }

            for (PropertyData propertyData : propertyDataList) {
                String[] valueAndName = uniquePropertyValueMap.get(propertyData.getProperty());
                if (propertyData.getValue().equalsIgnoreCase(valueAndName[0])) {
                    throw new ServerException(valueAndName[1] + "已存在");
                }
            }
        }
    }
    @Transactional(rollbackFor=Exception.class)
    public String create(EntityData data) {
        val identifier = data.getIdentifier();
        val pid = data.fetchTreePidAndReplaceToPersist();
        val entityDef = EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在"));
        if (entityDef.isTimelineEnabled()) {
            if (data.getDataStartTime() == 0) {
                throw new ServerException("时间轴必须指定生效时间");
            }
            if(data.getDataStartTime() % 10000 != 0l){
                throw new ServerException("时间轴生效时间必须精确到天");
            }
        } else {
            data.setDataStartTime(0);
        }
        // 唯一值字段校验
        checkUniquePropertyValue(data, entityDef, data.getDataStartTime());
        if (pid != null && !ROOT_NODE_PID.equals(pid)) {
            val parent = EntityData.load(identifier, pid, data.getDataStartTime()).getOrThrow(new ServerException("父节点数据不存在"));
            String label = entityDef.getLabel();
            String name = parent.getBid();
            if (StringUtils.isNotEmpty(label)) {
                name = (String) parent.fetchPropertyValue(label);
            }
            var path = StringUtils.trimToEmpty((String) parent.fetchPropertyValue("pid.path"));
            if (StringUtils.isNotEmpty(path) && path.split("/").length >= 9) {
                throw new ServerException("树深度大于10");
            }
            var namePath = StringUtils.trimToEmpty((String) parent.fetchPropertyValue("pid.namePath"));
            path = StringUtils.join(Lists.newArrayList(path, parent.getBid()), "/");
            namePath = StringUtils.join(Lists.newArrayList(namePath, name), "/");
            if (path.startsWith("/")) {
                path = path.substring(1);
                namePath = namePath.substring(1);
            }
            data.addPropertyValue("pid.path", path, PropertyDataType.PID);
            data.addPropertyValue("pid.namePath", namePath, PropertyDataType.PID);
        } else {
            data.getProperties().removeIf(propertyData -> PropertyDataType.PID.equals(propertyData.getDataType()));
            data.addPropertyValue("pid.pid", null, PropertyDataType.PID);
            data.addPropertyValue("pid.path", "", PropertyDataType.PID);
            data.addPropertyValue("pid.namePath", "", PropertyDataType.PID);
        }
        val change = data.create();
        val event = new EntityDataOperationEvent(data.getIdentifier(),
                data.getBid(), change, OperationType.ADD, data.getDataStartTime(), System.currentTimeMillis());
        event.publish();
        if(Lists.newArrayList("entity.hr.JobGrade", "entity.hr.JobGradeChannel")
                .contains(data.getIdentifier())){
            SpringUtil.getBean(EntityDataService.class).clearJobGradeAndChannelCache(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        }
        return data.getBid();
    }
    @Transactional(rollbackFor=Exception.class)
    public String createWithRelation(EntityData data, List<RelationOperationDto> operations) {
        String bid = this.create(data);
        if (!CollectionUtils.isEmpty(operations)) {
            for (RelationOperationDto operation : operations) {
                if (operation.getSourceId() == null) {
                    operation.setSourceId(bid);
                }
                this.operateRelation(JsonEnhanceUtil.toObject(operation, EntityRelation.class),
                        operation.getOperationType(),
                        operation.getSourceId(), operation.getStartTime());
            }
        }
        return bid;
    }

    private String fetchTreePidName(EntityDef entityDef) {
        if (entityDef.fetchAllProperties().isEmpty()) {
            return "";
        }
        Optional<PropertyDef> optional = entityDef.fetchAllProperties().stream().filter(o -> o.getProperty().equals(Constant.PROPERTY_PID)).findFirst();
        return optional.map(PropertyDef::getName).orElse("");
    }
    @Transactional(rollbackFor=Exception.class)
    public void update(EntityData data) {
        val identifier = data.getIdentifier();
        val pid = data.fetchTreePidAndReplaceToPersist();
        val entityDef = EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在"));
        if (entityDef.isTimelineEnabled()) {
            if (data.getDataStartTime() == 0) {
                throw new ServerException("时间轴必须指定生效时间");
            }
            if(data.getDataStartTime() % 10000 != 0l){
                throw new ServerException("时间轴生效时间必须精确到天");
            }
        } else {
            data.setDataStartTime(0);
        }

        val exist = loadOrNull(identifier, data.getBid(), data.getDataStartTime());
        if (null != exist) {
            data.setRelations(exist.getRelations());
        }

        // 唯一值字段校验
        checkUniquePropertyValue(data, entityDef, data.getDataStartTime());
        if (pid != null && !ROOT_NODE_PID.equals(pid)) {
            val parent = EntityData.load(identifier, pid, data.getDataStartTime()).getOrThrow(new ServerException("父节点数据不存在"));
            String label = entityDef.getLabel();
            String name = parent.getId();
            if (StringUtils.isNotEmpty(label)) {
                name = (String) parent.fetchPropertyValue(label);
            }
            var path = StringUtils.trimToEmpty((String) parent.fetchPropertyValue("pid.path"));
            if (StringUtils.isNotEmpty(path) && path.split("/").length >= 9) {
                throw new ServerException("树深度大于10");
            }
            // 当前树节点的父级不允许是自己或者自己的下级
            if (parent.getBid().equals(data.getBid()) || (StringUtils.isNotBlank(path) && path.contains(data.getBid()))) {
                String pidName = fetchTreePidName(entityDef);
                pidName = StringUtils.isBlank(pidName) ? "上级" : pidName;
                throw new ServerException(String.format("%s不允许是自己或者自己的下级", pidName));
            }
            var namePath = StringUtils.trimToEmpty((String) parent.fetchPropertyValue("pid.namePath"));
            path = StringUtils.join(Lists.newArrayList(path, parent.getBid()), "/");
            namePath = StringUtils.join(Lists.newArrayList(namePath, name), "/");
            if (path.startsWith("/")) {
                path = path.substring(1);
                namePath = namePath.substring(1);
            }
            data.addPropertyValue("pid.path", path, PropertyDataType.PID);
            data.addPropertyValue("pid.namePath", namePath, PropertyDataType.PID);
        } else {
            data.getProperties().removeIf(propertyData -> PropertyDataType.PID.equals(propertyData.getDataType()));
            data.addPropertyValue("pid.pid", null, PropertyDataType.PID);
            data.addPropertyValue("pid.path", "", PropertyDataType.PID);
            data.addPropertyValue("pid.namePath", "", PropertyDataType.PID);
        }
        List<EntityData> updateList = Lists.newArrayList();
        if (entityDef.treeOrNot()) {
            String label = entityDef.getLabel();
            String name = data.getBid();
            if (StringUtils.isNotEmpty(label)) {
                name = (String) data.fetchPropertyValue(label);
            }
            val bid = data.getBid();
            val path = StringUtils.trimToEmpty((String) data.fetchPropertyValue("pid.path"));
            val namePath = StringUtils.trimToEmpty((String) data.fetchPropertyValue("pid.namePath"));
            updateList = reCalcAllPath(identifier, bid, name, path, namePath, data.getDataStartTime());
        }
        List<EntityDataChange> dataChanges = data.updateWithTimeline();
        val event = new EntityDataOperationEvent(data.getIdentifier(), data.getBid(),
                dataChanges, OperationType.UPDATE, data.getDataStartTime(), System.currentTimeMillis());
        event.publish();
        updateList.forEach(update -> {
            List<EntityDataChange> influencedChanges = update.updateWithTimeline();
            val eventUpdate = new EntityDataOperationEvent(update.getIdentifier(), update.getBid(),
                    influencedChanges, OperationType.UPDATE, update.getDataStartTime(), System.currentTimeMillis());
            eventUpdate.publish();
        });
        if(Lists.newArrayList("entity.hr.JobGrade", "entity.hr.JobGradeChannel")
                .contains(data.getIdentifier())){
            SpringUtil.getBean(EntityDataService.class).clearJobGradeAndChannelCache(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        }
    }
    @Transactional(rollbackFor=Exception.class)
    public void updateWithRelation(EntityData data, List<RelationOperationDto> operations) {
        this.update(data);
        if (!CollectionUtils.isEmpty(operations)) {
            for (RelationOperationDto operation : operations) {
                this.operateRelation(JsonEnhanceUtil.toObject(operation, EntityRelation.class),
                        operation.getOperationType(),
                        operation.getSourceId(), operation.getStartTime());
            }
        }
    }

    /**
     *
     * @param identifier
     * @param bid 当前节点bid
     * @param name 当前节点名称
     * @param path 当前节点父节点bidPath
     * @param namePath 当前节点父节点名称path
     * @param startTime
     * @return
     */
    private List<EntityData> reCalcAllPath(String identifier, String bid, String name, String path, String namePath, long startTime) {
        List<EntityData> updateList = Lists.newArrayList();
        val dataList = page(identifier, DataFilter.regex("pid$path", bid),
                1, 5000, Lists.newArrayList(),false, startTime).getItems();
        dataList.forEach(data -> {
            List<String> pathList = Lists.newArrayList(data.fetchPropertyValue("pid.path").toString().split("/"));
            List<String> pathNameList = Lists.newArrayList(data.fetchPropertyValue("pid.namePath").toString().split("/"));
            val index = pathList.indexOf(bid);
            // 父节点中没有当前节点bid
            if (index == -1) {
                return;
            }
            pathList = pathList.subList(index + 1, pathList.size());
            pathNameList = pathNameList.subList(index + 1, pathNameList.size());
            List<String> newPathList = Lists.newArrayList(path, bid);
            List<String> newNamePathList = Lists.newArrayList(namePath, name);
            newPathList.addAll(pathList);
            newNamePathList.addAll(pathNameList);
            var newPath = StringUtils.join(newPathList, "/");
            if (newPath.split("/").length >= 10) {
                throw new ServerException("树深度大于10");
            }
            var newNamePath = StringUtils.join(newNamePathList, "/");
            for (PropertyData propertyData : data.getProperties()) {
                if (propertyData.getProperty().equals("pid.path")) {
                    if (newPath.startsWith("/")) {
                        newPath = newPath.substring(1);
                    }
                    propertyData.setValue(newPath);
                }
                if (propertyData.getProperty().equals("pid.namePath")) {
                    if (newNamePath.startsWith("/")) {
                        newNamePath = newNamePath.substring(1);
                    }
                    propertyData.setValue(newNamePath);
                }
            }
            data.setDataStartTime(startTime);
            updateList.add(data);
        });
        return updateList;
    }

    private void checkDelete(String identifier, String bid, long startTime) {
        val entityDef = EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在"));
        val hierarchyPropDefPair = entityDef.loadHierarchyProperties();
        val isTree = hierarchyPropDefPair.first().join(hierarchyPropDefPair.second())
                .join(entityDef.getCustomProperties()).join(entityDef.getStandardProperties())
                .exists(propertyDef ->
                        PropertyDataType.PID.equals(propertyDef.getDataType()));
        if (isTree) {
            val children = page(identifier, DataFilter.eq(Constant.PROPERTY_PID_QUERY, bid).andNe("deleted", "true"), 1, 1, Lists.newArrayList(), false, startTime)
                    .getItems();
            if (!children.isEmpty()) {
                throw new ServerException("节点已被子节点引用");
            }
        }
    }
    @Transactional(rollbackFor=Exception.class)
    public void delete(String identifier, String bid, long startTime) {
        checkDelete(identifier, bid, startTime);
        List<EntityDataChange> changes = EntityData.delete(identifier, bid, startTime);
        if(Lists.newArrayList("entity.hr.JobGrade", "entity.hr.JobGradeChannel")
                .contains(identifier)){
            SpringUtil.getBean(EntityDataService.class).clearJobGradeAndChannelCache(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        }
        val delete = new EntityData();
        delete.setBid(bid);
        delete.setIdentifier(identifier);
        val event = new EntityDataOperationEvent(delete.getIdentifier(), delete.getBid(),
                changes, OperationType.DELETE, startTime, System.currentTimeMillis());
        event.publish();
    }

    @SneakyThrows
    @Transactional(rollbackFor=Exception.class)
    public void softDelete(String identifier, String bid, long startTime) {
        List<EntityDataChange> result = Lists.newArrayList();
        checkDelete(identifier, bid, startTime);
        val dataList = EntityData.loadAll(identifier, bid);
        if (dataList.isEmpty()) {
            throw new ServerException("数据不存在");
        }
        for(EntityData entityData : dataList){
            if(entityData.getDataEndTime() < startTime){
                //nothing to do
            } else if (entityData.getDataStartTime() < startTime && entityData.getDataEndTime() >= startTime) {
                val entityDataCopy = entityData.copy();
                val originalEndTime = entityData.getDataEndTime();
                entityData.setDataEndTime(startTime - 1);
                entityData.directUpdate();

                entityData.setId(null);
                entityData.setDataStartTime(startTime);
                entityData.setDataEndTime(originalEndTime);
                entityData.setDeleted(true);
                val properties = entityData.getProperties();
                for (PropertyData propertyData : properties) {
                    if (propertyData.getProperty().equals("deleted")) {
                        propertyData.setValue("true");
                    }
                }
                //entityData.directUpdate();
                entityData.insert();
                result.add(new EntityDataChange(entityDataCopy, entityData));
            } else if (!(startTime < entityData.getDataStartTime())) {
                val entityDataCopy = entityData.copy();
                entityData.setDeleted(true);
                val properties = entityData.getProperties();
                for (PropertyData propertyData : properties) {
                    if (propertyData.getProperty().equals("deleted")) {
                        propertyData.setValue("true");
                    }
                }
                entityData.directUpdate();
                result.add(new EntityDataChange(entityDataCopy, entityData));
            }
        }
        if(Lists.newArrayList("entity.hr.JobGrade", "entity.hr.JobGradeChannel")
                .contains(identifier)){
            SpringUtil.getBean(EntityDataService.class).clearJobGradeAndChannelCache(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        }
        val delete = new EntityData();
        delete.setBid(bid);
        delete.setIdentifier(identifier);
        val event = new EntityDataOperationEvent(delete.getIdentifier(), delete.getBid(), result,
                OperationType.SOFT_DELETE, startTime, System.currentTimeMillis());
        event.publish();
    }

    public EntityData load(String identifier, String bid, long time) {
        val result = EntityData.load(identifier, bid, time).getOrThrow(new ServerException("数据不存在"));
        return result;
    }

    public EntityData loadOrNull(String identifier, String bid, long time) {
        val result = EntityData.load(identifier, bid, time).getOrNull();
        return result;
    }

    public EntityData load(String identifier, String bid, long time, List<String> relateList, DataQueryDto query) {
        val result = EntityData.load(identifier, bid, time, relateList, query).getOrThrow(new ServerException("数据不存在"));
        return result;
    }

    public EntityData loadOrNull(String identifier, String bid, long time, List<String> relateList, DataQueryDto query) {
        val result = EntityData.load(identifier, bid, time, relateList, query).getOrNull();
        return result;
    }

    public PageResult<EntityData> page(String identifier, DataFilter filter, int pageNo, int pageSize, List<String> relateList, boolean group, long time) {
        return page(identifier, filter, pageNo, pageSize, relateList, Lists.newArrayList(), group, null, time, null);
    }

    public PageResult<EntityData> page(String identifier, DataFilter filter, int pageNo, int pageSize, List<String> relateList, List<String> specifyProperties, boolean group, String orderBy, long time, DataQueryDto query) {
        return EntityData.page(identifier, filter, pageNo, pageSize, relateList, specifyProperties, group, orderBy, time, query);
    }

    @Transactional(rollbackFor=Exception.class)
    public void operateRelation(EntityRelation entityRelation, RelationOperationType operationType, String bid, long startTime) {
        val identifier = entityRelation.getIdentifier();
        val timelineEnabled = EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在")).isTimelineEnabled();
        if (timelineEnabled) {
            if (startTime == 0) {
                throw new ServerException("生效时间必填");
            }
        }
        List<EntityDataChange> changes = entityDataDomainService.operateRelation(bid, entityRelation, operationType, startTime);
        val event = new EntityRelationOperationEvent(entityRelation.getIdentifier(), bid,
                changes, operationType, startTime, System.currentTimeMillis());
        event.publish();
    }

    public List<EntityData> range(String identifier, String bid, long startTime, long endTime) {
        return EntityData.range(identifier, bid, startTime, endTime);
    }
    @Transactional(rollbackFor=Exception.class)
    public void batchUpdate(EntityData data, DataFilter filter) {
        data.batchUpdate(filter);
    }
    @Transactional(rollbackFor=Exception.class)
    public void batchDeleteWithoutRecord(String identifier, DataFilter filter) {
        val entityDef = EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在"));
        if(entityDef.isTimelineEnabled()){
            throw new ServerException("模型开启了时间轴！");
        }
        if(entityDef.isTree()){
            throw new ServerException("模型开启了树形结构！");
        }
        val relatedCount = EntityRelationDef.checkEntityDefRelatedCount(identifier);
        if(relatedCount > 0){
            throw new ServerException("模型被其他模型关联！");
        }
        EntityData.batchDeleteWithoutRecord(identifier, filter);
    }
    @Transactional(rollbackFor=Exception.class)
    public void batchInsertWithoutRecord(String identifier, List<EntityData> dataList) {
        val entityDef = EntityDef.load(identifier).getOrThrow(new ServerException("模型不存在"));
        if(entityDef.isTimelineEnabled()){
            throw new ServerException("模型开启了时间轴！");
        }
        if(entityDef.isTree()){
            throw new ServerException("模型开启了树形结构！");
        }
        EntityData.batchInsertWithoutRecord(identifier, dataList);
    }

    @Cache(key = "'hr_paas_job_grade_' + #args[0]",expire = 3600*48)
    public List<EntityData> listJobGrade(String tenantId){
        boolean addTenant = false;
        if(StringUtils.isEmpty(SecurityUserUtil.getSecurityUserInfo().getTenantId())){
            SecurityUserInfo user = new SecurityUserInfo();
            user.setTenantId(tenantId);
            addTenant = true;
            SecurityUserUtil.setSecurityUserInfo(user);
        }
        try{
            return page("entity.hr.JobGrade", DataFilter.eq("tenantId", tenantId),
                    1, 200, Lists.newArrayList(), false, System.currentTimeMillis()).getItems();
        }finally {
            if(addTenant){
                SecurityUserUtil.removeSecurityUserInfo();
            }
        }
    }

    @Cache(key = "'hr_paas_job_grade_channel_' + #args[0]",expire = 3600*48)
    public List<EntityData> listJobGradeChannel(String tenantId){
        boolean addTenant = false;
        if(StringUtils.isEmpty(SecurityUserUtil.getSecurityUserInfo().getTenantId())){
            SecurityUserInfo user = new SecurityUserInfo();
            user.setTenantId(tenantId);
            addTenant = true;
            SecurityUserUtil.setSecurityUserInfo(user);
        }
        try{
            return page("entity.hr.JobGradeChannel", DataFilter.eq("tenantId", tenantId),
                    1, 100, Lists.newArrayList(), false, System.currentTimeMillis()).getItems();
        }finally {
            if(addTenant){
                SecurityUserUtil.removeSecurityUserInfo();
            }
        }
    }

    @CacheDelete(
            value = {@CacheDeleteKey(value = "'hr_paas_job_grade_' + #args[0]"),
            @CacheDeleteKey(value = "'hr_paas_job_grade_channel_' + #args[0]")}
    )
    public void clearJobGradeAndChannelCache(String tenantId){

    }

    public long count(String identifier, DataFilter filter, List<String> relateList, boolean group, long queryTime) {
        return EntityData.count(identifier, filter, relateList, group, queryTime);
    }

    public PageResult<EntityData> max(MaxQueryDto query) {
        return EntityData.max(query.getIdentifier(), query.getFilter(), query.getMaxProperty(), query.getBy(), query.getPageSize(), query.getPageNo(), query.getQueryTime());
    }

    public List<Map<String, Object>> countByGroup(String identifier, DataFilter filter, List<String> by, long queryTime){
        return EntityData.countByGroup(identifier,filter,by,queryTime);
    }

    public PageResult<List<EntityDataDto>> join(DataJoin dataJoin) {
        val result = EntityData.join(dataJoin);
        return new PageResult<>(
                result.getItems().stream()
                        .map(it-> FastjsonUtil.convertList(it, EntityDataDto.class)).collect(Collectors.toList()),
                result.getPageNo(),result.getPageSize(), result.getTotal()
        );
    }

    public String searchableEncrypt(String str) {
        return EntityData.searchableEncrypt(str);
    }

    public List<Map<String, Object>> indicateByGroup(String identifier, DataFilter filter, List<String> by, long queryTime, IndicateType indicateType, String indicateProperty) {
        return EntityData.indicateByGroup(identifier,filter,by,queryTime, indicateType, indicateProperty);
    }
}
