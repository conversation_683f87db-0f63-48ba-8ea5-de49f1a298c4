package com.caidaocloud.hrpaas.service.application.auth.feign;

import com.caidaocloud.hrpaas.service.application.auth.dto.TenantDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

@FeignClient(value = "${feign.rename.caidaocloud-maintenance-service:caidaocloud-maintenance-service}", fallback = MaintenanceFeignFallBack.class, configuration = FeignConfiguration.class)
public interface MaintenanceFeignClient {
    @GetMapping("/api/maintenance/v1/tenant")
    Result<List<TenantDto>> tenantList();
}