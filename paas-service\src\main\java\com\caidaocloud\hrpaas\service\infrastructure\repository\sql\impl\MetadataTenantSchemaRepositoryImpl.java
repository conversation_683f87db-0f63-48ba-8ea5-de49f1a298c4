package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataTenantSchemaDo;
import com.caidaocloud.hrpaas.service.domain.metadata.repository.IMetadataTenantSchemaRepository;
import org.springframework.stereotype.Repository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.MetadataTenantSchemaPo;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Option;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;

@Repository
public class MetadataTenantSchemaRepositoryImpl implements IMetadataTenantSchemaRepository {

    @Autowired
    MetadataTenantSchemaMapper mapper;

    @NacosValue("${dbType:mysql}")
    private String dbType;

    @NacosValue("${anonymous.tenant:}")
    private String anonymousTenant;


    @Override
    public String insert(MetadataTenantSchemaDo metadataTenantSchemaDo) {
        MetadataTenantSchemaPo po = MetadataTenantSchemaPo.fromEntity(MetadataTenantSchemaPo.class, metadataTenantSchemaDo);
        mapper.insert(po);
        return po.getId().toString();
    }

    @Override
    public void update(MetadataTenantSchemaDo metadataTenantSchemaDo) {
        MetadataTenantSchemaPo po = MetadataTenantSchemaPo.fromEntity(MetadataTenantSchemaPo.class, metadataTenantSchemaDo);
        mapper.updateById(po);
    }

    @Override
    public Option<MetadataTenantSchemaDo> load(String tenantId) {
        LambdaQueryWrapper<MetadataTenantSchemaPo> queryWrapper = new QueryWrapper<MetadataTenantSchemaPo>().lambda();
        queryWrapper.eq(MetadataTenantSchemaPo::getStatus, "success")
                .eq(MetadataTenantSchemaPo::getTenantId, tenantId)
                .orderByDesc(MetadataTenantSchemaPo::getVersion)
                .last("limit 1");
        val po = mapper.selectOne(queryWrapper);
        if (po == null) {
            return Option.none();
        }
        return Option.some(po.toEntity(MetadataTenantSchemaDo.class));
    }

}
