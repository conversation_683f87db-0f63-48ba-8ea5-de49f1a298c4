package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.paas.common.dto.UserDynamicConfig;
import com.caidaocloud.hrpaas.service.domain.dynamic.entity.DynamicColumnConfig;
import com.caidaocloud.hrpaas.service.domain.dynamic.repository.DynamicColumnConfigRepository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.DynamicColumnConfigPo;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.UserDynamicColumnConfigPo;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class DynamicColumnConfigRepositoryImpl implements DynamicColumnConfigRepository {
    @Autowired
    private DynamicColumnConfigMapper dynamicColumnConfigMapper;
    @Autowired
    private UserDynamicColumnConfigMapper userDynamicColumnConfigMapper;
    @Override
    public DynamicColumnConfig loadByCode(String code) {
        return dynamicColumnConfigMapper.selectList(new LambdaQueryWrapper<DynamicColumnConfigPo>()
                .eq(DynamicColumnConfigPo::getCode, code)).stream().findFirst().map(it->it.toEntity()).orElse(null);
    }

    @Override
    public void create(DynamicColumnConfig dynamicColumnConfig) {
        DynamicColumnConfigPo config = DynamicColumnConfigPo.fromEntity(dynamicColumnConfig);
        dynamicColumnConfigMapper.insert(config);
    }

    @Override
    public void update(DynamicColumnConfig dynamicColumnConfig) {
        DynamicColumnConfigPo config = DynamicColumnConfigPo.fromEntity(dynamicColumnConfig);
        dynamicColumnConfigMapper.updateById(config);
    }

    @Override
    public List<DynamicColumnConfig> list() {
        val list = dynamicColumnConfigMapper.selectList(new LambdaQueryWrapper<>());
        return DynamicColumnConfigPo.toEntityList(list);
    }

    @Override
    public UserDynamicConfig loadUser(String code, String userId) {
        return userDynamicColumnConfigMapper.selectList(new LambdaQueryWrapper<UserDynamicColumnConfigPo>()
                .eq(UserDynamicColumnConfigPo::getCode, code).eq(UserDynamicColumnConfigPo::getUserId, userId)).stream()
                .findFirst().map(it-> FastjsonUtil.toObject(it.getConfig(), UserDynamicConfig.class)).orElse(null);
    }

    @Override
    public void addUser(String code, String userId, UserDynamicConfig userConfig) {
        val config = new UserDynamicColumnConfigPo();
        config.setConfig(FastjsonUtil.toJson(userConfig));
        config.setCode(code);
        config.setUserId(userId);
        config.setId(SnowUtil.createId());
        userDynamicColumnConfigMapper.insert(config);
    }

    @Override
    public void updateUser(String code, String userId, UserDynamicConfig userConfig) {
        userDynamicColumnConfigMapper.update(null,
                new LambdaUpdateWrapper<UserDynamicColumnConfigPo>().set(UserDynamicColumnConfigPo::getConfig,
                        FastjsonUtil.toJson(userConfig)).eq(UserDynamicColumnConfigPo::getUserId, userId)
                        .eq(UserDynamicColumnConfigPo::getCode, code));
    }

    public Sequence<DynamicColumnConfig> loadByCodes(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return Sequences.empty();
        }
        val dataList = dynamicColumnConfigMapper.selectList(new LambdaQueryWrapper<DynamicColumnConfigPo>()
                .in(DynamicColumnConfigPo::getCode, codeList));
        if (CollectionUtils.isEmpty(dataList)) {
            return Sequences.empty();
        }
        val result = dataList.stream().map(it -> it.toEntity()).collect(Collectors.toList());
        return Sequences.sequence(result);
    }
}
