package com.caidaocloud.hrpaas.paas.common.feign;

import com.caidaocloud.hrpaas.paas.match.vo.display.DisplayPropertyVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(value = "${feign.rename.caidaocloud-hr-paas-service:caidaocloud-hr-paas-service}",
        fallback = DisplayPropertyFeignFallback.class,
        configuration = {FeignConfiguration.class},
        contextId = "displayPropertyFeign")
public interface IDisplayPropertyFeign {
    @GetMapping("/api/hrpaas/display/v1/property")
    Result<DisplayPropertyVo> getProperty();
}
