package com.caidaocloud.metadata.domain.entity;


import com.caidaocloud.metadata.domain.repository.IBaseRepository;
import com.caidaocloud.metadata.domain.repository.RepositoryHolder;

public abstract class BaseEntity<T extends IBaseRepository>{

    private String id;

    public String getId(){
        return id;
    }

    public void setId(String id){
        this.id = id;
    }

    protected T repository() {
        return (T) RepositoryHolder.getRepository(this.getClass());
    }

    protected static <D extends BaseEntity> IBaseRepository<D> repository(Class<D> domainClazz){
        return RepositoryHolder.getRepository(domainClazz);
    }

    public String insert(){
        this.setId(repository().insert(this));
        return id;
    }

    public void update(){
        repository().update(this);
    }


}