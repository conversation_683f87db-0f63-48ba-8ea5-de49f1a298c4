package com.caidaocloud.hrpaas.metadata.sdk.feign;

import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MdDataFeignFallback implements IMdDataFeign{
    @Override
    public Result insert(EntityDataDto data) {
        return Result.fail();
    }

    @Override
    public Result insertWithRelation(DataWithRelationDTO data) {
        return Result.fail();
    }

    @Override
    public Result update(EntityDataDto data) {
        return Result.fail();
    }

    @Override
    public Result updateWithRelation(DataWithRelationDTO data) {
        return Result.fail();
    }

    @Override
    public Result one(DataQueryDto query, long queryTime) {
        return Result.fail();
    }

    @Override
    public Result oneOrNull(DataQueryDto query, long queryTime) {
        return Result.fail();
    }

    @Override
    public Result filter(DataQueryDto query, long queryTime) {
        return Result.fail();
    }

    @Override
    public Result delete(String identifier, String bid, long startTime) {
        return Result.fail();
    }

    @Override
    public Result softDelete(String identifier, String bid, long startTime) {
        return Result.fail();
    }

    @Override
    public Result operateRelation(RelationOperationDto operation) {
        return Result.fail();
    }

    @Override
    public Result range(String identifier, String bid, long startTime, long endTime) {
        return Result.fail();
    }

    @Override
    public Result batchUpdate(DataBatchUpdateDto query, long queryTime) {
        return Result.fail();
    }

    @Override
    public Result batchDeleteWithoutRecord(BatchDeleteDto deleteDto) {
        return Result.fail();
    }

    @Override
    public Result batchInsertWithoutRecord(String identifier, List<EntityDataDto> dataList) {
        return Result.fail();
    }

    @Override
    public Result allJobGrade() {
        return Result.fail();
    }

    @Override
    public Result allJobGradeChannel() {
        return Result.fail();
    }

    @Override
    public Result count(DataQueryDto query, long queryTime) {
        return Result.fail();
    }

    @Override
    public Result max(MaxQueryDto query) {
        return Result.fail();
    }

    @Override
    public Result countByGroup(GroupedCountDto query) {
        return Result.fail();
    }

    @Override
    public Result indicateByGroup(GroupedIndicateDto query) {
        return Result.fail();
    }

    @Override
    public Result join(DataJoin dataJoin) {
        return Result.fail();
    }

    @Override
    public Result<List<EmpSimple>> condition(DataQueryDto query, Long queryTime) {
        return null;
    }
}
