package com.caidaocloud.hrpaas.metadata.sdk.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权限资源dto
 *
 * <AUTHOR>
 * @date 2022/8/22
 **/
@Data
@Accessors(chain = true)
public class AuthResourceDto {

    private String tenantId;

    /**
     * 资源编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 语言编码
     */
    private String lang;

    /**
     * 资源分类
     */
    private String category;

    /**
     * API链接
     */
    private String url;

    /**
     * 动作
     */
    private String resourceAction;

    /**
     * 扩展字段
     */
    private String extension;

    /**
     * 父级code
     */
    private String parentCode;

}
