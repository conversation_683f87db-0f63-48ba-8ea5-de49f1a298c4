package com.caidaocloud.hrpaas.service.application.form.factory;

import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.service.application.form.dto.WfRegistrySeqDto;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.workflow.dto.WfComponentValueDto;
import com.caidaocloud.workflow.dto.WfMetaSeqConditionDto;
import com.caidaocloud.workflow.enums.WfSeqConditionCallTypeEnum;
import com.caidaocloud.workflow.enums.WfSeqConditionOperatorEnum;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 流程类型转换工具类
 *
 * <AUTHOR>
 * @date 2022/11/4
 **/
@Slf4j
public class WorkflowEntityFactory {

    /**
     * 创建注册序列流dto
     *
     * @param seqDto
     * @param funCode
     * @param tenantId
     * @param address
     * @param def
     * @return
     */
    public static WfMetaSeqConditionDto generateWfMetaSeqConditionDto(WfRegistrySeqDto seqDto,
                                                                      String funCode,
                                                                      String tenantId, String address,
                                                                      FormPropDef def) {
        if (log.isDebugEnabled()) {
            log.debug("prepare generate WfMetaSeqConditionDto, parameters: seqDto={} funCode={} tenantId={} address={} def={}",
                    FastjsonUtil.toJson(seqDto), funCode, tenantId, address, FastjsonUtil.toJson(def));
        }
        var isIlledge = def == null || seqDto == null || StringUtils.isBlank(seqDto.getSeqCode()) ||
                StringUtils.isBlank(funCode) || StringUtils.isBlank(tenantId) ||
                StringUtils.isBlank(address) || seqDto.getComponent() == null;
        PreCheck.preCheckArgument(isIlledge, ErrorMessage.fromCode("form.workflow.generate.seq.parameter"));
        List<WfComponentValueDto> componentList = Lists.newArrayList();
        if (PropertyDataType.Enum.equals(def.getDataType()) && !CollectionUtils.isEmpty(def.getEnumDef())) {
            def.getEnumDef().forEach(e -> {
                componentList.add(new WfComponentValueDto(e.getDisplay(), e.getValue()));
            });
        }
        if (!CollectionUtils.isEmpty(seqDto.getComponentEnumValue())) {
            componentList.addAll(seqDto.getComponentEnumValue());
        }
        var finalComponentList = Sequences.sequence(componentList).unique(e -> e.getDisplay()).toList();
        var seqName = StringUtils.isNotBlank(seqDto.getSeqName()) ? seqDto.getSeqName() : def.getName();
        return new WfMetaSeqConditionDto(seqName,
                seqDto.getSeqCode(),
                Lists.newArrayList(funCode),
                "caidaocloud-hr-paas-service",
                address,
                WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
                tenantId,
                Lists.newArrayList(WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                        WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                        WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                        WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN),
                seqDto.getComponent(),
                finalComponentList,
                seqDto.getDataSource(),false);
    }

}
