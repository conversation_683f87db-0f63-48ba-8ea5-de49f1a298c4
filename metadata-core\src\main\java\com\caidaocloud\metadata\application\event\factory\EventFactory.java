package com.caidaocloud.metadata.application.event.factory;

import java.util.List;

import com.caidaocloud.metadata.application.event.publish.EntityPropertySyncEvent;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Sequences;

/**
 *
 * <AUTHOR>
 * @date 2024/3/11
 */
public class EventFactory {
	public static EntityPropertySyncEvent createEntityPropertySyncEvent(EntityDef entityDef) {
		List<PropertyDef> syncStandardProperty = Sequences.sequence(entityDef.getStandardProperties()).filter(PropertyDef::isSync)
				.toList();
		List<PropertyDef> syncCustomProperty = Sequences.sequence(entityDef.getCustomProperties()).filter(PropertyDef::isSync)
				.toList();
		return new EntityPropertySyncEvent(entityDef.getIdentifier(), SecurityUserUtil.getSecurityUserInfo()
				.getTenantId());
	}
}
