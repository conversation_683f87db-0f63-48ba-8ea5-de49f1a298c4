package com.caidaocloud.metadata.interfaces.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.metadata.application.service.EntityDataService;
import com.caidaocloud.metadata.application.service.LockService;
import com.caidaocloud.metadata.domain.entity.EntityData;
import com.caidaocloud.hrpaas.metadata.sdk.enums.DataQueryType;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.metadata.domain.entity.EntityRelation;
import com.caidaocloud.metadata.infrastructure.repository.transaction.PaasTransactionManager;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/${service.name.short:hrpaas}/v1/data")
@Api(value = "模型数据操作", tags = "模型数据操作--后端接口")
public class EntityDataApiController {

    @Autowired
    private Locker locker;

    @Autowired
    private LockService lockService;

    @Autowired
    private EntityDataService entityDataService;

    @Autowired
    private PaasTransactionManager transactionManager;

    @PostMapping
    @ApiOperation("新增数据")
    public Result<String> insert(@RequestBody EntityDataDto data) {
        return Result.ok(entityDataService.create(JsonEnhanceUtil.toObject(data, EntityData.class)));
    }

    @PostMapping("/withRelation")
    @ApiOperation("带关联属性，新增数据")
    public Result<String> insertWithRelation(@RequestBody DataWithRelationDTO data) {
        if (data.getData() == null) {
            throw new ServerException("param error, data is null");
        }
        return Result.ok(entityDataService.createWithRelation(JsonEnhanceUtil.toObject(data.getData(), EntityData.class), data.getOperations()));
    }

    @PostMapping(value = "/one")
    @ApiOperation("根据ID查询数据")
    public Result<EntityDataDto> one(@RequestBody DataQueryDto query, @RequestParam long queryTime) {
        if (!DataQueryType.ONE.equals(query.getType())) {
            throw new ServerException("错误的查询类型");
        }
        return Result.ok(JsonEnhanceUtil.toObject(entityDataService.load(query.getIdentifier(), query.getBid(), queryTime, query.getRelatedProperties(),query),
                EntityDataDto.class));
    }

    @PostMapping(path = "/one/:nonnull")
    @ApiOperation("根据ID查询数据, 为空返回null")
    public Result<EntityDataDto> oneOrNull(@RequestBody DataQueryDto query, @RequestParam long queryTime){
        if(!DataQueryType.ONE.equals(query.getType())){
            throw new ServerException("错误的查询类型");
        }
        return Result.ok(JsonEnhanceUtil.toObject(entityDataService.loadOrNull(query.getIdentifier(), query.getBid(), queryTime, query.getRelatedProperties(), query),
                EntityDataDto.class));
    }

    @PostMapping(value = "/filter")
    @ApiOperation("查询数据")
    public Result<PageResult<EntityDataDto>> filter(@RequestBody DataQueryDto query, @RequestParam long queryTime) {
        if (!DataQueryType.PAGE.equals(query.getType())) {
            throw new ServerException("错误的查询类型");
        }
        val page = entityDataService.page(query.getIdentifier(), DataFilter.fromJsonString(query.getFilter()), query.getPageNo(), query.getPageSize(), query.getRelatedProperties(), query.getSpecifyProperties(), query.isGroup(), query.getOrderBy(), queryTime, query);
        return Result.ok(JsonEnhanceUtil.toPage(page, EntityDataDto.class));
    }

    @GetMapping(value = "/range")
    public Result<List<EntityDataDto>> range(@RequestParam String identifier, @RequestParam String bid, @RequestParam long startTime, @RequestParam long endTime){
        val result = JsonEnhanceUtil.toObjects(entityDataService.range(identifier, bid, startTime, endTime), EntityDataDto.class);
        return Result.ok(result);
    }

    @PutMapping
    @ApiOperation("更新数据")
    @SneakyThrows
    public Result<Boolean> update(@RequestBody EntityDataDto data) {
        lockService.doWithLock(data.getIdentifier(), data.getBid(), ()->{
            entityDataService.update(JsonEnhanceUtil.toObject(data, EntityData.class));
        });
        return Result.ok();
    }

    @PutMapping("/withRelation")
    @ApiOperation("更新数据")
    @SneakyThrows
    public Result<Boolean> updateWithRelation(@RequestBody DataWithRelationDTO data) {
        if (data.getData() == null) {
            throw new ServerException("param error, data is null");
        }
        lockService.doWithLock(data.getData().getIdentifier(), data.getData().getBid(), ()->{
            entityDataService.updateWithRelation(JsonEnhanceUtil.toObject(data.getData(), EntityData.class), data.getOperations());
        });
        return Result.ok();
    }

    @DeleteMapping
    @ApiOperation("删除数据")
    @SneakyThrows
    public Result<Boolean> delete(@RequestParam("identifier") String identifier, @RequestParam("bid") String bid, @RequestParam("startTime") long startTime) {
        lockService.doWithLock(identifier, bid, ()->{
            entityDataService.delete(identifier, bid, startTime);
        });
        return Result.ok();
    }

    @DeleteMapping("/soft")
    @ApiOperation("软删除数据")
    @SneakyThrows
    public Result<Boolean> softDelete(@RequestParam("identifier") String identifier, @RequestParam("bid") String bid, @RequestParam("startTime") long startTime) {
        lockService.doWithLock(identifier, bid, ()->{
            entityDataService.softDelete(identifier, bid, startTime);
        });
        return Result.ok();
    }

    @PutMapping("/relation")
    @ApiOperation("修改关联")
    @SneakyThrows
    public Result<Boolean> updateRelation(@RequestBody RelationOperationDto operation) {
        lockService.doWithLock(operation.getIdentifier(), operation.getSourceId(), ()->{
            entityDataService.operateRelation(JsonEnhanceUtil.toObject(operation, EntityRelation.class), operation.getOperationType(), operation.getSourceId(), operation.getStartTime());
        });
        return Result.ok();
    }

    @PostMapping("/:batch")
    @ApiOperation("批量新增")
    public Result<Boolean> batchInsertWithoutRecord(@RequestParam("identifier") String identifier, @RequestBody List<EntityDataDto> dataList){
        entityDataService.batchInsertWithoutRecord(identifier, FastjsonUtil.convertList(dataList, EntityData.class));
        return Result.ok();
    }

    @DeleteMapping("/:batch")
    @ApiOperation("批量删除")
    public Result<Boolean> batchDeleteWithoutRecord(@RequestBody BatchDeleteDto deleteDto){
        entityDataService.batchDeleteWithoutRecord(deleteDto.getIdentifier(), DataFilter.fromJsonString(deleteDto.getFilter()));
        return Result.ok();
    }

    @GetMapping("/job/grade/all")
    public Result<List<EntityDataDto>> allJobGrade(){
        val result = entityDataService.listJobGrade(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        return Result.ok(FastjsonUtil.convertList(result, EntityDataDto.class));
    }

    @GetMapping("/job/grade/channel/all")
    public Result<List<EntityDataDto>> allJobGradeChannel(){
        val result = entityDataService.listJobGradeChannel(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        return Result.ok(FastjsonUtil.convertList(result, EntityDataDto.class));
    }

    @PostMapping(value = "/count")
    @ApiOperation("查询总量")
    public Result<Long> count(@RequestBody DataQueryDto query, @RequestParam long queryTime) {
        val count = entityDataService.count(query.getIdentifier(), DataFilter.fromJsonString(query.getFilter()), query.getRelatedProperties(), query.isGroup(), queryTime);
        return Result.ok(count);
    }

    @PostMapping(value = "/max")
    @ApiOperation("查询最大值")
    public Result<PageResult<EntityData>> max(@RequestBody MaxQueryDto query) {
        val max = entityDataService.max(query);
        return Result.ok(max);
    }

    @PostMapping(value = "/count/grouped")
    @ApiOperation("查询分组总量")
    public Result<List<Map<String, Object>>> countByGroup(@RequestBody GroupedCountDto query) {
        val count = entityDataService.countByGroup(query.getIdentifier(), DataFilter.fromJsonString(query.getFilter()), query.getBy(), query.getQueryTime());
        return Result.ok(count);
    }

    @PostMapping(value = "/indicate/grouped")
    @ApiOperation("查询分组统计")
    public Result<List<Map<String, Object>>> indicateByGroup(@RequestBody GroupedIndicateDto query) {
        val indicate = entityDataService.indicateByGroup(query.getIdentifier(), DataFilter.fromJsonString(query.getFilter()), query.getBy(), query.getQueryTime(), query.getIndicateType(), query.getIndicateProperty());
        return Result.ok(indicate);
    }

    @PostMapping(value = "/join")
    @ApiOperation("联表查询")
    public Result<PageResult<List<EntityDataDto>>> join(@RequestBody DataJoin dataJoin) {
        return Result.ok(entityDataService.join(dataJoin));
    }


    @GetMapping("/encrypt/searchable")
    public Result<String> searchableEncrypt(@RequestParam("str") String str){
        return Result.ok(entityDataService.searchableEncrypt(str));
    }

    @PostMapping("/encrypt/searchable/batch")
    public Result<List<String>> batchSearchableEncrypt(@RequestBody List<String> strList){
        return Result.ok(strList.stream().map(it->entityDataService
                .searchableEncrypt(it)).collect(Collectors.toList()));
    }
}
