package com.caidaocloud.hrpaas.metadata.sdk.filter;

import java.util.List;

import com.caidaocloud.dto.FilterFunction;
import com.caidaocloud.hrpaas.metadata.sdk.enums.ConditionIdentifier;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/7/26
 */
@Data
public class SpecifiedMultiDataFilter extends MultiDataFilter  implements FilterFunction {
	private ConditionIdentifier identifier;

	private SpecifiedMultiDataFilter() {
		super();
	}

	protected SpecifiedMultiDataFilter(String identifier, FilterOperator operator) {
		super(operator);
		this.identifier = ConditionIdentifier.getByIdentifier(identifier);
	}


	@Override
	public SpecifiedMultiDataFilter andEq(String property, String value) {
		getFilters().add(new SpecifiedSimpleDataFilter(FilterOperator.EQ, property, value, identifier));
		return this;
	}

	@Override
	public SpecifiedMultiDataFilter andNe(String property, String value) {
		getFilters().add(new SpecifiedSimpleDataFilter(FilterOperator.NE, property, value, identifier));
		return this;
	}

	@Override
	public SpecifiedMultiDataFilter andIn(String property, List<String> values) {
		getFilters().add(new SpecifiedSimpleDataFilter(FilterOperator.IN, property, values, identifier));
		return this;
	}

	@Override
	public SpecifiedMultiDataFilter andGe(String property, String value) {
		getFilters().add(new SpecifiedSimpleDataFilter(FilterOperator.GE, property, value, identifier));
		return this;
	}

	@Override
	public SpecifiedMultiDataFilter andGt(String property, String value) {
		getFilters().add(new SpecifiedSimpleDataFilter(FilterOperator.GT, property, value, identifier));
		return this;
	}

	@Override
	public SpecifiedMultiDataFilter andLe(String property, String value) {
		getFilters().add(new SpecifiedSimpleDataFilter(FilterOperator.LE, property, value, identifier));
		return this;
	}

	@Override
	public SpecifiedMultiDataFilter andLt(String property, String value) {
		getFilters().add(new SpecifiedSimpleDataFilter(FilterOperator.LT, property, value, identifier));
		return this;
	}

	@Override
	public SpecifiedMultiDataFilter andRegex(String property, String value) {
		getFilters().add(new SpecifiedSimpleDataFilter(FilterOperator.REGEX, property, value, identifier));
		return this;
	}


	public SpecifiedMultiDataFilter and(SpecifiedMultiDataFilter specifiedMultiDataFilter) {
		SpecifiedMultiDataFilter parent = DataFilter.specifiedAnd(identifier.identifier);
		parent.getFilters().add(this);
		parent.getFilters().add(specifiedMultiDataFilter);
		return parent;
	}

	public SpecifiedMultiDataFilter or(SpecifiedMultiDataFilter specifiedMultiDataFilter) {
		SpecifiedMultiDataFilter parent = DataFilter.specifiedOr(identifier.identifier);
		parent.getFilters().add(this);
		parent.getFilters().add(specifiedMultiDataFilter);
		return parent;
	}

}
