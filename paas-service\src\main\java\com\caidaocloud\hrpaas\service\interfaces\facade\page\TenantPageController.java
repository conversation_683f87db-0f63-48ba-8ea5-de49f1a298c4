package com.caidaocloud.hrpaas.service.interfaces.facade.page;

import com.caidaocloud.hrpaas.service.application.page.dto.TenantPageDetailDto;
import com.caidaocloud.hrpaas.service.application.page.dto.TenantPageDto;
import com.caidaocloud.hrpaas.service.application.page.service.TenantPageService;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.interfaces.vo.page.TenantPageDetailVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.page.TenantPageVo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/hrpaas/page/v1")
@Api(value = "/api/hrpaas/page/v1", description = "页面接口", tags = "页面接口")
public class TenantPageController {

    @Autowired
    private TenantPageService tenantPageService;

    @ApiOperation("新增页面")
    @PostMapping
    @LogRecordAnnotation(menu = "管理中心-乐高引擎-页面管理", category = "新增",
            success = "新增了{{#tenantPage.i18nName.default}}")
    public Result<Boolean> addPage(@RequestBody TenantPageDto tenantPage){
        tenantPageService.addPage(tenantPage);
        return Result.ok();
    }

    @ApiOperation("编辑页面")
    @PutMapping
    @LogRecordAnnotation(menu = "管理中心-乐高引擎-页面管理", category = "编辑",
            success = "编辑了{{#tenantPage.i18nName.default}}")
    public Result<Boolean> alterPage(@RequestBody TenantPageDto tenantPage){
        String id = tenantPage.getId();
        tenantPageService.alterPage(id, tenantPage);
        return Result.ok();
    }

    @ApiOperation("查看页面")
    @GetMapping("/view")
    public Result<TenantPageVo> viewPage(@RequestParam String pageId){
        return Result.ok(tenantPageService.viewPage(pageId));
    }

    @ApiOperation("删除页面")
    @DeleteMapping
    @LogRecordAnnotation(menu = "管理中心-乐高引擎-页面管理", category = "删除",
            success = "删除了{{#name}}")
    public Result<Boolean> deletePage(@RequestParam("id") String id){
        TenantPageVo pageVo = tenantPageService.viewPage(id);
        LogRecordContext.putVariable("name", pageVo.getName());
        tenantPageService.deletePage(id);
        return Result.ok();
    }

    @ApiOperation("获取所有页面")
    @GetMapping
    public Result<List<TenantPageVo>> tree(@RequestParam(value = "pageType", required = false)PageType pageType){
        return Result.ok(tenantPageService.tree(pageType));
    }

    @ApiOperation("获取所有页面(业务端使用)")
    @GetMapping("/application")
    public Result<List<TenantPageVo>> tree(){
        return Result.ok(tenantPageService.tree(null));
    }

    @ApiOperation("获取页面detail")
    @GetMapping("/detail")
    public Result<TenantPageDetailVo> pageDetail(@RequestParam String pageId){
        return Result.ok(tenantPageService.pageDetail(pageId));
    }

    @ApiOperation("修改页面detail")
    @PutMapping("/detail")
    public Result<Boolean> alterPageDetail(@RequestBody TenantPageDetailDto pageDetail){
        tenantPageService.alterDetail(pageDetail);
        return Result.ok();
    }

}
