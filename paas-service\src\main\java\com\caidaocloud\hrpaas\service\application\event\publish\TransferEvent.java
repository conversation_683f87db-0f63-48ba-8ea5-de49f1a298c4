package com.caidaocloud.hrpaas.service.application.event.publish;

import com.googlecode.totallylazy.Lists;
import lombok.Data;

import java.util.List;

@Data
public class TransferEvent extends FormEvent {

    private String changeCause;
    // 异动的变更数据
    private List<TransferData> changeList = Lists.list();

    @Data
    public static class TransferData{
        // 异动前
        private String before;
        // 异动后
        private String after;
        // 异动编码
        private String changeCode;
    }
}
