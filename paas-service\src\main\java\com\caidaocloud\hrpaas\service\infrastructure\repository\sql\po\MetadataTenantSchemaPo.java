package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataTenantSchemaDo;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.SchemaStatus;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-06-24
 */
@Data
@TableName("paas_metadata_tenant_schema")
public class MetadataTenantSchemaPo extends BasePo<MetadataTenantSchemaPo, MetadataTenantSchemaDo> {

    private String tenantId;

    /**
     * 文件夹目录
     */
    private Integer folder;

    /**
     * 版本
     */
    private Long version;

    /**
     * 脚本文件名称
     */
    private String scriptName;

    /**
     * 脚本描述
     */
    private String description;

    /**
     * 文件内容总条数
     */
    private Integer checksum;

    /**
     * 执行开始时间
     */
    private Long installedTime;

    /**
     * 执行完毕时间
     */
    private Long executionTime;

    /**
     * 执行状态
     */
    private SchemaStatus status;
}
