package com.caidaocloud.hrpaas.service.test;

import com.caidaocloud.hrpaas.service.application.rules.IRule;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.hrpaas.service.application.uiform.service.CheckRuleService;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.CheckRuleDto;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class IRuleTest {
    @Resource
    private CheckRuleService ruleService;

    @Test
    public void test(){
        String checkRuleConfig = "[{\"fieldProp\":\"name\"}]";
        List<IRule> rules = FastjsonUtil.toList(checkRuleConfig, IRule.class);
        log.info("rules=", rules);

        Map ruleMap = ruleService.getRuleMap(checkRuleConfig, CheckRuleDto.class);
        log.info("ruleMap=", ruleMap);
    }
}
