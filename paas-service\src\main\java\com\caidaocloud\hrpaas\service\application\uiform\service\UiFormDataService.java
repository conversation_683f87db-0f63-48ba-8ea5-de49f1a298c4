package com.caidaocloud.hrpaas.service.application.uiform.service;

import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.OpEnum;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.EntityRelationType;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataValueFunction;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.query.*;
import com.caidaocloud.hrpaas.metadata.sdk.util.DataValueUtil;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.CheckRuleDto;
import com.caidaocloud.metadata.application.service.EntityDataService;
import com.caidaocloud.hrpaas.service.application.uiform.dto.UiFormDataEventDto;
import com.caidaocloud.hrpaas.service.application.uiform.dto.UiFormDataSimpleDto;
import com.caidaocloud.hrpaas.service.application.uiform.dto.UiFormDto;
import com.caidaocloud.hrpaas.service.application.uiform.dto.UiFormPropertyDef;
import com.caidaocloud.hrpaas.service.application.uiform.event.publish.UiFormDataPushEvent;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.WorkflowAction;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormDataDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormDataQueryDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormDataValueDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormDataWorkflowDto;
import com.caidaocloud.metadata.domain.entity.*;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequence;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-07-12
 */
@Slf4j
@Service
public class UiFormDataService {
    private final static String UIFORM_IDENTIFIER_FORMAT = "entity.hrpaas.UiForm.%s",
            UIFORM_RELATION_FORMAT_ID = "%s.bid", UIFORM_RELATION_FORMAT_CONDITION = "%s.%s";

    @Resource
    private ISessionService sessionService;
    @Resource
    private UiFormService uiFormService;
    @Resource
    private UiFormDataPushEvent uiFormDataPushEvent;
    @Resource
    private CheckRuleService checkRuleService;
    @Resource
    private EntityDataService entityDataService;

    private String getIdentifier(String formId){
        return String.format(UIFORM_IDENTIFIER_FORMAT, formId);
    }

    /**
     * 编辑数据
     * @param uiFormDataDto
     */
    @Transactional
    public UiFormDataDto putUiFormData(UiFormDataDto uiFormDataDto){
        UiFormDto uiFormDto = uiFormService.getUiFormDetail(uiFormDataDto.getFormId());
        PreCheck.preCheckArgument(null == uiFormDto || StringUtil.isEmpty(uiFormDto.getId()), "所属表单不存在!");
        String identifier = StringUtil.isNotEmpty(uiFormDto.getJoinModel()) ? uiFormDto.getJoinModel() : getIdentifier(uiFormDataDto.getFormId());

        return saveFormData(uiFormDataDto, uiFormDto, identifier);
    }

    private UiFormDataDto saveFormData(UiFormDataDto uiFormDataDto, UiFormDto uiFormDto, String identifier){
        // 查询模型定义
        EntityDef tabDef = EntityDef.load(identifier).getOrThrow(new ServerException("表单所属模型不存在！"));
        List<UiFormPropertyDef> propList = ObjectConverter.convertList(tabDef.fetchProperties(), UiFormPropertyDef.class);

        Map<String, UiFormPropertyDef> fieldMap = propList.stream().collect(Collectors.toMap(UiFormPropertyDef::getProperty, prop -> prop));

        // 加载关联模型
        loadRelationModel(identifier, fieldMap);

        Map<String, List> fieldRuleMap = checkRuleService.getRuleMap(uiFormDto.getCheckRuleConfig(), CheckRuleDto.class);

        DataSimple dataSimple = new UiFormDataSimpleDto();
        dataSimple.setIdentifier(identifier);
        // 数据体的 k/v
        NestPropertyValue npv = dataSimple.getProperties();
        int size = uiFormDataDto.getDataVals().size();
        List<UiFormDataValueDto<Object>> relationList = new ArrayList<>(size);
        List<DataValueFunction> embeddedList = new ArrayList<>(size);
        uiFormDataDto.getDataVals().forEach(dataValue -> {
            // 处理高级字段
            String dVal = doHighLevelField(dataValue, relationList, npv, fieldMap.get(dataValue.loadDataProp()), embeddedList);
            // 字段校验
            checkRuleService.checkRuleExecute(fieldRuleMap.get(dataValue.loadDataProp()), dVal);
        });
        dataSimple.setProperties(npv);

        UserInfo userInfo = sessionService.getUserInfo();
        Long userId = null;
        Long staffId = null;
        if(null != userInfo){
            userId = userInfo.getUserId();
            dataSimple.setUpdateBy("" + userId);
            staffId = userInfo.getStaffId();
        }
        dataSimple.setUpdateTime(System.currentTimeMillis());

        if(StringUtil.isEmpty(uiFormDataDto.getBid())){
            // 新增逻辑
            dataSimple.setCreateBy(dataSimple.getUpdateBy());
            dataSimple.setCreateTime(dataSimple.getUpdateTime());
            String bid = DataInsert.identifier(identifier).insert(dataSimple);
            uiFormDataDto.setBid(bid);

            // 发送 DataEvent 事件
            pushUiFormDataEvent(uiFormDto, uiFormDataDto, userId, staffId);
            ((UiFormDataSimpleDto) dataSimple).setWorkflowAction(WorkflowAction.apply);
            dataSimple.setBid(bid);
            // 更新数据的流程状态
            DataUpdate.identifier(identifier).update(dataSimple);

            // 处理关联模型
            replaceRelationList(identifier, bid, relationList);

            // 处理内嵌模型
            saveEmbeddedData(embeddedList, bid);

            return uiFormDataDto;
        }

        // 已经发起了流程、或挂载了工作流的数据不能进行修改
        if(StringUtil.isNotEmpty(uiFormDto.getFunctionType())){
            throw new ServerException("当前数据不支持修改!");
        }

        // 修改数据
        dataSimple.setBid(uiFormDataDto.getBid());
        DataUpdate.identifier(identifier).update(dataSimple);

        // 处理关联模型
        replaceRelationList(identifier, uiFormDataDto.getBid(), relationList);

        // 处理内嵌模型
        saveEmbeddedData(embeddedList, uiFormDataDto.getBid());

        return uiFormDataDto;
    }

    private void loadRelationModel(String identifier, Map<String, UiFormPropertyDef> fieldMap){
        Sequence<EntityRelationDef> defSequence = EntityDef.loadRelationsByIdentifier(identifier);
        if(defSequence.isEmpty()){
            return;
        }

        defSequence.forEach(entityRelationDef -> {
            UiFormPropertyDef pDef = new UiFormPropertyDef();
            pDef.setProperty(entityRelationDef.getProperty());
            setPropertyDefDataType(pDef, entityRelationDef);
            pDef.setSource(entityRelationDef.getIdentifier());
            pDef.setTarget(entityRelationDef.getRelatedIdentifier());
            fieldMap.put(entityRelationDef.getProperty(), pDef);
        });
    }

    /**
     * 设置关联模型的DataType
     * @param pDef
     * @param erd
     */
    private void setPropertyDefDataType(PropertyDef pDef, EntityRelationDef erd){
        // 判断是通用关联，还是内嵌模型
        pDef.setDataType(erd.isEmbedded() ? PropertyDataType.Embedded : PropertyDataType.Relation);
    }

    private void saveEmbeddedData(List<DataValueFunction> embeddedList, String dataId){
        if(embeddedList.isEmpty()){
            return;
        }

        val uiFormDto = new UiFormDto();
        for(DataValueFunction dataValue : embeddedList){
            List<UiFormDataDto> formDataDtoList = FastjsonUtil.toList(FastjsonUtil.toJson(dataValue.loadDataValue()), UiFormDataDto.class);
            if(null == formDataDtoList || formDataDtoList.isEmpty()){
                deleteRelationData(dataValue.loadSourceId(), dataValue.loadTargetId(), dataValue.loadDataProp() , dataId, null);
                RelationOperator.property(dataValue.loadSourceId(), dataValue.loadDataProp()).deleteRelationAll(dataId);
                continue;
            }

            List<String> dataIdList = new ArrayList<>(formDataDtoList.size());
            formDataDtoList.forEach(uiFormDataDto -> {
                uiFormDataDto = saveFormData(uiFormDataDto, uiFormDto, dataValue.loadTargetId());
                dataIdList.add(uiFormDataDto.getBid());
            });

            // 添加关联关系
            deleteRelationData(dataValue.loadSourceId(), dataValue.loadTargetId(), dataValue.loadDataProp() , dataId, dataIdList);
            RelationOperator.property(dataValue.loadSourceId(), dataValue.loadDataProp()).replaceAllRelations(dataId, dataIdList);
        }
    }

    /**
     * 删除关联模型数据
     * @param identifier
     * @param relatedIdentifier
     * @param property
     * @param dataId
     * @param excludeIds
     */
    private void deleteRelationData(String identifier, String relatedIdentifier, String property, String dataId, List<String> excludeIds){
        EntityRelation entityRelation = selectEntityRelation(identifier, dataId, property );
        if(entityRelation == null || entityRelation.getTargetIds().isEmpty()){
            return;
        }

        List<String> delIds = entityRelation.getTargetIds();
        if(CollectionUtils.isNotEmpty(excludeIds)){
            delIds.removeAll(excludeIds);
        }
        delIds.forEach(id -> DataDelete.identifier(relatedIdentifier).delete(id));
    }

    /**
     * 查询关联关系数据
     * @param identifier
     * @param id
     * @param property
     * @return
     */
    private EntityRelation selectEntityRelation(String identifier, String id, String property){
        Option<EntityData> masterData = EntityData.load(identifier, id, System.currentTimeMillis());
        if(masterData.isEmpty()){
            return null;
        }
        Option<EntityRelation> relationData = masterData.get().filterRelation(property);
        return relationData.getOrNull();
    }

    private String doHighLevelField(DataValueFunction dataValue, List relationList, NestPropertyValue npv, UiFormPropertyDef propertyDef, List embeddedList){
        PropertyDataType pDataType;
        String dataVal = "";
        if(null == propertyDef || null == (pDataType = propertyDef.getDataType())){
            return dataVal;
        }

        switch (pDataType) {
            case PID:
                // 树组件
                dataVal = FastjsonUtil.toJson(dataValue.loadDataValue());
                npv.add(dataValue.loadDataProp(), dataVal);
                break;
            case Dict:
                // 字典
                dataVal = objToString(dataValue.loadDataValue());
                npv.add(dataValue.loadDataProp(), dataVal);
                break;
            case Relation:
                // 关联属性
                relationList.add(dataValue);
                dataVal = objToString(dataValue.loadDataValue());
                npv.add(dataValue.loadDataProp(), dataVal);
                break;
            case Number:
                dataVal = objToString(dataValue.loadDataValue());
                npv.add(dataValue.loadDataProp(), dataVal);
                break;
            case Embedded:
                // 内嵌模型的字段处理
                dataValue.fitSourceId(propertyDef.getSource());
                dataValue.fitTargetId(propertyDef.getTarget());
                embeddedList.add(dataValue);
                break;
            default:
                dataVal = objToString(dataValue.loadDataValue());
                npv.add(dataValue.loadDataProp(), dataVal);
                break;
        }

        return dataVal;
    }

    private String objToString(Object obj){
        if(null == obj){
            return null;
        }

        if(obj instanceof Map){
            return FastjsonUtil.toJson(obj);
        }

        if(obj instanceof List){
            List list = (List) obj;
            return StringUtils.join(list.toArray(new Object[list.size()]),",");
        }
        return obj.toString();
    }

    /**
     * 替换当前模型的关联模型数据
     * @param identifier 当前模型
     * @param dataId 当前模型的数据 id
     * @param relationList 需要替换的关联模型数据
     */
    public void replaceRelationList(String identifier, String dataId, List<UiFormDataValueDto<Object>> relationList){
        if(relationList.isEmpty()){
            return;
        }
        relationList.forEach(dataValDto -> {
            List<String> targetIds;
            Object valueObj = dataValDto.getValue();
            if(valueObj instanceof List){
                targetIds = new ArrayList<>();
                for (Object o : (List) valueObj) {
                    targetIds.add(String.class.cast(o));
                }
            }else {
                targetIds = Lists.list(objToString(dataValDto.getValue()));
            }

            targetIds = targetIds.stream().filter(targetId -> StringUtil.isNotEmpty(targetId)).collect(Collectors.toList());

            RelationOperator.property(identifier, dataValDto.getProp()).replaceAllRelations(dataId, targetIds);
        });
    }

    private void pushUiFormDataEvent(UiFormDto uiFormDto, UiFormDataDto uiFormDataDto, Long userId, Long empId){
        if(StringUtil.isEmpty(uiFormDto.getFunctionType())){
            log.info("UiForm not bind workflow, UiFormDto=[{}]", FastjsonUtil.toJson(uiFormDto));
            return;
        }

        UiFormDataEventDto eventDto = UiFormDataEventDto.builder().
                formId(uiFormDataDto.getFormId()).
                formDataId(uiFormDataDto.getBid()).
                functionType(uiFormDto.getFunctionType()).
                tenantId(uiFormDto.getTenantId())
                .userId(userId)
                /**
                 * 默认发起新流程
                 */
                .action(WorkflowAction.apply)
                .empId(empId).build();

        uiFormDataPushEvent.publish(FastjsonUtil.toJson(eventDto));
    }

    public UiFormDataWorkflowDto getUiFormDataWorkflowDetail(String functionType, String dataId){
        UiFormDto uiFormDto = uiFormService.getUiFormDetailByFunType(functionType);
        PreCheck.preCheckArgument(null == uiFormDto || StringUtil.isEmpty(uiFormDto.getId()), "流程表单不存在!");

        String identifier = StringUtil.isNotEmpty(uiFormDto.getJoinModel()) ? uiFormDto.getJoinModel() : getIdentifier(uiFormDto.getId());
        UiFormDataSimpleDto dataSimple = DataQuery.identifier(identifier).queryInvisible().one(dataId, UiFormDataSimpleDto.class);
        UiFormDataWorkflowDto uiFormDataDto = new UiFormDataWorkflowDto();
        uiFormDataDto.setFunctionType(functionType);
        uiFormDataDto.setBid(dataId);
        uiFormDataDto.setFormId(uiFormDto.getId());
        uiFormDataDto.setWorkflowAction(dataSimple.getWorkflowAction());

        uiFormDataDto.setDataVals(JsonEnhanceUtil.toObjects(convertDataVal(dataSimple), UiFormDataValueDto.class));
        return uiFormDataDto;
    }

    private List<DataValueFunction> convertDataVal(DataSimple dataSimple){
        if(null == dataSimple.getProperties()){
            return null;
        }

        List<DataValueFunction> dataVals = new ArrayList<>(100);
        dataSimple.getProperties().forEach((dataField, dataVal) -> {
            UiFormDataValueDto dataValueDto = new UiFormDataValueDto();
            dataValueDto.setProp(dataField);
            if(null != dataVal){
                if(dataVal instanceof SimplePropertyValue){
                    dataValueDto.setValue(((SimplePropertyValue) dataVal).getValue());
                }else if(dataVal instanceof PropertyValue){
                    dataValueDto.setValue(FastjsonUtil.toJson(dataVal));
                }
            }

            dataVals.add(dataValueDto);
        });

        return dataVals;
    }

    @Transactional
    public void removeUiFormData(String formId, String dataId){
        UiFormDto uiFormDto = uiFormService.getUiFormDetail(formId);
        PreCheck.preCheckArgument(null == uiFormDto || StringUtil.isEmpty(uiFormDto.getId()), "所属表单不存在!");

        // 已经发起了流程、或挂载了工作流的数据不能进行删除
        if(StringUtil.isNotEmpty(uiFormDto.getFunctionType())){
            throw new ServerException("当前数据不支持删除!");
        }

        String identifier = StringUtil.isNotEmpty(uiFormDto.getJoinModel()) ? uiFormDto.getJoinModel() : getIdentifier(formId);
        DataDelete.identifier(identifier).delete(dataId);
    }

    @Transactional
    public void revokeUiFormData(String formId, String dataId){
        UiFormDto uiFormDto = uiFormService.getUiFormDetail(formId);
        PreCheck.preCheckArgument(null == uiFormDto || StringUtil.isEmpty(uiFormDto.getId()), "所属表单不存在!");

        UserInfo userInfo = sessionService.getUserInfo();
        Long userId = userInfo.getUserId(), empId = userInfo.getStaffId();
        UiFormDataEventDto eventDto = UiFormDataEventDto.builder().
                formId(formId).
                formDataId(dataId).
                functionType(uiFormDto.getFunctionType()).
                tenantId(uiFormDto.getTenantId())
                .userId(userId)
                /**
                 * 发起人撤销流程
                 */
                .action(WorkflowAction.revoke)
                .empId(empId).build();

        uiFormDataPushEvent.publish(FastjsonUtil.toJson(eventDto));

        String identifier = StringUtil.isNotEmpty(uiFormDto.getJoinModel()) ? uiFormDto.getJoinModel() : getIdentifier(formId);
        DataSimple dataSimple = new UiFormDataSimpleDto();
        dataSimple.setIdentifier(identifier);
        ((UiFormDataSimpleDto) dataSimple).setWorkflowAction(WorkflowAction.revoke);
        DataUpdate.identifier(identifier).update(dataSimple);
    }

    public PageResult getUiFormDataList(UiFormDataQueryDto queryDto, boolean isNext){
        UiFormDto dbUiForm = uiFormService.getUiFormDetail(queryDto.getFormId());
        PreCheck.preCheckArgument(null == dbUiForm || StringUtil.isEmpty(dbUiForm.getBid()), "所属表单不存在");

        String identifier = StringUtil.isBlank(dbUiForm.getJoinModel()) ? getIdentifier(dbUiForm.getBid()) : dbUiForm.getJoinModel();
        return queryUiFormData(identifier, queryDto, isNext);
    }

    private PageResult queryUiFormData(String identifier, UiFormDataQueryDto queryDto, boolean isNext){
        Map<String, EntityRelationDef> defMap = new HashMap<>();
        PageResult result = queryPageResult(queryDto, identifier, defMap);

        List<UiFormDto> dataList= null;
        if(null == result || null == (dataList = result.getItems()) || dataList.isEmpty()){
            return result;
        }

        List<UiFormDataDto> itemList = dataList.stream().map(ufd -> {
            UiFormDataDto uiFormData = null;
            NestPropertyValue npv = null;
            if(null != ufd && null != (npv = ufd.getProperties()) && !npv.isEmpty()){
                //
                Map<String, Map> joinValues = new HashMap<>();
                List<DataValueFunction<Object>> dataVals = new ArrayList(npv.size());
                npv.forEach((dField, dataVal) -> {
                    UiFormDataValueDto<Object> dataValue = new UiFormDataValueDto();
                    String [] fields = dField.split("\\.");
                    if(isNext && defMap.containsKey(fields[0]) && dField.equals(fields[0] + ".bid")){
                        // 处理内嵌模型
                        loadEmbeddedModelData(defMap, fields[0], dataValue, dataVal);
                    } else {
                        dataValue.setProp(dField);
                        // 组装数据
                        DataValueUtil.loadDataValue(dataValue, dataVal, joinValues, dataVals);
                    }

                    dataVals.add(dataValue);
                });

                if(!joinValues.isEmpty()){
                    joinValues.forEach((joinKey, joinVal) -> {
                        UiFormDataValueDto dataValue = new UiFormDataValueDto();
                        dataValue.setProp(joinKey);
                        dataValue.setValue(joinVal);
                        dataVals.add(dataValue);
                    });
                }

                uiFormData = new UiFormDataDto();
                uiFormData.setDataVals(JsonEnhanceUtil.toObjects(dataVals, UiFormDataValueDto.class));
                uiFormData.setFormId(queryDto.getFormId());
                uiFormData.setBid(ufd.getBid());
            }

            return uiFormData;
        }).collect(Collectors.toList());
        result.setItems(itemList);

        return result;
    }

    private void loadEmbeddedModelData(Map<String, EntityRelationDef> defMap, String dField, UiFormDataValueDto<Object> dataValue, PropertyValue dataVal){
        if(null == dataVal){
            return;
        }

        EntityRelationDef def = null;
        if(defMap.isEmpty() || null == (def = defMap.get(dField))){
            return;
        }

        SimplePropertyValue pValue = (SimplePropertyValue) dataVal;
        List<String> filterList = pValue.getArrayValues();
        UiFormDataQueryDto queryDto = new UiFormDataQueryDto();
        if(null != filterList && !filterList.isEmpty()){
            FilterElement filterElement = new FilterElement();
            filterElement.setOp(OpEnum.in);
            filterElement.setProp("bid");
            filterElement.setValue(filterList.stream().collect(Collectors.joining(",")));
            queryDto.setFilters(Lists.list(filterElement));
        }

        String identifier = def.getRelatedIdentifier();
        queryDto.setPageNo(1);
        // todo pageSize 单次查询最大值对应的常量值暂未确定放到哪个目录文件中，目前先在这边写固定值
        queryDto.setPageSize(5000);
        PageResult pageResult = queryUiFormData(identifier, queryDto, true);
        List dataList = null;
        if(null == pageResult || null == (dataList = pageResult.getItems()) || dataList.isEmpty()){
            return;
        }

        dataValue.setProp(dField);
        dataValue.setValue(dataList);
    }

    private PageResult queryPageResult(UiFormDataQueryDto queryDto, String identifier, Map<String, EntityRelationDef> defMap){
        String tenantId = sessionService.getTenantId();
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId);

        dataFilter = (DataFilter) queryDto.doDataFilter(queryDto.getFilters(), dataFilter);

        DataQuery query = DataQuery.identifier(identifier);
        // 查询关联模型
        assembleQueryRelation(identifier, query, defMap);

        if(StringUtil.isNotEmpty(queryDto.getDataId())){
            dataFilter = dataFilter.andEq("bid", queryDto.getDataId());
            queryDto.setPageNo(1);
            queryDto.setPageSize(1);
        }

        PageResult result = query.decrypt().specifyLanguage().queryInvisible()
                .limit(queryDto.getPageSize(), queryDto.getPageNo()).filter(dataFilter, UiFormDto.class);

        return result;
    }

    /**
     * 用于查询模型关联关系时，组装查询条件
     * @param identifier 相当于主表
     * @param dataQuery 组装查询条件
     */
    public void assembleQueryRelation(String identifier, DataQuery dataQuery, Map<String, EntityRelationDef> defMap){
        Sequence<EntityRelationDef> defSequence = EntityDef.loadRelationsByIdentifier(identifier);
        // 如果当前模型没有关联关系，则直接返回
        if(defSequence.isEmpty()){
            return;
        }

        Map<String, String> map = new HashMap();
        List<String> joinTableList = new ArrayList<>();
        defSequence.forEach(entityRelationDef -> {
            joinTableList.add(entityRelationDef.getRelatedIdentifier());
            map.put(entityRelationDef.getRelatedIdentifier(), entityRelationDef.getProperty());

            if(EntityRelationType.EMBEDDED.equals(entityRelationDef.getRelationType())){
                // 内嵌模型
                defMap.put(entityRelationDef.getProperty(), entityRelationDef);
            }
        });

        if(joinTableList.isEmpty()){
            return;
        }

        // 查询所有关联表的对应模型
        List<EntityDef> list = EntityDef.getListByIdentifier(joinTableList, joinTableList.size());
        if(null == list || list.isEmpty()){
            return;
        }

        List<String> queryList = new ArrayList(list.size());
        list.forEach(entityDef -> {
            // 根据 identifier 获取关联关系对应的 property，相当于获取表的别名
            String alias = map.get(entityDef.getIdentifier());
            queryList.add(String.format(UIFORM_RELATION_FORMAT_ID, alias));
            queryList.add(String.format(UIFORM_RELATION_FORMAT_CONDITION, alias, entityDef.getLabel()));
        });

        // 组装查询条件
        dataQuery.queryRelatedProperties(queryList.toArray(new String[0]));
    }

    public List<TreeData<LabelData>> getDataTree(String identifier, long queryTime) {
        return DataQuery.identifier(identifier).tree(LabelData.class, queryTime);
    }

    public List<LabelData> getProvince() {
        val result = DataQuery.identifier("entity.hrpaas.Province").limit(5000,1)
                .filter(DataFilter.eq("identifier", "entity.hrpaas.Province"), LabelData.class, System.currentTimeMillis())
                .getItems();
        for(LabelData data : result){
            data.getProperties().clear();
        }
        return result;
    }

    public List<LabelData> getCity(String provinceId) {
        val result = DataQuery.identifier("entity.hrpaas.City").limit(5000,1).queryRelatedProperties("province.name")
                .filter(DataFilter.eq("province.bid", provinceId), LabelData.class, System.currentTimeMillis())
                .getItems();
        for(LabelData data : result){
            data.getProperties().clear();
        }
        return result;
    }

    public List<ProvinceCity> getCityAll(String tenantId) {
        val result = DataQuery.identifier("entity.hrpaas.City")
                .limit(5000, 1)
                .queryRelatedProperties("province.name", "province.bid")
                .filter(DataFilter.eq("tenantId", tenantId), LabelData.class, System.currentTimeMillis())
                .getItems();
        List<ProvinceCity> provinceCityList = new ArrayList<>();
        for (LabelData data : result) {
            ProvinceCity provinceCity = new ProvinceCity();
            provinceCity.setCityId(data.getBid());
            provinceCity.setCityName(data.getLabel());

            SimplePropertyValue provinceName = (SimplePropertyValue) data.getProperties().get("province.name");
            provinceCity.setProvinceName(provinceName.getArrayValues().get(0));
            SimplePropertyValue provinceId = (SimplePropertyValue) data.getProperties().get("province.bid");
            provinceCity.setProvinceId(provinceId.getArrayValues().get(0));
            provinceCityList.add(provinceCity);
        }
        return provinceCityList;
    }
}
