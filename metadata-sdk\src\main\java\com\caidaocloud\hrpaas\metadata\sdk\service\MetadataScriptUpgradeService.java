package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMetadataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SchemaDto;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.ResourceUtils;

import javax.annotation.PostConstruct;
import java.io.*;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class MetadataScriptUpgradeService {
    @Value("${spring.application.name:sdk}")
    private String applicationName;

    @Value("${caidaocloud.hrpaas.upgradeSchema:true}")
    private boolean appUpgrade;

    @javax.annotation.Resource
    private IMetadataFeign metadataFeign;

    @PostConstruct
    public void initSchema() throws Exception{
        log.info("sdk init system schema start .............. appUpgrade = {}", appUpgrade);

        //upgradeHandler();

        log.info("sdk init system schema end ..............");
    }

    private void upgradeHandler() throws Exception {
        if(!appUpgrade){
            return;
        }

        Resource [] resources = null;
        try {
            resources = new PathMatchingResourcePatternResolver().getResources(ResourceUtils.CLASSPATH_URL_PREFIX + "metadata/script/**/*.json");
            // 加载 canonicalPath 目录下的所有 .json 文件
            log.info("Load all [.json] files in the canonicalpath directory path = {}", resources);
        } catch (FileNotFoundException e){
            log.warn("not found classpath:metadata/script catalogue, End script upgrade check ...... {}", e.getMessage());
            return;
        }

        if(null == resources || resources.length <= 0){
            log.warn("not found schema file ...... ");
            return;
        }

        syncSchema(resources);
    }

    public void syncSchema(Resource [] resources) throws IOException{
        String jsonStr = null;
        List<SchemaDto> schemaList = new ArrayList<>();
        SchemaDto schemaDto = null;
        for (Resource jsonFileResource : resources) {
            jsonStr = readFileToJson(jsonFileResource);
            if(StringUtil.isEmpty(jsonStr)){
                throw new ServerException("Script file cannot be empty.");
            }

            schemaDto = new SchemaDto();
            schemaDto.setSchemaInfo(jsonStr);
            schemaDto.setFolder(getFolder(jsonFileResource.getURL().getPath()));
            schemaDto.setSchemaModel(applicationName);
            schemaDto.setSchemaName(jsonFileResource.getFilename());
            schemaList.add(schemaDto);
        }

        if(isPassService()){
            // 直接升级
            SpringUtil.getBean(SchemaUpgradeService.class).upgrade(schemaList);
            return;
        }

        // other 通过 fegin 升级
        Result result = null;
        try {
            result = metadataFeign.upgrade(schemaList);
        } catch (Exception e){
            log.error("Feign Client Call exception,{}", e.getMessage(), e);
        }

        if(null == result || !result.isSuccess()){
            log.error("Upgrade [{}] script failed.", applicationName);
            throw new ServerException("Upgrade script failed");
        }
    }

    private String readFileToJson(Resource resource){
        try (InputStreamReader isr = new InputStreamReader(resource.getInputStream());
             BufferedReader br = new BufferedReader(isr)) {
            String line = br.readLine();
            StringBuilder sb = new StringBuilder();
            while (line != null) {
                sb.append(line);
                sb.append("\r\n");
                line = br.readLine();
            }

            return sb.toString();
        } catch (IOException e) {
            log.error("readFileToJson err,{}", e.getMessage(), e);
            throw new ServerException("fail to read file, Please check script file.");
        }
    }

    private List findFile(File dir) throws IOException {
        List result = new ArrayList();
        File[] dirFiles = dir.listFiles();
        for(File file : dirFiles){
            if(!file.isFile()){
                result.addAll(findFile(file));
            }

            // 查找指定的文件
            if(file.isFile() && file.getAbsolutePath().endsWith(".json") ){
                result.add(file);
            }
        }

        return result;
    }

    private Integer getFolder(String path){
        path = path.substring(path.indexOf("script/") + 7, path.indexOf("/V"));
        return Integer.parseInt(path);
    }

    private boolean isPassService(){
        return StringUtil.isNotEmpty(applicationName) && "caidaocloud-hr-paas-service".equals(applicationName);
    }
}
