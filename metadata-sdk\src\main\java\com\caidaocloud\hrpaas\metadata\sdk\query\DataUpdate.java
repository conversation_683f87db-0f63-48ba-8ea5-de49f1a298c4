package com.caidaocloud.hrpaas.metadata.sdk.query;

import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.enums.RelationOperationType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.service.DataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Data
public class DataUpdate {

    private String identifier;

    private List<RelationOperationDto> relationList;

    private boolean isMasterDataQuery = false;

    private DataUpdate() {

    }

    public DataUpdate setMasterDataQuery(boolean masterDataQuery) {
        isMasterDataQuery = masterDataQuery;
        return this;
    }

    protected DataUpdate(String identifier){
        this.identifier = identifier;
        this.relationList = Lists.newArrayList();
    }

    private void updateSlaves(String masterBid, DataTable dataTable, String slaveIdentifier, long dataStartTime, DataTable existed){
        existed.getDataList().stream().filter(old -> !dataTable.getDataList().stream()
                        .map(it -> it.getBid()).collect(Collectors.toList()).contains(old.getBid()))
                .forEach(it ->
                        DataDelete.identifier(slaveIdentifier).setMasterDataQuery(isMasterDataQuery)
                                .delete(it.getBid(), dataStartTime)
                );
        dataTable.getDataList().forEach(dataSlave -> {
            dataSlave.setMasterBid(masterBid);
            dataSlave.setDataStartTime(dataStartTime);
            if(StringUtils.isNotEmpty(dataSlave.getBid()) && existed.getDataList().stream()
                    .map(it->it.getBid()).collect(Collectors.toList()).contains(dataSlave.getBid())){
                dataSlave.setUpdateTime(System.currentTimeMillis());
                DataUpdate.identifier(slaveIdentifier).setMasterDataQuery(isMasterDataQuery).update(dataSlave);
            }else{
                dataSlave.setCreateTime(System.currentTimeMillis());
                dataSlave.setUpdateTime(dataSlave.getCreateTime());
                DataInsert.identifier(slaveIdentifier).setMasterDataQuery(isMasterDataQuery).insert(dataSlave);
            }
        });
    }

    public static DataUpdate identifier(String identifier) {
        DataUpdate request = new DataUpdate();
        request.identifier = identifier;
        request.relationList = Lists.newArrayList();
        return request;
    }

    public <T extends DataSimple> void update(T data) {
        try {
            MdQueryTL.set(isMasterDataQuery);
            val start = System.currentTimeMillis();
            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val metadata = metadataService.load(identifier);
            val entityData = data.toPersistData(metadata);
            entityData.setIdentifier(identifier);
            val entityDataService = SpringUtil.getBean(DataOperatorService.class);
            if(relationList.isEmpty()){
                entityDataService.update(entityData);
            }else{
                for(RelationOperationDto relation : relationList){
                    relation.setSourceId(data.getBid());
                    relation.setStartTime(data.getDataStartTime());
                }
                DataWithRelationDTO dataDTO = DataWithRelationDTO.builder()
                        .data(entityData)
                        .operations(relationList)
                        .build();
                entityDataService.updateWithRelation(dataDTO);
            }
            val end = System.currentTimeMillis();
            if (log.isDebugEnabled()) {
                log.debug("paas update time:" + (end-start));
            }
            val existed = DataQuery.identifier(identifier).setMasterDataQuery(isMasterDataQuery).one(data.getBid(), data.getClass(), data.getDataStartTime());
            data.getProperties().forEach((property, value) -> {
                if(value instanceof DataTable){
                    val existedDataTable = (DataTable)existed.getProperties().get(property);
                    metadata.fetchAllProperties().stream()
                            .filter(it->it.getProperty().equals(property)).findFirst().ifPresent(it->
                            updateSlaves(data.getBid(), (DataTable) value, fetchSlaveIdentifier(identifier, it.getProperty()), data.getDataStartTime(), existedDataTable));
                }
            });
        } finally {
            MdQueryTL.remove();
        }
    }

    private String fetchSlaveIdentifier(String identifier, String property){
        return identifier + "_" + property;
    }

    /**
     * 实现批量更新部分字段
     * 不支持关联关系更新
     */
    public <T extends DataSimple> void batchUpdate(DataFilter filter, DataUpdateDto data, long queryTime) {
        try {
            MdQueryTL.set(isMasterDataQuery);
            val entityDataService = SpringUtil.getBean(DataOperatorService.class);
            DataQuery query = DataQuery.identifier(this.identifier);
            query.setFilter(filter);
            DataQueryDto queryFilter = JsonEnhanceUtil.toObject(query, DataQueryDto.class);
            entityDataService.batchUpdate(DataBatchUpdateDto.bulid().setQuery(queryFilter).setBatchUpdateData(data), queryTime);
        } finally {
            MdQueryTL.remove();
        }
    }

    public <T extends DataSimple> void batchUpdate(DataFilter filter, Map<String, String> updateValue) {
        try {
            MdQueryTL.set(isMasterDataQuery);
            val entityDataService = SpringUtil.getBean(DataOperatorService.class);
            entityDataService.batchUpdate(identifier, filter, updateValue);
        } finally {
            MdQueryTL.remove();
        }
    }

    /**
     * 元数据加载
     *
     * @param data
     * @return com.caidaocloud.hrpaas.core.metadata.interfaces.dto.EntityDataDto
     * <AUTHOR>
     **/



    /**
     * 替代关联
     *
     * @param property
     * @param targetIds
     * @return com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert
     * <AUTHOR>
     */
    public DataUpdate replaceRelations(String property, List<String> targetIds) {
        val dto = buildRelationOperationDTO(RelationOperationType.REPLACE_ALL, null, property, targetIds);
        relationList.add(dto);
        return this;
    }

    private RelationOperationDto buildRelationOperationDTO(RelationOperationType type, String sourceId, String property, List<String> targetIds) {
        return RelationOperationDto.builder()
                .identifier(identifier)
                .sourceId(sourceId)
                .operationType(type)
                .property(property)
                .targetIds(targetIds)
                .build();
    }

}
