package com.caidaocloud.hrpaas.service;

import java.math.BigDecimal;

import com.caidaocloud.hrpaas.metadata.sdk.expression.function.Ceil;
import com.caidaocloud.hrpaas.metadata.sdk.expression.function.Floor;
import com.caidaocloud.hrpaas.metadata.sdk.expression.function.Round;
import com.googlecode.aviator.AviatorEvaluator;
import org.junit.Assert;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @date 2024/3/7
 */
public class ExpressionTest {
	@Test
	public void ROUND_TEST(){
		AviatorEvaluator.addFunction(new Round());
		AviatorEvaluator.addFunction(new Ceil());
		AviatorEvaluator.addFunction(new Floor());
		Assert.assertEquals(BigDecimal.valueOf(2), AviatorEvaluator.execute("round(1.5)"));
		Assert.assertEquals(BigDecimal.valueOf(1.57), AviatorEvaluator.execute("round(1.567,2)"));
		Assert.assertEquals(BigDecimal.valueOf(1.2), AviatorEvaluator.execute("ceil(1.135,1)"));
		Assert.assertEquals(BigDecimal.valueOf(2), AviatorEvaluator.execute("ceil(1.135)"));
		Assert.assertEquals(BigDecimal.valueOf(1.1), AviatorEvaluator.execute("floor(1.135,1)"));
		Assert.assertEquals(BigDecimal.valueOf(1), AviatorEvaluator.execute("floor(1.135)"));
	}
}
