package com.caidaocloud.hrpaas.service.application.rules;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 邮箱校验规则
 * <AUTHOR>
 * @date 2022-01-04
 */
@Service
public class EmailCheckRule implements ICheckRule {
    private final static String EMAIL_REGEX = "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$";
    private static Pattern EMAIL_PATTERN = Pattern.compile(EMAIL_REGEX);
    private static String EMAIL_TIPS = "邮箱不合法";

    @Override
    public String getRuleType() {
        return "email";
    }

    @Override
    public String [] getRuleGroup() {
        return new String [] {"String"};
    }

    @Override
    public void check(IRule checkRuleDto) {
        if(null == checkRuleDto || StringUtil.isEmpty(checkRuleDto.value())){
            return;
        }

        Matcher matcher = EMAIL_PATTERN.matcher(checkRuleDto.value());
        if(!matcher.matches()){
            String tips = StringUtil.isBlank(checkRuleDto.tips()) ? EMAIL_TIPS : checkRuleDto.tips();
            throw new ServerException(tips);
        }
    }
}
