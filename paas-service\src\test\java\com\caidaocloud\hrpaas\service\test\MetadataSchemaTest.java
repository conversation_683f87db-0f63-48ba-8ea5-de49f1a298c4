package com.caidaocloud.hrpaas.service.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.hrpaas.service.application.metadata.dto.MetadataSchemaInfoDto;
import com.caidaocloud.hrpaas.service.application.metadata.service.MetadataSchemaService;
import com.caidaocloud.hrpaas.service.application.metadata.service.MetadataTenantService;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataSchemaDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataTenantDto;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileFilter;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class MetadataSchemaTest {
    @Autowired
    private MetadataSchemaService metadataSchemaService;
    @Autowired
    private MetadataTenantService metadataTenantService;
    @Autowired
    private ResourceLoader resourceLoader;

    @Test
    public void readJsonFileTest(){
        String json = metadataSchemaService.fileToJson("metadata/script/20210624/V20210623062222_员工信息新增字段.json");
        log.info("-------------,fileToJson = {}", json);
    }

    @Test
    public void jsonToEntityTest(){
        List<MetadataSchemaInfoDto> list = getListByFile();
        log.info("====== List<MetadataSchemaInfoDto> = {}", JSON.toJSONString(list));
    }

    @Test
    public void syncSchemaVersionTest(){
        List<MetadataSchemaInfoDto> list = getListByFile();
        log.info("====== List<MetadataSchemaInfoDto> = {}", JSON.toJSONString(list));

        List<MetadataSchemaDto> schemaList = new ArrayList<>();

        MetadataSchemaDto metadataSchemaDto = new MetadataSchemaDto();
        metadataSchemaDto.setFolder(20210629);
        metadataSchemaDto.setSchemaModel("uiform");
        metadataSchemaDto.setSchemaName("V20210629114122_表单引擎初始化脚本.json");
        metadataSchemaDto.setVersion(20210629114122L);

        metadataSchemaDto.setContent(list);

        schemaList.add(metadataSchemaDto);
        //metadataSchemaService.syncSchemaVersion(schemaList);
    }

    @Test
    public void initMetadataTenant(){
        MetadataTenantDto metadataTenantDto = new MetadataTenantDto();
        metadataTenantDto.setTenantId("11645");
        metadataTenantDto.setCode("test");
        metadataTenantDto.setName("测试账号");
        metadataTenantDto.setThirdPart("test");
        //metadataTenantService.init(metadataTenantDto);
    }

    @Test
    public void initBatchMetadataTenant(){
        int corpid = 11645;
        for (int i = 0; i < 10; i++){
            MetadataTenantDto metadataTenantDto = new MetadataTenantDto();
            metadataTenantDto.setTenantId(String.valueOf(corpid));
            metadataTenantDto.setCode("test");
            metadataTenantDto.setName("测试账号");
            metadataTenantDto.setThirdPart("test");
            //metadataTenantService.init(metadataTenantDto);
            corpid ++;
        }
    }

    @Test
    public void initMultithreadMetadataTenant(){
        for (int i = 0; i < 10; i++){
            new Thread(){
                @Override
                public void run() {
                    ThreadLocalRandom localRandom = ThreadLocalRandom.current();
                    int corpid = localRandom.nextInt(100, 999);
                    System.out.println("----------" + corpid);
                    MetadataTenantDto metadataTenantDto = new MetadataTenantDto();
                    metadataTenantDto.setTenantId(String.valueOf(corpid));
                    metadataTenantDto.setCode("test");
                    metadataTenantDto.setName("测试账号");
                    metadataTenantDto.setThirdPart("test");
                    //metadataTenantService.init(metadataTenantDto);
                }
            }.start();
        }

        try {
            TimeUnit.SECONDS.sleep(30);
        } catch (Exception e){
            e.printStackTrace();
        }
    }

    private List<MetadataSchemaInfoDto> getListByFile(){
        String json = metadataSchemaService.fileToJson("metadata/script/20210629/V20210629114122_表单引擎初始化脚本.json");
        List<MetadataSchemaInfoDto> list = FastjsonUtil.toObject(json, new TypeReference<List<MetadataSchemaInfoDto>>(){});
        return list;
    }

    @Test
    public void readJsonFile(){
        Resource resource = resourceLoader.getResource("classpath:metadata/script");

        File directory = null;
        try {
            directory = resource.getFile();
            System.out.println(directory.getCanonicalPath());
        } catch (FileNotFoundException e){
            e.printStackTrace();
            return;
        } catch (Exception e){
            e.printStackTrace();
        }

        if(null == directory){
            return;
        }

        List list = null;
        try {
            list = findFile(directory);
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(list));
    }

    public static List findFile(File dir) throws IOException {
        List result = new ArrayList();
        File[] dirFiles = dir.listFiles();
        for(File temp : dirFiles){
            if(!temp.isFile()){
                result.addAll(findFile(temp));
            }
            //查找指定的文件
            if(temp.isFile() && temp.getAbsolutePath().endsWith(".json") ){
                System.out.println(temp.isFile() + " " + temp.getAbsolutePath());
                result.add(temp);
            }
        }

        return result;
    }


}
