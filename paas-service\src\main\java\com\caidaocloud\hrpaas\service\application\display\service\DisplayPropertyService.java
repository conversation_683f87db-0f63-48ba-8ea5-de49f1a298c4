package com.caidaocloud.hrpaas.service.application.display.service;

import com.caidaocloud.hrpaas.service.application.display.dto.DisplayPropertyDto;
import com.caidaocloud.hrpaas.service.domain.display.entity.DisplayPropertyDef;
import org.springframework.stereotype.Service;

@Service
public class DisplayPropertyService {
    public DisplayPropertyDef loadByCode(String code) {
        return DisplayPropertyDef.loadByCode(code);
    }

    public void saveByCode(DisplayPropertyDto dto) {
        //todo 后续分模块根据id查
        DisplayPropertyDef propertyDef = DisplayPropertyDef.loadByCode("emp");
        if (propertyDef != null) {
            DisplayPropertyDef.getUpdateDef(dto).update();
        }else {
            DisplayPropertyDef.getInsertDef(dto).insert();
        }
    }
}
