package com.caidaocloud.hrpaas.service.interfaces.facade.page;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.service.application.page.dto.TenantPageDetailDto;
import com.caidaocloud.hrpaas.service.application.page.dto.TenantPageDto;
import com.caidaocloud.hrpaas.service.application.page.service.EmpBasicPageService;
import com.caidaocloud.hrpaas.service.application.page.service.TenantPageService;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.interfaces.vo.page.TenantPageDetailVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.page.TenantPageVo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/hrpaas/basic/page/v1")
@Api(value = "/api/hrpaas/basic/page/v1", description = "页面接口", tags = "员工基础信息页面接口")
public class EmpBasicPageController {

    @Autowired
    private EmpBasicPageService empBasicPageService;


    @ApiOperation("获取页面detail")
    @GetMapping("/detail")
    public Result<TenantPageDetailVo> pageDetail() {
        return Result.ok(empBasicPageService.basicPageDetail());
    }

    @ApiOperation("获取可配置字段")
    @GetMapping("/field")
    public Result<List<MetadataVo>> fields() {
        return Result.ok(empBasicPageService.loadBasicMetadata());
    }

    @ApiOperation("修改页面detail")
    @PutMapping("/detail")
    @LogRecordAnnotation(menu = "管理中心-乐高引擎-页面管理-员工信息详情", category = "编辑", success = "编辑了员工信息详情页面")
    public Result<Boolean> alterPageDetail(@RequestBody TenantPageDetailDto pageDetail){
        empBasicPageService.alterDetail(pageDetail);
        return Result.ok();
    }
}
