package com.caidaocloud.metadata.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.metadata.infrastructure.repository.po.DataModificationPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface DataModificationMapper extends BaseMapper<DataModificationPo> {
    @Update("${execSql}")
    void exec(@Param("execSql") String execSql);
}
