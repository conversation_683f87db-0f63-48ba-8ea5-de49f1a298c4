package com.caidaocloud.metadata.application.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataAddressVo;
import com.caidaocloud.metadata.application.dto.TreeLeafDto;
import com.caidaocloud.metadata.domain.entity.AddressDo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MetadataAddressService {

    @Resource
    private CacheService cacheService;

    public List<MetadataAddressVo> selectList(String id) {
        id = null == id ? AddressDo.ROOT_PID : id;
        return ObjectConverter.convertList(AddressDo.getAllList(id), MetadataAddressVo.class);
    }

    public List<MetadataAddressVo> selectAllList() {
        return FastjsonUtil.toArrayList(cacheService.getValue(AddressDo.ADDRESS_ALL_KEY), MetadataAddressVo.class);
    }

    public void loadCache(){
        List<AddressDo> allList = AddressDo.getAllList(null);
        allList.forEach(address -> {
            String key = String.format(AddressDo.ADDRESS_KEY, address.getId());
            cacheService.cacheValue(key, FastjsonUtil.toJson(address));
        });
        cacheService.cacheValue(AddressDo.ADDRESS_ALL_KEY, FastjsonUtil.toJson(ObjectConverter.convertList(allList, MetadataAddressVo.class)));
    }

    public List<TreeData<TreeLeafDto>> tree(String mode) {
        List<AddressDo> items = AddressDo.getAllList(null);
        items = doFilterByType(items, mode);
        List<TreeData<TreeLeafDto>> tree = treeList(items);
        return tree;
    }

    private List<AddressDo> doFilterByType(List<AddressDo> items, String mode){
        if(null == items || items.isEmpty() || StringUtil.isEmpty(mode)){
            return items;
        }

        return items.stream().filter(item -> {
            if("PROVINCE".equals(mode)){
                // 省
                return "PROVINCE".equals(item.getType());
            } else if("PROVINCE_CITY".equals(mode)){
                // 省市模式
                return "PROVINCE".equals(item.getType()) || "CITY".equals(item.getType());
            }

            return false;
        }).collect(Collectors.toList());
    }

    private List<TreeData<TreeLeafDto>> treeList(List<AddressDo> items){
        List<TreeData<TreeLeafDto>> resultList = Lists.list();
        if(null == items || items.isEmpty()){
            return resultList;
        }

        Map<String, TreeData> treeDataMap = Maps.map(Sequences.sequence(items).map(data -> Pair.pair(data.getCode(), leafTreeData(data))));
        treeDataMap.forEach((k, v) -> {
            val data = (TreeLeafDto) v.getData();
            String pid = data.getPidPath();
            if (null == pid || AddressDo.ROOT_PID.equals(pid)) {
                // 顶级根节点，直接添加
                resultList.add(v);
            } else if (null != treeDataMap.get(pid) && null != treeDataMap.get(pid).getChildren()) {
                // 如果找到关联的父节点，则添加 Children
                treeDataMap.get(pid).getChildren().add(v);
            } else {
                doLeaf(pid, treeDataMap, v);
            }
        });

        return resultList;
    }

    private void doLeaf(String pid, Map<String, TreeData> treeDataMap, TreeData v){
        String [] split = pid.split("/");
        if(split.length == 2){
            treeDataMap.get(split[1]).getChildren().add(v);
        }
    }

    private TreeData leafTreeData(AddressDo data){
        TreeLeafDto leaf = new TreeLeafDto();
        leaf.setId(data.getId());
        leaf.setBid(data.getCode());
        leaf.setName(data.getAddress());
        leaf.setPidPath(data.getPidPath());
        leaf.setCode(data.getCode());
        leaf.setType(data.getType());
        return new TreeData(leaf);
    }

    public List<MetadataAddressVo> selectAddressByStr(List<String> list) {
        return ObjectConverter.convertList(AddressDo.getAddressList(list), MetadataAddressVo.class);
    }
}
