package com.caidaocloud.hrpaas.metadata.sdk.util;


import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.WebUtil;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import javax.sound.midi.Sequence;
import java.util.Locale;
import java.util.Map;
import java.util.function.BiFunction;

public class I18nUtil {

    public static Map<String, String> concat(String firstDefault, String secondDefault, Map<String, String> first, Map<String, String> second, String joiner){
        Map<String, String> result = Maps.map();
        if(first == null){
            first = Maps.map();
        }
        if(second == null){
            second = Maps.map();
        }
        first.putIfAbsent("default", firstDefault);
        second.putIfAbsent("default", secondDefault);
        val keys = Sequences.sequence(first.keySet())
                .join(second.keySet());
        val keySet = keys.toSet();
        for(String key : keySet){
            val firstValue = first.containsKey(key) ? first.get(key) : first.get("default");
            val secondValue = second.containsValue(key) ? second.get(key) : second.get("default");
            result.put(key, firstValue + joiner + secondValue);
        }
        return result;
    }

    public static void fillLang(String str, Map<String, String> i18nStr, BiFunction<String, Map<String,String>, Object> func){
        if(null == i18nStr || i18nStr.isEmpty()){
            i18nStr = Maps.map("default", str);
            try{
                val json = FastjsonUtil.toObject(str, Map.class);
                if(null != json && !json.isEmpty()){
                    i18nStr = json;
                }
            }catch(Exception e){
            }
        }
        func.apply(lang(i18nStr), i18nStr);
    }

    public static String lang(Map<String, String> i18n){
        Locale locale = MessageHandler.getLocaleByRequest(WebUtil.getRequest());
        String currentLang = locale.getLanguage();
        String langDetail = currentLang;
        val country = locale.getCountry();
        if(StringUtils.isNotEmpty(country)){
            langDetail = currentLang + "-" + country;
        }
        if(i18n.containsKey(currentLang)){
            return i18n.get(currentLang);
        }else{
            if(i18n.containsKey(langDetail)){
                return i18n.get(langDetail);
            }else{
                return i18n.get("default");
            }
        }
    }

}
