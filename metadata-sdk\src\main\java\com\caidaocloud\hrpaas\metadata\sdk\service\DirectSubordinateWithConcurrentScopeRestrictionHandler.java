package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.jarvis.cache.annotation.Cache;
import lombok.val;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class DirectSubordinateWithConcurrentScopeRestrictionHandler implements ScopeRestrictionHandler {

    @Override
    @Cache(key = "'3_scope_restriction_' + #args[0] + '_' + #args[1]",expire = 5 *60)
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        Boolean isFilter = AuthScopeFilterUtil.get();
        val tlUser = SecurityUserUtil.getThreadLocalSecurityUserInfo();
        try{
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            val currentEmp = SecurityUserUtil.getSecurityUserInfo().getEmpId();
            if(null == currentEmp){
                return Lists.list();
            }
            List<String> results = Lists.list();
            AuthScopeFilterUtil.put(false);
            for(int i = 1;i < 20;i++){
                val list = DataQuery.identifier("entity.hr.EmpWorkInfo")
                        .limit(1000, i).filterProperties(DataFilter.eq("leadEmpId$empId", currentEmp.toString())
                                , Lists.list("empId"), System.currentTimeMillis()).getItems()
                        .stream().map(it->it.get("empId")).collect(Collectors.toList());
                if(list.isEmpty()){
                    break;
                }else{
                    results.addAll(list);
                }
            }
            val now = System.currentTimeMillis();
            for(int i = 1;i < 20;i++){
                val list = DataQuery.identifier("entity.hr.EmpConcurrentPost")
                        .limit(1000, i).filterProperties(DataFilter
                                        .eq("postSuperior$empId", currentEmp.toString())
                                        .andLe("startDate", String.valueOf(now))
                                        .andGe("endDate", String.valueOf(now))
                                , Lists.list("empId"), now).getItems()
                        .stream().map(it->it.get("empId")).collect(Collectors.toList());
                if(list.isEmpty()){
                    break;
                }else{
                    results.addAll(list);
                }
            }
            return results.stream().distinct().collect(Collectors.toList());
        }finally {
            if(null == tlUser){
                SecurityUserUtil.removeSecurityUserInfo();
            }else{
                SecurityUserUtil.setSecurityUserInfo(tlUser);
            }
            AuthScopeFilterUtil.put(isFilter);
        }

    }

}
