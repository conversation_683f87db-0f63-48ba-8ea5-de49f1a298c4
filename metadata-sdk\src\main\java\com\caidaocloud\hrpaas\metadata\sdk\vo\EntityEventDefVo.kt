package com.caidaocloud.hrpaas.core.metadata.interfaces.vo

import com.caidaocloud.hrpaas.metadata.sdk.enums.EventDefStatus
import io.swagger.annotations.ApiModelProperty

data class EntityEventDefVo(
    @ApiModelProperty("id")
    val id : String,
    @ApiModelProperty("identifier")
    val identifier : String,
    @ApiModelProperty("名称")
    val name : String,
    @ApiModelProperty("关联模型identifier")
    val modelRef : String,
    @ApiModelProperty("是否标准事件")
    val builtIn : Boolean,
    @ApiModelProperty("是否追踪任意变化")
    val trackAll : Boolean,
    @ApiModelProperty("标准追踪字段")
    val standardTrackedProperties : List<TrackedPropertiesGroupVo>,
    @ApiModelProperty("自定义追踪字段")
    val customTrackedProperties : List<TrackedPropertiesGroupVo>,
    @ApiModelProperty("标准通知属性")
    val standardNotifyProperties : List<String>,
    @ApiModelProperty("自定义通知属性")
    val customNotifyProperties : List<String>,
    @ApiModelProperty("订阅者")
    var consumerList : List<String> = listOf(),
    @ApiModelProperty("是否追踪属性")
    var trackRelation : Boolean,
    @ApiModelProperty("状态")
    var status : EventDefStatus,
    @ApiModelProperty("创建时间")
    val createTime: Long,
    @ApiModelProperty("创建人")
    val createBy: String?,
    @ApiModelProperty("更新时间")
    val updateTime: Long,
    @ApiModelProperty("更新人")
    val updateBy: String?
)

data class TrackedPropertiesGroupVo(
    @ApiModelProperty("属性组")
    val properties : List<String> = listOf()
)