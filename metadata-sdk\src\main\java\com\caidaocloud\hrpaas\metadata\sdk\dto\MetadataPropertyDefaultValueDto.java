package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MetadataPropertyDefaultValueDto {

    @ApiModelProperty("属性")
    private String property;
    @ApiModelProperty("属性默认值")
    private String value;
    @ApiModelProperty("属性默认值--数组类")
    private List<String> arrayValues;
    @ApiModelProperty("属性默认值数据类型")
    private PropertyDataType dataType;

}
