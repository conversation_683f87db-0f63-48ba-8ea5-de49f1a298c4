package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 表单引擎Dto
 * <AUTHOR>
 * @date 2021-06-15
 */
@Data
public class UiFormBodyDto implements Serializable{
    private final static long serialVersionUID = 1L;

    @NotBlank
    @ApiModelProperty("表单ID")
    private String bid;

    @NotBlank
    @ApiModelProperty("页面schema:表单组件列表配置")
    private String schema;

    @ApiModelProperty("表单组件字段列表配置")
    private List<UiFormFieldDto> fieldConfig;
}