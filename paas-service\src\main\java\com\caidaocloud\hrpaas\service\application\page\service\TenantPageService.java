package com.caidaocloud.hrpaas.service.application.page.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AuthResourceDto;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMetadataAuthFeignClient;
import com.caidaocloud.hrpaas.paas.common.dto.PageDetailDto;
import com.caidaocloud.hrpaas.paas.common.event.PageDetailChangeEvent;
import com.caidaocloud.hrpaas.service.application.page.constant.PageConstant;
import com.caidaocloud.hrpaas.service.application.page.enums.EmpPageDetailKey;
import com.caidaocloud.hrpaas.service.application.page.enums.ResourceActionEnum;
import com.caidaocloud.hrpaas.service.application.page.enums.ResourceCategoryEnum;
import com.caidaocloud.hrpaas.service.application.page.feign.ImportExportFeign;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.service.application.page.dto.TenantPageDetailDto;
import com.caidaocloud.hrpaas.service.application.page.dto.TenantPageDto;
import com.caidaocloud.hrpaas.service.application.uiform.service.UiFormService;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.hrpaas.service.domain.form.repository.IFormDefRepository;
import com.caidaocloud.hrpaas.service.domain.page.entity.TenantPage;
import com.caidaocloud.hrpaas.service.domain.page.entity.TenantPageDetail;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.interfaces.vo.page.TenantPageDetailVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.page.TenantPageVo;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.WebUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TenantPageService {

    @Autowired
    private UiFormService uiFormService;

    private Map<String, TenantPageVo> standardTenantPage;

    private Map<String, String> standardTenantPageDetail;

    @Resource
    private IMetadataAuthFeignClient authFeignClient;

    @Resource
    private ImportExportFeign importExportFeign;
    @Resource
    private IFormDefRepository formDefRepository;

    @Transactional
    public void addPage(TenantPageDto tenantPageDto) {
        tenantPageDto.setId(null);
        if(StringUtils.isNotEmpty(tenantPageDto.getParentId())){
            if(!standardPage().containsKey(tenantPageDto.getParentId())){
                TenantPage parent = TenantPage.loadOne(tenantPageDto.getParentId());
                if(null == parent){
                    throw new ServerException("parent page not exist");
                }
            }
        }
        String id = SnowUtil.nextId();
        val now = System.currentTimeMillis();
        val tenantPage = FastjsonUtil.convertObject(tenantPageDto, TenantPage.class);
        tenantPage.setId(id);
        tenantPage.setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        tenantPage.setCreateTime(now);
        tenantPage.setName(FastjsonUtil.toJson(tenantPageDto.getI18nName()));
        tenantPage.setUpdateBy(tenantPage.getCreateBy());
        tenantPage.setUpdateTime(now);
        tenantPage.setPath(id);
        tenantPage.insert();
        val pageDetail = new TenantPageDetail();
        pageDetail.setId(id);
        if(tenantPage.getType().equals(PageType.NormalPage) || tenantPage.getType().equals(PageType.ModelPage)){
            pageDetail.setStandardPageConfig(tenantPageDto.getSchema());
        }
        pageDetail.setType(tenantPage.getType());
        pageDetail.setCreateTime(tenantPage.getCreateTime());
        pageDetail.setUpdateTime(tenantPage.getCreateTime());
        pageDetail.setCreateBy(tenantPage.getCreateBy());
        pageDetail.setUpdateBy(tenantPage.getUpdateBy());
        pageDetail.insert();

        registerAuthResource(tenantPage);
    }

    private void registerAuthResource(TenantPage tenantPage) {
        List<AuthResourceDto> authResource = tenantPage.authResource();
        if (!authResource.isEmpty()) {
            authFeignClient.createResources(authResource);
        }
    }


    @Transactional
    public void alterPage(String id, TenantPageDto tenantPageDto) {
        TenantPage existed = TenantPage.loadOne(id);
        if(null == existed){
            throw new ServerException("page not exist");
        }
        if(StringUtils.isNotEmpty(tenantPageDto.getParentId())){
            if(tenantPageDto.getParentId().equals(tenantPageDto.getId())){
                throw new ServerException("parent page can not be itself");
            }
            if(!standardPage().containsKey(tenantPageDto.getParentId())){
                TenantPage parent = TenantPage.loadOne(tenantPageDto.getParentId());
                if(null == parent){
                    throw new ServerException("parent page not exist");
                }
            }
        }
        val tenantPage = FastjsonUtil.convertObject(tenantPageDto, TenantPage.class);
        tenantPage.setId(id);
        tenantPage.setCreateBy(existed.getCreateBy());
        tenantPage.setCreateTime(existed.getCreateTime());
        tenantPage.setName(FastjsonUtil.toJson(tenantPageDto.getI18nName()));
        tenantPage.setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        tenantPage.setUpdateTime(System.currentTimeMillis());
        tenantPage.setPath(existed.getPath());
        tenantPage.update();
        if(tenantPageDto.getType().equals(PageType.NormalPage) || tenantPageDto.getType().equals(PageType.ModelPage)){
            val detail = new TenantPageDetailDto();
            detail.setId(tenantPageDto.getId());
            detail.setType(tenantPageDto.getType());
            detail.setStandardPageConfig(tenantPageDto.getSchema());
            alterDetail(detail);
        }
        refreshAuthResource(tenantPage);
    }

    private void refreshAuthResource(TenantPage tenantPage) {
        List<AuthResourceDto> authResource = tenantPage.authResource();
        if (!authResource.isEmpty()) {
            authFeignClient.update(authResource);
        }
    }

    public void deletePage(String id) {
        val child = TenantPage.loadAll().stream().filter(it->StringUtils.equals(it.getParentId(), id))
                .findAny();
        if(child.isPresent()){
            throw new ServerException("parent page not deletable");
        }
        TenantPage page = TenantPage.loadOne(id);
        TenantPage.deleteById(id);
        TenantPageDetail.deleteById(id);
        deleteAuthResource(page);
    }

    private void deleteAuthResource(TenantPage tenantPage) {
        List<AuthResourceDto> authResource = tenantPage.authResource();
        if (!authResource.isEmpty()) {
            authFeignClient.delete(authResource);
        }
    }

    public List<TenantPageVo> tree(PageType pageType) {
        List<Pair<String, TenantPageVo>> list = TenantPage.loadAll().stream()
                .filter(it-> pageType == null? true : pageType.equals(it.getType()))
                .map(it-> {
                    val vo = FastjsonUtil.convertObject(it, TenantPageVo.class);
                    vo.setI18nName(FastjsonUtil.toObject(it.getName(), Map.class));
                    setCurrentLangName(vo);
                    return Pair.pair(it.getId(), vo);
                }).collect(Collectors.toList());
        val pageMap = Maps.map(list);
        for(TenantPageVo standard: standardPage().values()){
            if(null == pageType || pageType.equals(standard.getType())){
                val vo = FastjsonUtil.convertObject(standard, TenantPageVo.class);
                if(null != vo.getI18nName()){
                    setCurrentLangName(vo);
                }
                pageMap.put(standard.getId(), vo);
            }
        }
        List<TenantPageVo> result = Lists.list();
        for(TenantPageVo tenantPageVo: pageMap.values()){
            if(tenantPageVo.getParentId() == null){
                result.add(tenantPageVo);
            }else{
                val parent = pageMap.get(tenantPageVo.getParentId());
                if(parent == null){
                    throw new ServerException("层级结构错误");
                }
                parent.getChildren().add(tenantPageVo);
            }
        }
        for(TenantPageVo tenantPageVo : result){
            for(TenantPageVo child : tenantPageVo.getChildren()){
                initPath(child, tenantPageVo.getPath());
            }
        }
        return result;
    }

    private void setCurrentLangName(TenantPageVo vo) {

        Locale locale = MessageHandler.getLocaleByRequest(WebUtil.getRequest());
        String currentLang = locale.getLanguage();
        String langDetail = currentLang;
        val country = locale.getCountry();
        if(StringUtils.isNotEmpty(country)){
            langDetail = currentLang + "-" + country;
        }
        if(vo.getI18nName().containsKey(currentLang)){
            vo.setName(vo.getI18nName().get(currentLang));
        }else{
            if(vo.getI18nName().containsKey(langDetail)){
                vo.setName(vo.getI18nName().get(langDetail));
            }else{
                vo.setName(vo.getI18nName().get("default"));
            }
        }

    }

    private void initPath(TenantPageVo tenantPageVo, String parentPath){
        tenantPageVo.setPath(parentPath + "/" + tenantPageVo.getPath());
        for(TenantPageVo child : tenantPageVo.getChildren()){
            initPath(child, tenantPageVo.getPath());
        }
    }

    @SneakyThrows
    private Map<String, TenantPageVo> standardPage(){
        if(null == standardTenantPage){
            synchronized (this){
                if(null == standardTenantPage){
                    val standardPage = FastjsonUtil.toList(
                            IOUtils.toString(new ClassPathResource("page/page.json").getInputStream(), "UTF-8"),
                            TenantPageVo.class
                    );
                    standardTenantPage = Maps.map();
                    for(TenantPageVo standard: standardPage){
                        flattenAddToMap(standardTenantPage, standard);
                    }
                }
            }
        }
        return standardTenantPage;
    }

    @SneakyThrows
    private String empBasicPageConfig(){
        return IOUtils.toString(new ClassPathResource("page/standard_emp_basic_page.json").getInputStream(), "UTF-8");
    }

/*
    @SneakyThrows
    private Map<String, String> standardPageDetail(){
        if(null == standardTenantPageDetail){
            synchronized (this){
                if(null == standardTenantPageDetail){
                    standardTenantPageDetail = Maps.map();
                    for(String pagePath:
                            Lists.list(
                                    "page/standard_emp.json","page/standard_pre_emp.json")){
                        val pageDetail = IOUtils.toString(new ClassPathResource("pagePath").getInputStream(),
                                "UTF-8");
                        val pageId = FastjsonUtil.toObject(pageDetail,Map.class).get("key");
                        standardTenantPageDetail.put(String.valueOf(pageId), pageDetail);
                    }
                }
            }
        }
        return standardTenantPageDetail;
    }*/
    @SneakyThrows
    private Map<String, String> standardPageDetail(){
        if(null == standardTenantPageDetail){
            synchronized (this){
                if(null == standardTenantPageDetail){
                    List<String> list = FastjsonUtil.toList(IOUtils.toString(new ClassPathResource("page/standard_page_detail.json").getInputStream(),
                            "UTF-8"), String.class);
                    standardTenantPageDetail = Maps.map();
                    for (String pageDetail : list) {
                        val pageId = FastjsonUtil.toObject(pageDetail,Map.class).get("key");
                        standardTenantPageDetail.put(String.valueOf(pageId), pageDetail);
                    }
                }
            }
        }
        return standardTenantPageDetail;
    }

    private void flattenAddToMap(Map<String, TenantPageVo> map, TenantPageVo tenantPageVo){
        map.put(tenantPageVo.getId(), tenantPageVo);
        for(TenantPageVo child:tenantPageVo.getChildren()){
            flattenAddToMap(map, child);
        }
        tenantPageVo.getChildren().clear();
    }

    public TenantPageDetailVo pageDetail(String pageId){
        TenantPageDetail pageDetail = TenantPageDetail.loadById(pageId);
        if(null == pageDetail){
            if(standardPage().containsKey(pageId)){
                pageDetail = new TenantPageDetail();
                pageDetail.setId(pageId);
                pageDetail.setType(PageType.StandardPage);
                pageDetail.setStandardPageConfig(standardPageDetail().get(pageId));
            }else{
                throw new ServerException("page not found");
            }
        }
        return FastjsonUtil.convertObject(pageDetail, TenantPageDetailVo.class);
    }



    public void alterDetail(TenantPageDetailDto pageDetail){
        val pageId = pageDetail.getId();
        if(StringUtils.isEmpty(pageDetail.getStandardPageConfig()) && standardPage().containsKey(pageId)){
            throw new ServerException("标准页面配置错误");
        }
        TenantPageDetail existed = TenantPageDetail.loadById(pageId);
        if(null == existed){
            if(standardPage().containsKey(pageId)){
                existed = new TenantPageDetail();
                existed.setId(pageId);
                existed.setType(PageType.StandardPage);
                existed.setStandardPageConfig(pageDetail.getStandardPageConfig());
                existed.setCreateTime(System.currentTimeMillis());
                existed.setUpdateTime(existed.getCreateTime());
                existed.setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
                existed.setUpdateBy(existed.getCreateBy());
                existed.insert();
            }else{
                throw new ServerException("page not found");
            }
        }else{
            BeanUtils.copyProperties(pageDetail, existed);
            existed.setUpdateTime(System.currentTimeMillis());
            existed.setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
            existed.update();
        }

        registerPageDetailAuthResource(existed);
        new PageDetailChangeEvent(existed.getId(), SecurityUserUtil.getSecurityUserInfo().getTenantId()).publish();
    }

    private void registerPageDetailAuthResource(TenantPageDetail existed) {
        if (!"page.detail.employee".equals(existed.getId())) {
            return;
        }
        log.info("注册员工信息子集页面权限，pageId={}", existed.getId());
        List<PageDetailDto.TabDetail> tabDetails = convertTabDetail(existed);
        List<AuthResourceDto> authResourceDtoList =
                buildResources(tabDetails);
        log.info("注册子集页面权限,data={}", authResourceDtoList);
        authFeignClient.createResources(authResourceDtoList);
    }

    private List<AuthResourceDto> buildResources(List<PageDetailDto.TabDetail> customPageDeatail) {
        List<AuthResourceDto> list = com.google.common.collect.Lists.newArrayList();
        for (PageDetailDto.TabDetail tabDetail : customPageDeatail) {
            String formId = StringUtils.substringAfterLast(tabDetail.getKey(), ".");
            Option<FormDef> option = formDefRepository.load(formId);
            if (option.isEmpty()) {
                throw new ServerException("表单不存在，formId=" + formId);
            }
            FormDef formDef = option.get();
            log.info("生成自定义表单子集页面权限，子集code={},子集名称={}", formDef.getId(), formDef.getName());
            var code = String.format("%s%s", PageConstant.EMP_PAGE_DETAIL_PREFIX, tabDetail.getKey().replaceAll("\\.", "_"));
            list.add(new AuthResourceDto().setCode(code)
                    .setName(tabDetail.getLabel()).setLang("zh").setCategory(ResourceCategoryEnum.MENU.toString())
                    .setUrl(String.format("/api/hrpaas/v1/form/data/one/sub/%s", formId))
                    .setResourceAction(ResourceActionEnum.VIEW.toString())
                    .setParentCode(PageConstant.EMP_PAGE_CODE));
            list.add(new AuthResourceDto().setCode(code)
                    .setName(tabDetail.getLabel()).setLang("zh").setCategory(ResourceCategoryEnum.MENU.toString())
                    .setUrl(String.format("/api/hrpaas/v1/form/data/page/sub/%s", formId))
                    .setResourceAction(ResourceActionEnum.VIEW.toString())
                    .setParentCode(PageConstant.EMP_PAGE_CODE));
            // 新增
            var addCode = String.format("%s_add", code);
            list.add(new AuthResourceDto().setCode(addCode)
                    .setName("编辑").setLang("zh").setCategory(ResourceCategoryEnum.FUNC.toString())
                    .setUrl(String.format("/api/hrpaas/v1/form/data/create/sub/%s", formId))
                    .setResourceAction(ResourceActionEnum.ADD.toString()).setParentCode(code));
            // 挂载流程的表单不能编辑或删除
            if (!formDef.isWorkflowAvailable()) {
                // 编辑
                var editCode = String.format("%s_add", code);
                list.add(new AuthResourceDto().setCode(editCode)
                        .setName("编辑").setLang("zh").setCategory(ResourceCategoryEnum.FUNC.toString())
                        .setUrl(String.format("/api/hrpaas/v1/form/data/update/sub/%s",code))
                        .setResourceAction(ResourceActionEnum.EDIT.toString()).setParentCode(code));

                // 删除
                var deleteCode = String.format("%s_add", code);
                list.add(new AuthResourceDto().setCode(deleteCode)
                        .setName("编辑").setLang("zh").setCategory(ResourceCategoryEnum.FUNC.toString())
                        .setUrl(String.format("/api/hrpaas/v1/form/data/delete/sub/%s",code))
                        .setResourceAction(ResourceActionEnum.DELETE.toString()).setParentCode(code));

            }
        }
        return list;
    }


    private List<PageDetailDto.TabDetail> convertTabDetail(TenantPageDetail existed) {
        Set<String> keySet = EmpPageDetailKey.getValues();
        PageDetailDto pageDetail = FastjsonUtil.toObject(existed
                .getStandardPageConfig(), PageDetailDto.class);
        return Sequences.sequence(pageDetail.getChildList())
                .filter(tabDetail -> !keySet.contains(tabDetail.getKey()) && tabDetail.getKey()
                        .startsWith("entity.form."))
                .toList();
    }

    public TenantPageVo viewPage(String pageId) {
        TenantPage tenantPage = TenantPage.loadOne(pageId);
        val vo = FastjsonUtil.convertObject(tenantPage, TenantPageVo.class);
        vo.setI18nName(FastjsonUtil.toObject(tenantPage.getName(), Map.class));
        if(null != vo.getI18nName()){
            setCurrentLangName(vo);
        }
        if(vo.getType().equals(PageType.NormalPage) || vo.getType().equals(PageType.ModelPage)){
            vo.setSchema(pageDetail(pageId).getStandardPageConfig());
        }
        return vo;
    }
}
