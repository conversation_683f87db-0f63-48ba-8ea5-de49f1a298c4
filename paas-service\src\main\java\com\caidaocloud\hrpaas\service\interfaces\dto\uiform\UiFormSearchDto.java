package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UiFormSearchDto extends BasePage {
    /**
     * 表单名称
     */
    @ApiModelProperty("表单名称")
    private String name;

    /**
     * 表单类型
     */
    @ApiModelProperty("表单类型")
    private PageType type;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间开始区间")
    private Long startCreateTime;

    @ApiModelProperty("创建时间结束区间")
    private Long endCreateTime;
}
