package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import com.caidaocloud.hrpaas.metadata.sdk.filter.FilterOperator;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UiFormDataValueQueryDto extends UiFormDataValueDto{
    /**
     * 字符串，字段ID，必填，最大长度16位
     */
    @ApiModelProperty("字段ID")
    private String prop;

    /**
     * 当前字段 ID 对应的字段值
     */
    @ApiModelProperty("字段值或区间查询最小值")
    private String value;

    @ApiModelProperty("查询操作符")
    private FilterOperator operator;

    @ApiModelProperty("区间查询最大值")
    private String maxValue;
}
