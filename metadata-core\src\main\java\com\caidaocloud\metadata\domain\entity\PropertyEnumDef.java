package com.caidaocloud.metadata.domain.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class PropertyEnumDef {
    private String value;
    private String display;
    private Map<String, String> i18nDisplay;


    public boolean coveredBy(List<PropertyEnumDef> enumDef) {
        return enumDef.stream().map(it->it.getValue()).collect(Collectors.toList()).contains(value);
    }
}
