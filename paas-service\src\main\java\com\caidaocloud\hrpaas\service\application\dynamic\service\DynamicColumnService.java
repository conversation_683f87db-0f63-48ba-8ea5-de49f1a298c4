package com.caidaocloud.hrpaas.service.application.dynamic.service;

import com.caidaocloud.hrpaas.paas.common.dto.UserDynamicConfig;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicColumnConfigDto;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicPropertyDto;
import com.caidaocloud.hrpaas.paas.common.event.DynamicColumnSaveEvent;
import com.caidaocloud.hrpaas.service.domain.dynamic.entity.DynamicColumnConfig;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DynamicColumnService {
    public void save(DynamicColumnConfigDto config) {
        DynamicColumnConfig existed = DynamicColumnConfig.loadByCode(config.getCode());
        if (null == existed) {
            FastjsonUtil.convertObject(config, DynamicColumnConfig.class).create();
        } else {
            FastjsonUtil.convertObject(config, DynamicColumnConfig.class).updateById(existed.getId());
            if (checkColumnModify(existed.getProperties(), config.getProperties())) {
                new DynamicColumnSaveEvent(config.getCode(), SecurityUserUtil.getSecurityUserInfo().getTenantId()).publish();
            }
        }
    }

    /**
     * 校验是否有新增动态列
     *
     * @param exist
     * @param data
     * @return
     */
    private boolean checkColumnModify(List<DynamicPropertyDto> exist, List<DynamicPropertyDto> data) {
        for (DynamicPropertyDto item : data) {
            boolean isExist = exist.stream().anyMatch(e -> e.equals(item));

            if (!isExist) {
                return true;
            }
        }
        return false;
    }

    public List<DynamicColumnConfig> list() {
        return DynamicColumnConfig.list();
    }

    public DynamicColumnConfig loadByCode(String code) {
        return DynamicColumnConfig.loadByCode(code);
    }

    public void saveUser(UserDynamicConfig config, String userId) {
        String code = config.getCode();
        UserDynamicConfig existed = DynamicColumnConfig.loadUser(code, userId);
        if (null == existed) {
            DynamicColumnConfig.addUser(code, userId, config);
        } else {
            DynamicColumnConfig.updateUser(code, userId, config);
        }

    }

    public UserDynamicConfig loadUser(String code, String userId) {
        UserDynamicConfig config = DynamicColumnConfig.loadUser(code, userId);
        if (null == config) {
            return new UserDynamicConfig();
        } else {
            return config;
        }
    }

    public List<DynamicColumnConfig> loadByCode(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return Lists.newArrayList();
        }
        codeList = codeList.stream().map(e -> e.toUpperCase()).collect(Collectors.toList());
        return DynamicColumnConfig.loadByCode(codeList);
    }
}
