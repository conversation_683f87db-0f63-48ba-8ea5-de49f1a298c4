package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataKvDo;
import com.caidaocloud.metadata.infrastructure.configuration.handler.BooleanTypeHandler;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("paas_kv")
public class MetadataKvPo extends BasePo<MetadataKvPo, MetadataKvDo> {
    private String property;
    private String content;
    private String i18n;
    /**
     * 是否删除。0 默认值，未删除，1 已删除
     */
    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean deleted;
}
