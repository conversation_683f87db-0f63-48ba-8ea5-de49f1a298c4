package com.caidaocloud.hrpaas.service.application.uiform.enums;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/1/7
 */
public enum SchemaPropertyDataTypeRelEnum {
    String("string"),
    Number("number"),
    Timestamp("datetime"),
    Enum("enum"),
    <PERSON><PERSON><PERSON>("boolean"),
    <PERSON><PERSON>("pid"),
    Job_Grade_Range("Job_Grade_Range"),
    PROVINCE_CITY("PROVINCE_CITY"),
    Dict("Dict");

    private String schemaFieldDataType;

    SchemaPropertyDataTypeRelEnum(String schemaFieldDataType) {
        this.schemaFieldDataType = schemaFieldDataType;
    }

    public String getSchemaFieldDataType() {
        return schemaFieldDataType;
    }

    public void setSchemaFieldDataType(java.lang.String schemaFieldDataType) {
        this.schemaFieldDataType = schemaFieldDataType;
    }

    public static PropertyDataType getPropertyDataTypeBySchemaFieldDataType(String schemaFieldDataType) {
        if (StringUtils.isEmpty(schemaFieldDataType)) {
            return PropertyDataType.String;
        }
        for (SchemaPropertyDataTypeRelEnum relEnum : SchemaPropertyDataTypeRelEnum.values()) {
            if (schemaFieldDataType.equals(relEnum.getSchemaFieldDataType())) {
                return PropertyDataType.valueOf(relEnum.name());
            }
        }
        return PropertyDataType.String;
    }
}
