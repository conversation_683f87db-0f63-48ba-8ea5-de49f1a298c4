package com.caidaocloud.hrpaas.service.interfaces.facade.template;

import com.caidaocloud.hrpaas.service.application.metadata.service.ImportTemplateService;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.ImportTemplateDo;
import com.caidaocloud.hrpaas.service.interfaces.dto.template.ImportTemplateDto;
import com.caidaocloud.hrpaas.service.interfaces.vo.template.ImportTemplateVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/hrpaas/v1/web/template")
@Api(value = "导入模板操作", tags = "导入模板操作--后端接口")
public class ImportTemplateController {

    @Autowired
    private ImportTemplateService importTemplateService;

    @PostMapping(value="/save")
    @ApiOperation("新增导入模板")
    public Result<Boolean> save(@RequestBody ImportTemplateDto importTemplateDto){
        importTemplateService.saveImportTemplate(importTemplateDto);
        return Result.ok(true);
    }

    @PostMapping(value="/update")
    @ApiOperation("修改导入模板")
    public boolean update(@RequestBody ImportTemplateDto importTemplateDto){
        importTemplateService.updateImportTemplate(importTemplateDto);
        return true;
    }

    @DeleteMapping(value = "/delete")
    @ApiOperation("删除模板数据")
    public boolean delete(@RequestParam("id") String id){
        importTemplateService.delete(id);
        return true;
    }

    @PostMapping(value="/filter")
    @ApiOperation("查看模板")
    public Result<ImportTemplateVo> filter(@RequestBody ImportTemplateDto importTemplateDto){
        ImportTemplateDo importTemplateDo = importTemplateService.selectImportTemplate(importTemplateDto);
        ImportTemplateVo importTemplateVo = ObjectConverter.convert(importTemplateDo, ImportTemplateVo.class);
        return Result.ok(importTemplateVo);
    }

    @ApiOperation("获取模板列表")
    @GetMapping("/list")
    public Result<List<ImportTemplateVo>> getList(){
        List<ImportTemplateDo> importTemplateList = importTemplateService.getList();
        return Result.ok(ObjectConverter.convertList(importTemplateList, ImportTemplateVo.class));
    }

    @GetMapping(value="/filterCode")
    @ApiOperation("根据模板编码查看详情")
    public Result<ImportTemplateVo> filterCode(@RequestParam String templateCode){
        ImportTemplateDo importTemplateDo = importTemplateService.selectImportTemplateByCode(templateCode);
        ImportTemplateVo importTemplateVo = ObjectConverter.convert(importTemplateDo, ImportTemplateVo.class);
        return Result.ok(importTemplateVo);
    }
}
