package com.caidaocloud.hrpaas.service.test;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class QueryTest {
    public void qTest(){
        DataFilter dataFilter = DataFilter.eq("tenantId", "caidaotest").
                andNe("deleted", Boolean.TRUE.toString());

    }
}
