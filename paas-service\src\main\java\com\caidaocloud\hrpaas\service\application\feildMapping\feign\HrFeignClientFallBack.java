package com.caidaocloud.hrpaas.service.application.feildMapping.feign;

import com.caidaocloud.hrpaas.service.application.feildMapping.dto.ChangeDefDto;
import com.caidaocloud.web.Result;

import java.util.List;

public class HrFeignClientFallBack implements HrFeignClient {

    @Override
    public Result<List<ChangeDefDto>> getByMappingConfigId(String mappingConfigId) {
        return null;
    }
}
