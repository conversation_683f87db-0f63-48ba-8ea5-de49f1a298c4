package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.jarvis.cache.annotation.Cache;
import lombok.val;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MyselfScopeRestrictionHandler implements ScopeRestrictionHandler {

    @Override
    @Cache(key = "'9_scope_restriction_' + #args[0] + '_' + #args[1]",expire = 5 *60)
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        return Lists.list(userId);
    }

}
