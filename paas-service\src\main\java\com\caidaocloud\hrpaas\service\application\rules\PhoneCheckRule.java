package com.caidaocloud.hrpaas.service.application.rules;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 手机号校验规则
 * <AUTHOR>
 * @date 2022-01-04
 */
@Service
public class PhoneCheckRule implements ICheckRule {
    private final static String PHONE_REGEX = "/^1(3\\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\\d|9[0-35-9])\\d{8}$/";
    private static Pattern PHONE_PATTERN = Pattern.compile(PHONE_REGEX);
    private static String PHONE_TIPS = "手机号不合法";

    @Override
    public String getRuleType() {
        return "phone";
    }

    @Override
    public String [] getRuleGroup() {
        return new String [] {"String"};
    }

    @Override
    public void check(IRule checkRuleDto) {
        if(null == checkRuleDto || StringUtil.isEmpty(checkRuleDto.value())){
            return;
        }

        Matcher matcher = PHONE_PATTERN.matcher(checkRuleDto.value());
        if(!matcher.matches()){
            String tips = StringUtil.isBlank(checkRuleDto.tips()) ? PHONE_TIPS : checkRuleDto.tips();
            throw new ServerException(tips);
        }
    }
}
