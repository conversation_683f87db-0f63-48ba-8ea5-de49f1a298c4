package com.caidaocloud.hrpaas.service.test;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceRequestMethod;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.DataSourceType;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiDataSourceDto;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiGeneralDataSourceDto;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class DataSourceTypeTest {
    private DataSourceType type;

    private String name;

    public static void main(String[] args) {
        getSalaryReason();

        System.out.println(DataSourceType.CUSTOM.toString());

        DataSourceTypeTest dataSourceTypeTest = new DataSourceTypeTest();
        dataSourceTypeTest.setName("aaaa");
        dataSourceTypeTest.setType(DataSourceType.EXTERNAL_SYSTEM);

        System.out.println(JSON.toJSONString(dataSourceTypeTest));

        String str = "{\"name\":\"aaaa\",\"type\":\"1\"}";

        DataSourceTypeTest test = JSON.parseObject(str, DataSourceTypeTest.class);
        System.out.println(test.getName());
        System.out.println(test.getType());

    }

    private static UiDataSourceDto getSalaryReason(){
        UiDataSourceDto uiDataSourceDto = new UiDataSourceDto();
        uiDataSourceDto.setType(DataSourceType.SYSTEM_DICT);
        uiDataSourceDto.setSourceName("调薪原因");
        uiDataSourceDto.setDescription("薪资管理，调薪原因字典下拉框");

        Map params = new HashMap(10);

        Map mongoMap = new HashMap(20);
        mongoMap.put("url", "api/bcc/dict/getSystemParams");
        mongoMap.put("requestMethod", "POST");

        params.put("typeCode", "SalaryReason");
        mongoMap.put("requestParams", params);

        String mongoJson = FastjsonUtil.toJson(mongoMap);

        System.out.println("mongoJson=" + mongoJson);
        uiDataSourceDto.setConfig(bulidGeneralDataSource("api/bcc/dict/getSystemParams", DataSourceRequestMethod.POST, params));
        return uiDataSourceDto;
    }

    private static UiGeneralDataSourceDto bulidGeneralDataSource(String url, DataSourceRequestMethod requestMethod, Map requestParams){
        UiGeneralDataSourceDto dataSource = new UiGeneralDataSourceDto();
        dataSource.setUrl(url);
        dataSource.setRequestMethod(requestMethod);
        dataSource.setRequestParams(requestParams);

        System.out.println("bulidGeneralDataSource=" + FastjsonUtil.toJson(dataSource));
        return dataSource;
    }
}
