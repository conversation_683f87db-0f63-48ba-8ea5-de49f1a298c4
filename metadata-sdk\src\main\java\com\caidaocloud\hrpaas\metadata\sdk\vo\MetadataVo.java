package com.caidaocloud.hrpaas.metadata.sdk.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.DefStatus;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.google.common.collect.Lists;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.totallylazy.Sequences;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Slf4j
public class MetadataVo {

    @ApiModelProperty("业务模型全限定名")
    private String identifier;
    @ApiModelProperty("业务模型名称")
    private String name;

    private Map<String, String> i18nName;

    /**
     * 所属微服务，模型中的数据即存储在名为{owner}_entity_{tenantId}的collection中
     */
    @ApiModelProperty("所属微服务")
    private String owner;

    @ApiModelProperty("是否内置模型")
    private boolean builtIn;

    @ApiModelProperty("状态")
    private DefStatus status;

    @ApiModelProperty("是否树结构")
    private boolean tree = false;

    /**
     * 父模型，父模型中的所有属性都会被本模型继承。父模型的owner必须与本模型相同（entity.common.BaseModel例外）。
     * 所有模型实际上最终都会继承entity.common.BaseModel模型
     */
    @ApiModelProperty("父模型")
    private String parent;

    @ApiModelProperty("标题属性")
    private String label;

    @ApiModelProperty("是否开启时间轴")
    private boolean timelineEnabled;

    @ApiModelProperty("业务主键对应的属性")
    private List<String> mainKey;

    @ApiModelProperty("创建时间")
    private long createTime;
    @ApiModelProperty("创建人")
    private String createBy;
    @ApiModelProperty("更新时间")
    private long updateTime;
    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("标准属性")
    private List<MetadataPropertyVo> standardProperties;

    @ApiModelProperty("自定义属性")
    private List<MetadataPropertyVo> customProperties;

    @ApiModelProperty("继承标准属性")
    private List<MetadataPropertyVo> inheritedStandardProperties;

    @ApiModelProperty("继承自定义属性")
    private List<MetadataPropertyVo> inheritedCustomProperties;

    @ApiModelProperty("关联关系")
    private List<MetadataRelationVo> relations = Lists.newArrayList();

    public List<MetadataPropertyVo> fetchAllProperties() {
        List<MetadataPropertyVo> allProperties = Lists.newArrayList();
        if (null != standardProperties) {
            allProperties.addAll(standardProperties);
        }
        if (null != customProperties) {
            allProperties.addAll(customProperties);
        }
        if (null != inheritedStandardProperties) {
            allProperties.addAll(inheritedStandardProperties);
        }
        if (null != inheritedCustomProperties) {
            allProperties.addAll(inheritedCustomProperties);
        }
        return allProperties;
    }

    @Deprecated
    public void doExpExecute(List<MetadataPropertyVo> properties, Map dataMap,NestPropertyValue nestPropertyValue) {
        Map<String, Object> map = convertData(this, dataMap);
        Sequences.sequence(properties).filter(MetadataPropertyVo::isExpEnable).forEach(expProp -> {
            Object value = expProp.getDataType() == PropertyDataType.Number || expProp.getDataType() == PropertyDataType.Timestamp ? 0 : "";
            try {
                value = AviatorEvaluator.execute(expProp.getExpression(), map);
            }
            catch (Exception e) {
                log.error("expression execute error, expression: {}", expProp.getExpression(), e);
            }
            dataMap.put(expProp.getProperty(), value);
            nestPropertyValue.add(expProp.getProperty(), String.valueOf(value));
        });
    }

    public void doExpExecute(Map dataMap,NestPropertyValue nestPropertyValue) {
        doExpExecute(fetchAllProperties(), dataMap, nestPropertyValue);
    }

    public void doExpExecute(List<MetadataPropertyVo> properties,Map dataMap) {
        doExpExecute(fetchAllProperties(), dataMap, new NestPropertyValue());
    }

    private static Map<String, Object> convertData(MetadataVo metadata, Map dataMap) {
        Map<String, Object> map = new HashMap<>();
        Sequences.sequence(metadata.fetchAllProperties()).forEach(p -> {
            if (p.getDataType().isComponent()) {
                return;
            }
            Object value = dataMap.get(p.getProperty());
            switch (p.getDataType()) {
            case Timestamp:
                map.put(p.getProperty(), value == null ? null : Long.valueOf(String.valueOf(value)));
                break;
            case Number:
                map.put(p.getProperty(), value == null ? null : Double.valueOf(String.valueOf(value)));
                break;
            case String:
                map.put(p.getProperty(), value);
            default:
            }
        });
        return map;
    }
}
