package com.caidaocloud.hrpaas.service.application.form.feign;

import com.caidaocloud.hrpaas.service.application.form.dto.WfTaskApproveDTO;
import com.caidaocloud.hrpaas.service.application.form.dto.WfTaskRevokeDTO;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.*;
import com.caidaocloud.workflow.feign.WfRegisterFeignFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
    value = "${feign.rename.caidaocloud-workflow-service-v2:caidaocloud-workflow-service-v2}",
    fallback = WfRuntimeFeignFallback.class,
    configuration = FeignConfiguration.class,
    contextId = "wfRuntimeFeign"
)
public interface IWfRuntimeFeign {

    @PostMapping("/api/workflow/v2/operate/task/approve")
    Result approve(WfTaskApproveDTO approve);

    @PostMapping("/api/workflow/v2/operate/task/revoke")
    Result revoke(WfTaskRevokeDTO approve);

}
