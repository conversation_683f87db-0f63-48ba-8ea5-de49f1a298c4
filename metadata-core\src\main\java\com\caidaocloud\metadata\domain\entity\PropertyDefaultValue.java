package com.caidaocloud.metadata.domain.entity;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import lombok.Data;
import lombok.val;

import java.util.List;

@Data
public class PropertyDefaultValue {

    private String value;
    private List<String> arrayValues;
    private PropertyDataType type;
    public static PropertyDefaultValue newInstance(PropertyDataType dataType, String value, List<String> arrayValues) {
        val defaultValue = new PropertyDefaultValue();
        defaultValue.value = value;
        defaultValue.type = dataType;
        defaultValue.arrayValues = arrayValues;
        return defaultValue;
    }

}
