package com.caidaocloud.metadata.infrastructure.repository.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.enums.IndicateType;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SpecifiedMultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.CountUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.metadata.domain.entity.EntityData;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.EntityRelationDef;
import com.caidaocloud.metadata.domain.entity.PropertyData;
import com.caidaocloud.metadata.domain.repository.IEntityDataRepository;
import com.caidaocloud.metadata.domain.repository.IEntityDefRepository;
import com.caidaocloud.metadata.infrastructure.repository.impl.util.SpecifiedDataFilterParser;
import com.caidaocloud.metadata.infrastructure.repository.po.BatchSqlRecordPo;
import com.caidaocloud.metadata.infrastructure.repository.po.EntityDefPo;
import com.caidaocloud.metadata.infrastructure.repository.utils.EncryptionTool;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.metadata.infrastructure.repository.impl.util.SqlGeneratorUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.*;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @Author: Jarvis
 * @Desc:
 * @Date: 6/11/2021 3:57 PM
 */
@Repository
@Slf4j
public class EntityDataRepositoryImpl implements IEntityDataRepository {

    @Value("${anonymous.tenant:}")
    private String anonymousTenant;

    @NacosValue("${dbType:mysql}")
    private String dbType;

    @Value("${property.encrypt.key.common:jlt4fdgak56jsdladsjdgladsjgldsjl}")
    private String encryptKey;

    public String getTenantEncryptKey(){
        val tenantId = getTenantId();
        val tenantSecret = SpringUtil.getContext().getEnvironment().getProperty("property.encrypt.key." + tenantId, "");
        if(StringUtils.isEmpty(tenantSecret)){
            if(StringUtils.isEmpty(encryptKey)){
                throw new ServerException("密钥为空");
            }
            return encryptKey;
        }else{
            return tenantSecret;
        }
    }

    private Map<String, String> escapeChar = Maps.map(
            "mysql", "`",
            "postgresql", "\""
    );
    private Map.Entry<String, String> propertyToField;

    private String colEscapeChar(){
        return escapeChar.get(dbType);
    }

    @Autowired
    private EntityDataMapper mapper;

    @Autowired
    private EntityDefMapper defMapper;

    @Autowired
    private IEntityDefRepository defRepository;

    @Autowired
    private BatchSqlRecordMapper batchSqlRecordMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private String getTenantId() {
        String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        if(StringUtil.isEmpty(tenantId)){
            if(ThreadLocalUtil.isAnonymousAllowed()){
                tenantId = anonymousTenant;
            }
        }
        return tenantId;
    }

    @Override
    public String insert(EntityData entityData) {
        if (ThreadLocalUtil.isAnonymousAllowed() && StringUtil.isEmpty(entityData.getTenantId())) {
            entityData.setTenantId(anonymousTenant);
            entityData.removePropertyValue("tenantId");
            entityData.addPropertyValue("tenantId", anonymousTenant, PropertyDataType.String);
        }
        if (StringUtils.isEmpty(entityData.getId())) {
            entityData.setId(SnowUtil.nextId());
        }
        Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                .eq(EntityDefPo::getIdentifier, entityData.getIdentifier());
        val def = this.defMapper.selectOne(wrapper);
        Map<String, String> propertiesToFieldMap = FastjsonUtil.toObject(def.getPropertyToFieldMapping(), Map.class);
        StringBuilder fields = new StringBuilder();
        StringBuilder values = new StringBuilder();
        fields.append("id").append(",");
        fields.append("identifier").append(",");
        fields.append("bid").append(",");
        fields.append("tenant_id").append(",");
        fields.append("create_time").append(",");
        fields.append("update_time").append(",");
        fields.append("create_by").append(",");
        fields.append("update_by").append(",");
        fields.append("data_start_time").append(",");
        fields.append("data_end_time").append(",");
        fields.append("deleted");

        val params = Lists.list(Long.valueOf(entityData.getId()), entityData.getIdentifier(), entityData.getBid(),
                entityData.getTenantId(), entityData.getCreateTime(), entityData.getUpdateTime(),
                entityData.getCreateBy(), entityData.getUpdateBy(), entityData.getDataStartTime(),
                entityData.getDataEndTime(), entityData.isDeleted()
        );
        values.append(
                StringUtils.join(
                        Sequences.sequence(params).map(it -> "?").toList(),
                        ","
                )
        );
        val defEntity = def.toEntity(EntityDef.class);
        entityData.getProperties().forEach(property -> {
                    if (propertiesToFieldMap.containsKey(property.getProperty())) {
                        String value = null;
                        if (property.getDataType().isArray()) {
                            if (null != property.getArrayValues()) {
                                value = FastjsonUtil.toJson(property.getArrayValues());
                            }
                        }
                        else {
                            value = property.getValue();
                        }
                        fields.append(",").append(colEscapeChar()).
                                append(propertiesToFieldMap.get(property.getProperty()))
                                .append(colEscapeChar());
                        values.append(",?");
                        val encrypted = defEntity.fetchProperties().stream().filter(it ->
                                (StringUtils.equals(it.getProperty(), property.getProperty())
                                        || property.getProperty().startsWith(it.getProperty() + "."))
                                        && it.isEncrypted()
                        ).findFirst().isPresent();
                        params.add(encrypted ?
                                EncryptionTool.searchableEncrypt(value, getTenantEncryptKey()) : value);
                    }
                }
        );
        val sql = "insert into " +
                SnakeCaseConvertor.toSnake(entityData.getIdentifier()) +
                "_" + getTenantId() + "(" +
                fields + ") values (" +
                values + ")";
        jdbcTemplate.update(sql, params.toArray());
        entityData.getBid();
        updateRelations(def, entityData, Lists.list());
        return entityData.getId();
    }

    @Override
    public void update(EntityData entityData){
        update(entityData, Lists.list());
    }

    @Override
    public void update(EntityData entityData, List<EntityRelationDef> relationDefs) {
        if(ThreadLocalUtil.isAnonymousAllowed() && StringUtil.isEmpty(entityData.getTenantId())){
            entityData.setTenantId(anonymousTenant);
            entityData.removePropertyValue("tenantId");
            entityData.addPropertyValue("tenantId", anonymousTenant, PropertyDataType.String);
        }
        Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                .eq(EntityDefPo::getIdentifier, entityData.getIdentifier());
        val def = this.defMapper.selectOne(wrapper);
        Map<String,String> propertiesToFieldMap = FastjsonUtil.toObject(def.getPropertyToFieldMapping(), Map.class);
        val setSql = new StringBuilder();
        List<Object> params = Lists.list(entityData.getIdentifier(),entityData.getBid(),
                entityData.getTenantId(), entityData.getCreateTime(),entityData.getUpdateTime(),
                entityData.getCreateBy(),entityData.getUpdateBy(),entityData.getDataStartTime(),
                entityData.getDataEndTime(),entityData.isDeleted());
        setSql.append("identifier").append("=?,")
                .append("bid").append("=?,")
                .append("tenant_id").append("=?,")
                .append("create_time").append("=?,")
                .append("update_time").append("=?,")
                .append("create_by").append("=?,")
                .append("update_by").append("=?,")
                .append("data_start_time").append("=?,")
                .append("data_end_time").append("=?,")
                .append("deleted").append("=?");
        val defEntity = def.toEntity(EntityDef.class);
        entityData.getProperties().forEach(property-> {
                    if(propertiesToFieldMap.containsKey(property.getProperty())){
                        String value = null;
                        if(property.getDataType().isArray()){
                            if(null != property.getArrayValues()){
                                value = FastjsonUtil.toJson(property.getArrayValues());
                            }
                        }else{
                            value = property.getValue();
                        }

                        setSql.append(",").append(colEscapeChar()).
                                append(propertiesToFieldMap.get(property.getProperty()))
                                .append(colEscapeChar()).append("=");
                        setSql.append("?");
                        val encrypted = defEntity.fetchProperties().stream().filter(it->
                                (StringUtils.equals(it.getProperty(), property.getProperty())
                                        || property.getProperty().startsWith(it.getProperty() + "."))
                                        && it.isEncrypted()
                        ).findFirst().isPresent();
                        params.add(encrypted?
                                EncryptionTool.searchableEncrypt(value, getTenantEncryptKey()) : value);
                    }
                }
        );
        val sql = "update " + SnakeCaseConvertor.toSnake(entityData.getIdentifier()) +
                "_" + getTenantId() + " set " + setSql + " where id = ?";
        params.add(Long.valueOf(entityData.getId()));
        jdbcTemplate.update(sql, params.toArray());
        updateRelations(def, entityData, relationDefs);
    }

    // TODO: 2022/9/16 update relationDefs
    private void updateRelations(EntityDefPo def, EntityData entityData,List<EntityRelationDef> relationDefs){
        relationDefs.forEach(relation->{
            val tableName = SnakeCaseConvertor.toSnake(entityData.getIdentifier()+".relation."+relation.getProperty() + "_" + getTenantId());
            val sql = "delete from " + tableName + " where source_id = ? and data_start_time = ?";
            val params = new Object[]{entityData.getBid(), entityData.getDataStartTime()};
            jdbcTemplate.update(sql, params);
        });
        Set<String> updateProperties = Sequences.sequence(relationDefs).map(EntityRelationDef::getProperty).toSet();
        // 关联关系property属性不能重复
        entityData.getRelations().stream()
                .filter(releation->updateProperties.contains(releation.getProperty())).forEach(relation->{
            val tableName = SnakeCaseConvertor.toSnake(entityData.getIdentifier()+".relation."+relation.getProperty() + "_" + getTenantId());
            relation.getTargetIds().forEach(targetId->{
                val sql ="insert into " + tableName +
                        "(identifier, property, source_id, target_id," +
                        "create_time, create_by, data_start_time, data_end_time)" +
                        "values (?,?,?,?,?,?,?,?)";
                val params = new Object[]{entityData.getIdentifier(),
                        relation.getProperty(), entityData.getBid(), targetId,
                        entityData.getCreateTime(), entityData.getCreateBy(),
                        entityData.getDataStartTime(), entityData.getDataEndTime()
                };
                jdbcTemplate.update(sql, params);
            }
            );
        });
    }

    @Override
    public List<EntityData> loadAll(String identifier, String bid){
        val tableName = SnakeCaseConvertor.toSnake(identifier) +
                "_" + getTenantId();
        val sql = "select * from " + tableName + " where bid = ?";
        val result = jdbcTemplate.queryForList(sql, new Object[]{ bid });
        return dbDataToData(result, identifier, Maps.map(), false);
    }

    @Override
    public List<EntityData> range(String identifier, String bid, long startTime, long endTime) {
        val tableName = SnakeCaseConvertor.toSnake(identifier) +
                "_" + getTenantId();
        val sql = "select * from " + tableName + " where bid = ? and data_start_time <= ? and data_end_time >= ?";
        val params = new Object[]{bid, endTime, startTime};
        val result = jdbcTemplate.queryForList(sql, params);
        return dbDataToData(result, identifier, Maps.map(), false);
    }

    @Override
    public Option<EntityData> load(String identifier, String bid, long time, List<String> relateList, DataQueryDto query) {
        SqlGeneratorUtil.initQuerySqlGenInfo(getTenantId(), colEscapeChar(), dbType, query);
        try{
            SqlGeneratorUtil.addProperties(relateList, ".");
            val jdbcParams = SqlGeneratorUtil.generateSqlAndReturnJdbcParams(identifier, bid, time, null, 0, 0);
            val sql = SqlGeneratorUtil.getSelectSql();
            Map<String, PropertyDataType> relationDataTypeMap = SqlGeneratorUtil.getRelationDataTypes();
            val result = jdbcTemplate.queryForList(sql, jdbcParams);
            if(result.isEmpty()){
                return Option.none();
            }
            val merged = dbDataToData(result, identifier, relationDataTypeMap, true);
            return Option.some(merged.get(0));
        }finally {
            SqlGeneratorUtil.clearQuerySqlGenInfo();
        }

    }


    @Override
    public PageResult<EntityData> page(String identifier, DataFilter filter, int pageNo, int pageSize, List<String> relateList, List<String> specifyProperties, boolean group, String orderBy, long time, DataQueryDto query) {
        if(pageSize > CountUtil.MAX_LIMIT){
            throw new ServerException("超出最大查询限制");
        }
        SqlGeneratorUtil.initQuerySqlGenInfo(getTenantId(), colEscapeChar(), dbType, query);
        try{
            if(group){
                SqlGeneratorUtil.setGroup(true);
            }
            SqlGeneratorUtil.addProperties(relateList, ".");
            SqlGeneratorUtil.addFilter(filter);
            val jdbcParams = SqlGeneratorUtil.generateSqlAndReturnJdbcParams(identifier, null, time, filter,specifyProperties, pageSize, pageNo, orderBy);
            val countSql = SqlGeneratorUtil.getCountSql();
            val count = jdbcTemplate.queryForObject(countSql, jdbcParams, Long.class);
            if(count == 0){
                return new PageResult<>(Lists.list(), pageNo, pageSize, 0);
            }
            String sql = SqlGeneratorUtil.getSelectSql();
            if(!SqlGeneratorUtil.getQuerySqlGenInfo().isQueryOneTable() && group){
                val bidSql = SqlGeneratorUtil.getBidSql();
                val bidList = Sequences.sequence(jdbcTemplate.queryForList(bidSql, jdbcParams)).map(it->"'" + it.get("bid").toString()+ "'").toList();
                if(bidList.isEmpty()){
                    return new PageResult<>(Lists.list(), pageNo, pageSize, count.intValue());
                }
                sql = sql.replace("##bidList##",
                        StringUtils.join(bidList, ","));
            }
            val result = jdbcTemplate.queryForList(sql, jdbcParams);
            Map<String, PropertyDataType> relationDataTypeMap = SqlGeneratorUtil.getRelationDataTypes();
            boolean mergeSameBid = group;
            if (time == -1l) {
                mergeSameBid = false;
            }
            return new PageResult(dbDataToData(result, identifier, relationDataTypeMap, mergeSameBid), pageNo, pageSize, count.intValue());
        }finally {
            SqlGeneratorUtil.clearQuerySqlGenInfo();
        }
    }


    @Override
    public void delete(String identifier, String id){
        Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                .eq(EntityDefPo::getIdentifier, identifier);
        val def = this.defMapper.selectOne(wrapper);
        val tableName = SnakeCaseConvertor.toSnake(identifier) +
                "_" + getTenantId();
        val sql = "delete from " + tableName + " where id = ?";
        jdbcTemplate.update(sql, new Object[]{Long.valueOf(id)});
        updateRelations(def, new EntityData(), Lists.list());
    }

    @Override
    public long countBids(String identifier, List<String> bidList, long time) {
        if(bidList.isEmpty()){
            return 0;
        }
        val tableName = SnakeCaseConvertor.toSnake(identifier) +
                "_" + getTenantId();
        val sql = "select count(1) from " + tableName +
                " where bid in (" +
                StringUtils.join(
                        Sequences.sequence(bidList).map(it->"?").toList(),
                        ","
                ) + ") and " +
                "data_start_time <= ? and data_end_time >= ?";
        Sequence<Object> params = Sequences.sequence(bidList);
        return jdbcTemplate.queryForObject(sql, params.append(time).append(time).toArray(), Long.class);
    }

    @Override
    public void batchUpdate(String identifier, DataFilter filter, EntityData entity) {
        throw new NotImplementedException("not supported");
    }

    private List<EntityData> dbDataToData(List<Map<String,Object>> dbResult, String identifier, Map<String, PropertyDataType> relations, Boolean mergeSameBid){
        EntityDefPo defPo;
        if(SqlGeneratorUtil.getQuerySqlGenInfo() != null){
            defPo = SqlGeneratorUtil.fetchDefPo(identifier);
        }else{
            Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                    .eq(EntityDefPo::getIdentifier, identifier);
            defPo = this.defMapper.selectOne(wrapper);
        }
        val def = defPo.toEntity(EntityDef.class);
        val propertiesDef = def.fetchProperties();
        Map<String,String> propertiesToFieldMap = FastjsonUtil.toObject(defPo.getPropertyToFieldMapping(), Map.class);
        List<EntityData> dataList = dbResult.stream().map(record->{
                    val data = FastjsonUtil.convertObject(record, EntityData.class);
                    record.forEach((column, value)->{
                        for(Map.Entry<String, String> propertyToField : propertiesToFieldMap.entrySet()){
                            if(StringUtils.equals(propertyToField.getValue(), column)){
                                val property = propertyToField.getKey();
                                propertiesDef.stream()
                                        .filter(it->
                                                !it.isExpEnable() && (StringUtils.equals(property, it.getProperty()) ||
                                                        property.startsWith(it.getProperty() + "."))
                                        )
                                        .findFirst().ifPresent(propertyDef->{
                                    val propertyData = new PropertyData();
                                    propertyData.setProperty(property);
                                    propertyData.setDataType(propertyDef.getDataType());
                                    if(propertyDef.getDataType().isArray()){
                                        if(value != null){
                                            val decrypted = EncryptionTool.isSearchableEncrypted(value.toString())
                                                    ? EncryptionTool.searchableDecrypt(value.toString(), getTenantEncryptKey())
                                                    : value.toString();
                                            val valueArray = FastjsonUtil.toArrayList(decrypted, String.class);
                                            propertyData.setArrayValues(valueArray);
                                        }
                                    }else{
                                        if(value != null){
                                            val decrypted = EncryptionTool.isSearchableEncrypted(value.toString())
                                                    ? EncryptionTool.searchableDecrypt(value.toString(), getTenantEncryptKey())
                                                    : value.toString();
                                            propertyData.setValue(decrypted);
                                        }
                                    }
                                    data.getProperties().add(propertyData);
                                });
                                return;
                            }
                        }
                        for(String relation: relations.keySet()){
                            if(StringUtils.equals(SnakeCaseConvertor.toSnake(relation), column)){
                                val propertyData = new PropertyData();
                                propertyData.setProperty(relation);
                                propertyData.setDataType(relations.get(relation).toArray());
                                if(relations.get(relation).isArray()){
                                    if(value != null){
                                        val decrypted = EncryptionTool.isSearchableEncrypted(value.toString())
                                                ? EncryptionTool.searchableDecrypt(value.toString(), getTenantEncryptKey())
                                                : value.toString();
                                        propertyData.setArrayValues(FastjsonUtil.toArrayList(decrypted, String.class));
                                    }else{
                                        propertyData.setArrayValues(Lists.list());
                                    }
                                }else{
                                    if(value != null){
                                        val decrypted = EncryptionTool.isSearchableEncrypted(value.toString())
                                                ? EncryptionTool.searchableDecrypt(value.toString(), getTenantEncryptKey())
                                                : value.toString();
                                        propertyData.setValue(decrypted);
                                        propertyData.setArrayValues(Lists.list(decrypted));
                                    }else{
                                        propertyData.setArrayValues(Lists.list());
                                    }
                                }
                                data.getProperties().add(propertyData);
                                return;
                            }
                        }
                    });
            if (SqlGeneratorUtil.isExpression()) {
                def.doExpProperty(data, record, SqlGeneratorUtil.getSpecifyExpProperty(), getTenantEncryptKey());
            }
                    return data;
                }
        ).collect(Collectors.toList());
        if(!mergeSameBid){
            return dataList;
        }
        List<EntityData> merged = Lists.list();
        for(EntityData entityData : dataList){
            val existedO = merged.stream()
                    .filter(it->StringUtils.equals(it.getBid(), entityData.getBid())).findAny();
            if(existedO.isPresent()){
                val existed = existedO.get();
                for(String relation: relations.keySet()){
                    for(PropertyData toBeAddProperty: entityData.getProperties()){
                        if(toBeAddProperty.getProperty().equals(relation)){
                            val existedPropO = existed.getProperties().stream().filter(it->it.getProperty().equals(relation)).findFirst();
                            if(existedPropO.isPresent()){
                                val existedProp = existedPropO.get();
                                existedProp.getArrayValues().addAll(toBeAddProperty.getArrayValues());
                            }else{
                                existed.getProperties().add(toBeAddProperty);
                            }
                        }
                    }

                }
            }else{
                merged.add(entityData);
            }
        }
        return merged;
    }

    @Override
    public void batchDelete(String identifier, DataFilter filter) {
        val table = SnakeCaseConvertor.toSnake(identifier) + "_" + getTenantId();
        SqlGeneratorUtil.initQuerySqlGenInfo(getTenantId(), colEscapeChar(), dbType, null);
        try{
            val where = SqlGeneratorUtil.toWhereCauses(identifier, filter);
            val sql = "delete from " + table + " where "+ where.first();
            jdbcTemplate.update(sql, where.second());
            val record = new BatchSqlRecordPo();
            record.setExecTime(System.currentTimeMillis());
            record.setSqlContent(sql);
            //batchSqlRecordMapper.insert(record);
        }finally {
            SqlGeneratorUtil.clearQuerySqlGenInfo();
        }
    }

    @Override
    public void batchInsert(String identifier, List<EntityData> dataList) {
        if(dataList.isEmpty()){
            return;
        }
        for(EntityData entityData : dataList){
            if(ThreadLocalUtil.isAnonymousAllowed()  && StringUtil.isEmpty(entityData.getTenantId())){
                entityData.setTenantId(anonymousTenant);
                entityData.removePropertyValue("tenantId");
                entityData.addPropertyValue("tenantId", anonymousTenant, PropertyDataType.String);
            }
            if(StringUtils.isEmpty(entityData.getId())){
                entityData.setId(SnowUtil.nextId());
            }
        }
        Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                .eq(EntityDefPo::getIdentifier, identifier);
        val def = this.defMapper.selectOne(wrapper);
        Map<String,String> propertiesToFieldMap = FastjsonUtil.toObject(def.getPropertyToFieldMapping(), Map.class);
        StringBuilder fields = new StringBuilder();
        List<StringBuilder> valuesList = Lists.list();
        fields.append("id").append(",");
        fields.append("identifier").append(",");
        fields.append("bid").append(",");
        fields.append("tenant_id").append(",");
        fields.append("create_time").append(",");
        fields.append("update_time").append(",");
        fields.append("create_by").append(",");
        fields.append("update_by").append(",");
        fields.append("data_start_time").append(",");
        fields.append("data_end_time").append(",");
        fields.append("deleted");
        AtomicBoolean fieldsAdded = new AtomicBoolean(false);
        val params = Lists.list();

        val columns = new HashMap<String, String>();
        for(EntityData entityData : dataList){
            entityData.getProperties().forEach(property-> {
                        if(propertiesToFieldMap.containsKey(property.getProperty())){
                            columns.put(property.getProperty(), propertiesToFieldMap.get(property.getProperty()));
                        }
                    }
            );
        }


        for(EntityData entityData : dataList){
            StringBuilder values = new StringBuilder();
            valuesList.add(values);
            val tempParams = Lists.list(Long.valueOf(entityData.getId()),entityData.getIdentifier(),entityData.getBid(),
                    entityData.getTenantId(),entityData.getCreateTime(),entityData.getUpdateTime(),
                    entityData.getCreateBy(),entityData.getUpdateBy(),entityData.getDataStartTime(),
                    entityData.getDataEndTime(),entityData.isDeleted()
            );

            values.append(Long.valueOf(entityData.getId())+",")
                    .append("'"+entityData.getIdentifier()+"',")
                    .append("'" + entityData.getBid() + "',")
                    .append("'" + entityData.getTenantId() + "',")
                    .append(entityData.getCreateTime()+",")
                    .append(entityData.getUpdateTime()+",")
                    .append("'" + entityData.getCreateBy() + "',")
                    .append("'" + entityData.getUpdateBy() + "',")
                    .append(entityData.getDataStartTime()+",")
                    .append(entityData.getDataEndTime()+",")
                    .append(entityData.isDeleted());

//            values.append(
//                    StringUtils.join(
//                            Sequences.sequence(tempParams).map(it->"?").toList(),
//                            ","
//                    )
//            );

            val defEntity = def.toEntity(EntityDef.class);

            columns.forEach((propertyName, column)->{
                if(!(fieldsAdded.get())){
                    fields.append(",").append(colEscapeChar()).append(column)
                            .append(colEscapeChar());
                }
                //values.append(",?");
                val propertyOp = entityData.getProperties().stream().filter(it->it.getProperty().equals(propertyName)).findAny();
                String value = null;
                if(propertyOp.isPresent()){
                    val property = propertyOp.get();
                    if(property.getDataType().isArray()){
                        if(null != property.getArrayValues()){
                            value = FastjsonUtil.toJson(property.getArrayValues());
                        }
                    }else{
                        value = property.getValue();
                    }
                    val encrypted = defEntity.fetchProperties().stream().filter(it->
                            (StringUtils.equals(it.getProperty(), property.getProperty())
                                    || property.getProperty().startsWith(it.getProperty() + "."))
                                    && it.isEncrypted()
                    ).findFirst().isPresent();
                    tempParams.add(encrypted?
                            EncryptionTool.searchableEncrypt(value, getTenantEncryptKey()) : value);
                }else{
                    tempParams.add(value);
                }
                if(value == null){
                    values.append(",null ");
                }else{
                    values.append(",'"+value.replaceAll("'","''").replaceAll("\"","\"\"")+"'");
                }

            });
            fieldsAdded.set(true);
            //params.addAll(tempParams);
        }
        val sql = "insert into " +
                SnakeCaseConvertor.toSnake(identifier) +
                "_" + getTenantId() + "(" +
                fields + ") values" +
                StringUtils.join(
                        valuesList.stream().map(values-> "(" + values + ")").collect(Collectors.toList()),
                        ","
                );
        jdbcTemplate.update(sql, params.toArray());
        val record = new BatchSqlRecordPo();
        record.setExecTime(System.currentTimeMillis());
        record.setSqlContent(sql);
        //batchSqlRecordMapper.insert(record);
    }

    @Override
    public long count(String identifier, DataFilter filter,List<String> relateList, boolean group, long time) {
        SqlGeneratorUtil.initQuerySqlGenInfo(getTenantId(), colEscapeChar(), dbType, null);
        try{
            if(group){
                SqlGeneratorUtil.setGroup(true);
            }
            SqlGeneratorUtil.addProperties(relateList, ".");
            SqlGeneratorUtil.addFilter(filter);
            val jdbcParams = SqlGeneratorUtil.generateSqlAndReturnJdbcParams(identifier, null, time, filter, 1, 1);
            val countSql = SqlGeneratorUtil.getCountSql();
            val count = jdbcTemplate.queryForObject(countSql, jdbcParams, Long.class);
            return count;
        }finally {
            SqlGeneratorUtil.clearQuerySqlGenInfo();
        }
    }

    @Override
    public PageResult<EntityData> max(String identifier, String filter, String maxProperty, List<String> by, int pageSize, int pageNo, long queryTime) {
        SqlGeneratorUtil.initQuerySqlGenInfo(getTenantId(), colEscapeChar(), dbType, null);
        try{
            val tableName = SnakeCaseConvertor.toSnake(identifier) + "_" + getTenantId();
            val maxColumn = colEscapeChar()+SnakeCaseConvertor.toSnake(maxProperty)+colEscapeChar();
            val maxed = maxFunction(maxColumn);
            val filters = DataFilter.fromJsonStringArray(filter);
            val where = SqlGeneratorUtil.toWhereCauses(identifier, filters.get(0));
            val groupWhere = SqlGeneratorUtil.toWhereCauses(identifier, filters.get(1));
            val groupColumn = by.stream().map(it->colEscapeChar() +
                    SnakeCaseConvertor.toSnake(it) + colEscapeChar()).collect(Collectors.toList());
            val groupBy = StringUtils.join(groupColumn, ",");
            val onColumn = groupColumn.stream().map(it->"t1." + it + "=t2."+it).collect(Collectors.toList());

            val countSql = new StringBuilder("select count(1) from (select * from ");
            val selectSql = new StringBuilder("select t1.* from (select * from ");
            for(StringBuilder sql : Lists.list(countSql, selectSql)){
                sql.append(tableName)
                        .append(appendWhereDataTime(queryTime)).append(" and ").append(where.first()).append(")")
                        .append(" t1 inner join ").append(" (select ")
                        .append(StringUtils.join(groupColumn, ",")).append(", ").append(maxed)
                        .append(" max_column from ").append(tableName)
                        .append(appendWhereDataTime(queryTime)).append(" and ").append(groupWhere.first()).append(" group by ").append(groupBy)
                        .append(") t2 on ").append(StringUtils.join(onColumn, " and ")).append(" and t1.")
                        .append(maxColumn).append("=t2.max_column");
            }
            selectSql.append(" order by t1.update_time desc,t1.id asc ").append(limit(pageNo, pageSize));
            val params = Lists.list(where.second());
            params.addAll(Lists.list(groupWhere.second()));
            log.info("max count sql "+ countSql);
            val count = jdbcTemplate.queryForObject(countSql.toString(), params.toArray(), Long.class);
            if(count == 0){
                return new PageResult(Lists.list(),  pageNo, pageSize, 0);
            }
            log.info("max sql "+ selectSql);
            val result = jdbcTemplate.queryForList(selectSql.toString(), params.toArray());
            return new PageResult(dbDataToData(result, identifier, Maps.map(), false), pageNo, pageSize, count.intValue());
        }finally {
            SqlGeneratorUtil.clearQuerySqlGenInfo();
        }
    }

    private String appendWhereDataTime(long time, String prefix){
        return new StringBuilder().append(" where ").append(prefix).append("data_start_time <= ")
                .append(time).append(" and ").append(prefix).append("data_end_time >= ").append(time).toString();
    }

    private String appendWhereDataTime(long time){
        return appendWhereDataTime(time, "");
    }

    private String maxFunction(String column){
        if("mysql".equals(dbType)){
            return "max(cast(" + column + " as signed INTEGER))";
        }else if("postgresql".equals(dbType)){
            return "cast(max(cast(" + column + " as BIGINT)) as varchar)";
        }else{
            throw new ServerException("unsupported db type");
        }
    }

    @Override
    public List<Map<String, Object>> countByGroup(String identifier, DataFilter filter, List<String> bys, long queryTime) {
        try{
            SqlGeneratorUtil.initQuerySqlGenInfo(getTenantId(), colEscapeChar(), dbType, null);
            val tableName = SnakeCaseConvertor.toSnake(identifier) + "_" + getTenantId();
            val where = SqlGeneratorUtil.toWhereCauses(identifier, filter);
            val groupColumn = bys.stream().map(it->colEscapeChar() +
                    SnakeCaseConvertor.toSnake(it) + colEscapeChar()).collect(Collectors.toList());
            val groupBy = StringUtils.join(groupColumn, ",");
            val sql = new StringBuilder("select count(1) count, ").append(groupBy)
                    .append(" from ").append(tableName).append(appendWhereDataTime(queryTime)).append(" and ")
                    .append(where.first()).append(" group by ").append(groupBy);
            val result = jdbcTemplate.queryForList(sql.toString(), where.second());
            return result.stream().map(it->{
                Map<String, Object> converted = new HashMap<>();
                converted.put("count", Long.valueOf(it.get("count").toString()));
                for(String by : bys){
                    val value = it.get(SnakeCaseConvertor.toSnake(by));
                    converted.put(by, value == null?null:
                            EncryptionTool.isSearchableEncrypted(value.toString())?
                                    EncryptionTool.decrypt(value.toString(), getTenantEncryptKey()):value.toString());
                }
                return converted;
            }).collect(Collectors.toList());
        }finally {
            SqlGeneratorUtil.clearQuerySqlGenInfo();
        }

    }



    @Override
    public PageResult<List<EntityData>> join(DataJoin dataJoin) {
        try{
            SqlGeneratorUtil.initQuerySqlGenInfo(getTenantId(), colEscapeChar(), dbType, null);
            val firstModel = dataJoin.getFirstModel();
            val secondModel = dataJoin.getSecondModel();
            val firstJoin = dataJoin.getFirstJoin();
            val thirdModel = dataJoin.getThirdModel();
            val secondJoin = dataJoin.getSecondJoin();
            List<Object> params = Lists.list();
            List<String> selectColumn = Lists.list();
            List<String> tables = Lists.list();
            List<String> on = Lists.list();
            selectColumn.addAll(genJoinSelectColumn(firstModel, "first_table"));
            tables.add(SnakeCaseConvertor.toSnake(firstModel.getIdentifier())+"_"+getTenantId() + " first_table");
            selectColumn.addAll(genJoinSelectColumn(secondModel, "second_table"));
            tables.add(SnakeCaseConvertor.toSnake(secondModel.getIdentifier())+"_"+getTenantId() + " second_table");
            if(dataJoin.getModelCount() == 3){
                selectColumn.addAll(genJoinSelectColumn(thirdModel, "third_table"));
                tables.add(SnakeCaseConvertor.toSnake(thirdModel.getIdentifier())+"_"+getTenantId() + " third_table");
            }
            on.addAll(firstJoin.getJoin().stream().map(it->{
                val leftProperty = it.getLeftProperty();
                val rightProperty = it.getRightProperty();
                return "first_table." +
                        SqlGeneratorUtil.fetchColumnName(SqlGeneratorUtil.fetchDefPo(firstModel.getIdentifier()), leftProperty)
                        + " = " +
                        "second_table." +
                        SqlGeneratorUtil.fetchColumnName(SqlGeneratorUtil.fetchDefPo(secondModel.getIdentifier()), rightProperty);
            }).collect(Collectors.toList()));
            if(dataJoin.getModelCount() == 3){
                on.addAll(secondJoin.getJoin().stream().map(it->{
                    val isLeftFirstTable = it.getLeftIdentifier().equals(firstModel.getIdentifier());
                    return (isLeftFirstTable?"first_table.":"second_table.") +
                            SqlGeneratorUtil.fetchColumnName(SqlGeneratorUtil.fetchDefPo((isLeftFirstTable?firstModel:secondModel).getIdentifier()), it.getLeftProperty())
                            + " = " +
                            "third_table." +
                            SqlGeneratorUtil.fetchColumnName(SqlGeneratorUtil.fetchDefPo(thirdModel.getIdentifier()), it.getRightProperty());
                }).collect(Collectors.toList()));
            }
            val countSql = new StringBuilder("select count(1) ");
            val selectSql = new StringBuilder("select ").append(StringUtils.join(selectColumn,","));
            val sqlList = Lists.list(countSql, selectSql);

            sqlList.forEach(sql->{
                        sql.append(" from ").append(tables.get(0) + " join " + tables.get(1)).append(" on ")
                                .append(on.get(0));
                        if(tables.size() == 3){
                            sql.append(" join " + tables.get(2)).append(" on ").append(on.get(1));

                        }
                        sql.append(" where ")
                                .append(appendWhereDataTime(dataJoin.getQueryTime(), "first_table.").replace(" where", ""))
                                .append(appendWhereDataTime(dataJoin.getQueryTime(), "second_table.").replace(" where", " and"));
                    }
            );
            if(null != firstModel.getFilter()){
                val firstWhere = SqlGeneratorUtil.toWhereCauses(firstModel.getIdentifier(), DataFilter.fromJsonString(firstModel.getFilter()), "first_table.");
                sqlList.forEach(sql->sql.append(" and "+firstWhere.first()));
                params.addAll(Lists.list(firstWhere.second()));
            }
            if(null != secondModel.getFilter()){
                val secondWhere = SqlGeneratorUtil.toWhereCauses(secondModel.getIdentifier(), DataFilter.fromJsonString(secondModel.getFilter()),"second_table.");
                sqlList.forEach(sql->sql.append(" and "+secondWhere.first()));
                params.addAll(Lists.list(secondWhere.second()));
            }
            if(dataJoin.getModelCount() == 3){
                sqlList.forEach(sql->sql.append(appendWhereDataTime(dataJoin.getQueryTime(), "third_table.").replace(" where", " and")));
                if(null!= thirdModel.getFilter()){
                    val thirdWhere = SqlGeneratorUtil.toWhereCauses(thirdModel.getIdentifier(), DataFilter.fromJsonString(thirdModel.getFilter()),"third_table.");
                    sqlList.forEach(sql->sql.append(" and "+thirdWhere.first()));
                    params.addAll(Lists.list(thirdWhere.second()));
                }

            }


            selectSql.append(" order by first_table.update_time desc,first_table.id asc ").append(limit(dataJoin.getPageNo(), dataJoin.getPageSize()));
            val count = jdbcTemplate.queryForObject(countSql.toString(),params.toArray(),Long.class);
            if(count == 0l){
                return new PageResult<>(Lists.list(), dataJoin.getPageNo(), dataJoin.getPageSize(), 0);
            }
            val result = jdbcTemplate.queryForList(selectSql.toString(), params.toArray());
            val finalResult = result.stream().map(data->{
                List<EntityData> list = Lists.list();
                Map<String, Object> firstModelData = Maps.map();
                Map<String, Object> secondModelData = Maps.map();
                Map<String, Object> thirdModelData = Maps.map();
                data.forEach((key, value)->{
                    if(key.startsWith("first_table_")){
                        firstModelData.put(key.replace("first_table_", ""), value);
                    }
                    if(key.startsWith("second_table_")){
                        secondModelData.put(key.replace("second_table_", ""), value);
                    }
                    if(key.startsWith("third_table_")){
                        thirdModelData.put(key.replace("third_table_", ""), value);
                    }
                });
                list.addAll(dbDataToData(Lists.list(firstModelData), firstModel.getIdentifier(), Maps.map(), false));
                list.addAll(dbDataToData(Lists.list(secondModelData), secondModel.getIdentifier(), Maps.map(), false));
                if(dataJoin.getModelCount() == 3){
                    list.addAll(dbDataToData(Lists.list(thirdModelData), thirdModel.getIdentifier(), Maps.map(), false));
                }
                return list;
            }).collect(Collectors.toList());
            return new PageResult(finalResult, dataJoin.getPageNo(), dataJoin.getPageSize(), count.intValue());
        }finally {
            SqlGeneratorUtil.clearQuerySqlGenInfo();
        }
    }

    private String limit(int pageNo, int pageSize){
        if(pageSize == -1){
            return " ";
        }
        if("postgresql".equals(dbType)){
            return new StringBuilder(" limit ").append(pageSize).append(" offset ").append(pageSize*(pageNo-1)).append(" ").toString();
        }else{
            return new StringBuilder(" limit ").append(pageSize*(pageNo-1)).append(", ").append(pageSize).append(" ").toString();
        }

    }

    private List<String> genJoinSelectColumn(DataJoin.ModelInfo model, String tableName){
        val def = SqlGeneratorUtil.fetchDefPo(model.getIdentifier());
        Map<String,String> propertiesToFieldMap = FastjsonUtil.toObject(def.getPropertyToFieldMapping(), Map.class);
        return model.getProperties().stream().map(property->{
            String name = propertiesToFieldMap.get(property);
            if(null == name){
                name = SnakeCaseConvertor.toSnake(property);
            }
            return tableName + "." + colEscapeChar() + name + colEscapeChar() + " " + tableName + "_"+name;
        }).collect(Collectors.toList());
    }

    @Override
    public String searchableEncrypt(String str) {
        return EncryptionTool.searchableEncrypt(str, getTenantEncryptKey());
    }

    @Override
    public List<Map<String, Object>> indicateByGroup(String identifier, DataFilter filter, List<String> bys, long queryTime, IndicateType indicateType, String indicateProperty) {
        try{
            SqlGeneratorUtil.initQuerySqlGenInfo(getTenantId(), colEscapeChar(), dbType, null);
            val tableName = SnakeCaseConvertor.toSnake(identifier) + "_" + getTenantId();
            val where = SqlGeneratorUtil.toWhereCauses(identifier, filter);
            val groupColumn = bys.stream().map(it->colEscapeChar() +
                    SnakeCaseConvertor.toSnake(SqlGeneratorUtil.fetchColumnName(SqlGeneratorUtil.fetchDefPo(identifier),
                            it)) + colEscapeChar()).collect(Collectors.toList());
            val groupBy = StringUtils.join(groupColumn, ",");
            val sql = new StringBuilder("select " + indicateFunc(identifier,indicateType, indicateProperty) +" indicate, ").append(groupBy)
                    .append(" from ").append(tableName).append(appendWhereDataTime(queryTime)).append(" and ")
                    .append(where.first()).append(" group by ").append(groupBy);
            val result = jdbcTemplate.queryForList(sql.toString(), where.second());
            return result.stream().map(it->{
                Map<String, Object> converted = new HashMap<>();
                converted.put("indicate", it.get("indicate"));
                for(String by : bys){
                    val value = it.get(SnakeCaseConvertor.toSnake(SqlGeneratorUtil.fetchColumnName(SqlGeneratorUtil.fetchDefPo(identifier),
                            by)));
                    converted.put(by, value == null?null:
                            EncryptionTool.isSearchableEncrypted(value.toString())?
                                    EncryptionTool.decrypt(value.toString(), getTenantEncryptKey()):value.toString());
                }
                return converted;
            }).collect(Collectors.toList());
        }finally {
            SqlGeneratorUtil.clearQuerySqlGenInfo();
        }
    }

    private String indicateFunc(String identifier, IndicateType indicateType, String indicateProperty){
        if(!"mysql".equals(dbType) && !"postgresql".equals(dbType)){
            throw new ServerException("unsupported db type");
        }
        val column = colEscapeChar() +
                SnakeCaseConvertor
                        .toSnake(SqlGeneratorUtil.fetchColumnName(SqlGeneratorUtil.fetchDefPo(identifier),
                                indicateProperty))+colEscapeChar();
        switch (indicateType){
            case COUNT:return "count(1)";
            case AVG:if("mysql".equals(dbType)){
                return "avg(cast(" + column + " as Decimal(50,2)))";
            }else if("postgresql".equals(dbType)){
                return "cast(avg(cast(" + column + " as Decimal(50,2))) as varchar)";
            };
            case UNIQUE:
            case MAX:if("mysql".equals(dbType)){
                return "max(cast(" + column + " as Decimal(50,2)))";
            }else if("postgresql".equals(dbType)){
                return "cast(max(cast(" + column + " as Decimal(50,2))) as varchar)";
            }
            case MIN:if("mysql".equals(dbType)){
                return "min(cast(" + column + " as Decimal(50,2)))";
            }else if("postgresql".equals(dbType)){
                return "cast(min(cast(" + column + " as Decimal(50,2))) as varchar)";
            }
            case SUM:if("mysql".equals(dbType)){
                return "sum(cast(" + column + " as Decimal(50,2)))";
            }else if("postgresql".equals(dbType)){
                return "cast(sum(cast(" + column + " as Decimal(50,2))) as varchar)";
            }
        }
        throw new ServerException("");
    }

    @Override
    public List<EmpSimple> loadByCondition(SpecifiedMultiDataFilter filter, long queryTime) {
        SqlGeneratorUtil.initQuerySqlGenInfo(SecurityUserUtil.getSecurityUserInfo()
                .getTenantId(), colEscapeChar(), dbType, null);
        Pair<String, List<Object>> pair = new SpecifiedDataFilterParser(filter, queryTime).generateSqlAndParam();
        String sql = pair.first();
        List<Object> params = pair.second();
        if (log.isDebugEnabled()) {
            log.debug("condition sql>>>>>>>>>>>\n{}\nparam>>>>>>>>>>>>>>>>\n{}", sql, params);
        }
        val result = jdbcTemplate.queryForList(sql, params.toArray());
        return Sequences.sequence(result).map(m -> FastjsonUtil.convertObject(m, EmpSimple.class)).toList();
    }

}


