package com.caidaocloud.hrpaas.metadata.sdk.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.enums.DefStatus;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class MetadataPropertyVo {
    @ApiModelProperty("属性")
    private String property;
    @ApiModelProperty("属性对应存储字段")
    private String persistProperty;
    @ApiModelProperty("默认值")
    private DefaultValueDto defaultValue;
    @ApiModelProperty("名称")
    private String name;
    private Map<String, String> i18nName;
    @ApiModelProperty("数据类型")
    private PropertyDataType dataType;
    @ApiModelProperty("数据源")
    private String datasource;
    @ApiModelProperty("控件类型")
    private String widgetType;
    @ApiModelProperty("显示格式")
    private String format;
    @ApiModelProperty("状态")
    private DefStatus status;
    @ApiModelProperty("枚举定义")
    private List<PropertyEnumDefDto> enumDef;
    @ApiModelProperty("子属性定义")
    private List<MetadataPropertyVo> objDef;
    @ApiModelProperty("是否唯一")
    private boolean unique = false;
    @ApiModelProperty("是否可见")
    private boolean visible = true;
    @ApiModelProperty("是否可修改")
    private boolean modifiable = true;
    @ApiModelProperty("是否必填")
    private boolean required = false;
    @ApiModelProperty("是否加密")
    private boolean encrypted = false;
    @ApiModelProperty("app上是否显示")
    private boolean showOnApp = true;
    @ApiModelProperty("工作流是否可用")
    private boolean availableOnWorkflow = true;

    private String onEvent;

    private String placeholder;

    private Map<String, String> i18nPlaceholder;

    @ApiModelProperty("校验规则")
    private List<RuleDto> rules = Lists.newArrayList();
    @ApiModelProperty("其他设置")
    private List<MetadataPropertyExtDto> extList = Lists.newArrayList();

    private List<MetadataPropertyVo> slaveProperties;

    @ApiModelProperty("是否实时计算")
    private boolean expEnable = false;
    @ApiModelProperty("计算表达式")
    private String expression;
    @ApiModelProperty("是否实时同步")
    private boolean sync = false;
}
