package com.caidaocloud.hrpaas.paas.common.feign;

import java.util.List;

import com.caidaocloud.hrpaas.paas.common.dto.FormDataDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDataMapDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefDto;
import com.caidaocloud.hrpaas.paas.common.dto.PageDetailDto;
import com.caidaocloud.hrpaas.paas.common.dto.TenantPageDetailDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        value = "${feign.rename.caidaocloud-hr-paas-service:caidaocloud-hr-paas-service}",
        fallback = FormFeignFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "paasPageFeignClient"
)
public interface IPageFeignClient {
    @GetMapping("/api/hrpaas/page/v1/detail")
    Result<TenantPageDetailDto> pageDetail(@RequestParam String pageId);
}