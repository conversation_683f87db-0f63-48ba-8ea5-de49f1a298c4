package com.caidaocloud.hrpaas.service.test;

import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.hrpaas.service.application.uiform.service.render.GeneralExtDataSourceRender;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class GeneralExtDataSourceRenderTest {
    @Resource
    private GeneralExtDataSourceRender generalExtDataSourceRender;

    @Test
    public void doPostTest(){
        // doGet 测试
        String body = generalExtDataSourceRender.doGet("http://115.29.176.239:3000/mock/55/api/getOperations", new HashMap<>());
        log.info("doGet = {}", body);

        // doPost 空参数测试
        body = generalExtDataSourceRender.doPost(null, "http://115.29.176.239:3000/mock/55/api/postSelectData", null);
        log.info("doPost HTTP_USER_PWD model = {}", body);

        // doPost 正确参数测试
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("requestName", "root");
        requestParams.put("requestPassword", "123456");
        body = generalExtDataSourceRender.doPost(null, "http://115.29.176.239:3000/mock/55/api/postSelectData", requestParams);
        log.info("doPost HTTP_USER_PWD model = {}", body);

        /**
         * doPost 错误参数测试
         * 错误的 requestName
         */
        requestParams.put("requestName", "admin");
        requestParams.put("requestPassword", "123456");
        body = generalExtDataSourceRender.doPost(null, "http://115.29.176.239:3000/mock/55/api/postSelectData", requestParams);
        log.info("doPost HTTP_USER_PWD model, error params = {}", body);

        /**
         * doPost 错误参数测试
         * 错误的 requestPassword
         */
        requestParams.put("requestName", "root");
        requestParams.put("requestPassword", "111111");
        body = generalExtDataSourceRender.doPost(null, "http://115.29.176.239:3000/mock/55/api/postSelectData", requestParams);
        log.info("doPost HTTP_USER_PWD model, error params = {}", body);
    }
}
