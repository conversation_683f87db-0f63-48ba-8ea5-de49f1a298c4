package com.caidaocloud.metadata.infrastructure.repository.transaction;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.metadata.application.transaction.TransactionService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
@Aspect
@Component
@Slf4j
public class PaasTransactionAspect {
	@Autowired
	private TransactionService transactionService;

	@Pointcut("execution(* com.caidaocloud.metadata.interfaces.facade.EntityDataApiController.*(..))")
	public void entityDataPointcut() {}

	@Pointcut("execution(* com.caidaocloud.metadata.interfaces.facade.MetadataApiController.one(..))")
	public void metadataPointcut() {}


	@Around("entityDataPointcut() || metadataPointcut()")
	public Object doTransaction(ProceedingJoinPoint joinPoint) throws Throwable {
		// 没有获取到transactionId，走正常逻辑
		String transactionId = TransactionInterceptorAdapter.transactionIdHolder.get();
		if (StringUtils.isEmpty(transactionId)) {
			return joinPoint.proceed();
		}

		// 获取事务id以及用户信息
		SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
		FutureTask<Object> task = new FutureTask<>(() -> {
			try {
				TransactionInterceptorAdapter.transactionIdHolder.set(transactionId);
				return joinPoint.proceed();
			}
			catch (Throwable e) {
				log.error("do transaction failed,tx id={},class={},method={},args={}", transactionId, joinPoint.getSignature()
						.getDeclaringTypeName(), joinPoint.getSignature()
						.getName(), FastjsonUtil.toJson(joinPoint.getArgs()));
				throw new ServerException(e.getMessage(), e);
			}finally {
				TransactionInterceptorAdapter.transactionIdHolder.remove();
			}
		});

		// 将任务添加到队列并获取结果
		transactionService.doTask(transactionId, task);
		Object retVal;
		try {
			retVal = task.get();
		} catch (ExecutionException e) {
			// 抛出futureTask执行异常
			throw e.getCause();
		}
		if (log.isDebugEnabled()) {
			log.debug("transaction result={}", FastjsonUtil.toJson(retVal));
		}
		return retVal;
	}
}
