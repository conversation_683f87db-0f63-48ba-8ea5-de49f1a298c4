2024-08-27 11:31:01.092 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-08-27 11:31:01.120 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-08-27 11:31:04.112 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-hr-paas-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-08-27 11:31:05.016 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-hr-paas-service-config, group is : CORE_HR_GROUP
2024-08-27 11:31:10.907 [main] INFO  c.c.r.c.s.LogRecordConfigureSelector - logRecord auto config start .....
2024-08-27 11:31:13.729 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-08-27 11:31:13.734 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-08-27 11:31:13.884 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 127ms. Found 0 repository interfaces.
2024-08-27 11:31:17.234 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-08-27 11:31:17.240 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-08-27 11:31:18.099 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] auto register filter urls by scan class packageName
2024-08-27 11:31:18.515 [main] INFO  org.reflections.Reflections - Reflections took 363 ms to scan 2 urls, producing 16 keys and 353 values 
2024-08-27 11:31:18.625 [main] INFO  c.c.r.c.service.IUrlResourceService - [record] scan package result:[]
2024-08-27 11:31:23.519 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:23.527 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:23.529 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:23.668 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:23.901 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:24.036 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:24.084 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:24.585 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:24.617 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:24.838 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:25.515 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-08-27 11:31:25.720 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2024-08-27 11:31:25.922 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2024-08-27 11:31:26.014 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script start ..............
2024-08-27 11:31:26.014 [main] INFO  c.c.h.s.a.m.s.MetadataSchemaService - init system script end ..............
2024-08-27 11:31:26.804 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:31.696 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-27 11:31:31.697 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-27 11:31:31.709 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-27 11:31:31.709 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-27 11:31:33.643 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:33.755 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:33.791 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:34.825 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:34.874 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:34.912 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:37.249 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:initAllTenantSubordinate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4b50ebea[class com.caidaocloud.hrpaas.service.application.auth.service.AllSubordinateService#initAllTenantSubordinate]
2024-08-27 11:31:37.252 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:prepareRecentScheduleTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@165a78d4[class com.caidaocloud.hrpaas.service.application.schedule.ScheduleTaskService$$EnhancerBySpringCGLIB$$c21b2285#prepareRecentTask]
2024-08-27 11:31:37.341 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:37.360 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:37.415 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:37.485 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:37.514 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:37.530 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:37.580 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-08-27 11:31:37.615 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-08-27 11:31:37.628 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-08-27 11:31:37.645 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-08-27 11:31:37.985 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-08-27 11:31:38.557 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: dynamicTableSetUsingGET_1
2024-08-27 11:31:38.928 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2024-08-27 11:31:39.220 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addConsumerListUsingPOST_1
2024-08-27 11:31:39.226 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addEventDefUsingPOST_1
2024-08-27 11:31:39.230 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPUT_1
2024-08-27 11:31:39.234 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadByModelRelUsingGET_1
2024-08-27 11:31:39.237 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: openUsingPUT_1
2024-08-27 11:31:39.244 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeConsumerListUsingDELETE_1
2024-08-27 11:31:39.246 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateConsumerListUsingPUT_1
2024-08-27 11:31:39.249 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateEventDefUsingPUT_1
2024-08-27 11:31:39.379 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-08-27 11:31:39.464 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2024-08-27 11:31:39.498 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageSelfUsingGET_1
2024-08-27 11:31:39.543 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2024-08-27 11:31:39.700 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_1
2024-08-27 11:31:39.750 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2024-08-27 11:31:39.764 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: filterUsingPOST_1
2024-08-27 11:31:39.774 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2024-08-27 11:31:39.824 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2024-08-27 11:31:39.848 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_2
2024-08-27 11:31:39.859 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_1
2024-08-27 11:31:39.937 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2024-08-27 11:31:39.973 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2024-08-27 11:31:39.978 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-08-27 11:31:40.221 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2024-08-27 11:31:40.228 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2024-08-27 11:31:40.233 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteRelationUsingDELETE_1
2024-08-27 11:31:40.249 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertCustomPropsUsingPOST_1
2024-08-27 11:31:40.253 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: insertRelationUsingPOST_1
2024-08-27 11:31:40.263 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: oneUsingGET_3
2024-08-27 11:31:40.269 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingGET_2
2024-08-27 11:31:40.283 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryRelationUsingGET_1
2024-08-27 11:31:40.287 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: recoverCustomPropsUsingPUT_1
2024-08-27 11:31:40.291 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: removeCustomPropsUsingPUT_1
2024-08-27 11:31:40.295 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: searchUsingGET_1
2024-08-27 11:31:40.299 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2024-08-27 11:31:40.303 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateCustomPropsUsingPUT_1
2024-08-27 11:31:40.350 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addPropertyUsingPOST_1
2024-08-27 11:31:40.373 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: alterPageDetailUsingPUT_1
2024-08-27 11:31:40.375 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deletePageUsingDELETE_1
2024-08-27 11:31:40.378 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageDetailUsingGET_1
2024-08-27 11:31:40.387 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_1
2024-08-27 11:31:40.391 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: treeUsingGET_2
2024-08-27 11:31:40.416 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2024-08-27 11:31:40.506 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingGET_1
2024-08-27 11:31:40.880 [main] WARN  com.zaxxer.hikari.HikariConfig - HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2024-08-27 11:31:40.880 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-08-27 11:31:41.098 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-08-27 11:31:41.836 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-27 11:31:41.897 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-workflow-service-v2 instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-workflow-service-v2,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-27 11:31:41.914 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-27 11:31:42.050 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-workflow-service-v2.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-27 11:31:42.054 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-workflow-service-v2 initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-workflow-service-v2,current list of Servers=[***************:10019],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10019;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@7689b31
2024-08-27 11:31:42.111 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:42.522 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-business-config-center.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-27 11:31:42.523 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-business-config-center instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-business-config-center,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-08-27 11:31:42.524 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-08-27 11:31:42.546 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-business-config-center.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-08-27 11:31:42.547 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-business-config-center initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-business-config-center,current list of Servers=[***************:10003],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10003;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@1485c961
2024-08-27 11:31:42.552 [main] WARN  c.c.h.s.a.u.s.hook.HookTemplate - BaseHookBindable is Empty..........
2024-08-27 11:31:42.712 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-08-27 11:31:42.713 [Thread-38] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-08-27 11:31:42.713 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-08-27 11:31:42.713 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-08-27 11:31:42.715 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-08-27 11:31:42.726 [Thread-35] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-08-27 11:31:42.733 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-hr-paas-service-local', registryValue='http://192.168.130.34:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-08-27 11:31:42.733 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-08-27 11:31:42.733 [Thread-37] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-08-27 11:31:42.733 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-08-27 11:31:42.735 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-08-27 11:31:42.735 [Thread-34] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-08-27 11:31:42.768 [Thread-37] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-08-27 11:31:42.768 [Thread-37] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-08-27 11:31:42.839 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-08-27 11:31:42.960 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-08-27 11:31:42.973 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
