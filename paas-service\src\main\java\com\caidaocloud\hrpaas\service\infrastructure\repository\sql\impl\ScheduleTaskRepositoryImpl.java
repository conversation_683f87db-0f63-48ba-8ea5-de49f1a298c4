package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.hrpaas.service.domain.schedule.entity.ScheduleTask;
import com.caidaocloud.hrpaas.service.domain.schedule.repository.IScheduleTaskRepository;
import org.springframework.stereotype.Repository;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.LongTermScheduleTaskPo;
import com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po.RecentScheduleTaskPo;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.stream.Collectors;

@Repository
public class ScheduleTaskRepositoryImpl implements IScheduleTaskRepository {

    @Autowired
    private RecentScheduleTaskMapper recentMapper;

    @Autowired
    private LongTermScheduleTaskMapper longTermMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public String insert(ScheduleTask entity) {
        throw new UnsupportedOperationException("not supported");
    }

    @Override
    public void update(ScheduleTask entity) {
        throw new UnsupportedOperationException("not supported");
    }

    @Override
    public List<ScheduleTask> listSomeTimedUp() {
        val nextDay = DateUtil.getMidnightTimestamp() + 24 * 3600 * 1000l;
        long timeUp = System.currentTimeMillis() + 5 * 60 * 1000;
        if(timeUp > nextDay){
            timeUp = nextDay;
        }
        val wrapper = new LambdaQueryWrapper<RecentScheduleTaskPo>().lt(RecentScheduleTaskPo::getExecTime,
                timeUp).orderByAsc(RecentScheduleTaskPo::getExecTime);
        val page = new Page<RecentScheduleTaskPo>(1, 20);
        val list = recentMapper.selectPage(page, wrapper).getRecords();
        return list.stream().map(it->it.toEntity(ScheduleTask.class)).collect(Collectors.toList());
    }

    @Override
    public void delete(String taskTopic, String taskId) {
        longTermMapper.delete(new LambdaQueryWrapper<LongTermScheduleTaskPo>()
                .eq(LongTermScheduleTaskPo::getTaskTopic, taskTopic)
                .eq(LongTermScheduleTaskPo::getTaskId, taskId));
        recentMapper.delete(new LambdaQueryWrapper<RecentScheduleTaskPo>()
                .eq(RecentScheduleTaskPo::getTaskTopic, taskTopic)
                .eq(RecentScheduleTaskPo::getTaskId, taskId));
    }

    @Override
    public void prepareRecentTask(long time) {
        jdbcTemplate.execute("insert into recent_schedule_task select * from long_term_schedule_task l where l.exec_time <= " + time);
        jdbcTemplate.execute("delete from long_term_schedule_task where exec_time <= " + time);
    }

    @Override
    public void deleteRecentByIds(List<String> ids) {
        if(ids.isEmpty()){
            return;
        }
        jdbcTemplate.execute("insert into backup_schedule_task(tenant_id, task_topic, task_id, task_detail, exec_time)  " +
                "select tenant_id, task_topic, task_id, task_detail, exec_time from recent_schedule_task l where l.id in ('"+
                StringUtils.join(ids, "','")+"')");
        List<RecentScheduleTaskPo> scheduleTasks = ids.stream().map(id ->{
            RecentScheduleTaskPo task = new RecentScheduleTaskPo();
            task.setId(Long.valueOf(id));
            return task;
        }).collect(Collectors.toList());
        recentMapper.deleteBatchIds(scheduleTasks);
    }

    @Override
    public String insertRecent(ScheduleTask scheduleTask) {
        recentMapper.insert(RecentScheduleTaskPo.fromEntity(RecentScheduleTaskPo.class, scheduleTask));
        return scheduleTask.getId();
    }

    @Override
    public String insertLongTerm(ScheduleTask scheduleTask) {
        longTermMapper.insert(LongTermScheduleTaskPo.fromEntity(LongTermScheduleTaskPo.class, scheduleTask));
        return scheduleTask.getId();
    }
}
