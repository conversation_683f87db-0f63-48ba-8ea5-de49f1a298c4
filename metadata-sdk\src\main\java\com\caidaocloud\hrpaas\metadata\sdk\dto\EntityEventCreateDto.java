package com.caidaocloud.hrpaas.metadata.sdk.dto;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.hrpaas.core.metadata.interfaces.dto.TrackedPropertiesGroupDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2022/12/16
 */
@Data
public class EntityEventCreateDto {
	@ApiModelProperty("identifier")
	private String identifier;
	@ApiModelProperty("关联模型identifier")
	private String modelRef;
	@ApiModelProperty("名称")
	private String name;
	@ApiModelProperty("是否追踪任意变化")
	private boolean trackAll = true;
	@ApiModelProperty("追踪属性")
	private List<TrackedPropertiesGroupDto> trackedProperties = new ArrayList<>();
	@ApiModelProperty("通知属性")
	private List<String> notifyProperties = new ArrayList<>();
	@ApiModelProperty("追踪属性是否关联属性")
	private boolean trackRelation;
	@ApiModelProperty("订阅者列表")
	private List<String> consumerList = new ArrayList<>();
}
