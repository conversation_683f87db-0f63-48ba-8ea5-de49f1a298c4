package com.caidaocloud.hrpaas.metadata.sdk.util;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.val;

import java.util.Map;

public class PersistNameUtil {

    public static Map<String, PropertyPersistInfo> generatePropertyPersistInfo(MetadataVo metadata){
        val propDefs = Sequences.join(metadata.getStandardProperties(), metadata.getInheritedStandardProperties(),
                metadata.getCustomProperties(), metadata.getInheritedCustomProperties());
        Map<String, PropertyPersistInfo> propToPersist = Maps.map();
        propDefs.forEach(propDef->{
            addPersist(propDef, propDef.getProperty(), propDef.getPersistProperty(), propToPersist);
        });
        return propToPersist;
    }

    private static void addPersist(MetadataPropertyVo propDef, String propPath, String persistPath, Map<String, PropertyPersistInfo> propToPersist){
        propToPersist.put(propPath, new PropertyPersistInfo(persistPath, propDef.getDataType()));
        if(PropertyDataType.Object.equals(propDef.getDataType())){
            propDef.getObjDef().forEach(subPropDef->
                    addPersist(subPropDef,
                            propPath + "." + subPropDef.getProperty(),
                            persistPath + "." + subPropDef.getPersistProperty(),
                            propToPersist)
            );
        }
    }

    public static Map<String, PersistPropertyDisplayInfo> generatePropertyDisplayInfo(boolean filterInvisible, MetadataVo metadata){
        val propDefs = Sequences.join(metadata.getStandardProperties(), metadata.getInheritedStandardProperties(),
                metadata.getCustomProperties(), metadata.getInheritedCustomProperties());
        Map<String, PersistPropertyDisplayInfo> propToDisplay = Maps.map();
        propDefs.forEach(propDef->{
            String defaultValue = null;
            if(null != propDef.getDefaultValue()){
                defaultValue = propDef.getDefaultValue().getValue();
            }
            addDisplay(filterInvisible, propDef, propDef.getProperty(), propDef.getPersistProperty(),defaultValue, propToDisplay);
        });
        return propToDisplay;
    }

    private static void addDisplay(boolean filterInvisible, MetadataPropertyVo propDef, String propPath, String persistPath, String defaultValue, Map<String, PersistPropertyDisplayInfo> propToDisplay){
        if(PropertyDataType.Object.equals(propDef.getDataType())){
            propToDisplay.put(persistPath, new PersistPropertyDisplayInfo(propPath, null));
            propDef.getObjDef().forEach(subPropDef->
                    addDisplay(filterInvisible, subPropDef,
                            propPath + "." + subPropDef.getProperty(),
                            persistPath + "." + subPropDef.getPersistProperty(), defaultValue,
                            propToDisplay)
            );
        }else{
            propToDisplay.put(persistPath, new PersistPropertyDisplayInfo(propPath, defaultValue));
        }
    }

    public static Option<PropertyPersistInfo> getPersist(String property, Map<String, PropertyPersistInfo> propToPersist){
        if(propToPersist.containsKey(property)){
            return Option.some(propToPersist.get(property));
        }else{
            return Option.none();
        }
    }

    public static Option<PersistPropertyDisplayInfo> getDisplay(String persistProperty, Map<String, PersistPropertyDisplayInfo> propToDisplay){
        if(propToDisplay.containsKey(persistProperty)){
            return Option.some(propToDisplay.get(persistProperty));
        }else{
            return Option.none();
        }
    }

    @Data
    public static class PersistPropertyDisplayInfo{
        private String displayProperty;
        private String defaultValue;

        public PersistPropertyDisplayInfo(String displayProperty, String defaultValue){
            this.displayProperty = displayProperty;
            this.defaultValue = defaultValue;
        }
    }

    @Data
    public static class PropertyPersistInfo{
        private String persistName;
        private PropertyDataType type;

        public PropertyPersistInfo(String persistName, PropertyDataType type){
            this.persistName = persistName;
            this.type = type;
        }
    }

}
