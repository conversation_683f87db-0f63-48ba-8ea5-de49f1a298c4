package com.caidaocloud.hrpaas.service.domain.condition.entity;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.paas.match.ConditionComponentEnum;
import com.caidaocloud.hrpaas.paas.match.ConditionOperator;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.PropertyDef;
import com.caidaocloud.metadata.domain.entity.PropertyEnumDef;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.caidaocloud.hrpaas.paas.match.ConditionOperator.*;

/**
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@Data
public class ConditionData {
	private String identifier;
	private String identifierName;
	private String property;
	private List<ConditionOperator> operators;
	private String name;
	private String code;
	private ConditionComponentEnum component;
	private Map<String, Object> dataSourceParams;
	private boolean enabled = true;
	private List<Map<String, Object>> componentValueEnum;

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		ConditionData that = (ConditionData) o;
		return identifier.equals(that.identifier) && property.equals(that.property);
	}

	@Override
	public int hashCode() {
		return Objects.hash(identifier, property);
	}

	public  static  ConditionDataBuilder builder(){
		return new ConditionDataBuilder();
	}

	public static class  ConditionDataBuilder{
		private EntityDef entity;
		private PropertyDef property;
		public 	ConditionDataBuilder entity(EntityDef entity) {
			this.entity = entity;
			return this;
		}

		public 	ConditionDataBuilder property(PropertyDef property) {
			this.property = property;
			return this;
		}

		public ConditionData build() {
			ConditionData conditionData = new ConditionData();
			conditionData.setIdentifier(entity.getIdentifier());
			conditionData.setIdentifierName(entity.getName());
			conditionData.setProperty(property.getProperty());
			if(Lists.list(PropertyDataType.Timestamp, PropertyDataType.Number, PropertyDataType.Integer).contains(property.getDataType())){
				conditionData.setOperators(Lists.list(EQ, NE, IN, LT, LE, GT, GE));
			}else{
				conditionData.setOperators(Lists.list(EQ, NE, IN));
			}
			conditionData.setName(property.getName());
			conditionData.setCode(property.getProperty());
			conditionData.setDataSourceParams(Maps.map());
			switch (property.getDataType()) {
			case Dict:
				conditionData.setComponent(ConditionComponentEnum.DICT_SELECTOR);
				// bcc接口参数
				String datasource = property.getDatasource();
				// 兼容自定义字段
				if (datasource.startsWith("/api/bcc")) {
					Pattern pattern = Pattern.compile("typeCode=([^&]+)");
					Matcher matcher = pattern.matcher(datasource);
					if (matcher.find()) {
						datasource = matcher.group(1);
					}
				}
				conditionData.setDataSourceParams(Maps.map("belongModule", "Employee", "typeCode", datasource));
				break;
			case Enum:
				conditionData.setComponent(ConditionComponentEnum.ENUM);
				List<PropertyEnumDef> enumDef = property.getEnumDef();
				conditionData.setComponentValueEnum(enumDef.stream().map(item ->
						Maps.map("display", item.getDisplay(), "value", (Object) item.getValue())).collect(Collectors.toList()));
				break;
			case Address:
				conditionData.setComponent(ConditionComponentEnum.ADDRESS);
				// 地址组织 datasource 接口参数
				conditionData.setDataSourceParams(Maps.map("datasource", property.getDatasource()));
				break;
			default:
				conditionData.setComponent(ConditionComponentEnum.STRING_INPUT);
			}
			return conditionData;
		}
	}


}
