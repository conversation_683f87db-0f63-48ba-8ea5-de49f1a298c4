package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DataComponent;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.*;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.reflections.Reflections;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.*;

@Data
@Slf4j
public class DataSimple extends AbstractData implements Serializable {

    protected NestPropertyValue properties = new NestPropertyValue();

    private static final Map<PropertyDataType, ComponentPropertyValue> componentMap = new HashMap<>();

    static{
        for(Class<? extends ComponentPropertyValue> clazz :
                new Reflections("com.caidaocloud.hrpaas.metadata.sdk.dto")
                        .getSubTypesOf(ComponentPropertyValue.class)){
            try {
                componentMap.put(
                        clazz.getAnnotation(DataComponent.class).dataType(),
                        clazz.newInstance()
                );
            } catch (Exception e) {
               log.error("unsupported component "+clazz.getName());
            }
        }
    }

    public static ComponentPropertyValue fetchComponentInstance(PropertyDataType dataType){
        return componentMap.get(dataType);
    }

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private Map<String, List<EntityRelationDto>> relationsCache = Maps.map();

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private MetadataVo metadata = null;

    public Optional<EntityRelationDto> fetchRelation(String property){
        if(!relationsCache.containsKey(property)){
            return Optional.empty();
        }
        return Optional.of(relationsCache.get(property).get(0));
    }

    public static <T extends DataSimple> T fromPersist(DataQuery query, MetadataVo metadata, EntityDataDto dataDto, Class<T> clazz) {
        if(null == dataDto){
            return null;
        }
        val dataMap = JsonEnhanceUtil.toObject(dataDto, Map.class);
        dataMap.remove("properties");
        val properties = dataDto.getProperties();
        val nestPropertyValue = new NestPropertyValue();
        metadata.fetchAllProperties().forEach(propDef->{
                    val dataType = propDef.getDataType();
                    if(dataType.equals(PropertyDataType.Data_Table)){

                    }else if(dataType.isComponent()){
                        try {
                            ThreadLocalUtil.LOCAL_REQUEST_DATA.set(query);
                            PropertyValue componentValue = fetchComponentInstance(dataType).extractDisplay(propDef, properties);
                            dataMap.put(propDef.getProperty(), componentValue);
                            nestPropertyValue.put(propDef.getProperty(), componentValue);
                        } catch (Exception e) {
                            log.error("组件转换失败", e);
                            throw new ServerException("组件转换失败", e);
                        } finally {
                            ThreadLocalUtil.LOCAL_REQUEST_DATA.remove();
                        }
                    }else if(dataType.equals(PropertyDataType.Object)){
                        NestPropertyValue nestValue = NestPropertyValue.toDisplay(properties, propDef, null);
                        if(nestValue != null){
                            nestPropertyValue.put(propDef.getProperty(), nestValue);
                        }
                    }else {
                        if (!StringUtils.equalsAny(propDef.getProperty(), "dataStartTime", "dataEndTime")) {
                            SimplePropertyValue simpleValue = SimplePropertyValue.toDisplay(properties, propDef, null);
                            if (simpleValue != null) {
                                dataMap.put(propDef.getProperty(), simpleValue.getType()
                                        .isArray() ? simpleValue.getArrayValues() : simpleValue.getValue());
                                nestPropertyValue.put(propDef.getProperty(), simpleValue);
                            }
                        }
                    }
        }
        );
        dataDto.getProperties().stream().filter(propertyDataDto ->
                propertyDataDto.getProperty().contains(".") &&
                        null != propertyDataDto.getDataType() && PropertyDataType.Attachment != propertyDataDto.getDataType() &&
                        propertyDataDto.getDataType().isArray()).forEach(propertyDataDto ->
                nestPropertyValue.put(propertyDataDto.getProperty(), new SimplePropertyValue(propertyDataDto.getArrayValues(), propertyDataDto.getDataType()))
        );
        val classFields = clazz.getDeclaredFields();
        Arrays.stream(classFields)
                .filter(field -> field.isAnnotationPresent(DisplayAsArray.class)
                        && dataMap.get(field.getName()) != null && dataMap.get(field.getName()) instanceof String)
                .forEach(field ->{
                    String value = (String) dataMap.get(field.getName());
                    try {
                        dataMap.put(field.getName(), FastjsonUtil.toObject(value, new TypeReference<List<Map>>(){}));
                    } catch (Exception exception) {
                        dataMap.put(field.getName(), FastjsonUtil.toArrayList(value, String.class));
                    }
                }
        );
        Arrays.stream(classFields)
                .filter(field -> field.isAnnotationPresent(DisplayAsObject.class)
                        && dataMap.get(field.getName()) != null && dataMap.get(field.getName()) instanceof String)
                .forEach(field ->{
                            String value = (String) dataMap.get(field.getName());
                            dataMap.put(field.getName(), FastjsonUtil.toObject(value, HashMap.class));
                        }
                );
        if (query.isExpression()) {
            // metadata.doExpExecute(dataMap, nestPropertyValue);
        }
        val data = JsonEnhanceUtil.toObject(dataMap, clazz);
        data.setProperties(nestPropertyValue);
        if(clazz.equals(LabelData.class)){
            String value = data.getBid();
            String label = metadata.getLabel();
            if(StringUtils.isNotEmpty(label)){
                SimplePropertyValue propertyValue = (SimplePropertyValue) data.getProperties().get(label);
                value = propertyValue.getValue();
            }
            ((LabelData) data).setLabel(value);
        }
        ((DataSimple)data).relationsCache = Sequences.sequence(dataDto.getRelations())
                .toMap(relation->relation.getProperty());
        return data;
    }

    public EntityDataDto toPersistData(MetadataVo metadata){
        EntityDataDto dataInfo = this.generateCommonInfo();
        metadata.fetchAllProperties().forEach(propDef ->{
            if(propDef.getDataType()
                    .equals(PropertyDataType.Data_Table)){
                return;
            }
            val propertyDataList = this.initPropertyData(propDef);
            dataInfo.getProperties().addAll(propertyDataList);
        });
        return dataInfo;
    }

    private List<PropertyDataDto> initPropertyData(MetadataPropertyVo propDef){
        List<PropertyDataDto> propertyDataList;
        if(propDef.getDataType().isComponent()){
            try {

                propertyDataList = fetchComponentInstance(propDef.getDataType()).extractPersist(this, propDef);
            } catch (Exception e) {
                log.error("组件转换失败", e);
                throw new ServerException("组件转换失败", e);
            }
        }else if(propDef.getDataType().equals(PropertyDataType.Object)){
            propertyDataList = NestPropertyValue.toPersist(this, propDef);
        }else{
            propertyDataList = SimplePropertyValue.toPersist(this, propDef);
        }
        return propertyDataList;
    }

    private EntityDataDto generateCommonInfo(){
        if(StringUtils.isEmpty(this.getId()) && StringUtils.isEmpty(this.getBid())){
            this.setBid(SnowUtil.nextId());
        }
        val tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        if(StringUtils.isEmpty(this.getTenantId())){
            this.setTenantId(tenantId);
        }else if(!this.getTenantId().equals(tenantId)){
            throw new ServerException("租户异常");
        }
        val result = JsonEnhanceUtil.toObject(this, EntityDataDto.class);
        result.setProperties(Lists.list());
        return result;
    }

    public static void demo(){
        val demo = new DataSimple();
        val properties = demo.properties;
        properties.add("name", "王旭");
        properties.add("age", "1");
        properties.add("hobbies", Lists.list("swimming","running"));
        properties.add("cardNo",
                new NestPropertyValue().add("cardType","IDCard")
                        .add("availableTime",
                                new NestPropertyValue()
                                        .add("start","2")
                                        .add("end","2")));
        DataInsert.identifier("...").insert(demo);

    }

    public String fetchPid(){
        val dataMap = JsonEnhanceUtil.toObject(this, Map.class);
        val customPropertyValue = this.getProperties().get("pid");
        String pid = null;
        if(dataMap.containsKey("pid")){
            val standardPropertyValue = dataMap.get("pid");
            if(null != standardPropertyValue){
                TreeParent tree = JsonEnhanceUtil.toObject(standardPropertyValue, TreeParent.class);
                if(null != tree){
                    pid = tree.getPid();
                }
            }
        }else if(null != customPropertyValue){
            if(customPropertyValue instanceof TreeParent){
                pid = ((TreeParent) customPropertyValue).getPid();
            }else{
                // 非json格式
                String value = ((SimplePropertyValue) customPropertyValue).getValue();
                if (value != null && !value.startsWith("{")) {
                    return value;
                }
                TreeParent tree =FastjsonUtil.toObject(
                        value, TreeParent.class);
                if(null != tree){
                    pid = tree.getPid();
                }

            }
        }
        return pid;
    }

    public List excludeField(){
        Field[] fields = AbstractData.class.getDeclaredFields();
        List<String> fieldList = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
        return fieldList;
    }

    public boolean containProperty(String property){
        Field[] fields = this.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (field.getName().equals(property)) {
                return true;
            }
        }
        return false;
    }

    public void extendedField(Map ext, Map nativeFields){
        NestPropertyValue props = getProperties();
        if(null == props){
            return;
        }

        List exFieldList = excludeField();
        props.forEach((prop, proValue) -> {
            // 排除掉原生字段
            if(nativeFields.containsKey(prop)){
                return;
            }

            // 排除掉系统字段
            if(exFieldList.contains(prop)){
                return;
            }

            if(simpleField(proValue, ext, prop)){
                return;
            }

            // 高级组件，则不处理，不用封装
            ext.put(prop, proValue);
        });
    }

    private boolean simpleField(PropertyValue proValue, Map ext, String prop){
        if(proValue instanceof SimplePropertyValue){
            if(((SimplePropertyValue) proValue).getType().isComponent()){
                // 如果是组件，则不处理
                ext.put(prop, proValue);
            } else if(((SimplePropertyValue) proValue).getType().isArray()){
                // 数组类型，则转换称字符串分隔的内容
                List<String> valueList = ((SimplePropertyValue) proValue).getArrayValues();
                ext.put(prop, null == valueList ? "" : valueList.stream().collect(Collectors.joining(",")));
            } else {
                // 普通字段，则直接返回值内容
                ext.put(prop, ((SimplePropertyValue) proValue).getValue());
            }

            return true;
        }

        return false;
    }

    public void appendMetadataInfo(MetadataVo metadata) {
        this.metadata = metadata;
    }

    public List<MetadataPropertyVo> fetchCustomPropertyDefList(){
        if(null == metadata){
            throw new ServerException("未附加metadata信息");
        }
        return metadata.getCustomProperties();
    }
}
