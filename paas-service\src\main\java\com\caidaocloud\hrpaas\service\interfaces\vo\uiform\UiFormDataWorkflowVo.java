package com.caidaocloud.hrpaas.service.interfaces.vo.uiform;

import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.WorkflowAction;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;


@Data
@Builder
@ApiModel("表单数据实体")
public class UiFormDataWorkflowVo extends UiFormDataVo{

    /**
     * 表单数据流程状态
     */
    @ApiModelProperty("流程状态")
    private WorkflowAction workflowAction;

    /**
     * 流程功能类型
     */
    private String functionType;

}
