package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.jarvis.cache.annotation.Cache;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CommonScopeRestrictionHandler implements ScopeRestrictionHandler {
    @Override
    @Cache(key = "'0_scope_restriction_' + #args[0] + '_' + #args[1]", expire = 5 * 60)
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        Boolean isFilter = AuthScopeFilterUtil.get();
        val tlUser = SecurityUserUtil.getThreadLocalSecurityUserInfo();
        try {
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            if (StringUtils.isEmpty(simpleValues)) {
                return Lists.list();
            }
            return Lists.list(simpleValues.split(","));
        } finally {
            if (null == tlUser) {
                SecurityUserUtil.removeSecurityUserInfo();
            } else {
                SecurityUserUtil.setSecurityUserInfo(tlUser);
            }
            AuthScopeFilterUtil.put(isFilter);
        }
    }
}