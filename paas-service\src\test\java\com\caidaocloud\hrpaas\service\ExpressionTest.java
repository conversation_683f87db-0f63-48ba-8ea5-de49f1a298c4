package com.caidaocloud.hrpaas.service;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.expression.function.ContractInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.service.SessionServiceImpl;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHelper;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorJavaType;
import com.googlecode.aviator.runtime.type.AviatorNil;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.googlecode.aviator.spring.SpringContextFunctionLoader;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import feign.Contract;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/2/29
 */
@SpringBootTest(classes = HrPaasApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class ExpressionTest {

	@SpyBean
	private ContractInfo contractInfo;

	@Before
	public void bf() {
		SecurityUserInfo info = new SecurityUserInfo();
		info.setTenantId("11");
		info.setEmpId(0L);
		info.setUserId(0L);
		SecurityUserUtil.setSecurityUserInfo(info);

		RequestHelper.getRequest()
				.setAttribute("_caidaocloud_userinfo_request_threadlocal_", ObjectConverter.convert(info, UserInfo.class));
		SessionServiceImpl service = (SessionServiceImpl) SpringUtil.getBean(ISessionService.class);
		try {
			Field field = SessionServiceImpl.class.getDeclaredField("threadLocalCache");
			field.setAccessible(true);
			field.set(service, true);
		}
		catch (NoSuchFieldException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	@Test
	public  void test() {
		Map<String, String> map = Maps.map(Pair.pair("a", "B"), Pair.pair("a", "B"), Pair.pair("a", "B"), Pair.pair("a", "B"));
		System.out.println(AviatorEvaluator.execute("a+b", Maps.map("a", 1, "b", 2)));
		System.out.println(AviatorEvaluator.execute("ContractInfo(empId,\"endTime\")", Maps.map("empId", "1", "endTime", System.currentTimeMillis())));
	}

	/**
	 * 司龄表达式01
	 */
	@Test
	public void SI_LING_EXP(){
		Mockito.doReturn(FunctionUtils.wrapReturn(1733550181000L)).when(contractInfo)
				.call(Mockito.anyMap(), Mockito.any(), Mockito.any());
		String exp = "round(monthDiff(hireDate,min(contractInfo(empId,\"endTime\"),now()))/12,1)";
		AviatorEvaluator.compile(exp);
		System.out.println(AviatorEvaluator.execute(exp,Maps.map("hireDate", 1683438181000L)));
	}

	@Test
	public void CONTRACT_TEST(){
		ContractInfo info = new ContractInfo();
		AviatorObject endDate = info.call(Maps.map("empId", "1699922881320961"), new AviatorJavaType("empId"), new AviatorString("endDate"));
		Assert.assertEquals(253402185600000L, FunctionUtils.getNumberValue(endDate, new HashMap<>()).longValue());
	}

	@Test
	public void NVL_TEST(){
		String exp = "nvl(contractInfo(empId,\"endTime\"),now())";
		AviatorEvaluator.compile(exp);
		Object result = AviatorEvaluator.execute(exp, Maps.map("empId", "-1"));
		Assert.assertNotEquals(AviatorNil.NIL, result);
		System.out.println(result);

	}
}