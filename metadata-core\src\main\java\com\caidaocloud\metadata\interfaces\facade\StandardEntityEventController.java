package com.caidaocloud.metadata.interfaces.facade;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityEventCreateDto;
import com.caidaocloud.metadata.application.service.EntityEventDefService;
import com.caidaocloud.web.Result;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @date 2023/1/16
 */
@RestController
@RequestMapping("/api/${service.name.short:hrpaas}/v1/event/standard")
public class StandardEntityEventController {

	@Autowired
	private EntityEventDefService entityEventDefService;

	@PostMapping
	public Result createEvent(@RequestBody EntityEventCreateDto def) {
		entityEventDefService.createStandardEvent(def);
		return Result.ok(true);
	}

}
