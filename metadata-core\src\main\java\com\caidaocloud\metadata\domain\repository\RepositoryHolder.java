package com.caidaocloud.metadata.domain.repository;

import java.util.concurrent.ConcurrentHashMap;

public class RepositoryHolder {

    private static ConcurrentHashMap<Class, IBaseRepository> holder = new ConcurrentHashMap();

    public static void registerRepository(Class domainClazz, IBaseRepository repository){
        holder.put(domainClazz, repository);
    }

    public static IBaseRepository getRepository(Class domainClazz){
        return holder.get(domainClazz);
    }

}
