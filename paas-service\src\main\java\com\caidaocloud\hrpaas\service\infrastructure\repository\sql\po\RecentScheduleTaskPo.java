package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.domain.schedule.entity.ScheduleTask;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@TableName("recent_schedule_task")
public class RecentScheduleTaskPo extends BasePo<RecentScheduleTaskPo, ScheduleTask> implements Serializable {

    private String tenantId;

    private String taskTopic;

    private String taskId;

    private String taskDetail;

    private long execTime;

}
