package com.caidaocloud.hrpaas.metadata.sdk.expression.function;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.caidaocloud.util.DateUtil;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;

import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/3/7
 */
@Component
public class MonthDiff extends AbstractFunction {

	public MonthDiff() {
		System.out.println(1);
	}

	@Override
	public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
		long a = FunctionUtils.getNumberValue(arg1, env).longValue();
		long b = FunctionUtils.getNumberValue(arg2, env).longValue();
		long diff = DateUtil.getMonthDiff(a, b);
		return FunctionUtils.wrapReturn(((double) diff));
	}

	@Override
	public String getName() {
		return "monthDiff";
	}
}
