package com.caidaocloud.hrpaas.metadata.sdk.feign;

import com.caidaocloud.hrpaas.metadata.sdk.dto.AuthResourceDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AuthRoleScopeFilterDetailDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public class MetadataAuthFeignFallback implements IMetadataAuthFeignClient{
    @Override
    public Result<List<AuthRoleScopeFilterDetailDto>> getRoleScope(String identifier, String subjectId) {
        return Result.fail();
    }

    @Override
    public Result<String> createResources(List<AuthResourceDto> resourceList) {
        return Result.fail();
    }

    @Override
    public Result<String> update(List<AuthResourceDto> resourceList) {
        return Result.fail();
    }

    @Override
    public Result<String> delete(List<AuthResourceDto> removeList) {
        return Result.fail();
    }
}
