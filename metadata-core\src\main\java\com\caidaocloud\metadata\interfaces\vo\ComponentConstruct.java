package com.caidaocloud.metadata.interfaces.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ComponentConstruct {

    @ApiModelProperty("标准树结构节点数据id, label")
    private LabelData labelData;

    @ApiModelProperty("职级范围")
    private JobGradeRange jobGradeRange;

    @ApiModelProperty("父节点，保存时只用保存pid，其他两个字段查看到的时候返回")
    private TreeParent treeParent;

    @ApiModelProperty("省市")
    private ProvinceCity provinceCity;

    @ApiModelProperty("附件")
    private Attachment attachment;
}
