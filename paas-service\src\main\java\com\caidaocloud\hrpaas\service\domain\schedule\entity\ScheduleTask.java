package com.caidaocloud.hrpaas.service.domain.schedule.entity;

import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.hrpaas.service.domain.schedule.repository.IScheduleTaskRepository;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ScheduleTask extends BaseEntity<IScheduleTaskRepository> implements Serializable {

    private static final long serialVersionUID = 5772580655533168041L;

    private String tenantId;

    private String taskTopic;

    private String taskId;

    private String taskDetail;

    private long execTime;

    public static void deleteRecentByIds(List<String> ids) {
        ((IScheduleTaskRepository)repository(ScheduleTask.class)).deleteRecentByIds(ids);
    }

    public static List<ScheduleTask> listSomeTimedUp() {
        return ((IScheduleTaskRepository)repository(ScheduleTask.class)).listSomeTimedUp();
    }

    public static void delete(String taskTopic, String taskId) {
        ((IScheduleTaskRepository)repository(ScheduleTask.class)).delete(taskTopic, taskId);
    }

    public static void prepareRecentTask() {
        long oneDay = 24 * 3600 * 1000l;
        ((IScheduleTaskRepository)repository(ScheduleTask.class)).prepareRecentTask(System.currentTimeMillis() + 3 * oneDay);
    }

    @Override
    public String insert(){
        long oneDay = 24 * 3600 * 1000l;
        if(execTime < DateUtil.getMidnightTimestamp() + 3 * oneDay){
            return repository().insertRecent(this);
        }else{
            return repository().insertLongTerm(this);
        }
    }
}
