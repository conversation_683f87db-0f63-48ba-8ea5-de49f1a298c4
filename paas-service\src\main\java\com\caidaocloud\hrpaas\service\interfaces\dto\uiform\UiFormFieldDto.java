package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UiFormFieldDto {
    /**
     * 字符串，字段ID，必填，最大长度16位
     */
    @ApiModelProperty("字段ID")
    private String prop;

    /**
     * 字符串，字段名称，必填，最大长度32位
     */
    @ApiModelProperty("字段名称")
    private String label;

    @ApiModelProperty("字段类型")
    private String type;

    @ApiModelProperty("字段数据类型")
    private String dataType;

    @ApiModelProperty("数据源ID")
    private String dataSourceId;
}
