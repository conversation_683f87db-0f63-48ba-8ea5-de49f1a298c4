package com.caidaocloud.hrpaas.service.application.form.service;

import javax.annotation.Resource;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.metadata.application.service.MetadataPropertyService;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.caidaocloud.metadata.domain.factory.PropertyDefFactory;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2024/8/14
 */
@Service
@Slf4j
public class FormAuthDefService {
	@Resource
	private FormDefService formDefService;
	@Resource
	private MetadataPropertyService metadataPropertyService;
	public void initAuth() {
		for (FormDef def : formDefService.defList()) {
			metadataPropertyService.insertStandardProps("entity.form."+(StringUtils.isEmpty(def.getCodeAsId())?def.getId():def.getCodeAsId()),
					Sequences.sequence(Lists.list(PropertyDefFactory.createPropertyDef("form_owner_organize", "员工所属组织", PropertyDataType.String),
							PropertyDefFactory.createPropertyDef("form_owner_emp_type", "员工用工类型", PropertyDataType.Dict))), DefChannel.API);
		}
	}

	public void initApproveTime() {
		for (FormDef def : formDefService.defList()) {
			if (def.isWorkflowAvailable()) {
				try {
					metadataPropertyService.insertStandardProps("entity.form."+(StringUtils.isEmpty(def.getCodeAsId())?def.getId():def.getCodeAsId()),
							Sequences.sequence(Lists.list(PropertyDefFactory.createPropertyDef("formApprovalTime", "表单审批时间", PropertyDataType.Timestamp))), DefChannel.API);
				}
				catch (Exception e) {
					log.warn("初始化表单审批时间字段失败, formId:{}", def.getId(), e);
				}
			}
		}
	}
}
