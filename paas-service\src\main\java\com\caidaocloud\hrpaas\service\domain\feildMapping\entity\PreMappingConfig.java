package com.caidaocloud.hrpaas.service.domain.feildMapping.entity;

import com.caidaocloud.hrpaas.service.application.form.dto.FormPropertyMapping;
import com.caidaocloud.hrpaas.service.domain.feildMapping.repository.PreMappingConfigRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;

@Data
@Slf4j
public class PreMappingConfig {
    
    private String id;
    
    /**
     * 映射类型
     */
    private ConverterType type = ConverterType.paas;
    /**
     * 映射源identifier
     */
    private String source;
    /**
     * 映射源名称
     */
    private String sourceName;
    /**
     * 映射目标identifier
     */
    private String target;
    /**
     * 映射目标名称
     */
    private String targetName;
    /**
     * 属性映射
     */
    private List<FormPropertyMapping> mapping;

    /**
     * 模型字段映射名称
     */
    private String name;

    /**
     * 模型字段映射类别
     */
    private String category;
    
    private Long createTime;

    private String createBy;

    private Long updateTime;

    private String updateBy;

    public void initCreateInfo(){
        initCreateInfo(null);
    }

    public void initCreateInfo(PreMappingConfig existed){
        val user = SecurityUserUtil.getSecurityUserInfo();
        updateBy = null == user.getUserId() ? null : user.getUserId().toString();
        updateTime = System.currentTimeMillis();
        if(null == existed){
            createBy = updateBy;
            createTime = updateTime;
        }else{
            createBy = existed.createBy;
            createTime = existed.createTime;
        }
    }

    public static List<PreMappingConfig> listConfig(List<String> sourceList) {
        return SpringUtil.getBean(PreMappingConfigRepository.class).list(sourceList);
    }

    public static List<PreMappingConfig> listByCategory(String category) {
        return SpringUtil.getBean(PreMappingConfigRepository.class).listByCategory(category);
    }
    
    public static void delete(Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        SpringUtil.getBean(PreMappingConfigRepository.class).deleteByIds(ids);
    }

    public static PreMappingConfig load(String id) {
        return SpringUtil.getBean(PreMappingConfigRepository.class).selectById(id);
    }

    public static List<PreMappingConfig> listByids(List<String> idList) {
        return SpringUtil.getBean(PreMappingConfigRepository.class).listByIds(idList);
    }

    public static PreMappingConfig loadByName(String name) {
        return SpringUtil.getBean(PreMappingConfigRepository.class).loadByName(name);
    }
    
    public void delete() {
        SpringUtil.getBean(PreMappingConfigRepository.class).delete(this);
    }

    public String save() {
        initCreateInfo();
        return SpringUtil.getBean(PreMappingConfigRepository.class).insert(this);
    }

    public void update() {
        initCreateInfo(this);
        SpringUtil.getBean(PreMappingConfigRepository.class).update(this);
    }
    
    public static List<PreMappingConfig> listFormByCategory(String category) {
        return SpringUtil.getBean(PreMappingConfigRepository.class).listFormByCategory(category);
    }
}
