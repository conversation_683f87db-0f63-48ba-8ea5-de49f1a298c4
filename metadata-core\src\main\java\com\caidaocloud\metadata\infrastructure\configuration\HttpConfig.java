package com.caidaocloud.metadata.infrastructure.configuration;

import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021-09-08
 */
@Configuration
public class HttpConfig {

    // 秒
    private static final int DEFAULT_MAX_TIMEOUT = 5;

    @Bean
    public OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(DEFAULT_MAX_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(DEFAULT_MAX_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(DEFAULT_MAX_TIMEOUT, TimeUnit.SECONDS)
                .build();
    }

    @Bean
    public RestTemplate restTemplate(OkHttpClient httpClient) {
        OkHttp3ClientHttpRequestFactory httpRequestFactory = new OkHttp3ClientHttpRequestFactory(httpClient);
        RestTemplate restTemplate = new RestTemplate(httpRequestFactory);
        // 配置消息转换器，处理所有响应乱码
        List<HttpMessageConverter<?>> httpMessageConverter = restTemplate.getMessageConverters();
        // 设置编码
        httpMessageConverter.set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        restTemplate.setMessageConverters(httpMessageConverter);
        return restTemplate;
    }
}
