package com.caidaocloud.hrpaas.service.address;

import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeData;
import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.metadata.application.dto.TreeLeafDto;
import com.caidaocloud.metadata.application.service.MetadataAddressService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class TreeTest {
    @Resource
    private MetadataAddressService metadataAddressService;
    @Test
    public void getTree(){
        List<TreeData<TreeLeafDto>> tree = metadataAddressService.tree(null);
        log.info("------------tree data={}", FastjsonUtil.toJson(tree));
    }

    @Before
    public void bf() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setUserId(0L);
        userInfo.setTenantId("8");
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }
}
