package com.caidaocloud.metadata.interfaces.facade;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.metadata.application.dto.MetadataQueryModelDataDto;
import com.caidaocloud.metadata.application.service.MetadataPropertyService;
import com.caidaocloud.metadata.application.service.MetadataRelationService;
import com.caidaocloud.metadata.application.service.MetadataService;
import com.caidaocloud.metadata.domain.entity.EntityRelationDef;
import com.caidaocloud.metadata.domain.enums.DefChannel;
import com.caidaocloud.metadata.interfaces.convertor.MetadataConvertor;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataRelationVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.metadata.interfaces.vo.ComponentConstruct;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Sequences;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/${service.name.short:hrpaas}/v1/web/metadata")
@Api(value = "元数据维护", tags = "元数据--前端接口")
public class MetadataWebController {

    @Autowired
    private MetadataService metadataService;

    @Autowired
    private MetadataPropertyService metadataPropertyService;

    @Autowired
    private MetadataRelationService metadataRelationService;

    @PostMapping
    @ApiOperation("新增元数据信息")
    @LogRecordAnnotation(menu = "管理中心-乐高引擎-模型管理-模型设计", category = "新增", success = "新增了{{#metadata.basicInfo.getI18Name()}}")
    public boolean create(@RequestBody MetadataDto metadata){
        metadataService.create(MetadataConvertor.fromDto(metadata), DefChannel.PAGE);
        return true;
    }

    @PutMapping("/basic")
    @ApiOperation("修改元数据基本信息")
    @LogRecordAnnotation(menu = "管理中心-乐高引擎-模型管理-模型设计", category = "编辑", success = "编辑了{defName{#basicInfoDto.identifier}}")
    public boolean update(@RequestBody MetadataBasicInfoDto basicInfoDto){
        metadataService.update(MetadataConvertor.fromDto(basicInfoDto), DefChannel.PAGE);
        return true;
    }

    @GetMapping
    @ApiOperation("查询元数据信息")
    public MetadataVo one(@RequestParam("identifier") String identifier){
        val def = metadataService.one(identifier);
        val inherited = metadataService.loadHierarchyProperties(def);
        val relations = metadataRelationService.loadRelationsByIdentifier(identifier);
        val relationsDto = JsonEnhanceUtil.toObjects(relations, MetadataRelationVo.class).toList();
        return MetadataConvertor.toVo(def, inherited, relationsDto);
    }

    @PostMapping("/more")
    @ApiOperation(value = "查询多个元数据信息", tags = "v1.4")
    public List<MetadataVo> more(@RequestBody String [] identifiers){
        if(null == identifiers || identifiers.length <= 0){
            return new ArrayList<>();
        }

        List dataList = new ArrayList(identifiers.length);
        for (String identifier : identifiers){
            val def = metadataService.one(identifier);
            val inherited = metadataService.loadHierarchyProperties(def);
            val relations = metadataRelationService.loadRelationsByIdentifier(identifier);
            val relationsDto = JsonEnhanceUtil.toObjects(relations, MetadataRelationVo.class).toList();
            dataList.add(MetadataConvertor.toVo(def, inherited, relationsDto));
        }

        return dataList;
    }

    @DeleteMapping
    @ApiOperation("删除元数据信息")
    @LogRecordAnnotation(menu = "管理中心-乐高引擎-模型管理-模型设计", category = "删除", success = "删除了{defName{#identifier}}")
    public boolean delete(@RequestParam("identifier") String identifier){
        metadataService.delete(identifier, DefChannel.PAGE);
        return true;
    }

    @GetMapping("/page")
    @ApiOperation("元数据信息列表")
    public PageResult<MetadataVo> page(@RequestParam(value = "keywords", required = false)String keywords, @RequestParam("pageNo")int pageNo, @RequestParam("pageSize")int pageSize){
        val page = metadataService.page(keywords, pageNo, pageSize);
        return MetadataConvertor.toVoPage(page);
    }

    @GetMapping("/:search")
    @ApiOperation("元数据信息搜索")
    public List<MetadataVo> search(@RequestParam("identifierPattern") String identifierPattern,
                                   @RequestParam(value = "filterInheritable", defaultValue="false") boolean filterInheritable,
                                   @RequestParam(value = "filterTree", defaultValue = "false") boolean filterTree,
                                   @RequestParam(value = "maxCount", defaultValue = "10") int maxCount){
        val defs = metadataService.search(identifierPattern, filterInheritable,filterTree, maxCount);
        return MetadataConvertor.toVoList(defs);
    }

    @PostMapping("/property")
    @ApiOperation("新增元数据自定义属性")
    public boolean insertCustomProps(@RequestBody MetadataPropertyInsertDto insert){
        val props = MetadataConvertor.toPropDefSeq(insert);
        metadataPropertyService.insertCustomProps(insert.getIdentifier(), props, DefChannel.PAGE);
        return true;
    }

    @PostMapping("/props")
    @ApiOperation(value = "批量新增修改元数据自定义属性", tags = "v1.2")
    public boolean insertCusProps(@RequestBody MetadataPropertyInsertDto insert){
        val props = MetadataConvertor.toPropDefSeq(insert);
        metadataPropertyService.saveCustomProps(insert.getIdentifier(), props, DefChannel.PAGE);
        return true;
    }

    @PostMapping("/propsAndRelations")
    @ApiOperation(value = "批量更新属性及关联")
    @LogRecordAnnotation(menu = "管理中心-乐高引擎-模型管理-模型设计", category = "编辑", success = "编辑了{defName{#update.identifier}}")
    public boolean updatePropsAndRelations(@RequestBody MetadataPropAndRelationUpdateDto update){
        metadataService.updatePropsAndRelations(update, DefChannel.PAGE);
        return true;
    }

    @PutMapping("/property")
    @ApiOperation("更新元数据自定义属性")
    public boolean updateCustomProps(@RequestBody MetadataPropertyUpdateDto update){
        val props = MetadataConvertor.toPropDefSeq(update);
        metadataPropertyService.updateCustomProps(update.getIdentifier(), props, DefChannel.PAGE);
        return true;
    }

    @PutMapping("/property/:remove")
    @ApiOperation("禁用元数据自定义属性")
    public boolean removeCustomProps(@RequestBody MetadataPropertyRemoveOrDeleteDto remove){
        metadataPropertyService.removeCustomProps(remove.getIdentifier(),
                Sequences.sequence(remove.getProps()), DefChannel.PAGE);
        return true;
    }

    @PutMapping("/property/:recover")
    @ApiOperation("恢复元数据自定义属性")
    public boolean recoverCustomProps(@RequestBody MetadataPropertyRecoverDto recover){
        val props = MetadataConvertor.toDefaultValueSeq(recover);
        metadataPropertyService.recoverCustomProps(recover.getIdentifier(), props, DefChannel.PAGE);
        return true;
    }

    @PostMapping("/property/:delete")
    @ApiOperation("删除元数据自定义属性")
    public boolean physicalDeleteCustomProps(@RequestBody MetadataPropertyRemoveOrDeleteDto delete){
        val props = Sequences.sequence(delete.getProps());
        metadataPropertyService.physicalDeleteCustomProps(delete.getIdentifier(), props, DefChannel.PAGE);
        return true;
    }

    @PostMapping("/relation")
    @ApiOperation("新增元数据关联")
    public boolean insertRelation(@RequestBody MetadataRelationDto relation){
        metadataRelationService
                .addRelation(JsonEnhanceUtil.toObject(relation, EntityRelationDef.class), DefChannel.PAGE);
        return true;
    }

    @DeleteMapping("/relation")
    @ApiOperation("删除元数据关联")
    public boolean deleteRelation(@RequestParam("identifier") String identifier, @RequestParam("property") String property){
        metadataRelationService.deleteRelation(identifier, property, DefChannel.PAGE);
        return true;
    }

    @GetMapping("/relation")
    @ApiOperation("查询元数据关联")
    public List<MetadataRelationVo> queryRelation(@RequestParam("identifier") String identifier){
        val relations = metadataRelationService.loadRelationsByIdentifier(identifier);
        return JsonEnhanceUtil.toObjects(relations, MetadataRelationVo.class).toList();
    }

    @PostMapping("/queryModelData")
    @ApiOperation(value = "查询元数据关联模型数据", tags = "v1.3")
    public PageResult<KeyValue> queryModelData(@RequestBody MetadataQueryModelDataDto queryDto, @RequestParam(value = "queryTime", required = false, defaultValue = "0") long queryTime){
        if(queryTime == 0){
            queryTime = System.currentTimeMillis();
        }
        return metadataService.queryModelData(queryDto, queryTime);
    }

    @GetMapping("/component/demo")
    @ApiOperation("查询组件定义")
    public List<ComponentConstruct> componentDemo(){
        throw new ServerException("为什么要调用我");
    }

    @GetMapping("property/enum")
    @ApiOperation("查询属性枚举定义")
    public Result<List<KeyValue>> findPropertyEnumDef(@RequestParam("identifier") String identifier, @RequestParam("property") String property) {
        return Result.ok(metadataService.loadPropertyEnumDef(identifier, property));
    }
}
