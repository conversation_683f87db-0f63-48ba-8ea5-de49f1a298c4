package com.caidaocloud.hrpaas.metadata.sdk.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public interface ComponentPropertyValue extends PropertyValue{
    String UIFORM_RELATION_FORMAT_TXT = "%s_txt", UIFORM_RELATION_FORMAT_FULLPATH_NAME_TXT = "%s_fullpath_txt",
        UIFORM_RELATION_FORMAT_FULLPATH_ID_TXT = "%s_fullpath";

    PropertyValue extractDisplay(MetadataPropertyVo propDef, List<PropertyDataDto> properties);

    List<PropertyDataDto> extractPersist(DataSimple data, MetadataPropertyVo propDef);

    Map<String, Boolean> propertySuffixToWhetherLong();

    Map<PropertyDataType, ComponentPropertyValue> componentHolder = new ConcurrentHashMap<>();

}
