package com.caidaocloud.hrpaas.service.application.form.dto;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.util.SecurityUserUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MappingConfigDto {
    @ApiModelProperty("配置id")
    private String id;
    @ApiModelProperty("映射源identifier")
    private String source;
    @ApiModelProperty("映射源名称")
    private String sourceName;
    @ApiModelProperty("映射目标identifier")
    private String target;
    @ApiModelProperty("映射目标名称")
    private String targetName;
    @ApiModelProperty("属性映射")
    private List<FormPropertyMapping> mapping;
    @ApiModelProperty("模型字段映射名称")
    private String name;
    @ApiModelProperty("模型字段映射类别")
    private String category;

    public static DataSimple queryEmpData(String identifier, String empId, long dataStartTime) {
        PageResult<DataSimple> result = DataQuery.identifier(identifier).queryInvisible().decrypt()
                .specifyLanguage()
                .filter(DataFilter.eq("empId", empId).andNe("deleted", Boolean.TRUE.toString())
                        .andEq("tenantId", SecurityUserUtil.getSecurityUserInfo()
                                .getTenantId()), DataSimple.class, dataStartTime);
        if (result.getItems().isEmpty()) {
            return null;
        }
        return result.getItems().get(0);
    }
}
