package com.caidaocloud.metadata.interfaces.facade;

import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeData;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataAddressVo;
import com.caidaocloud.metadata.application.dto.TreeLeafDto;
import com.caidaocloud.metadata.application.service.MetadataAddressService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/hrpaas/v1/address")
@Api(value = "地址组件模型数据操作", tags = "地址组件模型数据操作接口")
public class MetadataAddressController {
    @Resource
    private MetadataAddressService metadataAddressService;

    @GetMapping("/selectList")
    @ApiOperation("地址分层下拉列表")
    public Result<List<MetadataAddressVo>> selectList(@RequestParam(value = "id", required = false) String id){
        return Result.ok(metadataAddressService.selectList(id));
    }

    @GetMapping("/selectAllList")
    @ApiOperation("全量地址下拉列表")
    public Result<List<MetadataAddressVo>> selectAllList(){
        return Result.ok(metadataAddressService.selectAllList());
    }

    @GetMapping("/refresh")
    @ApiOperation("刷新缓存")
    public Result refresh(){
        metadataAddressService.loadCache();
        return Result.ok();
    }

    @GetMapping("/tree")
    @ApiOperation("地址树")
    public Result<List<TreeData<TreeLeafDto>>> tree(@RequestParam(value = "mode", required = false) String mode){
        return Result.ok(metadataAddressService.tree(mode));
    }

    @PostMapping("/codeList")
    @ApiOperation("地址名称列表查询")
    public Result<List<MetadataAddressVo>> getAddressByStr(@RequestBody List<String> list){
        return Result.ok(metadataAddressService.selectAddressByStr(list));
    }
}
