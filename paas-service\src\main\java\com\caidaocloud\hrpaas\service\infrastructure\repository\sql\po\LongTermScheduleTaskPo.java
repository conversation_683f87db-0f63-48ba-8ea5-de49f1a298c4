package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.domain.schedule.entity.ScheduleTask;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("long_term_schedule_task")
public class LongTermScheduleTaskPo  extends BasePo<LongTermScheduleTaskPo, ScheduleTask> {

    private String tenantId;

    private String taskTopic;

    private String taskId;

    private String taskDetail;

    private long execTime;
}
