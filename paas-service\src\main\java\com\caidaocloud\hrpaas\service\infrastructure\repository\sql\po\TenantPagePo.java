package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.domain.page.entity.TenantPage;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageTemplate;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.metadata.infrastructure.configuration.handler.BooleanTypeHandler;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("paas_tenant_page")
public class TenantPagePo extends BasePo<TenantPagePo, TenantPage> {

    private String name;

    private String icon;

    private PageType type;

    @TableField("_desc")
    private String desc;

    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean showNav;

    private String parentId;

    @TableField(typeHandler = BooleanTypeHandler.class)
    private boolean isStandard;

    private String joinModel;

    private PageTemplate pageTemplate;

    private long createTime;

    private String createBy;

    private long updateTime;

    private String updateBy;

    private String path;
}
