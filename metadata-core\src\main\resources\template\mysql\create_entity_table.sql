create table if not exists $tableName (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `identifier` varchar(50) NOT NULL,
    `bid` varchar(50) NOT NULL,
    `tenant_id` varchar(20) NOT NULL,
    `create_time` bigint NOT NULL,
    `update_time` bigint default NULL,
    `create_by` varchar(50) default NULL,
    `update_by` varchar(50) default NULL,
    `data_start_time` bigint default NULL,
    `data_end_time` bigint default NULL,
    `deleted` tinyint default 0,
    ##varcharLoopBy:,:`$column_name` varchar(200) default NULL##,
    ##encryptLoopBy:,:`$column_name` varchar(600) default NULL##,
    ##textLoopBy:,:`$column_name` text default NULL##,
    PRIMARY KEY (`id`) USING BTREE,
    index `idx_bid` (`bid`),
    index `idx_tenant_id` (`tenant_id`),
    index `idx_deleted` (`deleted`),
    index `idx_data_start_time` (`data_start_time`),
    index `idx_data_end_time` (`data_end_time`)
)ENGINE = InnoDB COMMENT = '$table_desc';