package com.caidaocloud.hrpaas.service.interfaces.dto.uiform;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataValueFunction;

public abstract class UiFormDataValueBaseDto<T> implements DataValueFunction<T> {
    private String source;

    private String target;

    @Override
    public String loadSourceId() {
        return this.source;
    }

    @Override
    public String loadTargetId() {
        return this.target;
    }

    @Override
    public void fitSourceId(String sourceId) {
        this.source = sourceId;
    }

    @Override
    public void fitTargetId(String targetId) {
        this.target = targetId;
    }
}