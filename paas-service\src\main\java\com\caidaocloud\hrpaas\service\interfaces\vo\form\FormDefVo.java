package com.caidaocloud.hrpaas.service.interfaces.vo.form;

import com.caidaocloud.hrpaas.service.domain.form.enums.FormDefStatus;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormTarget;
import com.googlecode.totallylazy.Lists;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FormDefVo {

    private String id;

    private String name;

    private String code;

    private String codeAsId;

    private String identifier;

    private Map<String, String> i18nName;

    private FormTarget target;

    private boolean workflowNodeAvailable;

    private boolean workflowAvailable;

    private boolean standard = false;

    private boolean implantable = false;

    private String description;

    private FormDefStatus status;

    private List<FormHeaderDefVo> headers = Lists.list();

    private List<FormPropDefVo> properties = Lists.list();

    private String mappingId;
}
