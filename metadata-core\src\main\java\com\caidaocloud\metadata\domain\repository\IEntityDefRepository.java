package com.caidaocloud.metadata.domain.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.metadata.domain.dto.EntityDefSearchDto;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequence;

import java.util.List;

public interface IEntityDefRepository extends IBaseRepository<EntityDef> {

    Option<EntityDef> loadByIdentifier(String identifier);

    PageResult<EntityDef> page(String keywords, int pageNo, int pageSize);

    Sequence<EntityDef> list(EntityDefSearchDto query, int maxCount);

    List<EntityDef> getListByIdentifier(List<String> identifierList, int maxCount);

    void delete(EntityDef entityDef);

    default String fetchColumnName(String identifier, String property){
        return property;
    }

    void addIndex(String identifier, List<String> properties);
}
