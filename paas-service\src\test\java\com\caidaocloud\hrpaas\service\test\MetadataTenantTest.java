package com.caidaocloud.hrpaas.service.test;

import com.caidaocloud.hrpaas.service.HrPaasApplication;
import com.caidaocloud.hrpaas.service.application.metadata.service.MetadataTenantService;
import com.caidaocloud.hrpaas.service.interfaces.dto.metadata.MetadataTenantDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HrPaasApplication.class)
public class MetadataTenantTest {
    @Resource
    private MetadataTenantService metadataTenantService;

    @Test
    public void initMetadataTenant(){
        MetadataTenantDto metadataTenantDto = new MetadataTenantDto();
        metadataTenantDto.setTenantId("ttt");
        metadataTenantDto.setCode("ttt");
        metadataTenantDto.setName("测试账号");
        metadataTenantDto.setThirdPart("ttt");
        // metadataTenantService.saveMetadataTenant(metadataTenantDto);
        //metadataTenantService.initTable();
    }
}
