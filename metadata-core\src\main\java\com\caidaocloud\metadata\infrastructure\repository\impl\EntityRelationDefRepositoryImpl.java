package com.caidaocloud.metadata.infrastructure.repository.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.util.ThreadLocalUtil;
import com.caidaocloud.metadata.domain.entity.EntityDef;
import com.caidaocloud.metadata.domain.entity.EntityRelationDef;
import com.caidaocloud.metadata.domain.repository.IEntityDefRepository;
import com.caidaocloud.metadata.domain.repository.IEntityRelationDefRepository;
import com.caidaocloud.metadata.infrastructure.repository.po.EntityDefPo;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.metadata.infrastructure.repository.utils.HolderUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.SneakyThrows;
import lombok.val;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Repository;


/**
 * @Author: Jarvis
 * @Desc:
 * @Date: 6/11/2021 3:57 PM
 */
@Repository
public class EntityRelationDefRepositoryImpl implements IEntityRelationDefRepository {

    @Autowired
    private EntityDefMapper defMapper;

    @Autowired
    private IEntityDefRepository defRepository;

    @NacosValue("${dbType:mysql}")
    private String dbType;

    @NacosValue("${anonymous.tenant:}")
    private String anonymousTenant;

    private String getTenantId() {
        String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        if(StringUtil.isEmpty(tenantId)){
            if(ThreadLocalUtil.isAnonymousAllowed()){
                tenantId = anonymousTenant;
            }
        }
        return tenantId;
    }

    @Override
    public String insert(EntityRelationDef entityRelationDef) {
        val def = defRepository.loadByIdentifier(entityRelationDef.getIdentifier()).getOrThrow(new ServerException("模型不存在"));
        def.getRelationProperties().add(entityRelationDef);
        val po = EntityDefPo.fromEntity(EntityDefPo.class, def);
        createRefTable(def);
        Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                .eq(EntityDefPo::getIdentifier, entityRelationDef.getIdentifier());
        this.defMapper.update(po, wrapper);
        return String.valueOf(po.getId());
    }

    @Override
    public void update(EntityRelationDef entityRelationDef) {
        throw new UnsupportedOperationException("模型关联不能修改");
    }

    @Override
    public void delete(String identifier, String property){
        val def = defRepository.loadByIdentifier(identifier).getOrThrow(new ServerException("模型不存在"));

        val relations = Sequences.sequence(def.getRelationProperties());
        relations.removeIf(e -> property.equals(e.getProperty()));
        def.setRelationProperties(relations.toList());
        val po = EntityDefPo.fromEntity(EntityDefPo.class, def);
        Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                .eq(EntityDefPo::getIdentifier, identifier);
        this.defMapper.update(po, wrapper);
    }

    @Override
    public long checkEntityDefRelatedCount(String identifier) {
        Wrapper<EntityDefPo> wrapper = new LambdaQueryWrapper<EntityDefPo>()
                .like(EntityDefPo::getRelationProperties, "\"relatedIdentifier\":\""+identifier+"\"");
        return this.defMapper.selectCount(wrapper);
    }

    @Override
    public Sequence<EntityRelationDef> loadByIdentifier(String identifier) {
        val def = defRepository.loadByIdentifier(identifier).getOrThrow(new ServerException("模型不存在"));
        return Sequences.sequence(def.getRelationProperties());
    }

    @SneakyThrows
    private void createRefTable(EntityDef entityDef){
        String sqlTemplate = IOUtils
                .toString(new ClassPathResource("template/" + dbType + "/create_entity_relation_table.sql")
                        .getInputStream(), "UTF-8");
        val identifier = entityDef.getIdentifier();
        entityDef.getRelationProperties().forEach(relation ->{
                    val sql = sqlTemplate
                            .replaceAll("\\$tableName", SnakeCaseConvertor.toSnake(identifier+".relation."+relation.getProperty() + "_" + getTenantId()))
                            .replaceAll("\\$table_desc", entityDef.getName() + "-" + relation.getPropertyName());
                    defMapper.exec(sql);
                }
        );
    }


}
