package com.caidaocloud.hrpaas.metadata.sdk.query;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.DataQueryType;
import com.caidaocloud.hrpaas.metadata.sdk.enums.IndicateType;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.FilterOperator;
import com.caidaocloud.hrpaas.metadata.sdk.filter.MultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SpecifiedMultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.service.AuthScopeFetchService;
import com.caidaocloud.hrpaas.metadata.sdk.util.AuthScopeUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.CountUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMetadataAuthFeignClient;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.service.DataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Slf4j
public class DataQuery {

    private String identifier;

    private List<String> relatedProperties = Lists.list();

    private boolean decrypt = false;

    private boolean showDept = false;

    private Long queryTime;

    private boolean queryInvisible = false;

    private List<String> languages = Lists.list();

    private DataFilter filter;

    private int pageSize = 20;

    private int pageNo = 1;

    private boolean group = false;

    private boolean oneWithMetaData = false;

    private boolean isMasterDataQuery = false;

    private String orderBy;

    private boolean expression = false;
    private List<MetadataPropertyVo> specifyExpProperties = new ArrayList<>();

    private DataQuery(){

    }


    public DataQuery setMasterDataQuery(boolean masterDataQuery) {
        isMasterDataQuery = masterDataQuery;
        return this;
    }

    protected DataQuery(String identifier){
        this.identifier = identifier;
    }


    public static DataQuery identifier(String identifier){
        DataQuery request = new DataQuery();
        request.identifier = identifier;
        return request;
    }

    public DataQuery queryOneWithMetadata(){
        this.oneWithMetaData = true;
        return this;
    }

    public DataQuery queryRelatedProperties(String... properties) {
        this.relatedProperties = Lists.list(properties);
        return this;
    }

    public DataQuery group(){
        this.group = true;
        return this;
    }

    public DataQuery decrypt(){
        this.decrypt = true;
        return this;
    }

    public DataQuery dept(){
        this.showDept = true;
        return this;
    }

    public DataQuery exp(){
        this.expression = true;
        return this;
    }

    public DataQuery limit(int pageSize, int pageNo){
        if(pageSize > CountUtil.MAX_LIMIT){
            throw new ServerException("超出最大查询限制");
        }
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        return this;
    }

    public DataQuery queryInvisible(){
        this.queryInvisible = true;
        return this;
    }

    public DataQuery specifyLanguage(String... languages){
        this.languages = Lists.list(languages);
        return this;
    }


    public <T extends DataSimple> PageResult<T> filter(DataFilter filter, Class<T> clazz){
        return filter(filter, clazz, System.currentTimeMillis());
    }

    public <T extends DataSimple> List<T> range(String bid, long startTime, long endTime, Class<T> clazz){
        try{
            MdQueryTL.set(this.isMasterDataQuery);
            QueryInfoCache.init();
            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val metadata = metadataService.load(identifier);
            List<EntityDataDto> dataList = SpringUtil.getBean(DataOperatorService.class).range(identifier, bid, startTime, endTime);
            for(EntityDataDto data: dataList){
                replaceRelatedPropertyNameInResult(data);
            }
            return Sequences.sequence(dataList).map(data->
                    DataSimple.fromPersist(this, metadata, data, clazz)).toList();
        }finally {
            MdQueryTL.remove();
            QueryInfoCache.clear();
        }
    }

    public static List<LabelData> allJobGrade(){
        //todo 查询md
        val labelName = "jobGradeName";
        List<EntityDataDto> jobGradeList = SpringUtil.getBean(DataOperatorService.class).allJobGrade();
        return jobGradeList.stream().map(it->{
            val value = it.getProperties().stream().filter(
                    prop->labelName.equals(prop.getProperty())
            ).findFirst().get().getValue();
            it.setProperties(null);
            val data = JsonEnhanceUtil.toObject(it, LabelData.class);
            data.setLabel(value);
            return data;
                }
        ).collect(Collectors.toList());
    }

    public static List<LabelData> allJobGradeChannel(){
        //todo 查询md
        val labelName = "channelName";
        List<EntityDataDto> jobGradeList = SpringUtil.getBean(DataOperatorService.class).allJobGradeChannel();
        return jobGradeList.stream().map(it->{
            val value = it.getProperties().stream().filter(
                    prop->labelName.equals(prop.getProperty())
            ).findFirst().get().getValue();
            it.setProperties(null);
            val data = JsonEnhanceUtil.toObject(it, LabelData.class);
            data.setLabel(value);
            return data;
                }
        ).collect(Collectors.toList());
    }

    public long count(DataFilter filter, long queryTime){
        try{
            MdQueryTL.set(isMasterDataQuery);
            this.queryTime = queryTime;
            this.filter = addScope(identifier, filter, queryTime);
            val query = JsonEnhanceUtil.toObject(this, DataQueryDto.class);
            query.setType(DataQueryType.PAGE);
            long count = SpringUtil.getBean(DataOperatorService.class).count(query, queryTime);
            return count;
        }finally {
            MdQueryTL.remove();
        }

    }

    public <T extends DataSimple> PageResult<T> filter(DataFilter filter, Class<T> clazz, long queryTime){
        return filter(filter, clazz, null, queryTime);
    }

    public <T extends DataSimple> PageResult<T> filter(DataFilter filter, Class<T> clazz, String orderBy, long queryTime){
        val start = System.currentTimeMillis();
        try{
            MdQueryTL.set(isMasterDataQuery);
            QueryInfoCache.init();
            this.queryTime = queryTime;
            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val metadata = metadataService.load(identifier);
            this.filter = addScope(identifier, filter, queryTime);
            this.orderBy = orderBy;
            val query = JsonEnhanceUtil.toObject(this, DataQueryDto.class);
            query.setType(DataQueryType.PAGE);
            val datas = SpringUtil.getBean(DataOperatorService.class).filter(query, queryTime);
            for(EntityDataDto data: datas.getItems()){
                replaceRelatedPropertyNameInResult(data);
            }
            val items = Sequences.sequence(datas.getItems()).map(data->
                    DataSimple.fromPersist(this, metadata, data, clazz)).toList();
            val end = System.currentTimeMillis();
            if (log.isDebugEnabled()) {
                log.debug(identifier + " filter 访问时长" + (end-start));
            }
            if (end - start > 500 && log.isDebugEnabled()) {
                log.debug("relation " + StringUtils.join(relatedProperties, ",") + ", filter" + FastjsonUtil.toJson(filter));
            }
            return new PageResult(items, datas.getPageNo(),datas.getPageSize(), datas.getTotal());
        }catch (Exception e){
            log.error("请求异常，identifier:"+ identifier +" filter: " + FastjsonUtil.toJson(this.filter));
            throw e;
        }finally {
            MdQueryTL.remove();
            QueryInfoCache.clear();
        }
    }

    public <T extends DataSimple> T oneOrNull(String bid, Class<T> clazz){
        return oneOrNull(bid, clazz, System.currentTimeMillis());
    }

    public <T extends DataSimple> T oneOrNull(String bid, Class<T> clazz, long queryTime){
        try{
            expression = true;
            MdQueryTL.set(isMasterDataQuery);
            QueryInfoCache.init();
            this.queryTime = queryTime;
            val query = JsonEnhanceUtil.toObject(this, DataQueryDto.class);
            query.setBid(bid);
            query.setType(DataQueryType.ONE);
            val data = SpringUtil.getBean(DataOperatorService.class).oneOrNull(query, queryTime);
            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val metadata = metadataService.load(identifier);
            replaceRelatedPropertyNameInResult(data);
            val result = DataSimple.fromPersist(this, metadata, data, clazz);
            metadata.fetchAllProperties().stream().filter(it->
                PropertyDataType.Data_Table.equals(it.getDataType())
            ).forEach(tableProperty->{
                val slaves = DataQuery.identifier(fetchSlaveIdentifier(identifier, tableProperty.getProperty()))
                        .setMasterDataQuery(isMasterDataQuery)
                        .limit(-1, 1)
                        .filter(DataFilter.eq("masterBid", bid), DataSlave.class, queryTime).getItems();
                result.getProperties().put(tableProperty.getProperty(), new DataTable(slaves));
            });
            if(oneWithMetaData){
                result.appendMetadataInfo(metadata);
            }
            return result;
        }finally {
            MdQueryTL.remove();
            QueryInfoCache.clear();
        }
    }

    private String fetchSlaveIdentifier(String identifier, String property){
        return identifier + "_" + property;
    }

    public <T extends DataSimple> T one(String bid, Class<T> clazz){
        return one(bid, clazz, System.currentTimeMillis());
    }

    public <T extends DataSimple> T one(String bid, Class<T> clazz, long queryTime){
        try{
            expression = true;
            MdQueryTL.set(isMasterDataQuery);
            QueryInfoCache.init();
            this.queryTime = queryTime;
            val query = JsonEnhanceUtil.toObject(this, DataQueryDto.class);
            query.setBid(bid);
            query.setType(DataQueryType.ONE);
            val data = SpringUtil.getBean(DataOperatorService.class).one(query, queryTime);
            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val metadata = metadataService.load(identifier);
            replaceRelatedPropertyNameInResult(data);
            val result = DataSimple.fromPersist(this, metadata, data, clazz);
            metadata.fetchAllProperties().stream().filter(it->
                    PropertyDataType.Data_Table.equals(it.getDataType())
            ).forEach(tableProperty->{
                val slaves = DataQuery.identifier(fetchSlaveIdentifier(identifier, tableProperty.getProperty()))
                        .setMasterDataQuery(isMasterDataQuery)
                        .limit(-1, 1)
                        .filter(DataFilter.eq("masterBid", bid), DataSlave.class, orderBy, queryTime).getItems();
                result.getProperties().put(tableProperty.getProperty(), new DataTable(slaves));
            });
            if(oneWithMetaData){
                result.appendMetadataInfo(metadata);
            }
            return result;
        }finally {
            MdQueryTL.remove();
            QueryInfoCache.clear();
        }
    }

    public <T extends DataSimple> List<TreeData<T>> tree(Class<T> clazz){
        return tree(clazz, System.currentTimeMillis(),null);
    }

    public <T extends DataSimple> List<TreeData<T>> tree(Class<T> clazz, long queryTime){
        return tree(clazz, queryTime,null);
    }

    public <T extends DataSimple> List<T> descendants(String bid, Class<T> clazz){
        return descendants(bid, clazz, System.currentTimeMillis());
    }

    public <T extends DataSimple> List<T> descendants(String bid, Class<T> clazz, long queryTime){
        try {
            MdQueryTL.set(isMasterDataQuery);
            this.queryTime = queryTime;
            DataFilter dataFilter = addScope(identifier, DataFilter.eq("identifier", identifier).andNe("deleted",Boolean.TRUE.toString()), queryTime);
            val treeDataMap = Maps.map(
                    Sequences.sequence(
                            limit(CountUtil.MAX_LIMIT, 1)
                                    .filter(dataFilter, clazz, queryTime)
                                    .getItems()
                    ).map(data->Pair.pair(data.getBid(), new TreeData(data)))
            );
            List<T> resultList = Lists.list();
            TreeData<T> resultTree = null;
            for(Map.Entry<String, TreeData> dataEntry : treeDataMap.entrySet()){
                val data = dataEntry.getValue().getData();
                String pid = data.fetchPid();
                if(null != pid){
                    if(null != treeDataMap.get(pid)){
                        treeDataMap.get(pid).getChildren().add(dataEntry.getValue());
                    }
                }
                if(clazz.equals(LabelData.class)){
                    data.getProperties().clear();
                }
                if(StringUtils.equals(data.getBid(), bid)){
                    resultTree = dataEntry.getValue();
                }
            }
            if(null != resultTree){
                addChildren(resultTree, resultList);
            }
            return resultList;
        } finally {
            MdQueryTL.remove();
        }
    }

    private <T extends DataSimple> void addChildren(TreeData<T> tree, List<T> resultList){
        tree.getChildren().forEach( child -> {
            resultList.add(child.getData());
            addChildren(child, resultList);
        });
    }

    public <T extends DataSimple> List<TreeData<T>> tree(Class<T> clazz, long queryTime, DataFilter filter){
        try {
            MdQueryTL.set(isMasterDataQuery);
            this.queryTime = queryTime;
            DataFilter dataFilter = DataFilter.eq("identifier", identifier).andNe("deleted",Boolean.TRUE.toString());
            if(filter != null){
                dataFilter = addScope(identifier, dataFilter.and(filter), queryTime);
            }
            val treeDataMap = Maps.map(
                    Sequences.sequence(
                            limit(CountUtil.MAX_LIMIT, 1)
                                    .filter(dataFilter, clazz, queryTime)
                                    .getItems()
                    ).map(data->Pair.pair(data.getBid(), new TreeData(data)))
            );
            List<TreeData<T>> resultList = Lists.list();
            for(Map.Entry<String, TreeData> dataEntry : treeDataMap.entrySet()){
                val data = dataEntry.getValue().getData();
                String pid = data.fetchPid();
                if(null == pid || "-1".equals(pid)){
                    resultList.add(dataEntry.getValue());
                }else{
                    if(null != treeDataMap.get(pid)){
                        treeDataMap.get(pid).getChildren().add(dataEntry.getValue());
                    }
                }
                if(clazz.equals(LabelData.class)){
                    data.getProperties().clear();
                    }
            }
            return resultList;
        } finally {
            MdQueryTL.remove();
        }
    }

    private void replaceRelatedPropertyNameInResult(EntityDataDto data){
        if(null != data && null != data.getProperties() && null != relatedProperties){
            for(PropertyDataDto property: data.getProperties()){
                for(String relatedProperty: relatedProperties){
                    if(SnakeCaseConvertor.toSnake(relatedProperty).equals(property.getProperty())){
                        property.setProperty(relatedProperty);
                        if(null!=property.getArrayValues()){
                            property.setValue(FastjsonUtil.toJson(property.getArrayValues()));
                        }
                    }
                }
            }
        }
    }

    public void test(){
        DataSimple result = DataQuery.identifier("entity.hr.person")
                .queryRelatedProperties("depart.area")
                .decrypt().specifyLanguage().queryInvisible().one("id", DataSimple.class);
    }


    public <T extends DataSimple> PageResult<T> max(DataFilter filter, DataFilter groupFilter,
                                                           String maxProperty, long queryTime,
                                                           Class<T> clazz,
                                                           String... by){
        val start = System.currentTimeMillis();
        try{
            MdQueryTL.set(isMasterDataQuery);
            QueryInfoCache.init();
            val query = new MaxQueryDto();
            query.setIdentifier(this.identifier);
            query.setFilter(FastjsonUtil.toJson(Lists.list(addScope(identifier, filter, queryTime),addScope(identifier, groupFilter, queryTime))));
            query.setMaxProperty(maxProperty);
            query.setQueryTime(queryTime);
            query.setPageNo(pageNo);
            query.setPageSize(pageSize);
            query.setBy(Lists.list(by));
            this.queryTime = queryTime;
            //this.filter = addScope(filter, queryTime);
            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val metadata = metadataService.load(identifier);
            val datas = SpringUtil.getBean(DataOperatorService.class).max(query);
            val items = Sequences.sequence(datas.getItems()).map(data->
                    DataSimple.fromPersist(this, metadata, data, clazz)).toList();
            val end = System.currentTimeMillis();
            if (log.isDebugEnabled()) {
                log.debug(identifier + " filter 访问时长" + (end-start));
            }
            return new PageResult(items, datas.getPageNo(),datas.getPageSize(), datas.getTotal());
        }finally {
            MdQueryTL.remove();
            QueryInfoCache.clear();
        }
    }


    public <T extends DataSimple> PageResult<T> max(DataFilter filter,
                                                    String maxProperty, long queryTime,
                                                    Class<T> clazz, String... by){
        return max(filter, filter, maxProperty, queryTime, clazz, by);
    }


    public List<GroupedIndicateResult> indicateByGroup(DataFilter filter,
                                                       String indicateProperty,
                                                       IndicateType type,
                                                       long queryTime,
                                                       String... bys){
        try {
            MdQueryTL.set(isMasterDataQuery);
            val dto = new GroupedIndicateDto();
            dto.setBy(Lists.list(bys).stream().map(it->it.replaceAll("\\$",".")).collect(Collectors.toList()));
            dto.setIdentifier(identifier);
            dto.setQueryTime(queryTime);
            dto.setFilter(FastjsonUtil.toJson(addScope(identifier, filter, queryTime)));
            dto.setIndicateType(type);
            dto.setIndicateProperty(indicateProperty);
            List<Map<String, Object>> result = SpringUtil.getBean(DataOperatorService.class).indicateByGroup(dto);
            return result.stream().map(it->{
                GroupedIndicateResult indicateResult = new GroupedIndicateResult();
                indicateResult.setIndicate(it.get("indicate").toString());
                for(String by: bys){
                    val value = it.get(by.replaceAll("\\$", "."));
                    indicateResult.getBy().put(by, null == value?null:value.toString());
                }
                return indicateResult;
            }).collect(Collectors.toList());
        } finally {
            MdQueryTL.remove();
        }
    }

    @Deprecated
    public List<GroupedCountResult> countByGroup(DataFilter filter, long queryTime,
                                                    String... bys){
        try {
            MdQueryTL.set(isMasterDataQuery);
            val dto = new GroupedCountDto();
            dto.setBy(Lists.list(bys).stream().map(it->it.replaceAll("\\$",".")).collect(Collectors.toList()));
            dto.setIdentifier(identifier);
            dto.setQueryTime(queryTime);
            dto.setFilter(FastjsonUtil.toJson(addScope(identifier, filter, queryTime)));
            List<Map<String, Object>> result = SpringUtil.getBean(DataOperatorService.class).countByGroup(dto);
            return result.stream().map(it->{
                GroupedCountResult countResult = new GroupedCountResult();
                countResult.setCount(Long.valueOf(it.get("count").toString()));
                for(String by: bys){
                    val value = it.get(by.replaceAll("\\$", "."));
                    countResult.getBy().put(by, null == value?null:value.toString());
                }
                return countResult;
            }).collect(Collectors.toList());
        } finally {
            MdQueryTL.remove();
        }
    }

    public PageResult<Map<String, String>> filterProperties(DataFilter filter, List<String> specifyProperties, long queryTime){
        val start = System.currentTimeMillis();
        if(!specifyProperties.isEmpty() && !relatedProperties.isEmpty()){
            throw new ServerException("不支持同时查询指定字段及关联字段");
        }
        try{
            MdQueryTL.set(isMasterDataQuery);
            QueryInfoCache.init();
            this.queryTime = queryTime;
            this.filter = addScope(identifier, filter, queryTime);
            val query = JsonEnhanceUtil.toObject(this, DataQueryDto.class);
            val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
            val metadata = metadataService.load(identifier);
            query.setType(DataQueryType.PAGE);
            query.setSpecifyProperties(specifyProperties);
            val datas = SpringUtil.getBean(DataOperatorService.class).filter(query, queryTime);
            val items = Sequences.sequence(datas.getItems()).map(data->{
                Map<String, String> map = Maps.map();
                data.getProperties().forEach(it -> {
                    if (it.getDataType().isArray()) {
                        if (null == it.getArrayValues()) {
                            map.put(it.getProperty(), null);
                        }else{
                            map.put(it.getProperty(), FastjsonUtil.toJson(it.getArrayValues()));
                        }
                    }else{
                        map.put(it.getProperty(), it.getValue());
                    }
                });
                Map<String, Object> basicMap = FastjsonUtil.convertObject(data, Map.class);
                specifyProperties.forEach(it -> {
                    if (basicMap.containsKey(it)) {
                        if (basicMap.get(it) != null) {
                            map.put(it, String.valueOf(basicMap.get(it)));
                        }
                    }
                });
                return map;
            }).toList();
            val end = System.currentTimeMillis();
            if (log.isDebugEnabled()) {
                log.debug(identifier + " filter 访问时长" + (end-start));
            }
            if(end-start>500 && log.isDebugEnabled()){
                log.debug("relation " + StringUtils.join(relatedProperties, ",") + ", filter" + FastjsonUtil.toJson(filter));
            }
            return new PageResult(items, datas.getPageNo(),datas.getPageSize(), datas.getTotal());
        }finally {
            MdQueryTL.remove();
            QueryInfoCache.clear();
        }
    }

    private Map<String, Object> convertData(MetadataVo metadata, Map<String, String> dataMap) {
        Map<String, Object> map = new HashMap<>();
        Sequences.sequence(metadata.fetchAllProperties()).forEach(p->{
            if (p.getDataType().isComponent()) {
                return;
            }
            Object value = dataMap.get(p.getProperty());
            switch (p.getDataType()) {
            case Timestamp:
                map.put(p.getProperty(), value == null ? null : Long.valueOf(String.valueOf(value)));
                break;
            case Number:
                map.put(p.getProperty(), value == null ? null : Double.valueOf(String.valueOf(value)));
                break;
            case String:
                map.put(p.getProperty(), value);
            default:
            }
        });
        return map;
    }


    private void specifyProperty(DataQueryDto query, MetadataVo metadata, List<String> specifyProperties) {
        List<MetadataPropertyVo> expPropertyList = new ArrayList<>();
        List<String> commonPropertyList = new ArrayList<>();
        List<MetadataPropertyVo> allProperties = metadata.fetchAllProperties();
        for (String property : specifyProperties) {
            Option<MetadataPropertyVo> option = Sequences.sequence(allProperties)
                    .find(p -> p.getProperty().equals(property) && p.isExpEnable());
            if (option.isDefined()) {
                expPropertyList.add(option.get());
            }
            else {
                commonPropertyList.add(property);
            }
        }
        this.specifyExpProperties = expPropertyList;
        query.setSpecifyProperties(commonPropertyList);
    }

    public static DataFilter addScope(String identifier, DataFilter filter, long time){
        if(null == filter){
            filter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId());
        }
        val cacheService = SpringUtil.getBean(CacheService.class);
        String scopeEnabled = SpringUtil.getContext().getEnvironment().getProperty("authScopeEnabled", "");
        if(StringUtils.isEmpty(scopeEnabled)){
            scopeEnabled = cacheService.getValue("authScopeEnabled");
        }else{
            cacheService.cacheValue("authScopeEnabled", scopeEnabled, -1);
        }
        if(!StringUtils.equals("true", scopeEnabled)){
            if(AuthScopeUtil.checkRevert()){
                return filter.and(DataFilter.eq("tenantId", "none"));
            }else{
                return filter;
            }
        }

        val identifierCheckEnabled = AuthScopeFilterUtil.checkIdentifier(identifier);

        Boolean addScope = AuthScopeFilterUtil.get();
//        if(Boolean.FALSE.equals(addScope)){
//            return filter;
//        }
        if(Boolean.FALSE.equals(addScope) ||
                (null == addScope && !identifierCheckEnabled)){
            return filter;
        }
        if(null == SecurityUserUtil.getSecurityUserInfo() || null == SecurityUserUtil.getSecurityUserInfo().getUserId()){
            return filter;
        }
        val userId = String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId());
        List<AuthRoleScopeFilterDetailDto> scopeFilterDetails = SpringUtil.getBean(AuthScopeFetchService.class)
                .fetchAuthScope(identifier, userId);
        AuthScopeFilterUtil.put(addScope);
        log.debug("scopeFilterDetails for identifier " + identifier + " " +userId+  " is " + FastjsonUtil.toJson(scopeFilterDetails));
        List<DataFilter> scopeFilters = Lists.list();
        if(!CollectionUtils.isEmpty(scopeFilterDetails)) {
            for(AuthRoleScopeFilterDetailDto scopeFilterDetail : scopeFilterDetails) {
                boolean toOr = scopeFilterDetail.isInToOr();
                List<String> values = scopeFilterDetail.getRestriction()
                        .toValues(scopeFilterDetail.getSimpleValues());
                if(values.isEmpty()){
                    if(AuthScopeUtil.checkRevert()){
                        scopeFilters.add(DataFilter.ne("tenantId", "none"));
                    }else{
                        scopeFilters.add(DataFilter.eq("tenantId", "none"));
                    }
                }else if(toOr){
                    if(AuthScopeUtil.checkRevert()){
                        val filters = values.stream().map(it->DataFilter
                                .notLike(scopeFilterDetail.getProperty(), it)).collect(Collectors.toList());
                        val scopeFilter = new MultiDataFilter(FilterOperator.AND, filters.toArray(new DataFilter[filters.size()]));
                        scopeFilters.add(scopeFilter);
                    }else{
                        val filters = values.stream().map(it->DataFilter
                                .regex(scopeFilterDetail.getProperty(), it)).collect(Collectors.toList());
                        val scopeFilter = new MultiDataFilter(FilterOperator.OR, filters.toArray(new DataFilter[filters.size()]));
                        scopeFilters.add(scopeFilter);
                    }
                }else if(values.size() > 1000){
                    List<List<String>> splitValues = Lists.list();
                    while(values.size() > 1000){
                        splitValues.add(values.subList(0,1000));
                        values = values.subList(1000, values.size() - 1);
                    }
                    splitValues.add(values);
                    if(AuthScopeUtil.checkRevert()){
                        val filters = splitValues.stream().map(it->DataFilter.notIn(scopeFilterDetail.getProperty(), it)).collect(Collectors.toList());
                        val scopeFilter = new MultiDataFilter(FilterOperator.AND, filters.toArray(new DataFilter[filters.size()]));
                        scopeFilters.add(scopeFilter);
                    }else{
                        val filters = splitValues.stream().map(it->DataFilter.in(scopeFilterDetail.getProperty(), it)).collect(Collectors.toList());
                        val scopeFilter = new MultiDataFilter(FilterOperator.OR, filters.toArray(new DataFilter[filters.size()]));
                        scopeFilters.add(scopeFilter);
                    }

                }else{
                    if(AuthScopeUtil.checkRevert()){
                        scopeFilters.add(DataFilter.notIn(scopeFilterDetail.getProperty(), values));
                    }else{
                        scopeFilters.add(DataFilter.in(scopeFilterDetail.getProperty(), values));
                    }
                }
            }
        }
        if(scopeFilters.isEmpty()){
            if(AuthScopeUtil.checkRevert()){
                return filter.and(DataFilter.eq("tenantId", "none"));
            }else{
                return filter;
            }
        }else{
            if(AuthScopeUtil.checkRevert()){
                return filter.and(new MultiDataFilter(FilterOperator.AND, scopeFilters.toArray(new DataFilter[scopeFilters.size()])));
            }else{
                return filter.and(new MultiDataFilter(FilterOperator.OR, scopeFilters.toArray(new DataFilter[scopeFilters.size()])));
            }

        }
    }

    public List<EmpSimple> loadByCondition(SpecifiedMultiDataFilter dataFilter) {

        try {
            MdQueryTL.set(isMasterDataQuery());
            setQueryTime(System.currentTimeMillis());
            val query = JsonEnhanceUtil.toObject(this, DataQueryDto.class);
            query.setType(DataQueryType.PAGE);
            query.setFilter(FastjsonUtil.toJson(dataFilter));
            return SpringUtil.getBean(DataOperatorService.class).loadByCondition(query, getQueryTime());
        }
        finally {
            MdQueryTL.remove();
        }

    }
}
