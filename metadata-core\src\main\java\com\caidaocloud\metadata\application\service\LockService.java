package com.caidaocloud.metadata.application.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.metadata.domain.entity.Constant;
import com.caidaocloud.metadata.infrastructure.repository.transaction.PaasTransactionManager;
import com.caidaocloud.lock.Locker;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.caidaocloud.metadata.infrastructure.repository.transaction.TransactionInterceptorAdapter.transactionIdHolder;

@Component
@Slf4j
public class LockService {

    @Autowired
    private Locker locker;

    @Autowired
    private PaasTransactionManager transactionManager;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @SneakyThrows
    public void doWithLock(String identifier, String dataBid, Runnable runnable){
        String transactionId = transactionIdHolder.get();
        val lockKey = Constant.modifyDataLockPrefix + identifier + "_" + dataBid;
        val lock = locker.getLock(lockKey);
        val locked = lock.tryLock(100, TimeUnit.MILLISECONDS);
        long now = System.currentTimeMillis();
        if(locked){
            try{
                log.debug("lock key : " + lockKey);
                if(StringUtils.isNotEmpty(transactionId)){
                    log.debug("transactionId:"+transactionId);
                    transactionManager.addLock(transactionId, lockKey);
                }

                val keys = redisTemplate.keys("transactionLockTimeRange" + lockKey + "*").stream().filter(key->{
                    if(StringUtils.isNotEmpty(transactionId)){
                        return !key.equals("transactionLockTimeRange" + lockKey + "-" + transactionId);
                    }else{
                        return !key.equals("transactionLockTimeRange" + lockKey);
                    }
                }).collect(Collectors.toList());
                if(StringUtils.isNotEmpty(transactionId)){
                    String timeRange = redisTemplate.opsForValue().get("transactionTimeRange" + transactionId);
                    if(StringUtils.isEmpty(timeRange)){
                        throw new ServerException("transaction not exist");
                    }
                    now = Long.valueOf(StringUtils.substringBefore(timeRange, ","));
                }
                for(String key : keys){
                    val timeRange = redisTemplate.opsForValue().get(key);
                    log.debug("lock time key : " + key + ", value:" + timeRange);
                    if(StringUtils.isNotEmpty(timeRange)){
                        val startEnd = Arrays.stream(timeRange.split(",")).map(it->Long.valueOf(it)).collect(Collectors.toList());
                        if(now >= startEnd.get(0) && now <= startEnd.get(1)){
                            throw new ServerException("数据操作中，请稍后重试");
                        }
                    }
                }
                if(StringUtils.isNotEmpty(transactionId)){
                    String timeRange = redisTemplate.opsForValue().get("transactionTimeRange" + transactionId);
                    if(StringUtils.isEmpty(timeRange)){
                        throw new ServerException("transaction not exist");
                    }
                    timeRange = StringUtils.substringBefore(timeRange, ",") + "," + DateUtil.MAX_TIMESTAMP;
                    log.debug("set lock time key : " + "transactionLockTimeRange" + lockKey +
                            "-" + transactionId + ", value:" + timeRange);
                    redisTemplate.opsForValue().set("transactionLockTimeRange" + lockKey +
                                    "-" + transactionId,
                            timeRange, 30, TimeUnit.SECONDS);
                }else{
                    val timeRange = now + "," + DateUtil.MAX_TIMESTAMP;
                    log.debug("set lock time key : " + "transactionLockTimeRange" + lockKey + ", value:" + timeRange);
                    redisTemplate.opsForValue().set("transactionLockTimeRange" + lockKey,
                            timeRange, 30, TimeUnit.SECONDS);
                }
                runnable.run();
            }finally {
                if(StringUtils.isEmpty(transactionId)){
                    val timeRange = now + "," + System.currentTimeMillis();
                    log.debug("set lock time key : " + "transactionLockTimeRange" + lockKey + ", value:" + timeRange);
                    redisTemplate.opsForValue().set("transactionLockTimeRange" + lockKey,
                            timeRange, 30, TimeUnit.SECONDS);
                    lock.unlock();
                    log.debug("unlock key : " + lockKey);
                }
            }
        }else{
            log.info("failed lock:"+lockKey+",transactionId:"+transactionId);
            throw new ServerException("数据正在被其他请求更新中，请稍后重试");
        }
    }

}
