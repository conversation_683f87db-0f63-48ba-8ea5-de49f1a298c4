package com.caidaocloud.hrpaas.paas.common.event;

import com.caidaocloud.hrpaas.paas.common.dto.PageDetailDto;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 *
 * <AUTHOR>
 * @date 2024/4/24
 */
public class PageDetailChangeEventTests {
	@Test
	public void json(){
		String json = "{\n"
				+ "\t\"childList\": [{\n"
				+ "\t\t\"childList\": [],\n"
				+ "\t\t\"label\": \"合同信息\",\n"
				+ "\t\t\"type\": \"EmpTab\",\n"
				+ "\t\t\"key\": \"entity.hr.Contract\",\n"
				+ "\t\t\"props\": {\n"
				+ "\t\t\t\"show\": true,\n"
				+ "\t\t\t\"disabled\": false\n"
				+ "\t\t}\n"
				+ "\t}, {\n"
				+ "\t\t\"childList\": [],\n"
				+ "\t\t\"label\": \"工作经历\",\n"
				+ "\t\t\"type\": \"EmpTab\",\n"
				+ "\t\t\"key\": \"entity.hr.EmpWorkOverview\",\n"
				+ "\t\t\"props\": {\n"
				+ "\t\t\t\"show\": true,\n"
				+ "\t\t\t\"disabled\": false\n"
				+ "\t\t}\n"
				+ "\t}, {\n"
				+ "\t\t\"childList\": [],\n"
				+ "\t\t\"label\": \"教育经历\",\n"
				+ "\t\t\"type\": \"EmpTab\",\n"
				+ "\t\t\"key\": \"entity.hr.EmpEduInfo\",\n"
				+ "\t\t\"props\": {\n"
				+ "\t\t\t\"show\": true,\n"
				+ "\t\t\t\"disabled\": false\n"
				+ "\t\t}\n"
				+ "\t}, {\n"
				+ "\t\t\"childList\": [],\n"
				+ "\t\t\"label\": \"家庭信息\",\n"
				+ "\t\t\"type\": \"EmpTab\",\n"
				+ "\t\t\"key\": \"entity.hr.FamilyInfo\",\n"
				+ "\t\t\"props\": {\n"
				+ "\t\t\t\"show\": true,\n"
				+ "\t\t\t\"disabled\": false\n"
				+ "\t\t}\n"
				+ "\t}, {\n"
				+ "\t\t\"childList\": [],\n"
				+ "\t\t\"label\": \"奖惩信息\",\n"
				+ "\t\t\"type\": \"EmpTab\",\n"
				+ "\t\t\"key\": \"entity.hr.EmpReward\",\n"
				+ "\t\t\"props\": {\n"
				+ "\t\t\t\"show\": true,\n"
				+ "\t\t\t\"disabled\": true\n"
				+ "\t\t}\n"
				+ "\t}, {\n"
				+ "\t\t\"childList\": [],\n"
				+ "\t\t\"label\": \"附件档案\",\n"
				+ "\t\t\"type\": \"EmpTab\",\n"
				+ "\t\t\"key\": \"entity.hr.EmpFileAttachment\",\n"
				+ "\t\t\"props\": {\n"
				+ "\t\t\t\"show\": true,\n"
				+ "\t\t\t\"disabled\": false\n"
				+ "\t\t}\n"
				+ "\t}, {\n"
				+ "\t\t\"childList\": [],\n"
				+ "\t\t\"label\": \"薪资信息\",\n"
				+ "\t\t\"type\": \"EmpTab\",\n"
				+ "\t\t\"key\": \"entity.hr.EmpSalaryChange\",\n"
				+ "\t\t\"props\": {\n"
				+ "\t\t\t\"show\": true,\n"
				+ "\t\t\t\"disabled\": false\n"
				+ "\t\t}\n"
				+ "\t}, {\n"
				+ "\t\t\"childList\": [],\n"
				+ "\t\t\"label\": \"员工其他合同信息表\",\n"
				+ "\t\t\"type\": \"EmpTab\",\n"
				+ "\t\t\"key\": \"entity.hr.EmpOtherContract\",\n"
				+ "\t\t\"props\": {\n"
				+ "\t\t\t\"show\": true,\n"
				+ "\t\t\t\"disabled\": true\n"
				+ "\t\t}\n"
				+ "\t}],\n"
				+ "\t\"label\": \"员工信息详情\",\n"
				+ "\t\"type\": \"EmpInfo\",\n"
				+ "\t\"key\": \"page.detail.employee\",\n"
				+ "\t\"script\": \"/**\\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系才到平台的技术支持获得服务（可能收费）。\\n* 我们可以用 JS 面板来开发一些定制度高功能。\\n* 你可以点击面板上方的 「使用帮助」了解。\\n*/\\n\\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\\nexport function didMount() {\\n\\tconsole.log(`「页面 JS」：当前页面地址 ${location.href}`);\\n // 更多相关 API 请参考：https://aliwork.com/developer/API\\n}\"\n"
				+ "}";
		System.out.println(FastjsonUtil.toObject(json, PageDetailDto.class));

	}
}