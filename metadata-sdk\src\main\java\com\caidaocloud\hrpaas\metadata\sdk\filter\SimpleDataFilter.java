package com.caidaocloud.hrpaas.metadata.sdk.filter;

import com.caidaocloud.dto.FilterFunction;
import lombok.Data;

import java.util.List;

@Data
public class SimpleDataFilter extends DataFilter implements FilterFunction {

    private final String type = "simple";

    private String property;

    private FilterOperator operator;

    private String value;

    private List<String> values;


    protected SimpleDataFilter(FilterOperator operator, String property, String value) {
        this.property = property;
        this.operator = operator;
        this.value = value;
    }

    protected SimpleDataFilter(FilterOperator operator, String property, List<String> values) {
        this.property = property;
        this.operator = operator;
        this.values = values;
    }

    protected SimpleDataFilter() {

    }

}
