package com.caidaocloud.hrpaas.metadata.sdk.enums;

import com.google.common.collect.Lists;

public enum PropertyDataType {
    String,
    Number,
    Timestamp,
    Enum,
    Integer,
    Boolean,
    Attachment,
    Object,
    PID,
    PROVINCE_CITY,
    Job_Grade_Range,
    String_Array,
    Number_Array,
    Timestamp_Array,
    Integer_Array,
    Boolean_Array,
    Enum_Array,
    Data_Table,
    // 字典类型
    Dict,//no_   _txt
    // 关联模型，只有表单引擎使用
    Relation, Embedded,
    // 选人组件
    Emp,
    // 地址组件
    Address,
    // 手机号+区号、电话号+区号组件
    Operation,
    Phone;

    public boolean isArray(){
        return Lists.newArrayList(String_Array, Number_Array, Timestamp_Array, Integer_Array, Boolean_Array, Attachment, Enum_Array).contains(this);
    }

    public boolean isComponent(){
        return Lists.newArrayList(<PERSON><PERSON>, Job_Grade_Range, Dict, PROVINCE_CITY, Attachment, Enum, Emp, Address, Phone).contains(this);
    }

    public PropertyDataType toArray() {
        switch (this){
            case String:return String_Array;
            case Number:return Number_Array;
            case Timestamp:return Timestamp_Array;
            case Integer:return Integer_Array;
            case Boolean:return Boolean_Array;
            default:return this;
        }
    }
}
