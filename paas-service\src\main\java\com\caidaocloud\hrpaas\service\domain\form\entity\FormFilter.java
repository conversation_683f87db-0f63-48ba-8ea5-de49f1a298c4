package com.caidaocloud.hrpaas.service.domain.form.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import lombok.Data;

import java.util.Map;

@Data
public class FormFilter extends DataSimple {

    private String key;

    private String userId;

    private String config;

    public static FormFilter load(String key, String userId) {
        return DataQuery.identifier("entity.form.FormFilter")
                .filter(DataFilter.eq("userId",userId).andEq("key", key),
                        FormFilter.class).getItems().stream().findFirst().orElse(null);
    }

    public void addOrUpdate() {
        DataDelete.identifier("entity.form.FormFilter")
                .batchDelete(DataFilter.eq("userId",userId).andEq("key", key));
        DataInsert.identifier("entity.form.FormFilter")
                .insert(this);
    }
}
