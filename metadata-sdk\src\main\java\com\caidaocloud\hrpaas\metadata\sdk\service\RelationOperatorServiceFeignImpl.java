package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.RelationOperationDto;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IEntityDataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMdDataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.query.MdQueryTL;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class RelationOperatorServiceFeignImpl implements  RelationOperatorService{

    @Autowired
    private IEntityDataFeign entityDataFeign;

    @Autowired
    private IMdDataFeign mdDataFeign;

    @Override
    public void operate(RelationOperationDto operation) {

        val result = MdQueryTL.get()?mdDataFeign.operateRelation(operation):entityDataFeign.operateRelation(operation);
        if(!result.isSuccess()){
            log.error("访问metadata异常, {}", result);
            throw new ServerException("访问metadata异常");
        }
    }
}
