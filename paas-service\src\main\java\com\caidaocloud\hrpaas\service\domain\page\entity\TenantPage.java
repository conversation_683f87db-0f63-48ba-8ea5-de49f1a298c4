package com.caidaocloud.hrpaas.service.domain.page.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.AuthResourceDto;
import com.caidaocloud.hrpaas.service.application.page.constant.Menu;
import com.caidaocloud.hrpaas.service.application.page.dto.ImportFunctionDto;
import com.caidaocloud.hrpaas.service.application.page.feign.ImportExportFeign;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormDef;
import com.caidaocloud.metadata.domain.entity.BaseEntity;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageTemplate;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.domain.page.repository.ITenantPageRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.val;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class TenantPage extends BaseEntity<ITenantPageRepository> {

    private String id;

    private String name;

    private String icon;

    private PageType type;

    private String desc;

    private Boolean showNav;

    private String parentId;

    private boolean isStandard;

    private String joinModel;

    private PageTemplate pageTemplate;

    private long createTime;

    private String createBy;

    private long updateTime;

    private String updateBy;

    private String path;

    public static void deleteById(String id) {
        ((ITenantPageRepository)repository(TenantPage.class)).deleteById(id);
    }

    public static List<TenantPage> loadAll() {
        return ((ITenantPageRepository)repository(TenantPage.class)).loadAll();
    }

    public static TenantPage loadOne(String id) {
        return repository(TenantPage.class).load(id).getOrNull();
    }

    public List<AuthResourceDto> authResource() {
        if (Menu.HOME.getPageId().equals(id)) {
            return new ArrayList<>();
        }
        List<AuthResourceDto> auth = Lists.list();
        String code = authCode();
        String parentAuthCode = parentAuthCode();
        if (type == PageType.ModelPage) {
            val formDefId = joinModel;
            val form = FormDef.loadById(formDefId).get();
            auth.addAll(Lists.list(
                    new AuthResourceDto().setCode(code)
                            .setName("表单-" + form.getName()).setLang("zh").setCategory("MENU")
                            .setUrl("/api/hrpaas/v1/form/data/page/" + formDefId)
                            .setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .setResourceAction("VIEW").setParentCode(parentAuthCode),
                    new AuthResourceDto().setCode(code)
                            .setName("表单-" + form.getName()).setLang("zh").setCategory("MENU")
                            .setUrl("/api/hrpaas/v1/form/data/pageSelf/" + formDefId)
                            .setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .setResourceAction("VIEW").setParentCode(parentAuthCode),
                    new AuthResourceDto().setCode(code)
                            .setName("表单-" + form.getName()).setLang("zh").setCategory("MENU")
                            .setUrl("/api/hrpaas/v1/form/data/one/" + formDefId + "/:self_described")
                            .setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .setResourceAction("VIEW").setParentCode(parentAuthCode),
                    new AuthResourceDto().setCode(code)
                            .setName("表单-" + form.getName()).setLang("zh").setCategory("MENU")
                            .setUrl("/api/hrpaas/v1/form/data/one/" + formDefId)
                            .setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .setResourceAction("VIEW").setParentCode(parentAuthCode),
                    new AuthResourceDto().setCode("FORM_DATA_ADD_" + formDefId)
                            .setName("新增").setLang("zh").setCategory("FUNC")
                            .setUrl("/api/hrpaas/v1/form/data/create/" + formDefId)
                            .setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .setResourceAction("VIEW").setParentCode("FORM_DATA_VIEW_" + formDefId),
                    new AuthResourceDto().setCode("FORM_DATA_EDIT_" + formDefId)
                            .setName("编辑").setLang("zh").setCategory("FUNC")
                            .setUrl("/api/hrpaas/v1/form/data/update/" + formDefId)
                            .setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .setResourceAction("VIEW").setParentCode("FORM_DATA_VIEW_" + formDefId),
                    new AuthResourceDto().setCode("FORM_DATA_DEL_" + formDefId)
                            .setName("删除").setLang("zh").setCategory("FUNC")
                            .setUrl("/api/hrpaas/v1/form/data/delete/" + formDefId)
                            .setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .setResourceAction("VIEW").setParentCode("FORM_DATA_VIEW_" + formDefId),
                    new AuthResourceDto().setCode("FORM_DATA_IMPORT_" + formDefId)
                            .setName("导入").setLang("zh").setCategory("FUNC")
                            .setUrl("/api/import/v1/job/import")
                            .setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .setResourceAction("VIEW").setParentCode("FORM_DATA_VIEW_" + formDefId),
                    new AuthResourceDto().setCode("FORM_DATA_IMPORT_" + formDefId)
                            .setName("导入").setLang("zh").setCategory("FUNC")
                            .setUrl("api/import/v1/config/template")
                            .setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .setResourceAction("VIEW").setParentCode("FORM_DATA_VIEW_" + formDefId),
//                new AuthResourceDto().setCode("FORM_DATA_APPROVE_"+formDefId)
//                        .setName("审批").setLang("zh").setCategory("FUNC")
//                        .setUrl("/api/hrpaas/v1/form/data/approve/" + formDefId).setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
//                        .setResourceAction("VIEW").setParentCode("FORM_DATA_VIEW_"+formDefId),
                    new AuthResourceDto().setCode("FORM_DATA_REVOKE_"+formDefId)
                            .setName("撤回").setLang("zh").setCategory("FUNC")
                            .setUrl("/api/hrpaas/v1/form/data/revoke/" + formDefId).setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .setResourceAction("VIEW").setParentCode("FORM_DATA_VIEW_"+formDefId),
                    new AuthResourceDto().setCode("FORM_DATA_EXPORT_"+formDefId)
                            .setName("导出").setLang("zh").setCategory("FUNC")
                            .setUrl("/api/hrpaas/v1/form/data/export/" + formDefId).setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .setResourceAction("VIEW").setParentCode("FORM_DATA_VIEW_"+formDefId)
            ));
            SpringUtil.getBean(ImportExportFeign.class).add(new ImportFunctionDto("FORM_IMPORT_" + formDefId, "表单"+form.getName()));
        }
        else if (type == PageType.Folder) {
            Map<String, String> i18Name = FastjsonUtil.toObject(name, Map.class);
            auth.addAll(Lists.list(new AuthResourceDto().setCode(code)
                    .setName(i18Name.get("default")).setLang("zh").setCategory("MENU")
                    .setUrl("")
                    .setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId())
                    .setResourceAction("VIEW")
                    .setParentCode(parentAuthCode)));
        }
        return auth;
    }

    private String authCode() {
        switch (type) {
        case ModelPage:
            return "FORM_DATA_VIEW_" + joinModel;
        case Folder:
            return "FOLDER_" + id;
        default:
            return null;
        }
    }

    private String parentAuthCode() {
        String code = Menu.getCodeByNavBarPath(parentId);
        return code == null ? TenantPage.loadOne(parentId).authCode() : code;
    }
}
