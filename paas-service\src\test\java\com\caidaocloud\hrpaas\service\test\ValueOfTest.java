package com.caidaocloud.hrpaas.service.test;

public class ValueOfTest {

    private static volatile int num = 0;

    public void doSomething(String arg1, Object arg2) {
        System.out.println("String, Object");
    }

    public void doSomething(Object arg1, String arg2) {
        System.out.println("Object, String");
    }


    public static void test(){
        ValueOfTest test = new ValueOfTest();
        // test.doSomething(null, null);
    }

    public static void main(String[] args) {

        try {
            Class cl = ValueOfTest.class.getClassLoader().loadClass("com.caidaocloud.hrpaas.service.test.TestDto");
            System.out.println(cl);

            System.out.println(TestDto.class);
        } catch (Exception e){
            e.printStackTrace();
        }

        String str = "" + null;

        //num ++;
        System.out.println(str);

        System.out.println(String.valueOf(null));

        //System.out.println(num);
    }
}
