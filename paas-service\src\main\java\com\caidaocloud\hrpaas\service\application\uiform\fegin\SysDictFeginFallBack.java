package com.caidaocloud.hrpaas.service.application.uiform.fegin;

import com.caidaocloud.hrpaas.service.application.uiform.dto.SysDictDataSourceDto;
import com.caidaocloud.hrpaas.service.application.uiform.dto.SysDictRequestDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SysDictFeginFallBack implements ISysDictFeginClient{

    @Override
    public Result<SysDictDataSourceDto> postSysDictList(SysDictRequestDto dto) {
        return Result.fail();
    }

    @Override
    public Result<List<SysDictDataSourceDto>> getSysDictList(String typeCode, String belongModule) {
        return Result.fail();
    }
}
