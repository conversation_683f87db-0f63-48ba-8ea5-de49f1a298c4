package com.caidaocloud.hrpaas.service.application.uiform.enums;

/**
 * <AUTHOR>
 * @Date 2022/1/7
 */
public enum SchemaPropertyTypeEnum {
    Input("text"),
    Textarea("textArea"),
    InputNumber("int"),
    <PERSON><PERSON><PERSON>("date"),
    DateRange<PERSON>icker("dateRange"),
    Select("enum"),
    <PERSON><PERSON>("boolean"),
    EnumSelect("EnumSelect"),
    TreeSelect("TreeSelect"),
    <PERSON>oseRank("ChooseRank"),
    ModelAssociateSelect("COMMON"),
    EmbeddedModel("EMBEDDED"),
    Cascader("Cascader");

    private String widgetType;

    SchemaPropertyTypeEnum(String widgetType) {
        this.widgetType = widgetType;
    }

    public String getWidgetType() {
        return widgetType;
    }

    public void setWidgetType(String widgetType) {
        this.widgetType = widgetType;
    }

    public static String getWidgetType(String fieldType) {
        for (SchemaPropertyTypeEnum typeEnum : SchemaPropertyTypeEnum.values()) {
            if (typeEnum.toString().equals(fieldType)) {
                return typeEnum.getWidgetType();
            }
        }
        return "";
    }
}
