package com.caidaocloud.hrpaas.metadata.sdk.feign;

import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataAddressVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SchemaDto;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.TxFeignConfiguration;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "caidaocloud-hr-paas-service",
        fallback = MetadataFeignClientFallBack.class,
        configuration = {FeignConfiguration.class, TxFeignConfiguration.class},
        contextId = "metadataFeign", qualifier = "metadataFeign")
public interface IMetadataFeign {

    @GetMapping("/api/hrpaas/v1/metadata")
    Result one(@RequestParam("identifier") String identifier);

    @PostMapping("/api/hrpaas/metadata/migration/v1/upgrade")
    Result upgrade(List<SchemaDto> schemaList);

    @PostMapping("/api/hrpaas/v1/address/codeList")
    Result<List<MetadataAddressVo>> getAddressByStr(List<String> list);
}
