package com.caidaocloud.metadata.domain.entity

import com.caidaocloud.metadata.domain.enums.OperationTarget
import com.caidaocloud.metadata.domain.enums.OperationType
import com.caidaocloud.metadata.domain.repository.IDataModificationRepository


class DataModification (
    var identifier: String,
    val targetId: String,
    val operationTarget: OperationTarget,
    val data: String,
    val createTime: Long,
    val createBy: String,
    val operation: OperationType,
    val startTime : Long
): BaseEntity<IDataModificationRepository>(){
//    companion object{
//        fun initTable() {
//            (repository(DataModification::class.java) as IDataModificationRepository).createTable()
//        }
//    }
}

