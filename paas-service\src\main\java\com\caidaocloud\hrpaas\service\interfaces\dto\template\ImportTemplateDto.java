package com.caidaocloud.hrpaas.service.interfaces.dto.template;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("导入模板实体")
public class ImportTemplateDto {
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("tenantId")
    private String tenantId;

    @ApiModelProperty("附件")
    private Attachment files;

    @ApiModelProperty("模版code")
    private String templateCode;
}
