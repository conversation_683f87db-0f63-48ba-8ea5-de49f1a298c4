package com.caidaocloud.hrpaas.metadata.sdk.feign;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SysParamDictDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class DictFeignClientFallBack implements IDictFeignClient {
    @Override
    public Result one(String dictCode) {
        return Result.fail();
    }

    @Override
    public Result<SysParamDictDto> getDictByTypeAndCode(String dictCode, String dictType) {
        return Result.fail();
    }

    @Override
    public Result<SysParamDictDto> getDict(Long id) {
        return Result.fail();
    }

    @Override
    public Result<List<DictSimple>> getEnableDictList(String typeCode, String belongModule) {
        return null;
    }

}
