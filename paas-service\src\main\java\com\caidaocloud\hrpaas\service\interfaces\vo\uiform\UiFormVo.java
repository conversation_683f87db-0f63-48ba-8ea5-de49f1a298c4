package com.caidaocloud.hrpaas.service.interfaces.vo.uiform;

import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageType;
import com.caidaocloud.hrpaas.service.infrastructure.commons.enums.PageTemplate;
import com.caidaocloud.hrpaas.service.interfaces.dto.uiform.UiFormFieldDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-07-02
 */
@Data
public class UiFormVo {
    /**
     * 表单ID
     */
    @ApiModelProperty("表单ID")
    private String bid;
    /**
     * 表单名称
     */
    @ApiModelProperty("表单名称")
    private String name;

    /**
     * 表单类型
     */
    @ApiModelProperty("表单类型")
    private PageType type;

    /**
     * 管理页面关联的表单ID
     */
    @ApiModelProperty("关联表单ID")
    private String sourceId;

    /**
     * 表单组件列表配置
     */
    @ApiModelProperty("组件列表配置")
    private String config;

    /**
     * 表单组件字段列表配置
     */
    @ApiModelProperty("字段列表配置")
    private List<UiFormFieldDto> fieldConfig;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private long createTime;

    @ApiModelProperty("描述")
    private String desc;

    @ApiModelProperty("菜单名称")
    private String menuName;

    @ApiModelProperty("导航显示：true显示，false不显示")
    private Boolean showNav;

    @ApiModelProperty("关联模型")
    private String joinModel;

    @ApiModelProperty("关联模型的名称")
    private String joinModelName;

    @ApiModelProperty("页面模板")
    private PageTemplate pageTemplate;

    @ApiModelProperty("更新时间")
    private long updateTime;

    /**
     * 流程功能类型
     */
    @ApiModelProperty("流程功能类型")
    private String functionType;

    @ApiModelProperty("页面schema")
    private String schema;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否内置模型
     */
    private Boolean builtIn;
}
