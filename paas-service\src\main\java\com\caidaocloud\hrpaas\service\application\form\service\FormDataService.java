package com.caidaocloud.hrpaas.service.application.form.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.poi.excel.ExcelUtil;
import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect;
import com.caidaocloud.hrpaas.service.application.feildMapping.service.MappingConfigService;
import com.caidaocloud.hrpaas.service.application.form.dto.*;
import com.caidaocloud.hrpaas.service.application.form.feign.IWfRuntimeFeign;
import com.caidaocloud.hrpaas.service.domain.feildMapping.entity.PreMappingConfig;
import com.caidaocloud.hrpaas.service.domain.form.entity.*;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDataStatus;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormDefStatus;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormTarget;
import com.caidaocloud.hrpaas.service.domain.form.enums.FormWidgetType;
import com.caidaocloud.hrpaas.service.interfaces.vo.form.FormDataMapVo;
import com.caidaocloud.hrpaas.service.interfaces.vo.form.FormPageVo;
import com.caidaocloud.metadata.domain.entity.PropertyEnumDef;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FormDataService {

    @Autowired
    private TransferFromDataHolder transferFromDataHolder;

    @Autowired
    private MqMessageProducer<RabbitBaseMessage> msgProducer;

    @Autowired
    private IWfRegisterFeign iWfRegisterFeign;

    @Autowired
    private IWfRuntimeFeign iWfRuntimeFeign;

    @Resource
    private MappingConfigService mappingConfigService;

    @Value("${paas.form.export.lines:5000}")
    private int exportLines;

    @Autowired
    private FormDataExcelExportServer formDataExcelExportServer;

    @PaasTransactional
    public String create(String formId, FormDataDto formDataDto, boolean self) {
        val empId = String.valueOf(SecurityUserUtil.getSecurityUserInfo().getEmpId());
        return create(formId, formDataDto, self, empId, false);
    }


    /**
     * @param formId
     * @param formDataDto
     * @param self
     * @param empId
     * @param isSub       员工子集
     * @return
     */
    @PaasTransactional
    public String create(String formId, FormDataDto formDataDto, boolean self, String empId, boolean isSub) {
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        if (!FormDefStatus.PUBLISHED.equals(formDef.getStatus())) {
            throw new ServerException("表单定义未发布/已禁用");
        }
        try {
            if ((self || isSub) && FormTarget.EMP.equals(formDef.getTarget())) {
                val empSimple = new EmpSimple();

                empSimple.setEmpId(empId);
                EmpSimple.doEmpSimple(empSimple);
                val emp = FastjsonUtil.convertObject(empSimple, Map.class);
                formDataDto.getPropertiesMap().put("form_owner", emp);
                String finalEmpId = empId;
                DataSimple empData = DataQuery.identifier("entity.hr.EmpWorkInfo")
                        .filter(DataFilter.eq("empId", finalEmpId)
                                .andNe("deleted", Boolean.TRUE.toString()), DataSimple.class).getItems().get(0);
                // formDataDto.getPropertiesMap().put("form_owner_organize", ((SimplePropertyValue) empData.getProperties()
                //         .get("organize")).getValue());
                // formDataDto.getPropertiesMap().put("form_owner_emp_type", empData.getProperties().get("empType"));
                formDef.getProperties().stream()
                        .filter(it -> it.getProperty().equals("form_owner")).forEach(propertyDef -> {
                            if (!propertyDef.getComponentDetail().isEmpty()) {
                                for (FormPropEmpDef.EmpPropertyType empPropertyType : FormPropEmpDef.EmpPropertyType.values()) {
                                    Map empInfo = DataQuery.identifier(empPropertyType.toIdentifier())
                                            .filter(DataFilter.eq("empId", finalEmpId)
                                                    .andNe("deleted", Boolean.TRUE.toString()), DataSimple.class)
                                            .getItems().stream().map(item->(Map)item.getProperties()).findAny().orElse(Maps.map());
                                    empInfo.keySet().forEach(key -> {
                                        val value = empInfo.get(key);
                                        if (value != null) {
                                            if (value instanceof SimplePropertyValue) {
                                                empInfo.put(key, ((SimplePropertyValue) value).getValue());
                                            } else {
                                                empInfo.put(key, FastjsonUtil.convertObject(value, Map.class));
                                            }
                                        }
                                    });
                                    emp.put(empPropertyType, empInfo);
                                }
                            }
                        });
            }
            FormData formData = formDataDto.toEntity(formDef, null, self);//FastjsonUtil.convertObject(formDataDto, FormData.class);
            formData.setFormId(formId);
            formData.setCreateTime(System.currentTimeMillis());
            formData.setUpdateTime(formData.getCreateTime());
            val userId = SecurityUserUtil.getSecurityUserInfo().getUserId();
            formData.setCreateBy(String.valueOf(userId));
            formData.setUpdateBy(String.valueOf(userId));
            boolean workflowEnabled = false;
            if (formDef.isWorkflowAvailable()) {
                workflowEnabled = true;
            }
            formData.setStatus(workflowEnabled ? FormDataStatus.APPROVING : FormDataStatus.SAVED);
            val dataId = formData.create(formDef);

            if (workflowEnabled) {
                val begin = new WfBeginWorkflowDto();
                empId = formData.getTargetId();
                if (!formDef.getTarget().equals(FormTarget.EMP)) {
                    empId = null == SecurityUserUtil.getSecurityUserInfo().getEmpId() ?
                            null : SecurityUserUtil.getSecurityUserInfo().getEmpId().toString();
                }
                begin.setApplicantId(empId);
                if (StringUtils.isNotEmpty(empId)) {
                    String empName = ((SimplePropertyValue) DataQuery.identifier("entity.hr.EmpBasicInfo")
                            .one(empId, DataSimple.class).getProperties().get("name")).getValue();
                    begin.setApplicantName(empName);
                }
                begin.setBusinessId(dataId);
                begin.setEventTime(System.currentTimeMillis());
                begin.setFuncCode("FORM-FUNC-" + formId);
                TransactionAspect.registerSynchronization(() -> {
                            val result = iWfRegisterFeign.begin(begin);
                            if ("".equals(result.getData())) {
                                throw new ServerException("workflow not enabled");
                            }
                        }
                );

            } else {
                if (StringUtils.isNotEmpty(formDataDto.getWorkflowTaskId())) {
                    iWfRuntimeFeign.approve(new WfTaskApproveDTO(formDataDto.getWorkflowTaskId()));
                }
                FormData data = loadById(formId, dataId);
                data.setTargetId(formDataDto.getTargetId());
                syncEmpData(data, formDef, formDataDto.getDataStartTime());
            }
            return dataId;
        } finally {
            transferFromDataHolder.clear();
        }
    }

    private void syncEmpData(FormData formData, FormDef formDef, Long dataStartTime) {
        if (formData != null) {
            FormDataMapVo dataMapVo = FormDataMapVo.fromFormData(formData);
            Map propertiesMap = dataMapVo.getPropertiesMap();
            log.info("handlerMappingConfig.getFormDataMap propertiesMap = {}", FastjsonUtil.toJson(propertiesMap));

            if (StringUtils.isNotEmpty(formDef.getMappingId())) {
                List<PreMappingConfig> configList = mappingConfigService.listByIds(formDef.getMappingId());
                log.info("handlerMappingConfig.listByIds result = {}", FastjsonUtil.toJson(configList));
                if (!CollectionUtils.isEmpty(configList)) {
                    Map<String, List<PreMappingConfig>> targetMap = Sequences.sequence(configList).toMap(PreMappingConfig::getTarget);
                    for (Map.Entry<String, List<PreMappingConfig>> entry : targetMap.entrySet()) {
                        saveEmpData(formData.getTargetId(), entry.getValue(), propertiesMap, dataStartTime);
                    }
                }
            }
        }
    }

    public void saveEmpData(String empId, List<PreMappingConfig> mappingConfigs, Map propertiesMap, Long dataStartTime) {
        for (PreMappingConfig mappingConfig : mappingConfigs) {
            List<FormPropertyMapping> propertyMappings = mappingConfig.getMapping();
            DataSimple empData = MappingConfigDto.queryEmpData(mappingConfig.getTarget(), empId, dataStartTime == null ? com.caidaocloud.util.DateUtil.getCurrentTimestamp() : dataStartTime);
            if (empData != null) {
                for (FormPropertyMapping mapping : propertyMappings) {
                    convertData(mapping.getTarget(), (String) propertiesMap.get(mapping.getSource()), empData.getProperties());
                }
                empData.setUpdateTime(System.currentTimeMillis());
                DataUpdate.identifier(empData.getIdentifier()).update(empData);
            }
        }
    }

    public void convertData(String property, String value, NestPropertyValue properties) {
        if (StringUtils.isNotEmpty(value)) {
            switch (property) {
                case "workplace":
                    PageResult<DataSimple> workPlace = DataQuery.identifier("entity.hr.Workplace").filter(DataFilter.eq("name", value), DataSimple.class);
                    PreCheck.preCheckArgument(CollectionUtils.isEmpty(workPlace.getItems()), "工作地不存在");
                    DataSimple dataSimple = workPlace.getItems().get(0);
                    properties.add(property, dataSimple.getBid());
                    properties.add("workplaceTxt", dataSimple.getProperties().get("name"));
                    break;
                default:
                    properties.add(property, value);
                    break;
            }
        }
    }

    @Transactional
    @PaasTransactional
    public void workflowRejectCallback(String businessKey, String tenantId) {
        val userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        val splitBusinessKey = businessKey.split("_FORM-FUNC-");
        val formId = splitBusinessKey[1];
        val id = splitBusinessKey[0];
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        FormData.updateApproveStatusAndReturnTaskId(formId, id, FormDataStatus.REJECTED);
    }

    @Transactional
    @PaasTransactional
    public void workflowCallback(String businessKey, String tenantId) {
        val userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        val splitBusinessKey = businessKey.split("_FORM-FUNC-");
        val formId = splitBusinessKey[1];
        val id = splitBusinessKey[0];
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        val workflowTaskId = FormData.updateApproveStatusAndReturnTaskId(formId, id, FormDataStatus.APPROVED);
        if (StringUtils.isNotEmpty(workflowTaskId)) {
            iWfRuntimeFeign.approve(new WfTaskApproveDTO(workflowTaskId));
        }
        syncEmpData(loadById(formId, id), formDef, null);
    }

    public FormData loadById(String formId, String id) {
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        val data = FormData.loadById(formId, id, formDef, false, null, false);
        return data;
    }

    public FormData loadMineById(String formId, String id) {
        return loadMineById(formId, id, String.valueOf(SecurityUserUtil.getSecurityUserInfo().getEmpId()));
    }

    public FormData loadMineById(String formId, String id, String empId) {
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        val data = FormData.loadById(formId, id, formDef, true, empId, false);
        return data;
    }

    public FormData loadSubByEmpId(String formId, String empId, String id) {
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        val data = FormData.loadById(formId, null, formDef, false, empId, true);
        return data;
    }

    public void approve(String formId, FormDataApprovalDto formDataApprovalDto) {
        val formDef = FormDef.loadById(formId).get();
        val dataId = StringUtils.substringBefore(formDataApprovalDto.getBusinessKey(), "_");
        val propertiesMap = formDataApprovalDto.getFieldData();
        val existed = loadById(formId, dataId);
        val oldFormData = FormDataMapVo.fromFormData(existed);
        if (!oldFormData.getStatus().equals(FormDataStatus.APPROVING)) {
            throw new ServerException("表单状态异常");
        }
        for (String writableField : formDataApprovalDto.getWritableFields()) {
            oldFormData.getPropertiesMap().put(writableField, propertiesMap.get(writableField));
        }

        FormDataDto dataDto = new FormDataDto();
        dataDto.setId(dataId);
        dataDto.setPropertiesMap(oldFormData.getPropertiesMap());
        updateData(formDef, dataDto, existed, false);

        iWfRuntimeFeign.approve(new WfTaskApproveDTO(formDataApprovalDto.getTaskId(), formDataApprovalDto.getChoice(), formDataApprovalDto.getComment()));
    }

    private void updateData(FormDef formDef, FormDataDto formDataDto, FormData existed, boolean self) {
        if (existed == null) {
            throw new ServerException("表单数据不存在");
        }
        FormData formData = formDataDto.toEntity(formDef, existed, self);//FastjsonUtil.convertObject(formDataDto, FormData.class);
        formData.setFormId(formDef.getId());
        formData.setCreateTime(existed.getCreateTime());
        formData.setUpdateTime(System.currentTimeMillis());
        val userId = SecurityUserUtil.getSecurityUserInfo().getUserId();
        formData.setCreateBy(existed.getCreateBy());
        formData.setUpdateBy(String.valueOf(userId));
        formData.setStatus(existed.getStatus());
        formData.setSysProcessCode(existed.getSysProcessCode());
        formData.update(formDef);

        val dbFormDef = FormDef.loadById(formData.getFormId()).getOrThrow(new ServerException("表单定义不存在"));
        syncEmpData(loadById(formData.getFormId(), formData.getId()), dbFormDef, null);
    }

    @Transactional
    @PaasTransactional
    public void update(String formId, FormDataDto formDataDto, boolean self) {
        //not completely implemented
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        if (!FormDefStatus.PUBLISHED.equals(formDef.getStatus())) {
            throw new ServerException("表单定义未发布");
        }
        if (formDef.isWorkflowAvailable()) {
            throw ServerException.globalException(ErrorMessage.fromCode("form.with.workflow.update.forbidden"));
        }
        FormData existed = FormData.loadById(formDef.getId(), formDataDto.getId(), formDef, self, String.valueOf(SecurityUserUtil.getSecurityUserInfo()
                .getEmpId()), false);
        updateData(formDef, formDataDto, existed, self);
    }

    public void delete(String formId, String id, boolean self) {
        delete(formId, id, self, String.valueOf(SecurityUserUtil.getSecurityUserInfo().getEmpId()), false);
    }

    public void delete(String formId, String id, boolean self, String empId, boolean isSub) {
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        val formData = FormData.loadById(formId, id, formDef, self, empId, isSub);
        if (null == formData) {
            throw new ServerException("表单不存在");
        }
        if (!FormDataStatus.SAVED.equals(formData.getStatus()) && null != formData.getStatus()) {
            throw new ServerException("表单不允许删除");
        }
        FormData.delete(formId, id);
    }

    public FormPageVo page(String formId, int pageSize, int pageNo, String keywords, FormDataStatus status, boolean self, List<FilterElement> filters) {
        return page(formId, pageSize, pageNo, keywords, status, self, filters, String.valueOf(SecurityUserUtil.getSecurityUserInfo()
                .getEmpId()), false,false);
    }

    public FormPageVo page(String formId, int pageSize, int pageNo, String keywords, FormDataStatus status, boolean self, List<FilterElement> filters, String empId, boolean isPlain,boolean emptyStatus) {
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        if (!FormDefStatus.PUBLISHED.equals(formDef.getStatus())) {
            throw new ServerException("表单定义未发布/已禁用");
        }
        val result = new FormPageVo();
        val page = FormData.page(keywords, status, formId, pageSize, pageNo, formDef, self && FormTarget.EMP.equals(formDef.getTarget()), self && !FormTarget.EMP.equals(formDef.getTarget()), filters, empId,emptyStatus);
        Map<String, FormPropDef> formPropDefMap = null;
        if (isPlain) {
            formPropDefMap = formDef.getProperties().stream().collect(Collectors.toMap(e -> e.getProperty(), e -> e, (v1, v2) -> v1));
        }
        Map<String, FormPropDef> finalFormPropDefMap = formPropDefMap;
        val list = page.getItems().stream().map(it -> {
            Map<String, Object> data = Maps.map();
            if (formDef.isWorkflowAvailable()) {
                data.put("formStatus", it.getStatus());
                data.put("sysProcessCode", it.getSysProcessCode());
            }
            data.put("id", it.getId());
            it.getProperties().forEach(formPropertyData -> {
                Object value = formPropertyData.getValue();
                if (isPlain) {
                    data.put(formPropertyData.getProperty(), formPropertyData.convertData(finalFormPropDefMap));
                    return;
                }
                if (formPropertyData.getDataType().isComponent()) {
                    value = FastjsonUtil.toObject((String) value, Map.class);
                } else if (formPropertyData.getDataType().equals(PropertyDataType.Enum_Array)) {
                    value = FastjsonUtil.toList((String) value, Map.class);
                    if (((List<?>) value).isEmpty()) {
                        value = null;
                    }
                } else if (formPropertyData.getDataType().equals(PropertyDataType.Boolean)) {
                    if (String.valueOf(value).equals("true")) {
                        value = true;
                    } else if (String.valueOf(value).equals("false")) {
                        value = false;
                    } else {
                        value = null;
                    }
                }
                data.put(formPropertyData.getProperty(), value);
            });
            return data;
        }).collect(Collectors.toList());
        result.setDataList(new PageResult<>(list, pageNo, pageSize, page.getTotal()));
        return result;
    }

    public void revoke(String formId, String id, boolean self) {
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        val form = self ? loadMineById(formId, id) : loadById(formId, id);
        val status = form.getStatus();
        if (!status.equals(FormDataStatus.APPROVING)) {
            throw ServerException.globalException(ErrorMessage.fromCode("form.can.not.be.revoked"));
        }
        // iWfRuntimeFeign.revoke(new WfTaskRevokeDTO(form.getWorkflowTaskId()));
        iWfRuntimeFeign.revoke(new WfTaskRevokeDTO(null).setBusinessKey(form.getId() + "_" + "FORM-FUNC-" + form.getFormId()));
        form.setStatus(FormDataStatus.REVOKE);
        val oldFormData = FormDataMapVo.fromFormData(form);

        FormDataDto dataDto = new FormDataDto();
        dataDto.setId(id);
        dataDto.setPropertiesMap(oldFormData.getPropertiesMap());
        updateData(formDef, dataDto, form, self);

        //form.update(formDef);
    }

    public Optional<FormPropertyData> getPropertyValue(String formDefId, String id, String property) {
        if (StringUtils.isBlank(formDefId) || StringUtils.isBlank(id) || StringUtils.isBlank(property)) {
            return Optional.empty();
        }
        var formData = loadById(formDefId, id);
        if (CollectionUtils.isEmpty(formData.getProperties())) {
            return Optional.empty();
        }
        var propertyDataOptional = formData.getProperties().stream()
                .filter(e -> e.getProperty().equals(property)).findFirst();
        return propertyDataOptional;
    }

    public Map<String, String> getNoticeVar(String businessKey, List<String> variables) {
        Map<String, String> results = Maps.map();
        val splitBusinessKey = businessKey.split("_FORM-FUNC-");
        val formDefId = splitBusinessKey[1];
        val formId = splitBusinessKey[0];
        val propertiesMap = FormDataMapVo.fromFormData(loadById(formDefId, formId))
                .getPropertiesMap();
        for (String variable : variables) {
            val valueObj = propertiesMap.get(variable);
            String value = valueObj != null ? String.valueOf(valueObj) : null;
            results.put(variable, value);
        }
        return results;

    }

    public void export2(FormDef formDef, List<DataSimple> datas, HttpServletResponse response) throws IOException {
        ExportParams exportParams = new ExportParams();
        List<ExcelExportEntity> titleList = Lists.list();
        val propDefs = formDef.getProperties();
        List<List<String>> sheet = Lists.list();
        val properties = propDefs.stream().filter(it -> !Lists.list(FormWidgetType.CRUD, FormWidgetType.Link,
                FormWidgetType.Operation,FormWidgetType.RichText).contains(it.getWidgetType()) && !PropertyDataType.Attachment.equals(it.getDataType())).collect(Collectors.toList());
        List<String> titleNames = Lists.list();
        properties.forEach(it->{
            titleList.add(new ExcelExportEntity(it.getName(), it.getProperty()));
            if("form_owner".equals(it.getProperty())){
                titleList.add(new ExcelExportEntity("工号", "form_owner_work_no"));
            }
        });
        val dataMaps = datas.stream().map(data -> {
            Map dataMap = Maps.map();
            properties.stream().forEach(propDef -> {
                String value = null;
                var propertyValue = data.getProperties().get(propDef.getProperty());
                if (FormWidgetType.getSelectType().contains(propDef.getWidgetType())) {
                    propertyValue = data.getProperties().get(((FormSelectPropDef) propDef).txtPropKey());
                }
                if (null != propertyValue) {
                    if (propertyValue instanceof SimplePropertyValue) {
                        if (propDef.getDataType().isArray()) {
                            val values = ((SimplePropertyValue) propertyValue).getArrayValues()
                                    .stream().filter(it -> it != null).map(it -> {
                                        String transformed = it;
                                        if (PropertyDataType.Timestamp_Array.equals(propDef.getDataType())) {
                                            if (null != transformed) {
                                                val style = FastjsonUtil.toObject(propDef.getStyleExtras(), Map.class);
                                                String format = "yyyy-MM-dd";
                                                if (null != style.get("format") && !style.get("format").toString().isEmpty()) {
                                                    format = style.get("format").toString();
                                                }
                                                if(StringUtils.isEmpty(transformed)){
                                                    transformed = "";
                                                }else{
                                                    transformed = new SimpleDateFormat(format).format(new Date(Long.valueOf(transformed)));
                                                }
                                            }
                                        } else if (PropertyDataType.Boolean_Array.equals(propDef.getDataType())) {
                                            transformed = Boolean.valueOf(transformed) ? "是" : "否";
                                        } else if(PropertyDataType.Enum_Array.equals(propDef.getDataType())){
                                            for(PropertyEnumDef enumDef : propDef.getEnumDef()){
                                                if(enumDef.getValue().equals(transformed)){
                                                    transformed = enumDef.getDisplay();
                                                }
                                            }
                                        }
                                        return transformed;
                                    }).collect(Collectors.toList());
                            value = StringUtils.join(values, ",");
                        } else {
                            value = ((SimplePropertyValue) propertyValue).getValue();
                            if(PropertyDataType.Timestamp.equals(propDef.getDataType())){
                                if(StringUtils.isNotEmpty(value)){
                                    val style = FastjsonUtil.toObject(propDef.getStyleExtras(), Map.class);
                                    String format = "yyyy-MM-dd";
                                    if (null != style.get("format") && !style.get("format").toString().isEmpty()) {
                                        format = style.get("format").toString();
                                    }
                                    if(StringUtils.isEmpty(value)){
                                        value = "";
                                    }else{
                                        value = new SimpleDateFormat(format).format(new Date(Long.valueOf(value)));
                                    }
                                }
                            } else if (PropertyDataType.Boolean.equals(propDef.getDataType())) {
                                value = Boolean.valueOf(value) ? "是" : "否";
                            }
                        }
                    }
                    if (propertyValue instanceof DictSimple) {
                        value = ((DictSimple) propertyValue).getText();
                    }
                    if (propertyValue instanceof PhoneSimple) {
                        value = ((PhoneSimple) propertyValue).getValue();
                    }
                    if (propertyValue instanceof EnumSimple) {
                        value = ((EnumSimple) propertyValue).getText();
                    }
                    if (propertyValue instanceof Address) {
                        value = ((Address) propertyValue).getText();
                    }
                    if (propertyValue instanceof EmpSimple) {
                        value = ((EmpSimple) propertyValue).getName();
                    }
                }
                value = StringUtils.trimToEmpty(value);
                if("form_owner".equals(propDef.getProperty())){
                    dataMap.put(propDef.getProperty(), value);
                    dataMap.put("form_owner_work_no", propertyValue==null?"": StringUtils.trimToEmpty((((EmpSimple)propertyValue).getWorkno())));
                }else{
                    dataMap.put(propDef.getProperty(), value);
                }
            });
            return dataMap;
        }).collect(Collectors.toList());
        val workbook = ExcelExportUtil.exportBigExcel(exportParams, titleList, formDataExcelExportServer, dataMaps);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(formDef.getName() + ".xlsx", "UTF-8"));

        try{
            workbook.write(response.getOutputStream());
        }finally {
            response.getOutputStream().flush();
            response.getOutputStream().close();
            workbook.close();
        }


    }

    @SneakyThrows
    private void export(FormDef formDef, List<DataSimple> datas, HttpServletResponse response) {
        val propDefs = formDef.getProperties();
        List<List<String>> sheet = Lists.list();
        val properties = propDefs.stream().filter(it -> !Lists.list(FormWidgetType.CRUD, FormWidgetType.Link,
                FormWidgetType.Operation,FormWidgetType.RichText).contains(it.getWidgetType()) && !PropertyDataType.Attachment.equals(it.getDataType())).collect(Collectors.toList());
        List<String> titleNames = Lists.list();
        properties.forEach(it->{
            titleNames.add(it.getName());
            if("form_owner".equals(it.getProperty())){
                titleNames.add("工号");
            }
        });
        sheet.add(titleNames);
        datas.forEach(data -> {
            sheet.add(
                    properties.stream().map(propDef -> {
                        String value = null;
                        var propertyValue = data.getProperties().get(propDef.getProperty());
                        if (FormWidgetType.getSelectType().contains(propDef.getWidgetType())) {
                            propertyValue = data.getProperties().get(((FormSelectPropDef) propDef).txtPropKey());
                        }
                        if (null != propertyValue) {
                            if (propertyValue instanceof SimplePropertyValue) {
                                if (propDef.getDataType().isArray()) {
                                    val values = ((SimplePropertyValue) propertyValue).getArrayValues()
                                            .stream().filter(it -> it != null).map(it -> {
                                                String transformed = it;
                                                if (PropertyDataType.Timestamp_Array.equals(propDef.getDataType())) {
                                                    if (null != transformed) {
                                                        val style = FastjsonUtil.toObject(propDef.getStyleExtras(), Map.class);
                                                        String format = "yyyy-MM-dd";
                                                        if (null != style.get("format") && !style.get("format").toString().isEmpty()) {
                                                            format = style.get("format").toString();
                                                        }
                                                        if(StringUtils.isEmpty(transformed)){
                                                            transformed = "";
                                                        }else{
                                                            transformed = new SimpleDateFormat(format).format(new Date(Long.valueOf(transformed)));
                                                        }
                                                    }
                                                } else if (PropertyDataType.Boolean_Array.equals(propDef.getDataType())) {
                                                    transformed = Boolean.valueOf(transformed) ? "是" : "否";
                                                } else if(PropertyDataType.Enum_Array.equals(propDef.getDataType())){
                                                    for(PropertyEnumDef enumDef : propDef.getEnumDef()){
                                                        if(enumDef.getValue().equals(transformed)){
                                                            transformed = enumDef.getDisplay();
                                                        }
                                                    }
                                                }
                                                return transformed;
                                            }).collect(Collectors.toList());
                                    value = StringUtils.join(values, ",");
                                } else {
                                    value = ((SimplePropertyValue) propertyValue).getValue();
                                    if(PropertyDataType.Timestamp.equals(propDef.getDataType())){
                                        if(StringUtils.isNotEmpty(value)){
                                            val style = FastjsonUtil.toObject(propDef.getStyleExtras(), Map.class);
                                            String format = "yyyy-MM-dd";
                                            if (null != style.get("format") && !style.get("format").toString().isEmpty()) {
                                                format = style.get("format").toString();
                                            }
                                            if(StringUtils.isEmpty(value)){
                                                value = "";
                                            }else{
                                                value = new SimpleDateFormat(format).format(new Date(Long.valueOf(value)));
                                            }
                                        }
                                    } else if (PropertyDataType.Boolean.equals(propDef.getDataType())) {
                                        value = Boolean.valueOf(value) ? "是" : "否";
                                    }
                                }
                            }
                            if (propertyValue instanceof DictSimple) {
                                value = ((DictSimple) propertyValue).getText();
                            }
                            if (propertyValue instanceof PhoneSimple) {
                                value = ((PhoneSimple) propertyValue).getValue();
                            }
                            if (propertyValue instanceof EnumSimple) {
                                value = ((EnumSimple) propertyValue).getText();
                            }
                            if (propertyValue instanceof Address) {
                                value = ((Address) propertyValue).getText();
                            }
                            if (propertyValue instanceof EmpSimple) {
                                value = ((EmpSimple) propertyValue).getName();
                            }
                        }
                        value = StringUtils.trimToEmpty(value);
                        if("form_owner".equals(propDef.getProperty())){
                            return Lists.list(value, propertyValue==null?"": StringUtils.trimToEmpty((((EmpSimple)propertyValue).getWorkno())));
                        }else{
                            return Lists.list(value);
                        }
                    }).flatMap(it->it.stream()).collect(Collectors.toList())
            );
        });
        val writer = ExcelUtil.getBigWriter();
        try {
            writer.write(sheet);
            writer.flush(response.getOutputStream());
        } finally {
            writer.close();
        }
    }

    @SneakyThrows
    public void export(String formId, HttpServletResponse response) {
        val datas = DataQuery.identifier("entity.form." + formId).limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString()), DataSimple.class).getItems();
        export(FormDef.loadById(formId).get(), datas, response);
    }

    public void saveOrUpdateSub(String formId, FormDataDto formDataDto, String empId) {
        //not completely implemented
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        if (!FormDefStatus.PUBLISHED.equals(formDef.getStatus())) {
            throw new ServerException("表单定义未发布");
        }
        FormData existed = FormData.loadById(formDef.getId(), formDataDto.getId(), formDef, false, empId, true);
        if (existed != null) {
            if (formDef.isWorkflowAvailable()) {
                throw ServerException.globalException(ErrorMessage.fromCode("form.with.workflow.update.forbidden"));
            }

            updateData(formDef, formDataDto, existed, false);
        } else {
            create(formId, formDataDto, false, empId, true);
        }


    }

    public FormPageVo pageSub(String formId, int pageSize, int pageNo, String empId, boolean isPlain) {
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        return page(formId, pageSize, pageNo, null, formDef.isWorkflowAvailable() ? FormDataStatus.APPROVED : FormDataStatus.SAVED, true, new ArrayList<>(), empId, isPlain,true);
    }

    public void export(String formId, String keywords, FormDataStatus status, boolean self, List<FilterElement> filters, HttpServletResponse response) throws IOException {
        val formDef = FormDef.loadById(formId).getOrThrow(new ServerException("表单定义不存在"));
        if (!FormDefStatus.PUBLISHED.equals(formDef.getStatus())) {
            throw new ServerException("表单定义未发布/已禁用");
        }
        val page = FormData.page(keywords, status, formId, exportLines, 1, formDef, self && FormTarget.EMP.equals(formDef.getTarget()),
                self && !FormTarget.EMP.equals(formDef.getTarget()), filters,
                String.valueOf(SecurityUserUtil.getSecurityUserInfo().getEmpId()), DataSimple.class,false);
        export2(formDef, page.getItems(), response);
    }

}
