package com.caidaocloud.hrpaas.service.application.uiform.service;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.hrpaas.service.application.uiform.dto.SysParmDict;
import com.caidaocloud.hrpaas.service.infrastructure.util.SerializeUtil;
import com.caidaocloud.util.StringUtil;

import io.lettuce.core.api.async.RedisAsyncCommands;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class SysDictService {
    private static final String SYS_PARM_DICT_KEY = "SYS_PARM_DICT_KEY_";

    @Value("${caidaocloud.cache.newDict:false}")
    private boolean newDictCache;

    @Resource
    private CacheService cacheService;

    @Resource
    public RedisTemplate<String, Object> serializableRedisTemplate;

    public String getSysDictById(String dictId){
        if(StringUtil.isEmpty(dictId)){
            return dictId;
        }

        String cacheKey = String.format("%s%s", SYS_PARM_DICT_KEY, dictId);
        SysParmDict dictCache = newDictCache ? getDictByCache(cacheKey) : getDictByOldCache(cacheKey);
        if(null == dictCache || StringUtil.isEmpty(dictCache.getDictChnName())){
            return dictId;
        }

        return dictCache.getDictChnName();
    }

    /**
     * 从旧的 redis 中获取被序列化的字典缓存数据
     */
    private SysParmDict getDictByOldCache(String cacheKey){
        RedisConnectionFactory redisConnectionFactory = serializableRedisTemplate.getConnectionFactory();
        Object object = redisConnectionFactory.getConnection().getNativeConnection();
        RedisAsyncCommands redisAsyncCommands = (RedisAsyncCommands) object;

        Object str = redisAsyncCommands
                .getStatefulConnection()
                .sync().get(SerializeUtil.serialize(cacheKey));

        Object objectCache = SerializeUtil.unOldSerialize((byte[]) str);
        return (SysParmDict) objectCache;
    }

    /**
     * 从新的缓存中获取字典缓存数据
     */
    private SysParmDict getDictByCache(String cacheKey){
        String cacheVal = cacheService.getValue(cacheKey);
        log.info("getDictByCache cacheKey = {}, cacheVal = {}", cacheKey, cacheVal);
        return JSON.parseObject(cacheVal, SysParmDict.class);
    }
}
