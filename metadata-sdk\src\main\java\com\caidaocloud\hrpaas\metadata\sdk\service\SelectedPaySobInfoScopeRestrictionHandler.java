package com.caidaocloud.hrpaas.metadata.sdk.service;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.jarvis.cache.annotation.Cache;
import lombok.val;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class SelectedPaySobInfoScopeRestrictionHandler implements ScopeRestrictionHandler{
    @Override
    @Cache(key = "'select_pay_sob_info_scope_restriction_' + #args[0] + '_' + #args[1]",expire = 5 *60)
    public List<String> toValues(String userId, String simpleValues, SecurityUserInfo userInfo) {
        Boolean isFilter = AuthScopeFilterUtil.get();
        val tlUser = SecurityUserUtil.getThreadLocalSecurityUserInfo();
        try{
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            Map<String, String> simpleValuesMap = FastjsonUtil.toObject(simpleValues, Map.class);
            String values = simpleValuesMap.get("paySobInfoValue").toString();
            String property = simpleValuesMap.get("paySobInfo").toString();
            val splitValues = values.split(",");
            AuthScopeFilterUtil.put(false);
            List<String> splitValueList = Lists.list(splitValues);
            String filterProperty = property;
            val list = DataQuery.identifier("entity.payroll.PaySobInfo").limit(-1, 1).filterProperties(DataFilter.in(filterProperty, splitValueList),
                    Lists.list("bid"), System.currentTimeMillis()).getItems();
            return list.stream().map(it -> it.get("bid")).collect(Collectors.toList());
        }finally {
            if(null == tlUser){
                SecurityUserUtil.removeSecurityUserInfo();
            }else{
                SecurityUserUtil.setSecurityUserInfo(tlUser);
            }
            AuthScopeFilterUtil.put(isFilter);
        }
    }
}
