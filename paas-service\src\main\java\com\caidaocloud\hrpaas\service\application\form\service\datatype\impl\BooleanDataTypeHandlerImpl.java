package com.caidaocloud.hrpaas.service.application.form.service.datatype.impl;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.service.application.form.dto.WfRegistrySeqDto;
import com.caidaocloud.hrpaas.service.application.form.service.datatype.IDataTypeHandler;
import com.caidaocloud.hrpaas.service.domain.form.entity.FormPropDef;
import com.caidaocloud.workflow.dto.WfComponentValueDto;
import com.caidaocloud.workflow.enums.WfValueComponentEnum;
import com.google.common.collect.Lists;
import lombok.var;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 布尔datatype处理器
 *
 * <AUTHOR>
 * @date 2022/11/8
 **/
@Component
public class BooleanDataTypeHandlerImpl implements IDataTypeHandler {

    @Override
    public PropertyDataType getType() {
        return PropertyDataType.Boolean;
    }

    @Override
    public WfRegistrySeqDto generateSeq(FormPropDef formDef, String formDefId) {
        var seqCode = String.format("seq_%s_%s", formDef.getProperty(), formDefId);
        List<WfComponentValueDto> componentList = Lists.newArrayList();
        componentList.add(new WfComponentValueDto("开", "true"));
        componentList.add(new WfComponentValueDto("关", "false"));
        return new WfRegistrySeqDto(seqCode, formDef.getName(), "", WfValueComponentEnum.ENUM, componentList);
    }

}
