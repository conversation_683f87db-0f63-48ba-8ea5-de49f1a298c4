package com.caidaocloud.hrpaas.service.application.uiform.service.hook;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.util.SignUtil;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.groovy.runtime.InvokerHelper;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class HookTemplate implements ApplicationListener<ContextRefreshedEvent> {
    private HookBinding binding = new HookBinding();
    private Map<String, Object> scriptCache = new ConcurrentHashMap();
    private GroovyShell groovyShell = new GroovyShell();

    public void execute(String script, Map<String, Object> vars) {
        executeObject(script, vars);
    }

    private void setParameters(GroovyShell shell, Map<String, Object> vars) {
        if (vars == null) {
            return;
        }
        for (String key : vars.keySet()) {
            shell.setVariable(key, vars.get(key));
        }
    }

    public boolean executeBoolean(String script, Map<String, Object> vars) {
        Boolean rtn = (Boolean) executeObject(script, vars);
        return rtn.booleanValue();
    }

    public String executeString(String script, Map<String, Object> vars) {
        String str = String.valueOf(executeObject(script, vars));
        return str;
    }

    public int executeInt(String script, Map<String, Object> vars) {
        Integer rtn = (Integer) executeObject(script, vars);
        return rtn == null ? 0 : rtn.intValue();
    }

    public float executeFloat(String script, Map<String, Object> vars) {
        Float rtn = (Float) executeObject(script, vars);
        return rtn.floatValue();
    }

    public BigDecimal executeBigDecimal(String script, Map<String, Object> vars) {
        Object obj = executeObject(script, vars);
        BigDecimal rtn = new BigDecimal(0);
        if (obj != null) {
            if (obj instanceof BigDecimal) {
                rtn = (BigDecimal) obj;
            } else {
                if (StringUtils.isNotEmpty(String.valueOf(obj))) {
                    rtn = new BigDecimal(String.valueOf(obj));
                }
            }
        }
        return rtn;
    }

    public Object execObj(String script, Map<String, Object> vars) {
        log.info("Execute execObj, script={}, vars={}", script, JSON.toJSONString(vars));
        GroovyShell shell = new GroovyShell(binding);
        setParameters(shell, vars);
        script = script.replace("&apos;", "'").replace("&quot;", "\"").replace("&gt;", ">").replace("&lt;", "<").replace("&nuot;", "\n").replace("&amp;", "&");
        Object rtn = shell.evaluate(script);
        this.binding.clearVariables();
        return rtn;
    }

    public Object executeObject(String script, Map<String, Object> vars) {
        log.info("Execute executeObject, script={}, vars={}", script, JSON.toJSONString(vars));
        script = script.replace("&apos;", "'").replace("&quot;", "\"").replace("&gt;", ">").replace("&lt;", "<").replace("&nuot;", "\n").replace("&amp;", "&");
        String cacheKey = SignUtil.md5(script);

        Script shell = null;
        if (scriptCache.containsKey(cacheKey)) {
            shell = (Script) scriptCache.get(cacheKey);
        } else {
            shell = groovyShell.parse(script);
            scriptCache.put(cacheKey, shell);
        }

        this.binding.clearVariables();
        for (String key : vars.keySet()) {
            binding.setVariable(key, vars.get(key));
        }

        Object rtn = InvokerHelper.createScript(shell.getClass(), binding).run();
        return rtn;
    }

    public void clearCache() {
        groovyShell.getClassLoader().clearCache();
    }

    public boolean checkScript(String script, Map<String, Object> vars) {
        try {
            executeObject(script, vars);
            return true;
        } catch (Exception e) {
            log.error("checkScript err, script={}, vars={}", script, JSON.toJSONString(vars));
        } finally {
            clearCache();
        }

        return false;
    }

    public void setProperty(String name, Object val) {
        this.binding.setProperty(name, val);
    }

    public boolean hasProperty(String name) {
        return this.binding.hasProperty(name);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if(null == event || null == event.getApplicationContext()){
            log.error("ContextRefreshedEvent is null..........");
            return;
        }

        Map<String, BaseHookBindable> result = event.getApplicationContext().getBeansOfType(BaseHookBindable.class);
        if(null == result || result.isEmpty()){
            log.warn("BaseHookBindable is Empty..........");
            return;
        }

        for (String beanName : result.keySet()) {
            if(StringUtils.isNotBlank(beanName) && !hasProperty(beanName)) {
                BaseHookBindable scriptBindable = result.get(beanName);
                if(scriptBindable != null) {
                    setProperty(beanName, scriptBindable);
                }
            }
        }
    }
}
