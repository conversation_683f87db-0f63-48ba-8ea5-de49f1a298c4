package com.caidaocloud.metadata.infrastructure.repository.impl.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import com.caidaocloud.hrpaas.metadata.sdk.enums.ConditionIdentifier;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.FilterOperator;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SpecifiedMultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SpecifiedSimpleDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;

/**
 *
 * <AUTHOR>
 * @date 2023/7/26
 */
public class SpecifiedDataFilterParser  {
	private SpecifiedMultiDataFilter dataFilter;
	private long queryTime;

	private Map<ConditionIdentifier, String> aliasMap = Maps.map(ConditionIdentifier.WORK_INFO, "a.");
	private AtomicInteger alias = new AtomicInteger('b');


	private List<Object> params = new ArrayList<>();
	private StringBuilder whereBuilder = new StringBuilder();
	private StringBuilder selectBuilder = new StringBuilder("select a.emp_id ,a.name,a.workno from ");
	private String tenantId;

	private final String resultColumn = "empId";


	public SpecifiedDataFilterParser(SpecifiedMultiDataFilter dataFilter) {
		this(dataFilter, System.currentTimeMillis());
	}

	public SpecifiedDataFilterParser(SpecifiedMultiDataFilter dataFilter, long queryTime) {
		this.dataFilter = dataFilter;
		this.queryTime = queryTime;
		tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
	}


	public Pair<String, List<Object>> generateSqlAndParam() {
		generatePredict();
		generateSelect();
		return Pair.pair(selectBuilder.append(" where ").append(whereBuilder).toString(), params);
	}

	private void generateSelect() {
		Map.Entry<ConditionIdentifier, String> last = null;
		for (Map.Entry<ConditionIdentifier, String> entry : aliasMap.entrySet()) {
			String alias = entry.getValue();
			if (last == null) {
				appendFromSql(entry.getKey(), alias);
			}
			else {
				appendJoinSql(entry.getKey(), alias, last.getKey(), last.getValue());
			}
			last = entry;
		}
	}

	private void appendJoinSql(ConditionIdentifier curKey, String curAlias, ConditionIdentifier lastKey, String lastAlias) {
		String table = SnakeCaseConvertor.toSnake(curKey.identifier) + "_" + tenantId;
		selectBuilder.append(" join ").append(table).append(" as ").append(curAlias, 0, curAlias.length() - 1)
				.append(" on ").append(curAlias).append(curKey.keyColumn)
				.append('=').append(lastAlias).append(lastKey.keyColumn);

	}

	private void appendFromSql(ConditionIdentifier key, String alias) {
		String table = SnakeCaseConvertor.toSnake(key.identifier) + "_" + tenantId;
		selectBuilder.append(table).append(" as ").append(alias, 0, alias.length() - 1);
	}

	private void generatePredict() {
		generatePredict(dataFilter);
		for (Map.Entry<ConditionIdentifier, String> entry : aliasMap.entrySet()) {
			String alias = entry.getValue();
			whereBuilder.append(" and ")
					.append(alias).append("data_start_time <= ").append(queryTime)
					.append(" and ")
					.append(alias).append("data_end_time >= ").append(queryTime);
		}
	}

	private void generatePredict(SpecifiedMultiDataFilter dataFilter) {
		boolean flag = false;
		whereBuilder.append('(');
		for (DataFilter filter : dataFilter.getFilters()) {
			if (filter instanceof SpecifiedMultiDataFilter) {
				if (flag) {
					addConnector(dataFilter.getOperator());
				}
				generatePredict((SpecifiedMultiDataFilter) filter);
			}
			else {
				SpecifiedSimpleDataFilter specifiedSimpleDataFilter = (SpecifiedSimpleDataFilter) filter;
				String tableAlias = aliasMap.computeIfAbsent(specifiedSimpleDataFilter.getIdentifier(), k -> ((char) alias.getAndIncrement())+".");
				Pair<String, Object[]> pair = SqlGeneratorUtil.toWhereCauses(specifiedSimpleDataFilter.getIdentifier().identifier, specifiedSimpleDataFilter, tableAlias);
				if (flag) {
					addConnector(dataFilter.getOperator());
				}
				whereBuilder.append(pair.first());
				params.addAll(Lists.list(pair.second()));
			}
			flag = true;
		}
		whereBuilder.append(')');
	}

	private void addConnector(FilterOperator operator) {
		whereBuilder.append(' ')
				.append(operator)
				.append(' ');
	}

}
