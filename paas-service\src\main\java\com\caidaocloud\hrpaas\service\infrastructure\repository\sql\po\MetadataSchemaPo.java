package com.caidaocloud.hrpaas.service.infrastructure.repository.sql.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.hrpaas.service.domain.metadata.entity.MetadataSchemaDo;
import com.caidaocloud.metadata.infrastructure.repository.po.BasePo;
import lombok.Data;

@Data
@TableName("paas_metadata_schema")
public class MetadataSchemaPo extends BasePo<MetadataSchemaPo, MetadataSchemaDo> {

    private String schemaModel;

    /**
     * 脚本文件夹名
     */
    private Integer folder;

    /**
     * 版本
     */
    private Long version;

    /**
     * 脚本文件名称
     */
    private String schemaName;

    /**
     * 脚本内容
     */
    private String schemaInfo;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * UT 更新时间
     */
    private Long updateTime;

    /**
     * UT 更新人
     */
    private Long updateBy;
}
